package cn.xianlink.order.controller.region;

import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.common.api.enums.order.AccountTypeStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.api.constant.MoreGoodsStatusEnum;
import cn.xianlink.order.domain.bo.moreGoods.AddMoreGoodsBO;
import cn.xianlink.order.domain.bo.moreGoods.MoreGoodsConfirmBO;
import cn.xianlink.order.domain.bo.moreGoods.MoreGoodsPageBO;
import cn.xianlink.order.domain.bo.moreGoods.MoreGoodsRecordBo;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.domain.vo.moreGoods.MoreGoodsDetailVO;
import cn.xianlink.order.service.IMoreGoodsService;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 总仓助手-多货
 *
 * <AUTHOR>
 * @date 2024-05-25
 * @folder 总仓助手(小程序)/订单/多货单
 */
@Validated
@RequiredArgsConstructor
@RestController("regionMoreGoodsController")
@RequestMapping("/order/region/moreGoods")
public class MoreGoodsController extends BaseController {

    private final IMoreGoodsService iMoreGoodsService;

    @PostMapping("/moreGoodsPage")
    @ApiOperation(value = "多货单列表")
    public R<TableDataInfo<MoreGoodsDetailVO>> moreGoodsPage(@RequestBody MoreGoodsPageBO bo){
        return R.ok(iMoreGoodsService.moreGoodsPage(bo));
    }

    @GetMapping("/getMoreGoodsById/{id}")
    @ApiOperation(value = "多货单详情")
    public R<MoreGoodsDetailVO> getMoreGoodsById(@PathVariable("id") Long id){
        return R.ok(iMoreGoodsService.getMoreGoodsById(id));
    }

    @PostMapping("/addMoreGoods")
    @RepeatSubmit()
    @ApiOperation(value = "新建多货单")
    public R<Boolean> addMoreGoods(@RequestBody AddMoreGoodsBO bo){
        bo.setSourceType(AccountTypeStatusEnum.REGION.getCode());
        return R.ok(iMoreGoodsService.addMoreGoods(bo));
    }

    @PostMapping("/getByCityWhId")
    @ApiOperation(value = "根据城市仓id判断是否属地仓")
    public R<RemoteRegionWhVo> getByCityWhId(@RequestParam("cityWhId") Long cityWhId){
        return R.ok(iMoreGoodsService.getByCityWhId(cityWhId));
    }


    @PostMapping("/updateMoreGoods")
    @RepeatSubmit()
    @ApiOperation(value = "更新多货单")
    public R<Boolean> updateMoreGoods(@RequestBody MoreGoodsRecordBo bo){
        return R.ok(iMoreGoodsService.updateMoreGoods(bo));
    }

    @GetMapping("/getMoreGoodsCount")
    @ApiOperation(value = "获取多货单各状态数量")
    public R<CommonStatusCountVO> getMoreGoodsCount(@RequestParam(value = "regionWhId", required = false) Long regionWhId){
        AddMoreGoodsBO bo = new AddMoreGoodsBO();
        bo.setRegionWhId(regionWhId);
        bo.setStatus(MoreGoodsStatusEnum.UNCONFIRMED.getCode());
        return R.ok(iMoreGoodsService.getMoreGoodsCount(bo));
    }

    @PostMapping("/confirm")
    @RepeatSubmit()
    @ApiOperation(value = "确认多货单")
    public R<Boolean> confirm(@RequestBody MoreGoodsConfirmBO bo){
        return R.ok(iMoreGoodsService.confirm(bo));
    }

    @GetMapping("/getMoreGoodsByCode")
    @ApiOperation(value = "多货单详情，根据code查询")
    public R<MoreGoodsDetailVO> getMoreGoodsByCode(@RequestParam("code") String code){
        return R.ok(iMoreGoodsService.getMoreGoodsByCode(code));
    }

    @GetMapping("/getSupplierSkuIdById")
    @ApiOperation("根据批次id查询销售批次信息")
    public R<RemoteSupplierSkuInfoVo> getSupplierSkuInfoById(@RequestParam("supplierSkuId") Long supplierSkuId){
        return R.ok(iMoreGoodsService.getSupplierSkuInfoById(supplierSkuId));
    }

    @GetMapping("/cancelMoreGoods/{id}")
    @RepeatSubmit()
    @ApiOperation(value = "撤销多货单")
    public R<Boolean> cancelMoreGoods(@PathVariable("id") Long id){
        return R.ok(iMoreGoodsService.cancelMoreGoods(id));
    }

    @GetMapping("/adminCancelMoreGoods")
    @RepeatSubmit()
    @ApiOperation(value = "运维接口-撤销已确认多货单")
    public R<Boolean> adminCancelMoreGoods(@RequestParam("code") String code){
        return R.ok(iMoreGoodsService.adminCancelMoreGoods(code));
    }
}
