package cn.xianlink.order.controller.region;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.basic.api.RemoteBasCustomerService;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.vo.RemoteCustomerVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhParkingVo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.common.api.enums.order.BlameSourceTypeEnum;
import cn.xianlink.common.api.enums.order.DeliveryStatusEnum;
import cn.xianlink.common.api.enums.order.StockOutCreateSceneEnum;
import cn.xianlink.common.api.enums.system.SysUserTypeEnum;
import cn.xianlink.common.api.vo.RemoteSupplierSkuFileVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.json.utils.JsonUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.controller.CustomToolService;
import cn.xianlink.order.controller.sup.SupDeliveryAffairService;
import cn.xianlink.order.domain.OrderItem;
import cn.xianlink.order.domain.RwEntruckGoodsOrder;
import cn.xianlink.order.domain.bo.stockOut.BatchCreateStockoutBO;
import cn.xianlink.order.domain.delivery.bo.SupDeliveryAddBo;
import cn.xianlink.order.domain.delivery.bo.SupDeliveryGoodsAddBo;
import cn.xianlink.order.domain.delivery.bo.SupOrderDeliveryBo;
import cn.xianlink.order.domain.entruck.bo.RwEntruckGoodsAddBo;
import cn.xianlink.order.domain.entruck.bo.RwEntruckGoodsOrderQueryBo;
import cn.xianlink.order.domain.entruck.bo.RwEntruckRecordAddBo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsOrderCustomerVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsOrderSimplifyVo;
import cn.xianlink.order.domain.order.bo.UpdateOrderBo;
import cn.xianlink.order.domain.order.vo.QueryDeliveryVo;
import cn.xianlink.order.domain.order.vo.QueryItemListVo;
import cn.xianlink.order.service.IOrderItemService;
import cn.xianlink.order.service.IOrderService;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.RemoteUserService;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 总仓-配货订单
 *
 * <AUTHOR>
 * @date 2024-05-28
 * @folder 总仓助手(小程序)/配货订单
 */
@Validated
@CustomLog
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/region/deliveryOrder")
public class RegionDeliveryOrderController extends BaseController {

    private final transient CustomToolService customToolService;
    private final transient SupDeliveryAffairService supDeliveryAffairService;
    private final transient IOrderService orderService;
    private final transient IOrderItemService orderItemService;
    private final transient RegionEntruckAffairService regionEntruckAffairService;
    @DubboReference
    private final RemoteSupplierService remoteSupplierService;
    @DubboReference
    private final transient RemoteDeptService remoteDeptService;
    @DubboReference
    private final transient RemoteBasCustomerService remoteBasCustomerService;
    @DubboReference
    private final transient RemoteRegionWhService remoteRegionWhService;
    @DubboReference
    private final transient RemoteUserService remoteUserService;

    /**
     * 配货商品订单-完成配货
     */
    @RepeatSubmit()
    @PostMapping("/completeDeliveryOrder")
    public R<Void> completeDeliveryOrder(@Validated @RequestBody SupOrderDeliveryBo bo) {
        QueryDeliveryVo vo = orderService.getDeliveryInfoByCode(bo.getOrderCode(), true);
        if (vo.getDeliveryStatus().intValue() != DeliveryStatusEnum.WAIT_INSPECT.getCode()) {
            return R.warn("非待质检状态，不允许操作");
        }
        if (CollUtil.isEmpty(vo.getOrderItemList())) {
            throw new ServiceException("无订单项数据");
        }
        List<QueryItemListVo> itemlist = vo.getOrderItemList();
        QueryItemListVo itemVo = itemlist.get(0);

        LinkedHashMap<Long, RemoteRegionWhParkingVo> parkingVoMap = customToolService.getDeliveryLogistics(itemVo.getRegionWhId(), true);
        if (parkingVoMap.get(itemVo.getLogisticsId()) == null) {
            return R.warn("订单物流未设置装车位");
        }
        // 送货单数据
        SupDeliveryAddBo dBo = new SupDeliveryAddBo();
        BeanUtils.copyProperties(itemVo, dBo);
        dBo.setId(null);
        dBo.setDeliveryNo(itemVo.getOrderCode());
        dBo.setStatus(DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode());
        dBo.setRemark("配货订单");
        dBo.setGoodsList(itemlist.stream().map(e -> {
            SupDeliveryGoodsAddBo goods = new SupDeliveryGoodsAddBo();
            BeanUtils.copyProperties(e, goods);
            goods.setIsExemptInspect(1);
            goods.setDeliveryQuantity(e.getCount());
            return goods;
        }).collect(Collectors.toList()));
        dBo.setTotalDeliveryQuantity(dBo.getGoodsList().stream().mapToInt(SupDeliveryGoodsAddBo::getDeliveryQuantity).sum());
        // 装车记录数据
        RwEntruckRecordAddBo rBo = new RwEntruckRecordAddBo();
        BeanUtils.copyProperties(itemVo, rBo);
        rBo.setId(null);
        rBo.setStatus(DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode());
        rBo.setParkingNo(parkingVoMap.get(itemVo.getLogisticsId()).getParkingNo());
        rBo.setGoodsList(itemlist.stream().map(e -> {
            RwEntruckGoodsAddBo goods = new RwEntruckGoodsAddBo();
            BeanUtils.copyProperties(e, goods);
            goods.setDeliveryQuantity(e.getCount());
            goods.setEntruckQuantity(e.getCount());
            return goods;
        }).collect(Collectors.toList()));
//        // 关联的订单项列表
//        rBo.setOrderItemList(itemlist.stream().map(e -> {
//            RwEntruckGoodsOrder goods = new RwEntruckGoodsOrder();
//            BeanUtils.copyProperties(e, goods);
//            goods.setId(null);
//            goods.setOrderItemId(e.getId());
//            goods.setParkingNo(rBo.getParkingNo());
//            goods.setDeliveryQuantity(e.getCount());
//            return goods;
//        }).collect(Collectors.toList()));

        supDeliveryAffairService.createDelivery(dBo, ListUtil.toList(rBo));
        if (StrUtil.isNotBlank(bo.getRemark())) {
            UpdateOrderBo orderBo = new UpdateOrderBo();
            orderBo.setOrderId(vo.getId());
            orderBo.setRemark(bo.getRemark());
            orderService.updateOrder(orderBo);
        }
        return R.ok();
    }

    /**
     * 配货商品订单-缺货不采
     */
    @RepeatSubmit()
    @PostMapping("/orderNoPurchase")
    public R<Void> orderNoPurchase(@Validated @RequestBody SupOrderDeliveryBo bo) {
        QueryDeliveryVo vo = orderService.getDeliveryInfoByCode(bo.getOrderCode(), true);
        if (vo.getDeliveryStatus().intValue() != DeliveryStatusEnum.WAIT_INSPECT.getCode()) {
            return R.warn("非待质检状态，不允许操作");
        }
        if (CollUtil.isEmpty(vo.getOrderItemList())) {
            throw new ServiceException("无订单项数据");
        }
        List<BatchCreateStockoutBO> stockoutBOList = vo.getOrderItemList().stream().map(e -> {
            BatchCreateStockoutBO stockoutBo = new BatchCreateStockoutBO();
            stockoutBo.setOrderId(e.getOrderId());
            stockoutBo.setOrderItemId(e.getId());
            stockoutBo.setSaleDate(e.getSaleDate());
            stockoutBo.setRegionWhId(e.getRegionWhId());
            stockoutBo.setSkuSupplierId(e.getSupplierId());
            stockoutBo.setSkuSupplierDeptId(e.getSupplierDeptId());
            stockoutBo.setLogisticsId(e.getLogisticsId());
            stockoutBo.setType(BlameSourceTypeEnum.STOCKOUT.getCode());
            stockoutBo.setSupplierSkuId(e.getSupplierSkuId());
            stockoutBo.setSpuName(e.getSpuName());
            stockoutBo.setStockoutCount(e.getCount());
            stockoutBo.setCreateScene(StockOutCreateSceneEnum.NO_BUY_GOODS.getCode());
            stockoutBo.setBuyerId(LoginHelper.getLoginUser().getUserId());
            return stockoutBo;
        }).collect(Collectors.toList());
        // 创建缺货单
        log.keyword("orderNoPurchase").info("createStockout {}", JsonUtils.toJsonString(stockoutBOList));
        regionEntruckAffairService.createStockout(stockoutBOList);
        return R.ok();
    }

    /**
     * 配货订单商品标签打印查询
     */
    @PostMapping("/getDeliveryGoodsOrderLabelList")
    public R<List<RwEntruckGoodsOrderSimplifyVo>> getDeliveryGoodsOrderLabelList(@RequestBody RwEntruckGoodsOrderQueryBo bo) {
        if (ObjectUtil.isNull(bo.getOrderId())) {
            return R.warn("订单id不能为空");
        }
        List<OrderItem> itemlist = orderItemService.getByOrderId(bo.getOrderId());
        List<RwEntruckGoodsOrderSimplifyVo> gooodsList = new ArrayList<>();

        if (CollUtil.isNotEmpty(itemlist)) {
            List<Long> supplierSkuId = itemlist.stream().map(OrderItem::getSupplierSkuId).distinct().toList();
            List<Long> supplierIds = itemlist.stream().map(OrderItem::getSupplierId).distinct().toList();
            List<Long> logisticsIds = itemlist.stream().map(OrderItem::getLogisticsId).distinct().toList();
            List<Long> customerIds = itemlist.stream().map(OrderItem::getCustomerId).distinct().toList();

            Map<Long, List<RemoteSupplierSkuFileVo>> fileListMap = customToolService.getSkuFileListMap(supplierSkuId);
            Map<Long, RemoteSupplierSkuInfoVo> skuInfoMap = customToolService.getSkuInfoMap(supplierSkuId);
            Map<Long, RemoteSupplierVo> supplierMap = remoteSupplierService.getSupplierByIds(supplierIds).stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
            List<Long> supplierDeptIds = skuInfoMap.values().stream().filter(f -> f.getSupplierDeptId() > 0).map(RemoteSupplierSkuInfoVo::getSupplierDeptId).distinct().toList();
            Map<Long, String> deptNameMap = supplierDeptIds.size() > 0 ? remoteDeptService.getDeptNameMap(supplierDeptIds) : new HashMap<>();
            Map<Long, String> logisticsMap = customToolService.queryLogisticsNameMap(logisticsIds);
            Map<Long, RemoteCustomerVo> customerMap = remoteBasCustomerService.getByIds(customerIds).stream().collect(Collectors.toMap(RemoteCustomerVo::getId, Function.identity()));
            Map<Long, String> userPhoneMap = remoteUserService.getUserPhoneByRelations(SysUserTypeEnum.CUSTOMER_USER.getType(), customerIds);
            List<RemoteRegionWhParkingVo> parkingVos = remoteRegionWhService.queryParkingByLogisticsId(logisticsIds);
            for (OrderItem item : itemlist) {
                RwEntruckGoodsOrderSimplifyVo goods = BeanUtil.copyProperties(item, RwEntruckGoodsOrderSimplifyVo.class);
                goods.setDeliveryQuantity(item.getCount());
                goods.setSupplierCode(supplierMap.getOrDefault(goods.getSupplierId(), new RemoteSupplierVo()).getSimpleCode());
                goods.setSupplierName(supplierMap.getOrDefault(goods.getSupplierId(), new RemoteSupplierVo()).getName());
                goods.setSupplierAlias(supplierMap.getOrDefault(goods.getSupplierId(), new RemoteSupplierVo()).getAlias());
                goods.setSupplierDeptName(deptNameMap.get(goods.getSupplierDeptId()));
                goods.setFileList(fileListMap.get(goods.getSupplierSkuId()));
                RemoteSupplierSkuInfoVo skuInfoVo = skuInfoMap.get(goods.getSupplierSkuId());
                if (ObjectUtil.isNotNull(skuInfoVo)) {
                    goods.setSpuGrade(skuInfoVo.getSpuGrade());
                    goods.setSpuStandards(skuInfoVo.getSpuStandards());
                    goods.setProducer(skuInfoVo.getProducer());
                    goods.setPackageWord(skuInfoVo.getPackageWord());
                    // 产地简称
                    goods.setBrand(skuInfoVo.getBrand());
                    goods.setShortProducer(skuInfoVo.getShortProducer());
                    goods.setSkuLabel(skuInfoVo.getSkuLabel());
                }
                RwEntruckGoodsOrderCustomerVo customer = BeanUtil.copyProperties(item, RwEntruckGoodsOrderCustomerVo.class);
                customer.setDeliveryQuantity(item.getCount());
                customer.setOrderItemId(item.getId());
                customer.setDeliveryNumberDesc(customer.getDeliveryNumber().toString());
                if (StrUtil.isNotBlank(item.getPlaceNameLevel2())) {
                    customer.setDeliveryNumberDesc(item.getPlaceNameLevel2().substring(0, 1) + customer.getDeliveryNumber());
                }
                customer.setLogisticsName(logisticsMap.get(customer.getLogisticsId()));
                customer.setCustomerName(customerMap.getOrDefault(customer.getCustomerId(), new RemoteCustomerVo()).getName());
                customer.setCustomerAlias(customerMap.getOrDefault(customer.getCustomerId(), new RemoteCustomerVo()).getAlias());
                customer.setCustomerPhone(userPhoneMap.get(customer.getCustomerId()));
                if (CollUtil.isNotEmpty(parkingVos)) {
                    customer.setParkingNo(parkingVos.get(0).getParkingNo());
                }
                goods.setCustomerOrderList(ListUtil.toList(customer));
                gooodsList.add(goods);
            }
            //按车位号排序
//            gooodsList.sort(Comparator.comparing(
//                    simplifyVo -> Optional.ofNullable(simplifyVo.getCustomerOrderList())
//                            .filter(list -> !list.isEmpty())
//                            .map(list -> list.get(0).getParkingNo())
//                            .orElse("ZZZ") // 空值排到最后
//            ));
        }
        return R.ok(gooodsList);
    }
}
