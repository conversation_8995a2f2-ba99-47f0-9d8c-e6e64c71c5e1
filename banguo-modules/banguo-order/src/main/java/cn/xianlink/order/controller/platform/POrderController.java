package cn.xianlink.order.controller.platform;

import cn.dev33.satoken.exception.NotLoginException;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.api.enums.order.OrderCancelTypeEnum;
import cn.xianlink.common.api.enums.order.OrderStatusEnum;
import cn.xianlink.common.core.constant.HttpStatus;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.redis.utils.RedisUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.marketing.api.RemoteCouponUserService;
import cn.xianlink.order.api.RemoteOrderService;
import cn.xianlink.order.api.constant.RedisConstant;
import cn.xianlink.order.domain.PublicId;
import cn.xianlink.order.domain.dto.ConfirmOrderDTO;
import cn.xianlink.order.domain.dto.SubmitBo;
import cn.xianlink.order.domain.order.bo.AddOrderBo;
import cn.xianlink.order.domain.order.bo.QueryOrderPageBo;
import cn.xianlink.order.domain.order.vo.*;
import cn.xianlink.order.enums.OrderChannelEnum;
import cn.xianlink.order.service.ICartItemService;
import cn.xianlink.order.service.IOrderService;
import cn.xianlink.system.api.model.LoginUser;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import java.util.Calendar;
import java.util.List;

/**
 * 订单
 * 前端访问路由地址为:/order/platform/order
 * <AUTHOR>
 * @date 2024-05-27
 * @folder 采集平台(小程序)/订单/订单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/platform/order")
public class POrderController extends BaseController {

    private final IOrderService orderService;

    private final ICartItemService iCartItemService;



    @DubboReference
    private transient RemoteCouponUserService remoteCouponUserService;

    @DubboReference
    private transient RemoteOrderService remoteOrderService;



    //    @SaCheckPermission("system:order:list")
    @PostMapping("/queryPage")
    @Operation(summary = "订单列表查询")
    public R<TableDataInfo<QueryPageVo>> queryPlatformPage(@RequestBody QueryOrderPageBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if(user == null) {
            return R.ok();
        }
        bo.setCustomerIdList(Lists.newArrayList(user.getRelationId()));
        //如果点击了已支付，删除赠品下单标识
        if (ObjectUtil.equals(bo.getStatus(), OrderStatusEnum.ALREADY.getCode())){
            RedisUtils.deleteObject(RedisConstant.ORDER_COUPON_GIVE_TAG + user.getUserId());
        }
        return R.ok(orderService.queryPage(bo));
    }

    @PostMapping("/queryCount")
    @Operation(summary = "订单列表查询数量")
    public R<Long> queryPlatformCount(@RequestBody QueryOrderPageBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if(user == null) {
            return R.ok(0L);
        }
        bo.setCustomerId(user.getRelationId());
        return R.ok(orderService.queryPlatformCount(bo));
    }


//    @SaCheckPermission("system:order:query")
    @PostMapping("/getInfo")
    @Operation(summary = "获取订单详细信息")
    public R<GetInfoVo> getInfo(@RequestBody PublicId id) {
        LoginUser user = LoginHelper.getLoginUser();
        if(user == null) {
            return R.ok();
        }
        return R.ok(orderService.queryById(id.getId()));
    }

    @Log(title = "采集平台小程序-订单", businessType = BusinessType.INSERT)
    @PostMapping("/countAmount")
    @Operation(summary = "计算金额")
    public R<CountAmountVo> countAmount(@Validated(AddGroup.class) @RequestBody AddOrderBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if(user == null) {
            return R.ok();
        }
        bo.setCustomerId(user.getRelationId());
        return R.ok(orderService.countAmount(bo));
    }

    @Log(title = "采集平台小程序-订单", businessType = BusinessType.INSERT)
    @PostMapping("/validate")
    @Operation(summary = "前置检验")
    public R<ValidateVo> validate(@Validated(AddGroup.class) @RequestBody AddOrderBo bo) {
        try {
            return R.ok(orderService.validate(bo));
        } catch (NotLoginException e) {
            //不直接返回异常，自定义提示对用户更友好
            return R.fail(HttpStatus.UNAUTHORIZED, "状态过期，请重新登录");
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

//    @SaCheckPermission("system:order:add")
    @Log(title = "采集平台小程序-订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/add")
    @Operation(summary = "新增订单")
    public R<AddOrderVo> add(@Validated(AddGroup.class) @RequestBody AddOrderBo bo) {
        return R.ok(orderService.insertByBo(bo));
    }

    @Log(title = "采集平台小程序-确认订单页", businessType = BusinessType.INSERT)
    @PostMapping("/confirm")
    @Operation(summary = "确认订单页")
    public R<ConfirmOrderVo> confirm(@RequestBody ConfirmOrderDTO confirmOrderDTO) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isEmpty(loginUser)){
            throw new ServiceException("请重新登录");
        }
        Long customerId = loginUser.getRelationId();
        if (ObjectUtil.isNotEmpty(confirmOrderDTO.getCustomerId())){
            customerId = confirmOrderDTO.getCustomerId();
        }
        confirmOrderDTO.setOrderChannel(OrderChannelEnum.JC.getCode());
        confirmOrderDTO.setCustomerId(customerId);
        confirmOrderDTO.setUserId(loginUser.getUserId());
        ConfirmOrderVo confirmOrderVo = orderService.confirm(confirmOrderDTO);
        return R.ok(confirmOrderVo);
    }

    @Log(title = "采集平台小程序-提交", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/submit")
    @Operation(summary = "创建订单")
    public R<AddOrderVo> submit(@RequestBody SubmitBo submitBo) {
        return R.ok(orderService.submitLock(submitBo));
    }

//    @SaCheckPermission("system:order:cancel")
    @Log(title = "采集平台小程序-订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/cancel")
    @Operation(summary = "取消订单")
    public R<Void> cancel(@RequestBody PublicId id) {
        return toAjax(orderService.cancel(id.getId(), OrderCancelTypeEnum.BUYER.getCode()));
    }

//    @SaCheckPermission("system:order:query")
    @PostMapping("/getLossInfo")
    @Operation(summary = "报损的订单商品详情")
    public R<GetLossInfoVo> getLossInfo(@RequestBody PublicId id) {
        return R.ok(orderService.getLossInfo(id.getId()));
    }

    @PostMapping("/getUnpaidCount")
    @Operation(summary = "未支付订单统计数量")
    public R<List<GetUnpaidCountVo>> getUnpaidCount() {
        return R.ok(orderService.getUnpaidCount());
    }

    /**
     * 显示赠品下单标识
     * @return
     */
    @GetMapping("/show_give_tag")
    @Operation(summary = "显示赠品下单标识")
    public R<Boolean> showGiveTag() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Object cacheObject = RedisUtils.getCacheObject(RedisConstant.ORDER_COUPON_GIVE_TAG + loginUser.getUserId());
        Boolean tag = false;
        if (ObjectUtil.isNotEmpty(cacheObject)){
            tag = true;
        }
        return R.ok(tag);
    }


    /**
     * IM订单列表查询
     * @param bo
     * @return
     */
    @PostMapping("/orderList")
    @Operation(summary = "订单列表查询")
    public R<TableDataInfo<QueryPageVo>> orderList(@RequestBody QueryOrderPageBo bo) {
        return R.ok(orderService.orderList(bo));
    }

}
