package cn.xianlink.order.controller.admin;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.order.api.RemoteOrderService;
import cn.xianlink.order.api.vo.RemoteOrderDetailVo;
import cn.xianlink.order.service.IOrderService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 测试
 * @folder 般果管理中心/订单/订单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/admin/order/test")
public class ATestController {

    private final IOrderService orderService;
    private final RemoteOrderService remoteOrderService;

    @GetMapping("/index")
    @Operation(summary = "测试")
    public R<RemoteOrderDetailVo> index(@RequestParam String orderCode) {
        RemoteOrderDetailVo detailVo = remoteOrderService.getOrderDetailByCode(orderCode);
        return R.ok(detailVo);
    }

}
