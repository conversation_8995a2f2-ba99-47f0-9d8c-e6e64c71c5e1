package cn.xianlink.order.controller.city;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReUtil;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.common.api.enums.order.DeliveryStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.controller.CustomToolService;
import cn.xianlink.order.controller.region.RegionDeliveryAffairService;
import cn.xianlink.order.domain.delivery.bo.SupDeliveryScanCodeCheckBo;
import cn.xianlink.order.domain.entruck.bo.RwEntruckQueryBo;
import cn.xianlink.order.domain.entruck.bo.RwEntruckRecordQueryBo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckDepartVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsSupplierVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckRecordSumVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckRecordVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckVo;
import cn.xianlink.order.service.IRwDepartService;
import cn.xianlink.order.service.IRwEntruckRecordService;
import cn.xianlink.order.service.IRwEntruckService;
import cn.xianlink.order.service.ISortGoodsService;
import cn.xianlink.system.api.model.LoginUser;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 总仓-发车单
 *
 * <AUTHOR>
 * @date 2024-05-28
 * @folder 城市仓端(小程序)/发车单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/city/depart")
public class CityDepartController extends BaseController {

    private final transient IRwDepartService departService;
    private final transient IRwEntruckService entruckService;
    private final transient IRwEntruckRecordService entruckRecordService;
    private final transient CustomToolService customToolService;
    private final transient ISortGoodsService sortGoodsService;
    private final transient RegionDeliveryAffairService regionDeliveryAffairService;

    @DubboReference
    private final transient RemoteRegionWhService remoteRegionWhService;
    @DubboReference
    private final transient RemoteRegionLogisticsService remoteRegionLogisticsService;

    /**
     * 首页-待接车列表-计数
     */
    @GetMapping("/waitReceiveEntruckCount")
    public R<Long> waitReceiveEntruckCount(@RequestParam(name = "arrivalDate", required = false) String arrivalDate) {
        Long cityWhId = LoginHelper.getLoginUser().getRelationId();
        LocalDate localDate = LocalDate.now();
        if (StringUtils.isNotBlank(arrivalDate)) {
            if (!ReUtil.isMatch("^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01])$", arrivalDate)) {
                throw new ServiceException("倒仓日期格式错误");
            }
            localDate = LocalDate.parse(arrivalDate);
        }
        return R.ok(entruckService.waitReceiveEntruckCount(cityWhId, localDate));
    }
    /**
     * 首页-直属仓待收货-计数
     */
    @GetMapping("/waitReceiveDeliveryCount")
    public R<Long> waitReceiveDeliveryCount() {
        Long cityWhId = LoginHelper.getLoginUser().getRelationId();
        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryByAffiliatedCityWhId(cityWhId);
        if (regionWhVo == null) {
            return R.ok(0L);
        }
        LocalDate saleDate = customToolService.getLastSaleDate(regionWhVo.getId(), null);
        return R.ok(entruckRecordService.waitReceiveDeliveryCount(regionWhVo.getId(), cityWhId, saleDate));
    }

    /**
     * 查询城市仓-是否直属仓
     */
    @GetMapping("/getCityWhIsAffiliate")
    public R<RemoteRegionWhVo> getCityWhIsAffiliate() {
        Long cityWhId = LoginHelper.getLoginUser().getRelationId();
        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryByAffiliatedCityWhId(cityWhId);
        return R.ok(regionWhVo);
    }

    /**
     * 二维码扫码校验（直属仓接车）
     * @return
     */
    @PostMapping("/scanCodeCheck")
    public R<String> scanCodeCheck(@Validated @RequestBody SupDeliveryScanCodeCheckBo bo) {
        bo.setCityWhId(LoginHelper.getLoginUser().getRelationId());
        return R.ok("成功", regionDeliveryAffairService.scanCodeCheck(bo));
    }


    /**
     * 查询-装车单-待接车列表
     */
    @PostMapping("/page")
    public R<TableDataInfo<RwEntruckVo>> page(@Validated @RequestBody RwEntruckQueryBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        bo.setIsCityWhQuery(true);
        bo.setCityWhId(user.getRelationId());
        if (bo.getArrivalDate() == null) {
            bo.setArrivalDate(LocalDate.now());
        }
        //城市仓用户默认查询自己绑定的物流线，没有绑定就查询所有
        bo.setLogisticsIds(remoteRegionLogisticsService.queryAllIdByCityUser(user.getUserId()));
        // 查全部
        if (CollectionUtil.isEmpty(bo.getStatusList())) {
            bo.setStatusList(CollectionUtil.toList(DeliveryStatusEnum.COMPLETE_DEPART.getCode(),
                    DeliveryStatusEnum.COMPLETE_RECEIVE.getCode()));
        }
        TableDataInfo<RwEntruckVo> table = entruckService.customPageList(bo);
        // 加载是否完成分货标示
        List<String> entruckNoList = table.getRows().stream().filter(e -> e.getStatus().equals(DeliveryStatusEnum.COMPLETE_RECEIVE.getCode())).map(RwEntruckVo::getEntruckNo).toList();
        if (CollectionUtil.isNotEmpty(entruckNoList)){
            Map<String, Boolean> isMoreGoodsMap = sortGoodsService.getIsMoreGoodsMap(entruckNoList);
            if (isMoreGoodsMap.size() > 0) {
                for (RwEntruckVo vo : table.getRows()) {
                    if (isMoreGoodsMap.containsKey(vo.getEntruckNo())) {
                        vo.setIsFinish(isMoreGoodsMap.get(vo.getEntruckNo()));
                    }
                }
            }
        }
        return R.ok(table);
    }

    /**
     * 查询-装车单/发车单详情
     */
    @GetMapping("/info/{entruckId}")
    public R<RwEntruckDepartVo> info(@NotNull(message = "装车单id不能为空") @PathVariable Long entruckId) {
        RwEntruckDepartVo vo = new RwEntruckDepartVo();
        vo.setEntruckInfo(entruckService.selectAndCheckNullById(entruckId));
        vo.setDepartInfo(departService.selectAndCheckNullById(vo.getEntruckInfo().getDepartId()));
        return R.ok(vo);
    }

    /**
     * 查询-装车记录-待接车列表
     */
    @PostMapping("/deliveryPage")
    public R<TableDataInfo<RwEntruckRecordVo>> deliveryPage(@Validated @RequestBody RwEntruckRecordQueryBo bo) {
        Long cityWhId = LoginHelper.getLoginUser().getRelationId();
        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryByAffiliatedCityWhId(cityWhId);
        if (regionWhVo == null) {
            return R.ok();
        }
        bo.setRegionWhId(regionWhVo.getId());
        bo.setCityWhId(cityWhId);
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        bo.setStatusList(CollectionUtil.toList(DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode()));
        bo.setIsAffiliated(1);
        return R.ok(entruckRecordService.pageList(bo));
    }


    /**
     * 查询-装车单-装车记录商品数量列表
     */
    @GetMapping("/entruckRecordGoodsQuantityList")
    public R<List<Integer>> entruckRecordGoodsQuantityList(@NotNull(message = "装车单id不能为空") @RequestParam(name = "entruckId") Long entruckId,
                                                           @NotNull(message = "供应商skuid不能为空") @RequestParam(name = "supplierSkuId") Long supplierSkuId) {
        RwEntruckVo vo = entruckService.selectAndCheckNullById(entruckId);
        List<Integer> list = entruckRecordService.customEntruckRecordGoodsQuantityListByEntruckNo(entruckId, vo.getEntruckNo(), supplierSkuId)
                .stream().map(RwEntruckRecordSumVo::getEntruckQuantity).collect(Collectors.toList());
        return R.ok(list);
    }

    /**
     * 查询-已接车-装车单列表
     */
    @PostMapping("/entruckReceiveList")
    public R<List<RwEntruckVo>> entruckReceiveList(@Validated @RequestBody RwEntruckQueryBo bo) {
        if (bo.getReceiveDate() == null) {
            return R.warn("接车日期不能为空");
        }
        bo.setCityWhId(LoginHelper.getLoginUser().getRelationId());
        return R.ok(entruckService.customListByCityWhId(bo.getCityWhId(), bo.getReceiveDate()));
    }
}
