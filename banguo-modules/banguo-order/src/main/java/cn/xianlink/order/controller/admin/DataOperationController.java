package cn.xianlink.order.controller.admin;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.operation.ItemAfterSalesBo;
import cn.xianlink.order.service.IReportLossService;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据运维
 * <AUTHOR> xiaodaibing on 2024-12-13 10:43
 * @folder 般果管理中心/数据运维
 */
@CustomLog
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/admin/dataOperation")
public class DataOperationController extends BaseController {

    private final IReportLossService reportLossService;


    /**
     * 商品重开售后
     */
    @PostMapping("/itemOpenAfterSales")
    public R<Void> itemOpenAfterSales(@RequestBody ItemAfterSalesBo bo) {
        log.keyword("itemOpenAfterSales", bo.getItemId()).info("商品重开售后");
        reportLossService.reopenAfterSales(bo.getItemId());
        return R.ok();
    }


}