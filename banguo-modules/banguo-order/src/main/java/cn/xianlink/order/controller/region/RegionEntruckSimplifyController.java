package cn.xianlink.order.controller.region;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.api.enums.order.DeliveryStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.controller.CustomToolService;
import cn.xianlink.order.domain.entruck.bo.RwEntruckAddBo;
import cn.xianlink.order.domain.entruck.bo.RwEntruckGoodsBatchEditBo;
import cn.xianlink.order.domain.entruck.bo.RwWaitEntruckQueryBo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsCountVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckRecordGoodsSumVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckRecordVo;
import cn.xianlink.order.service.IRwEntruckGoodsService;
import cn.xianlink.order.service.IRwEntruckRecordService;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.bo.RemoteQuerySkuIdBo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.system.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 总仓-装车单-简化版
 *
 * <AUTHOR>
 * @date 2024-08-13
 * @folder 总仓助手(小程序)/装车单-分货装车
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/region/entruckSimplify")
public class RegionEntruckSimplifyController extends BaseController {
    private final transient IRwEntruckRecordService entruckRecordService;
    private final transient IRwEntruckGoodsService entruckGoodsService;
    private final transient RegionEntruckAffairService regionEntruckAffairService;
    private final transient CustomToolService customToolService;
    @DubboReference
    private final transient RemoteSupplierSkuService remoteSupplierSkuService;

    /**
     * 查询-待装车商品列表
     */
    @PostMapping("/waitEntruckGoodsList")
    public R<List<RwEntruckGoodsCountVo>> waitEntruckGoodsList(@Validated @RequestBody RwWaitEntruckQueryBo bo) {
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        LoginUser user = LoginHelper.getLoginUser();
        if (bo.getOwn() && user != null) {
            bo.setBuyerCode(user.getUserCode());
        }
        List<RwEntruckGoodsCountVo> list = entruckGoodsService.customEntruckGoodsSumListByRegionWhId(bo)
                .stream().filter(f -> f.getDeliveryQuantity().intValue() > f.getDiffQuantity()).collect(Collectors.toList());
        if (list.size() > 0) {
            list.sort(Comparator.comparingInt(RwEntruckGoodsCountVo::getWaitEntruckQuantity).reversed());
        }
        return R.ok(list);
    }

    /**
     * 查询-待装车商品列表-详情
     */
    @PostMapping("/waitEntruckGoodsInfo")
    public R<RwEntruckGoodsCountVo> waitEntruckGoodsInfo(@Validated @RequestBody RwWaitEntruckQueryBo bo) {
        if (bo.getSaleDate() == null) {
            return R.warn("销售日期为空");
        }
        // 商品条码-扫码进入
        if (StringUtils.isNotBlank(bo.getSupplierSkuCode())) {
//            RemoteSupplierSkuInfoVo skuInfoVo = remoteSupplierSkuService.getByCode(TemCode.getOriginalCode(bo.getSupplierSkuCode()));
            RemoteQuerySkuIdBo remoteQuerySkuIdBo = new RemoteQuerySkuIdBo().setSaleDate(bo.getSaleDate()).setSkuLabel(bo.getSupplierSkuCode());
            RemoteSupplierSkuInfoVo skuInfoVo = remoteSupplierSkuService.getByLabel(remoteQuerySkuIdBo);
            if (skuInfoVo == null) {
                return R.warn("未匹配到商品");
            }
            bo.setSupplierSkuId(skuInfoVo.getId());
        } else {
            if (ObjectUtil.isNull(bo.getSupplierSkuId())) {
                return R.warn("供应商商品skuId为空");
            }
        }
        List<RwEntruckGoodsCountVo> list = entruckGoodsService.customEntruckGoodsSumListByRegionWhId(bo);
        if (list.size() == 0) {
            return R.warn("未查到有效数据");
        }
        RwEntruckGoodsCountVo goodsVo = list.get(0);
        List<RwEntruckRecordGoodsSumVo> goodsLogisticsLis = entruckGoodsService.customEntruckGoodsLogisticsSumListBySkuId(bo);
        AtomicInteger atomic = new AtomicInteger(1000);
        goodsLogisticsLis.forEach(e ->
            e.setSort(NumberUtil.isInteger(e.getParkingNo()) ? Integer.parseInt(e.getParkingNo()) : atomic.getAndIncrement())
        );
        goodsLogisticsLis.sort(Comparator.comparingInt(RwEntruckRecordGoodsSumVo::getSort));
        goodsVo.setGoodsLogisticsList(goodsLogisticsLis);
        return R.ok(goodsVo);
    }

    /**
     * 查询-待装车商品列表-详情
     */
    @PostMapping("/batchWaitEntruckGoodsInfo")
    public R<List<RwEntruckGoodsCountVo>> batchWaitEntruckGoodsInfo(@Validated @RequestBody RwWaitEntruckQueryBo bo) {
        if (bo.getSaleDate() == null) {
            return R.warn("销售日期为空");
        }
        if (CollUtil.isEmpty(bo.getSupplierSkuIds())) {
            return R.warn("供应商商品skuIds为空");
        }
        List<RwEntruckGoodsCountVo> list = entruckGoodsService.customEntruckGoodsSumListByRegionWhId(bo);
        if (list.size() == 0) {
            return R.warn("未查到有效数据");
        }
        Map<Long, List<RwEntruckRecordGoodsSumVo>> goodsLogisticsListMap = entruckGoodsService.customEntruckGoodsLogisticsSumListBySkuId(bo)
                .stream().collect(Collectors.groupingBy(RwEntruckRecordGoodsSumVo::getSupplierSkuId));

        list.forEach(e-> e.setGoodsLogisticsList(goodsLogisticsListMap.get(e.getSupplierSkuId())));
        return R.ok(list);
    }
    /**
     * 装车操作-提交
     */
    @Log(title = "装车操作-提交", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/operateEntruck")
    public R<Void> operateEntruck(@Validated @RequestBody RwEntruckGoodsBatchEditBo bo) {
        regionEntruckAffairService.operateEntruckSimplify(bo);
        return R.ok();
    }

    /**
     * 完成分货装车前 - 差异查询
     */
    @PostMapping("/waitEntruckGoodsLogisticsList")
    public R<List<RwEntruckGoodsCountVo>> waitEntruckGoodsLogisticsList(@Validated @RequestBody RwWaitEntruckQueryBo bo) {
        if (bo.getSaleDate() == null) {
            return R.warn("销售日期为空");
        }
        return R.ok(entruckGoodsService.customWaitEntruckGoodsLogisticsList(bo));
    }

    /**
     * 完成分货装车
     */
    @Log(title = "完成分货装车", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/completeEntruck")
    public R<Void> completeEntruck(@Validated @RequestBody RwWaitEntruckQueryBo bo) {
        if (bo.getSaleDate() == null) {
            return R.warn("销售日期为空");
        }
        regionEntruckAffairService.completeEntruckSimplify(bo);
        return R.ok();
    }

    /**
     * 校验-创建装车单
     */
    @PostMapping("/checkCreateEntruck")
    public R<List<RwEntruckGoodsVo>> checkCreateEntruck(@Validated @RequestBody RwEntruckAddBo bo) {
        List<RwEntruckRecordVo> recordVos = entruckRecordService.completeEntruckList(bo.getRegionWhId(), bo.getLogisticsId(), bo.getSaleDate(), DeliveryStatusEnum.COMPLETE_INSPECT.getCode());
        if (recordVos.size() == 0) {
            return R.ok();
        }
        List<RwEntruckGoodsVo> diffGoodsList = new ArrayList<>();
        for (RwEntruckRecordVo recordVo : recordVos) {
            // 商品明细未操作装车size
            List<RwEntruckGoodsVo> goodsList = recordVo.getGoodsList().stream().filter(f -> f.getEntruckQuantity() == 0).collect(Collectors.toList());
            if (goodsList.size() == recordVo.getGoodsList().size()) {
                continue;
            }
            diffGoodsList.addAll(goodsList);
        }
        if (diffGoodsList.size() == 0) {
            return R.ok();
        }
        Map<Long, RwEntruckGoodsVo> map = new HashMap<>();
        for (RwEntruckGoodsVo goodsVo : diffGoodsList ) {
            RwEntruckGoodsVo value = map.get(goodsVo.getSupplierSkuId());
            if (value == null) {
                map.put(goodsVo.getSupplierSkuId(), goodsVo);
            } else {
                value.setDeliveryQuantity(value.getDeliveryQuantity() + goodsVo.getDeliveryQuantity());
                value.setEntruckQuantity(value.getEntruckQuantity() + goodsVo.getEntruckQuantity());
            }
        }
        List<RwEntruckGoodsVo> list = map.values().stream().toList();
        entruckRecordService.loadRwEntruckGoodsInfo(list);
        return R.ok(list);
    }
}
