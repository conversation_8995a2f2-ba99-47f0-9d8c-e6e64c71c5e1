package cn.xianlink.order.controller.region;


import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.api.enums.order.DeliveryStatusEnum;
import cn.xianlink.common.api.enums.order.OrderDeliverSourceEnum;
import cn.xianlink.common.api.enums.order.ToDosEnum;
import cn.xianlink.common.api.util.RoleUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.controller.TodoService;
import cn.xianlink.order.domain.todo.bo.TodoBo;
import cn.xianlink.order.domain.todo.vo.TodoGoodsVo;
import cn.xianlink.order.domain.todo.vo.TodoVo;
import cn.xianlink.system.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 总仓小程序-待办
 *
 * <AUTHOR>
 * @date 2025-01-05
 * @folder 总仓助手(小程序)/待办
 */
@Validated
@RequiredArgsConstructor
@RestController()
@RequestMapping("/order/region/todo/")
public class RegionTodoController extends BaseController {
    private transient final TodoService todoService;

    /**
     * 待办列表
     */
    @PostMapping("/list")
    public R<List<TodoVo>> list(@RequestBody TodoBo bo) {
        if (ObjectUtil.isNull(bo.getRegionWhId())) {
            return R.warn("总仓id不能为空");
        }
        LoginUser user = LoginHelper.getLoginUser();
        bo.setCacheUserId(user.getUserId());
        bo.setSource(OrderDeliverSourceEnum.REGION.getCode());
        if (!RoleUtil.isRegionAdmin(user.getRolePermission())) {
            bo.setBuyerCode(user.getUserCode());
            bo.setBuyerId(user.getUserId());
        }
        List<TodoVo> list = new ArrayList<>();
        // 送货待审核
        list.add(new TodoVo(ToDosEnum.AUDIT_DELIVERY.getCode(), todoService.regionDeliveryAuditList(bo)));
        // 待送货
        list.add(new TodoVo(ToDosEnum.WAIT_DELIVERY.getCode(), todoService.waitDeliveryList(bo)));
        // 待质检
        list.add(new TodoVo(ToDosEnum.WAIT_INSPECT.getCode(), todoService.waitInspectGoodsList(bo)));
        // 待装车 - 市采
        list.add(new TodoVo(ToDosEnum.WAIT_ENTRUCK.getCode(), todoService.waitEntruckGoodsList(bo, 1)));
        // 待分拣 - 集采、产地
        TodoBo bo2 = new TodoBo();
        BeanUtils.copyProperties(bo, bo2);
        bo2.setSource(OrderDeliverSourceEnum.REGION_PICKING.getCode());
        list.add(new TodoVo(ToDosEnum.WAIT_PICKING_PRINT.getCode(), todoService.waitDeliveryList(bo2)));
        // 待拣货 - 集采、产地
        list.add(new TodoVo(ToDosEnum.WAIT_PICKING_ENTRUCK.getCode(), todoService.waitEntruckGoodsList(bo, 3)));
        // 库房待拣货 - 集采、产地
        list.add(new TodoVo(ToDosEnum.RW_WAIT_PICKING_ENTRUCK.getCode(), todoService.waitEntruckGoodsList(bo, 2)));

        // 缺货待确认
        list.add(new TodoVo(ToDosEnum.AUDIT_STOCKOUT.getCode(), todoService.waitConfirmStockOutList(bo, 1)));
        // 少货待确认
        list.add(new TodoVo(ToDosEnum.AUDIT_LESS_GOODS.getCode(), todoService.waitConfirmStockOutList(bo, 2)));
        // 未生成发车单
        list.add(new TodoVo(ToDosEnum.WAIT_CREATE_ENTRUCK_NO.getCode(), todoService.waitCreateEntruckNoCount(bo)));
        // 待发车
        list.add(new TodoVo(ToDosEnum.WAIT_DEPART.getCode(), todoService.waitDepartCount(bo, DeliveryStatusEnum.WAIT_DEPART.getCode())));

        return R.ok(list.stream().filter(f-> f.getSkuCount() > 0).collect(Collectors.toList()));
    }


    /**
     * 待办明细
     */
    @PostMapping("/details")
    public R<List<TodoGoodsVo>> details(@RequestBody TodoBo bo) {
        if (ObjectUtil.isNull(bo.getRegionWhId())) {
            return R.warn("总仓id不能为空");
        }
        if (ObjectUtil.isNull(bo.getTodoCode())) {
            return R.warn("待办类型不能为空");
        }
        LoginUser user = LoginHelper.getLoginUser();
        bo.setCacheUserId(user.getUserId());
        bo.setSource(OrderDeliverSourceEnum.REGION.getCode());
        if (!RoleUtil.isRegionAdmin(user.getRolePermission())) {
            bo.setBuyerCode(user.getUserCode());
            bo.setBuyerId(user.getUserId());
        }
        List<TodoGoodsVo> list = new ArrayList<>();
        // 送货待审核
        if (bo.getTodoCode().intValue() == ToDosEnum.AUDIT_DELIVERY.getCode()) {
            list = todoService.regionDeliveryAuditList(bo).getDataList();
        }
        // 待送货
        else if (bo.getTodoCode().intValue() == ToDosEnum.WAIT_DELIVERY.getCode()) {
            list = todoService.waitDeliveryList(bo).getDataList();
        }
        // 待质检
        else if (bo.getTodoCode().intValue() == ToDosEnum.WAIT_INSPECT.getCode()) {
            list = todoService.waitInspectGoodsList(bo).getDataList();
        }
        // 待装车 - 市采
        else if (bo.getTodoCode().intValue() == ToDosEnum.WAIT_ENTRUCK.getCode()) {
            list = todoService.waitEntruckGoodsList(bo, 1).getDataList();
        }
        // 待分拣 - 集采、产地
        else if (bo.getTodoCode().intValue() == ToDosEnum.WAIT_PICKING_PRINT.getCode()) {
            TodoBo bo2 = new TodoBo();
            BeanUtils.copyProperties(bo, bo2);
            bo2.setSource(OrderDeliverSourceEnum.REGION_PICKING.getCode());
            list = todoService.waitDeliveryList(bo2).getDataList();
        }
        // 待分拣 - 集采、产地
        else if (bo.getTodoCode().intValue() == ToDosEnum.WAIT_PICKING_ENTRUCK.getCode()) {
            list = todoService.waitEntruckGoodsList(bo, 3).getDataList();
        }
        // 库房待拣货 - 集采、产地
        else if (bo.getTodoCode().intValue() == ToDosEnum.RW_WAIT_PICKING_ENTRUCK.getCode()) {
            list = todoService.waitEntruckGoodsList(bo, 2).getDataList();
        }
        //  缺货待确认
        else if (bo.getTodoCode().intValue() == ToDosEnum.AUDIT_STOCKOUT.getCode()) {
            list = todoService.waitConfirmStockOutList(bo, 1).getDataList();
        }
        //  少货待确认
        else if (bo.getTodoCode().intValue() == ToDosEnum.AUDIT_LESS_GOODS.getCode()) {
            list = todoService.waitConfirmStockOutList(bo, 2).getDataList();
        }

        return R.ok(list);
    }
}