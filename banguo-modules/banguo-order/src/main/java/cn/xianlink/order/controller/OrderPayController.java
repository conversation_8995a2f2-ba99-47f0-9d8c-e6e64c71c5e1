package cn.xianlink.order.controller;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.pay.OrderPaySignBo;
import cn.xianlink.order.service.IOrderPayService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 *
 * <AUTHOR> on 2024/5/30:10:14
 * @folder 采集平台(小程序)/订单/订单支付
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/pay")
public class OrderPayController extends BaseController {

    private final IOrderPayService orderPayService;


    /**
     * 获取订单支付签名
     * <AUTHOR> on 2024/5/29:16:15
     * @param bo
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.order.domain.vo.pay.OrderWeixinPayVo>
     */
    @Log(title = "获取订单支付签名", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PostMapping()
    public R<JSONObject> paySign(@RequestBody @Valid OrderPaySignBo bo) {
        return R.ok(JSON.parseObject(orderPayService.orderPayLock(bo)));
    }


    /**
     * 用户支付完成回调
     * <AUTHOR> on 2024/12/16:16:00
     * @param bo
     * @return cn.xianlink.common.core.domain.R<com.alibaba.fastjson.JSONObject>
     */
    @Log(title = "支付完成的回调", businessType = BusinessType.OTHER)
    @PostMapping("/completeCall")
    public R<Void> completeCall(@RequestBody @Valid OrderPaySignBo bo) {
        orderPayService.userCompleteCall(bo);
        return R.ok();
    }
}
