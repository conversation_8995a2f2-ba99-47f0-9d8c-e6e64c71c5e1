package cn.xianlink.order.controller.region;

import cn.xianlink.common.api.enums.order.OrderBusinessTypeEnum;
import cn.xianlink.common.api.enums.order.OrderCancelTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.controller.CustomToolService;
import cn.xianlink.order.domain.PublicId;
import cn.xianlink.order.domain.bo.report.StatCitySaleBo;
import cn.xianlink.order.domain.dto.CustomerOrderDTO;
import cn.xianlink.order.domain.dto.OrderFixPriceDTO;
import cn.xianlink.order.domain.order.bo.QueryOrderPageBo;
import cn.xianlink.order.domain.order.bo.QueryOrderPageDeliveryBo;
import cn.xianlink.order.domain.order.bo.UpdateOrderBo;
import cn.xianlink.order.domain.order.vo.*;
import cn.xianlink.order.domain.vo.order.CityOrderWeightVO;
import cn.xianlink.order.service.IOrderItemService;
import cn.xianlink.order.service.IOrderService;
import cn.xianlink.system.api.model.LoginUser;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 订单
 * 前端访问路由地址为:/order/region/order
 * <AUTHOR>
 * @date 2024-05-28
 * @folder 总仓助手(小程序)/订单/订单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/region/order")
public class ROrderController extends BaseController {

    private final IOrderService orderService;

    private final IOrderItemService iOrderItemService;
    private final transient CustomToolService customToolService;

    @GetMapping("/getByOrderIdAndSupplierId")
    @Operation(summary = "根据订单号和供应商id查询对应的商品批次信息")
    public R<List<OrderItemSupplierSkuInfoVo>> getByOrderCodeAndSupplierId(@RequestParam("orderCode") String orderCode,
                                                                         @RequestParam(value = "supplierId", required = false) Long supplierId) {
        return R.ok(iOrderItemService.getByOrderCodeAndSupplierId(orderCode, supplierId));
    }

    @PostMapping("/queryReplaceSupplierAmount")
    @Operation(summary = "根据销售批次id查询已支付订单货款")
    public R<BigDecimal> queryReplaceSupplierAmount(@Validated(AddGroup.class) @RequestBody PublicId id) {
        return R.ok(orderService.queryReplaceSupplierAmount(id.getId()));
    }

    @PostMapping("/queryRegionPage")
    @Operation(summary = "订单列表查询")
    public R<TableDataInfo<QueryPageVo>> queryRegionPage(@RequestBody QueryOrderPageBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if(user == null) {
            throw new ServiceException("非法请求");
        }
        bo.setRegionWhId(user.getRelationId());
        return R.ok(orderService.queryPage(bo));
    }

    @PostMapping("/getInfo")
    @Operation(summary = "获取订单详细信息")
    public R<GetInfoVo> getInfo(@RequestBody PublicId id) {
        return R.ok(orderService.queryById(id.getId()));
    }

    @PostMapping("/customer_order_page")
    @Operation(summary = "客户订单列表")
    public R<TableDataInfo<CustomerOrderVO>> customerOrderPage(@RequestBody @Valid CustomerOrderDTO bo) {
        return R.ok(orderService.customerOrderPage(bo));
    }
    @GetMapping("/customer_order_info/{orderCode}")
    @Operation(summary = "客户订单详细信息")
    public R<CustomerOrderVO> customerOrderInfo(@PathVariable("orderCode") String orderCode) {
        return R.ok(orderService.customerOrderInfo(orderCode ,null));
    }



    @GetMapping("/fix_price_order_info")
    @Operation(summary = "获取调价订单商品")
    public R<List<CustomerOrderItemVO>> fixPriceOrderInfo(@RequestParam("orderCode") String orderCode, @RequestParam(value = "orderItemId",required = false) Long orderItemId) {
        return R.ok(orderService.fixPriceOrderInfo(orderCode,orderItemId));
    }

    @PostMapping("/fix_price_info")
    @Operation(summary = "计算调价信息")
    public R<OrderFixPriceVO> fixPriceInfo(@RequestBody  @Valid  OrderFixPriceDTO orderFixPriceDTO) {
        return R.ok(orderService.fixPriceInfo(orderFixPriceDTO));
    }

    @PostMapping("/affirm_fix_price")
    @RepeatSubmit()
    @Operation(summary = "确认调价")
    public R<Void> affirmFixPrice(@RequestBody @Valid  OrderFixPriceDTO orderFixPriceDTO) {
        orderService.affirmFixPrice(orderFixPriceDTO);
        return R.ok();
    }
    @RepeatSubmit()
    @PostMapping("/cancel")
    @Operation(summary = "取消订单")
    public R<Void> cancel(@RequestBody PublicId id) {
        return toAjax(orderService.cancel(id.getId(), OrderCancelTypeEnum.REGION.getCode()));
    }

    @RepeatSubmit()
    @PostMapping("/updateOrder")
    @Operation(summary = "修改订单备注")
    public R<Void> updateOrder(@Validated(EditGroup.class) @RequestBody UpdateOrderBo bo) {
        return toAjax(orderService.updateOrder(bo));
    }

    @GetMapping("/city_weight")
    @Operation(summary = "城市仓吨位")
    public R<TableDataInfo<CityOrderWeightVO>> cityWeight(@RequestParam("regionWhId") Long regionWhId
            , @RequestParam(value = "cityWhId",required = false) Long cityWhId
            , @RequestParam(value = "saleDate",required = false) LocalDate saleDate, PageQuery pageQuery) {
        return R.ok(orderService.cityWeight(regionWhId,cityWhId,saleDate,pageQuery));
    }

//    @GetMapping("/city_sale_data")
//    @Operation(summary = "城市仓销售数据")
//    public R<TableDataInfo<CityOrderWeightVO>> citySaleData(@RequestParam("regionWhId") Long regionWhId
//            , @RequestParam(value = "cityWhId",required = false) Long cityWhId
//            , @RequestParam(value = "saleDate",required = false) LocalDate saleDate, PageQuery pageQuery) {
//        return R.ok(orderService.citySaleData(regionWhId,cityWhId,saleDate,pageQuery));
//    }
//
//    @GetMapping("/city_sale_data_total")
//    @Operation(summary = "城市仓销售数据合计")
//    public R<CityOrderWeightVO> citySaleDataTotal(@RequestParam("regionWhId") Long regionWhId
//            , @RequestParam(value = "cityWhId",required = false) Long cityWhId
//            , @RequestParam(value = "saleDate",required = false) LocalDate saleDate) {
//        return R.ok(orderService.citySaleDataTotal(regionWhId,cityWhId,saleDate));
//    }

    @PostMapping("/city_sale_data")
    @Operation(summary = "城市仓销售数据")
    public R<TableDataInfo<CityOrderWeightVO>> citySaleData(@RequestBody StatCitySaleBo bo) {
        return R.ok(orderService.citySaleData(bo.getRegionWhId(), bo.getCityWhId(), bo.getSaleDate(), bo.getSkuIds(), bo));
    }

    @PostMapping("/city_sale_data_total")
    @Operation(summary = "城市仓销售数据合计")
    public R<CityOrderWeightVO> citySaleDataTotal(@RequestBody StatCitySaleBo bo) {
        return R.ok(orderService.citySaleDataTotal(bo.getRegionWhId(), bo.getCityWhId(), bo.getSaleDate(), bo.getSkuIds()));
    }

    @PostMapping("/queryPageDelivery")
    @Operation(summary = "配货订单列表查询")
    public R<TableDataInfo<QueryDeliveryVo>> queryPageDelivery(@RequestBody QueryOrderPageDeliveryBo bo) {
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        return R.ok(orderService.queryPageDelivery(bo));
    }

    @GetMapping("/getDeliveryInfo/{code}")
    @Operation(summary = "获取配货订单详细信息")
    public R<QueryDeliveryVo> getDeliveryInfoByCode(@NotBlank(message = "订单号不能为空") @PathVariable String code) {
        return R.ok(orderService.getDeliveryInfoByCode(code, true));
    }

}