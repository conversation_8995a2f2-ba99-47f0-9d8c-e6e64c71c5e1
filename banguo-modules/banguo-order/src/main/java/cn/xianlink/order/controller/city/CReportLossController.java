package cn.xianlink.order.controller.city;

import cn.xianlink.basic.api.domain.vo.RemoteRcsVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.report.CustomerStatisticsSearchBo;
import cn.xianlink.order.domain.bo.report.ReportLossMiniListSearchBo;
import cn.xianlink.order.domain.vo.report.ReportLossMiniDetailVo;
import cn.xianlink.order.domain.vo.report.ReportLossMiniListVo;
import cn.xianlink.order.domain.vo.report.ReportNumStatisticsVo;
import cn.xianlink.order.service.ICustomerStatisticsService;
import cn.xianlink.order.service.IReportLossService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 报损单
 *
 * <AUTHOR> xiaodaibing on 2024-05-30 17:29
 * @folder 城市仓端(小程序)/报损单
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/city/report")
public class CReportLossController extends BaseController {

    private final IReportLossService reportLossService;

    private final ICustomerStatisticsService customerStatisticsService;


    /**
     * 列表
     * @param bo
     * @return cn.xianlink.common.core.domain.R<TableDataInfo < ReportLossMiniListVo>>
     * <AUTHOR> on 2024/6/11:15:24
     */
    @PostMapping("/list")
    public R<TableDataInfo<ReportLossMiniListVo>> list(@RequestBody ReportLossMiniListSearchBo bo) {
        bo.setCityWhId(LoginHelper.getLoginUser().getRelationId());
        return R.ok(reportLossService.miniPage(bo));
    }


    /**
     * 获取详情
     *
     * @param id
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.order.domain.vo.report.ReportLossMiniDetailVo>
     * <AUTHOR> on 2024/6/6:15:22
     */
    @GetMapping("/detail/{id}")
    public R<ReportLossMiniDetailVo> detail(@PathVariable("id") Long id) {
        ReportLossMiniDetailVo vo = reportLossService.detail(id);
        if (!vo.getCityWhId().equals(LoginHelper.getLoginUser().getRelationId())){
            return R.fail("无权限查看");
        }
        return R.ok(vo);
    }


    /**
     * 各状态数量统计
     * 目前只统计待审核、驳回、申诉中
     *
     * @param
     * @return cn.xianlink.common.core.domain.R<java.util.List < cn.xianlink.order.domain.vo.report.ReportNumStatisticsVo>>
     * <AUTHOR> on 2024/6/11:9:47
     */
    @GetMapping("/numStatistics")
    public R<List<ReportNumStatisticsVo>> numStatistics() {
        LoginHelper.getLoginUser().getRelationId();
        return R.ok(reportLossService.numStatistics(null,null,LoginHelper.getLoginUser().getRelationId(),null, null));
    }



    /**
     * 客户报损统计
     * <AUTHOR> on 2024/7/29:15:28
     * @param searchBo
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.basic.api.domain.vo.RemoteRcsVo>
     */
    @PostMapping("/customerStatistics")
    public R<RemoteRcsVo> customerStatistics(@Valid @RequestBody CustomerStatisticsSearchBo searchBo) {
        if (searchBo.getCycle() > 12) {
            return R.ok(customerStatisticsService.getYearReportLoss(searchBo.getCustomerId(), searchBo.getCycle()));
        } else {
            return R.ok(customerStatisticsService.getMonthReportLoss(searchBo.getCustomerId(), searchBo.getCycle()));
        }
    }

}
