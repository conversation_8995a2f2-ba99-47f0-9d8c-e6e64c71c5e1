package cn.xianlink.order.controller.region;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.common.api.util.RoleUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.controller.CustomToolService;
import cn.xianlink.order.domain.entruck.bo.RwEntruckGoodsEditBo;
import cn.xianlink.order.domain.entruck.bo.RwEntruckGoodsOrderQueryBo;
import cn.xianlink.order.domain.entruck.bo.RwEntruckRecordEditPickingBo;
import cn.xianlink.order.domain.entruck.bo.RwWaitEntruckQueryBo;
import cn.xianlink.order.domain.entruck.vo.*;
import cn.xianlink.order.service.IRwEntruckGoodsService;
import cn.xianlink.order.service.IRwEntruckRecordService;
import cn.xianlink.order.service.ISupDeliveryService;
import cn.xianlink.system.api.model.LoginUser;
import jakarta.validation.constraints.NotNull;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 总仓-装车单-拣货装车
 *
 * <AUTHOR>
 * @date 2024-08-13
 * @folder 总仓助手(小程序)/装车单-拣货装车
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/region/entruckPicking")
@CustomLog
public class RegionEntruckPickingController extends BaseController {
    private final transient RegionDeliveryAffairService regionDeliveryAffairService;
    private final transient RegionEntruckAffairService regionEntruckAffairService;
    private final transient CustomToolService customToolService;
    private final transient IRwEntruckRecordService rwEntruckRecordService;
    private final transient IRwEntruckGoodsService rwEntruckGoodsService;
    private final transient ISupDeliveryService supDeliveryService;
    private final transient IRwEntruckGoodsService entruckGoodsService;


    /**
     * 查询-待拣货列表（物流+商品）
     */
    @PostMapping("/waitEntruckList")
    public R<List<RwWaitEntruckLogisticsVo>> waitEntruckList(@Validated @RequestBody RwWaitEntruckQueryBo bo) {
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        bo.setIsListViewQuery(true);
        List<RwWaitEntruckLogisticsVo> list = regionDeliveryAffairService.waitEntruckLogisticsList(bo);
//        if (ObjectUtil.isNotNull(bo.getPickingStatus())) {
//            return R.ok(list.stream().filter(f -> f.getPickingStatus().intValue() == bo.getPickingStatus()).collect(Collectors.toList()));
//        }
        return R.ok(list);
    }

    /**
     * 查询-待拣货详情（物流+商品）
     */
    @PostMapping("/waitEntruckDetails")
    public R<RwWaitEntruckLogisticsVo> waitEntruckDetails(@Validated @RequestBody RwWaitEntruckQueryBo bo) {
        if (bo.getSaleDate() == null) {
            return R.warn("销售日期不能为空");
        }
        if (bo.getLogisticsId() == null) {
            return R.warn("物流线id不能为空");
        }
        List<RwWaitEntruckLogisticsVo> list = regionDeliveryAffairService.waitEntruckLogisticsList(bo);
        if (list.size() == 0) {
            return R.ok("无符合条件的数据");
        }
        list.get(0).getGoodsList().sort(Comparator.comparingInt(RwWaitEntruckGoodsVo::getWaitEntruckQuantity).reversed());
        return R.ok(list.get(0));
    }

//    /**
//     * 待拣货详情 - 单商品不采
//     */
//    @Log(title = "待拣货详情 - 单商品不采", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PostMapping("/waitEntruckGoodsNoPurchase")
//    public R<Void> waitEntruckGoodsNoPurchase(@Validated @RequestBody RwWaitEntruckQueryBo bo) {
//        if (bo.getSaleDate() == null) {
//            return R.warn("销售日期不能为空");
//        }
//        if (bo.getLogisticsId() == null) {
//            return R.warn("物流线id不能为空");
//        }
//        if (bo.getSupplierSkuId() == null) {
//            return R.warn("供应商商品批次id不能为空");
//        }
//        bo.setSupplierSkuIds(ListUtil.toList(bo.getSupplierSkuId()));
//        List<RwWaitEntruckLogisticsVo> list = regionDeliveryAffairService.waitEntruckLogisticsList(bo, false);
//        if (list.size() == 0) {
//            return R.warn("没有可以操作不采购的数量");
//        }
//        RwWaitEntruckLogisticsVo vo = list.get(0);
//        RwWaitEntruckGoodsVo goods = vo.getGoodsList().get(0);
//
//        RwEntruckGoodsAddBo goodsBo = new RwEntruckGoodsAddBo();
//        goodsBo.setSupplierSkuId(goods.getSupplierSkuId());
//        goodsBo.setSpuName(goods.getSpuName());
//        goodsBo.setDeliveryQuantity(goods.getWaitEntruckQuantity());
//
//        RwEntruckRecordAddBo recordBo = new RwEntruckRecordAddBo();
//        recordBo.setSaleDate(vo.getSaleDate());
//        recordBo.setRegionWhId(vo.getRegionWhId());
//        recordBo.setLogisticsId(vo.getLogisticsId());
//        recordBo.setSupplierId(goods.getSupplierId());
//        recordBo.setSupplierDeptId(goods.getSupplierDeptId());
//        recordBo.setGoodsList(ListUtil.toList(goodsBo));
//
//        regionDeliveryAffairService.waitDeliveryNoPurchase(ListUtil.toList(recordBo), false);
//        return R.ok();
//    }

//    /**
//     * 装车操作-提交
//     */
//    @Log(title = "装车操作-提交", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PostMapping("/operateEntruck")
//    public R<Void> operateEntruck(@Validated @RequestBody RwEntruckPickingLogisticsAddBo bo) {
//        regionDeliveryAffairService.operateEntruck30(bo);
//        return R.ok();
//    }

    /**
     * 创建拣货单-送货单&装车记录
     */
    @Log(title = "创建拣货单-送货单&装车记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/createPicking")
    public R<RwWaitEntruckQueryBo> createPicking(@Validated @RequestBody RwWaitEntruckQueryBo bo) {
        if (bo.getSaleDate() == null) {
            return R.warn("销售日期不能为空");
        }
        if (bo.getLogisticsId() == null) {
            return R.warn("物流线id不能为空");
        }
        if (RoleUtil.isRegionAdmin(LoginHelper.getLoginUser().getRolePermission())) {
            bo.setBasPortageTeamId(customToolService.getBasPortageTeamIdAndCheck(bo.getRegionWhId(), LoginHelper.getLoginUser().getUserId()));
        }
        regionDeliveryAffairService.operateEntruck20(bo);
        if (!bo.getIsCreateNoSuccess()) {
            return R.warn("无有效库存可以操作");
        }
        return R.ok(bo);
    }

    /**
     * 创建拣货单-送货单&装车记录
     */
    @Log(title = "创建拣货单-送货单&装车记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/batchCreatePicking")
    public R<RwWaitEntruckQueryBo> batchCreatePicking(@Validated @RequestBody RwWaitEntruckQueryBo bo) {
        if (bo.getSaleDate() == null) {
            return R.warn("销售日期不能为空");
        }
        if (CollUtil.isEmpty(bo.getLogisticsIdList())) {
            return R.warn("物流线id列表不能为空");
        }
        if (RoleUtil.isRegionAdmin(LoginHelper.getLoginUser().getRolePermission())) {
            bo.setBasPortageTeamId(customToolService.getBasPortageTeamIdAndCheck(bo.getRegionWhId(), LoginHelper.getLoginUser().getUserId()));
        }
        List<String> pickingNoList = new ArrayList<>();
        for (int i = 0; i < bo.getLogisticsIdList().size(); i++) {
            try {
                bo.setLogisticsId(bo.getLogisticsIdList().get(i));
                bo.setIsCreateNoSuccess(false);
                regionDeliveryAffairService.operateEntruck20(bo);
                if (bo.getIsCreateNoSuccess()) {
                    pickingNoList.add(bo.getPickingNo());
                }
            } catch (Exception e) {
                log.keyword("createPicking").warn(e.getMessage(), e);
                if (i == bo.getLogisticsIdList().size() - 1 && pickingNoList.size() == 0) {
                    return R.warn(e.getMessage());
                }
            }
        }
        if (pickingNoList.size() == 0) {
            return R.warn("无有效库存可以操作");
        }
        bo.setPickingNoList(pickingNoList);
        return R.ok(bo);
    }

    /**
     * 查询-拣货单列表
     */
    @PostMapping("/pagePicking")
    public R<TableDataInfo<RwEntruckRecordGoodsSumVo>> pagePicking(@Validated @RequestBody RwWaitEntruckQueryBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        if (!RoleUtil.isRegionAdmin(user.getRolePermission())) {
            // 用户装卸队查询权限
            bo.setBasPortageTeamId(customToolService.getBasPortageTeamIdAndCheck(bo.getRegionWhId(), user.getUserId()));
        }
        TableDataInfo<RwEntruckRecordGoodsSumVo> table =rwEntruckGoodsService.pagePicking(bo);
        return R.ok(table);
    }

    /**
     * 查询-拣货单详情
     */
    @GetMapping("/pickingInfo/{pickingNo}")
    public R<RwEntruckRecordGoodsSumVo> pickingInfo(@NotNull(message = "拣货单号不能为空") @PathVariable String pickingNo) {
        RwEntruckRecordGoodsSumVo vo = rwEntruckGoodsService.pickingInfo(pickingNo, true);
        vo.setGoodsList(rwEntruckRecordService.queryGoodsListByIds(vo.getRecordIdList()));
        if (StrUtil.isBlank(vo.getEntruckName())) {
            for (int i = vo.getGoodsList().size() - 1; i >= 0; i--) {
                String createName = vo.getGoodsList().get(i).getCreateName();
                if (StrUtil.isNotBlank(createName)) {
                    vo.setEntruckName(createName);
                    break;
                }
            }
        }
        return R.ok(vo);
    }

    /**
     * 查询-拣货单详情
     */
    @PostMapping("/batchPickingInfo")
    public R<List<RwEntruckRecordGoodsSumVo>> batchPickingInfo(@Validated @RequestBody RwWaitEntruckQueryBo bo) {
        if (CollUtil.isEmpty(bo.getPickingNoList())) {
            return R.warn("拣货单号列表不能为空");
        }
        List<RwEntruckRecordGoodsSumVo> vos = rwEntruckGoodsService.pickingList(bo.getPickingNoList());
        List<Long> recordIds = new ArrayList<>();
        vos.forEach(vo -> {
            recordIds.addAll(vo.getRecordIdList());
            vo.setGoodsList(new ArrayList<>());
        });
        List<RwEntruckGoodsVo> goodsVoList = rwEntruckRecordService.queryGoodsListByIds(recordIds);
        for (RwEntruckRecordGoodsSumVo vo : vos) {
            List<Long> voRecordIds = vo.getRecordIdList();
            vo.getGoodsList().addAll(goodsVoList.stream().filter(f -> voRecordIds.contains(f.getRecordId())).collect(Collectors.toList()));
        }
        return R.ok(vos);
    }

    /**
     * 装车操作-单条商品-提交
     */
    @Log(title = "装车操作-单条商品-提交", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/operateEntruckGoods")
    public R<Void> operateEntruckGoods(@Validated @RequestBody RwEntruckGoodsEditBo bo) {
        if (bo.getEntruckQuantity() > 0) {
            regionEntruckAffairService.operateEntruckGoods(bo);
//            if (ObjectUtil.isNotNull(bo.getRecordId())) {
//                // 调用云仓出库单
//                String pickingNo = rwEntruckRecordService.selectAndCheckNullById(bo.getRecordId()).getPickingNo();
//                RwEntruckRecordGoodsSumVo vo = rwEntruckGoodsService.pickingInfo(pickingNo, false);
//                if (vo.getStatus().intValue() == DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode()) {
//                    regionEntruckAffairService.createCwStock(vo.getRegionWhId(),vo.getSaleDate(), pickingNo, rwEntruckGoodsService.queryListByRecordIds(vo.getRecordIdList()));
//                }
//            }
        }
        return R.ok();
    }

    /**
     * 装车操作-提交
     */
    @Log(title = "装车操作-提交", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/operateEntruckPicking")
    public R<Void> operateEntruckPicking(@Validated @RequestBody RwEntruckRecordEditPickingBo bo) {
        regionEntruckAffairService.operateEntruckPicking(bo);
        return R.ok();
    }

//    /**
//     * 完成拣货装车前 - 差异查询
//     */
//    @PostMapping("/waitEntruckGoodsLogisticsList")
//    public R<List<RwWaitEntruckGoodsVo>> waitEntruckGoodsLogisticsList(@Validated @RequestBody RwWaitEntruckQueryBo bo) {
//        if (bo.getSaleDate() == null) {
//            return R.warn("销售日期不能为空");
//        }
//        Map<Long, RwWaitEntruckGoodsVo> goodsMap = new HashMap<>();
//        List<RwWaitEntruckLogisticsVo> list = regionDeliveryAffairService.waitEntruckLogisticsList(bo);
//        for (RwWaitEntruckLogisticsVo vo : list) {
//            for (RwWaitEntruckGoodsVo goods : vo.getGoodsList()) {
//                if (goods.getWaitEntruckQuantity() <= 0) {
//                    continue;
//                }
//                RwWaitEntruckLogistics2Vo vo2 = new RwWaitEntruckLogistics2Vo();
//                BeanUtils.copyProperties(vo, vo2);
//                vo2.setWaitEntruckQuantity(goods.getWaitEntruckQuantity());
//                if (goodsMap.containsKey(goods.getSupplierSkuId())) {
//                    goodsMap.get(goods.getSupplierSkuId()).getLogisticsList().add(vo2);
//                } else {
//                    goods.getLogisticsList().add(vo2);
//                    goodsMap.put(goods.getSupplierSkuId(), goods);
//                }
//            }
//        }
//        return R.ok(goodsMap.values().stream().toList());
//    }

//    /**
//     * 完成拣货装车
//     */
//    @Log(title = "完成拣货装车", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PostMapping("/completeEntruck")
//    public R<Void> completeEntruck(@Validated @RequestBody RwWaitEntruckQueryBo bo) {
//        if (bo.getSaleDate() == null) {
//            return R.warn("销售日期不能为空");
//        }
//        regionDeliveryAffairService.completeEntruckPicking(bo);
//        return R.ok();
//    }


    /**
     * 拣货单商品标签打印查询
     */
    @PostMapping("/getDeliveryGoodsOrderLabelList")
    public R<List<RwEntruckGoodsOrderSimplifyVo>> getDeliveryGoodsOrderLabelList(@RequestBody RwEntruckGoodsOrderQueryBo bo) {
        if (StrUtil.isBlank(bo.getPickingNo())) {
            return R.warn("拣货单号不能为空");
        }
        return R.ok(entruckGoodsService.getDeliveryGoodsOrderLabelSimplifyList(bo));
    }
}
