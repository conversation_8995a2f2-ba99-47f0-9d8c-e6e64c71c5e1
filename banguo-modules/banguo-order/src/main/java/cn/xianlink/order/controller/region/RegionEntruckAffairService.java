package cn.xianlink.order.controller.region;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhParkingVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteWhNexusVo;
import cn.xianlink.common.api.enums.order.DeliveryStatusEnum;
import cn.xianlink.common.api.enums.product.TransSourceTypeEnum;
import cn.xianlink.common.api.enums.product.TransTypeCodeEnum;
import cn.xianlink.common.api.enums.product.TransTypeIoFlagEnum;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.constant.OrderCacheNames;
import cn.xianlink.order.controller.CustomToolService;
import cn.xianlink.order.domain.*;
import cn.xianlink.order.domain.bo.stockOut.BatchCreateStockoutBO;
import cn.xianlink.order.domain.delivery.bo.SupDeliveryAddBo;
import cn.xianlink.order.domain.delivery.bo.SupDeliveryGoodsAddBo;
import cn.xianlink.order.domain.delivery.bo.SupWaitDeliveryQueryBo;
import cn.xianlink.order.domain.delivery.vo.SupDeliveryGoodsSumVo;
import cn.xianlink.order.domain.delivery.vo.SupDeliveryVo;
import cn.xianlink.order.domain.depart.vo.RwDepartVo;
import cn.xianlink.order.domain.entruck.bo.*;
import cn.xianlink.order.domain.entruck.vo.*;
import cn.xianlink.order.domain.mq.DeliveryDiffMessageBo;
import cn.xianlink.order.domain.order.bo.QueryDeliverBo;
import cn.xianlink.order.domain.order.vo.QueryDeliverVo;
import cn.xianlink.order.domain.vo.stockOut.StockoutSkuRecordVO;
import cn.xianlink.order.mq.producer.AffiliatedReceiveBatchProducer;
import cn.xianlink.order.mq.producer.CreateStockoutProducer;
import cn.xianlink.order.mq.producer.DeliveryDiffHandleProducer;
import cn.xianlink.order.service.*;
import cn.xianlink.product.api.RemoteCwStockService;
import cn.xianlink.product.api.domain.bo.RemoteCwTransDetailDTO;
import cn.xianlink.product.api.domain.bo.RemoteCwTransHeadDTO;
import cn.xianlink.system.api.model.LoginUser;
import com.baomidou.lock.annotation.Lock4j;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
@CustomLog
public class RegionEntruckAffairService {
    private final transient ISupDeliveryService deliveryService;
    private final transient ISupDeliveryGoodsService deliveryGoodsService;
    private final transient IRwEntruckRecordService entruckRecordService;
    private final transient IRwEntruckGoodsService entruckGoodsService;
    private final transient IRwEntruckService entruckService;
    private final transient IRwDepartService departService;
    private final transient IOrderService orderService;
    private final transient CustomToolService customToolService;
    private final transient IStockoutSkuRecordService stockoutSkuRecordService;
    private final transient CreateStockoutProducer createStockoutProducer;
    private final transient DeliveryDiffHandleProducer deliveryDiffHandleProducer;
    private final transient AffiliatedReceiveBatchProducer affiliatedReceiveBatchProducer;
    @DubboReference
    private final transient RemoteRegionWhService remoteRegionWhService;
    @DubboReference
    private final transient RemoteCwStockService remoteCwStockService;

    /**
     * 加载送货单数据、获取装车单记录addBoList
     */
    public List<RwEntruckRecordAddBo> getEntruckRecordAddBoList(SupDeliveryAddBo bo, String source, boolean isDelivery) {
        // 加载可分配物流，用于缺货单
        LinkedHashMap<Long, RemoteRegionWhParkingVo> logisticsVoMap = customToolService.getDeliveryLogistics(bo.getRegionWhId(), isDelivery);
        if (logisticsVoMap.size() == 0) {
            return new ArrayList<>();
        }
        List<Long> logisticsIdList = logisticsVoMap.keySet().stream().toList();
        // 查询条件  提前发车
        if (YNStatusEnum.ENABLE.getCode().equals(bo.getIsEarlyEnd())) {
            logisticsIdList = customToolService.queryLogisticsIsEarlyEndMap(logisticsIdList, bo.getSaleDate())
                    .entrySet().stream().filter(entry -> YNStatusEnum.ENABLE.getCode().equals(entry.getValue())).map(Map.Entry::getKey).collect(Collectors.toList());
        }
        // 查询订单商品项
        QueryDeliverBo orderBo = new QueryDeliverBo();
        orderBo.setSource(source);
        orderBo.setSaleDate(bo.getSaleDate());
        orderBo.setRegionWhId(bo.getRegionWhId());
        orderBo.setSupplierId(bo.getSupplierId());
        orderBo.setSupplierSkuId(bo.getGoodsList().stream().map(SupDeliveryGoodsAddBo::getSupplierSkuId).collect(Collectors.toList()));
        orderBo.setLogisticsIdList(logisticsIdList);
        List<QueryDeliverVo> orderItemVos = orderService.queryDeliver(orderBo);
        if (CollectionUtil.isEmpty(orderItemVos)) {
            return new ArrayList<>();
        }
        // 供应商送货中、已装车、差异补送
        SupWaitDeliveryQueryBo deliveryBo = new SupWaitDeliveryQueryBo();
        deliveryBo.setSaleDate(bo.getSaleDate());
        deliveryBo.setRegionWhId(bo.getRegionWhId());
        deliveryBo.setSupplierId(bo.getSupplierId());
        deliveryBo.setSupplierSkuIds(orderBo.getSupplierSkuId());
        deliveryBo.setLogisticsIdList(orderBo.getLogisticsIdList());
        Map<String, RwEntruckGoodsSumVo> entruckListMap = entruckGoodsService.customLogisticsSumListBySupplierId(deliveryBo)
                .stream().collect(Collectors.toMap(RwEntruckGoodsSumVo::getLogisticsIdAndSupplierSkuIdKey, Function.identity()));
        // 不补送、不采购：生成缺货单
        Map<String, Integer> stockoutListMap = stockoutSkuRecordService.queryStockoutSkuCountByLogisticsId(bo.getSaleDate(), bo.getRegionWhId(), bo.getSupplierId(), orderBo.getLogisticsIdList())
                .stream().collect(Collectors.toMap(StockoutSkuRecordVO::getLogisticsIdAndSupplierSkuIdKey, StockoutSkuRecordVO::getStockoutCount));
        // 计算待送货= 需求数-已装车-缺货记录
        orderItemVos.forEach(item ->
            item.setCount(NumberUtil.sub(item.getCount(),
                    entruckListMap.get(item.getLogisticsIdAndSupplierSkuIdKey()) != null ? entruckListMap.get(item.getLogisticsIdAndSupplierSkuIdKey()).totalQuantity() : 0,
                    stockoutListMap.getOrDefault(item.getLogisticsIdAndSupplierSkuIdKey(), 0)
            ).intValue())
        );
        Map<Long, SupDeliveryGoodsAddBo> goodsMap = bo.getGoodsList().stream().filter(f -> f.getDeliveryQuantity() > 0).map(e -> {
            SupDeliveryGoodsAddBo entity = new SupDeliveryGoodsAddBo();
            BeanUtils.copyProperties(e, entity);
            return entity;
        }).collect(Collectors.toList()).stream().collect(Collectors.toMap(SupDeliveryGoodsAddBo::getSupplierSkuId, Function.identity(), (key1, key2) -> key2));
        // 按商品logisticsId分组
        Map<Long, List<QueryDeliverVo>> itemListMap = orderItemVos.stream().filter(f -> f.getCount() > 0).collect(Collectors.groupingBy(QueryDeliverVo::getLogisticsId));
        // 按车位顺序给物流线分配
        List<RwEntruckRecordAddBo> recordBos = new ArrayList<>();
        int totalEntruckRecordQuantity = 0;
        for (Long logisticsId : logisticsIdList) {
            // 物流线无需求商品项
            if (!itemListMap.containsKey(logisticsId)) {
                continue;
            }
            List<RwEntruckGoodsAddBo> goodsBos = new ArrayList<>();
            for (QueryDeliverVo item : itemListMap.get(logisticsId)) {
                SupDeliveryGoodsAddBo goods = goodsMap.get(item.getSupplierSkuId());
                if (ObjectUtil.isNull(goods) || goods.getDeliveryQuantity() <= 0) {
                    continue;
                }
                RwEntruckGoodsAddBo goodsVo = new RwEntruckGoodsAddBo();
                BeanUtils.copyProperties(goods, goodsVo);
                // 剩余发货商品数量 = 发货商品数量 - 物流商品需求数量
                int surplusQuantity = NumberUtil.sub(goods.getDeliveryQuantity(), item.getCount()).intValue();
                if (surplusQuantity >= 0) {
                    goods.setDeliveryQuantity(surplusQuantity);
                    goodsVo.setDeliveryQuantity(item.getCount());
                } else {
                    goods.setDeliveryQuantity(0);
                }
                totalEntruckRecordQuantity += goodsVo.getDeliveryQuantity();
                goodsBos.add(goodsVo);
            }

            if (goodsBos.size() > 0) {
                RwEntruckRecordAddBo recordBo = new RwEntruckRecordAddBo();
                recordBo.setSaleDate(bo.getSaleDate());
                recordBo.setRegionWhId(bo.getRegionWhId());
                recordBo.setSupplierId(bo.getSupplierId());
                recordBo.setSupplierName(bo.getSupplierName());
                recordBo.setSupplierDeptId(bo.getSupplierDeptId());
                recordBo.setSupplierDeptName(bo.getSupplierDeptName());
                recordBo.setLogisticsId(logisticsId);
                recordBo.setParkingNo(logisticsVoMap.get(logisticsId).getParkingNo());
                recordBo.setCityWhId(logisticsVoMap.get(logisticsId).getCityWhId());
                recordBo.setIsAffiliated(logisticsVoMap.get(logisticsId).getIsAffiliated());
                recordBo.setGoodsList(goodsBos);
                recordBos.add(recordBo);
            }
        }
        bo.setTotalDeliveryQuantity(bo.getGoodsList().stream().mapToInt(SupDeliveryGoodsAddBo::getDeliveryQuantity).sum());
        if (totalEntruckRecordQuantity != bo.getTotalDeliveryQuantity()) {
            throw new ServiceException("总件数大于待送货件数");
        }
        return recordBos;
    }
    // 按订单项送货业务，生成送货单关联订单项数据
    public void loadDeliveryOrderItemList(List<RwEntruckRecordAddBo> recordBos) {
        if (CollUtil.isEmpty(recordBos)) {
            return;
        }
        RwEntruckRecordAddBo recordBo = recordBos.get(0);
        if (!this.checkIsOrderItemDelivery(recordBo.getRegionWhId(), recordBo.getSaleDate())) {
            return;
        }

        List<Long> supplierSkuIds = recordBos.stream().flatMap(parent -> parent.getGoodsList().stream())
                .map(RwEntruckGoodsAddBo::getSupplierSkuId).distinct().collect(Collectors.toList());
        List<Long> logisticsIds = recordBos.stream().map(RwEntruckRecordAddBo::getLogisticsId).collect(Collectors.toList());

        QueryDeliverBo orderBo = new QueryDeliverBo();
        orderBo.setSaleDate(recordBo.getSaleDate());
        orderBo.setRegionWhId(recordBo.getRegionWhId());
        orderBo.setSupplierSkuId(supplierSkuIds);
        orderBo.setLogisticsIdList(logisticsIds);
        orderBo.setIsOrderItemDelivery(YNStatusEnum.ENABLE.getCode());
        Map<String, List<QueryDeliverVo>> itemListMap2 = orderService.queryDeliver(orderBo)
                .stream().collect(Collectors.groupingBy(QueryDeliverVo::getLogisticsIdAndSupplierSkuIdKey));
        for (RwEntruckRecordAddBo recordAddBo : recordBos) {
            List<RwEntruckGoodsOrder> deliveryDrderItemList = new ArrayList<>();
            for (RwEntruckGoodsAddBo goodsAddBo : recordAddBo.getGoodsList()) {
                List<QueryDeliverVo> itemList = itemListMap2.get(String.format("%s-%s", recordAddBo.getLogisticsId(), goodsAddBo.getSupplierSkuId()));
                if (CollUtil.isEmpty(itemList)) {
                    throw new ServiceException("关联客户订单失败1");
                }
                int surplusQuantity = goodsAddBo.getDeliveryQuantity();
                for (QueryDeliverVo item : itemList) {
                    if (surplusQuantity <= 0) {
                        break;
                    }
                    RwEntruckGoodsOrder goodsOrder = BeanUtil.copyProperties(item, RwEntruckGoodsOrder.class);
                    goodsOrder.setParkingNo(recordAddBo.getParkingNo());
                    goodsOrder.setPickingNo(recordAddBo.getPickingNo());
                    goodsOrder.setDeliveryQuantity(surplusQuantity >= item.getWaitDeliveryQuantity() ? item.getWaitDeliveryQuantity() : surplusQuantity);
                    deliveryDrderItemList.add(goodsOrder);
                    surplusQuantity -= goodsOrder.getDeliveryQuantity();
                }
                //送货数量关联待送货订单项，未关联完
                if (surplusQuantity > 0) {
                    throw new ServiceException("关联客户订单失败2");
                }
            }
            recordAddBo.setOrderItemList(deliveryDrderItemList);
        }
    }
    public boolean checkIsOrderItemDelivery(Long regionWhId, LocalDate saleDate){
        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(regionWhId);
        if (YNStatusEnum.ENABLE.getCode().equals(regionWhVo.getIsOrderItemDelivery())
                && (ObjectUtil.isNull(regionWhVo.getEffectSaleDate()) || saleDate.compareTo(regionWhVo.getEffectSaleDate()) >= 0)) {
           return true;
        } else {
            return false;
        }
    }

    /**
     * 装车操作-单条商品-提交
     */
    @Transactional(rollbackFor = Throwable.class)
    public void operateEntruckGoods(RwEntruckGoodsEditBo bo) {
        if (bo.getEntruckQuantity() < 0) {
            throw new ServiceException("装车件数不能为负数");
        }
        List<RwEntruckGoodsVo> goodsVoList = entruckGoodsService.queryListByIds(ListUtil.toList(bo.getId()));
        if (goodsVoList.size() == 0) {
            throw new ServiceException("装车商品id不存在");
        }
        RwEntruckGoodsVo goodsVo = goodsVoList.get(0);
        if (bo.getEntruckQuantity() + goodsVo.getEntruckQuantity() > goodsVo.getDeliveryQuantity()) {
            throw new ServiceException(goodsVo.getSpuName() + "，装车合计提交超过应装数量");
        }
        // 加载装车数据list
        List<RwEntruckGoods> entityList = new ArrayList<>();
        if (bo.getEntruckQuantity() == 0) {
            RwEntruckGoods entity = new RwEntruckGoods();
            entity.setId(goodsVo.getId());
            entity.setDiffStatus(DeliveryStatusEnum.ENTRUCK_NO_DIFF.getCode());
            if (goodsVo.getDeliveryQuantity().intValue() > bo.getEntruckQuantity()) {
                entity.setDiffStatus(DeliveryStatusEnum.ENTRUCK_HAVE_DIFF.getCode());
            }
            entity.setEntruckTime(DateTime.now());
            entityList.add(entity);
        } else {
            RwEntruckGoodsBatchEditBo batchEditBo = new RwEntruckGoodsBatchEditBo();
            batchEditBo.setIds(bo.getId().toString());
            batchEditBo.setEntruckQuantity(bo.getEntruckQuantity());
            entityList = this.getRwEntruckGoodsList(batchEditBo, goodsVoList);
        }
        // 更新数据、状态
        if (entityList.size() > 0) {
            bo.setRecordId(goodsVo.getRecordId());
            bo.setDeliveryId(goodsVo.getDeliveryId());
            this.autoUpdateDeliveryEntruckStatus(goodsVoList, entityList, 3, true);
        }
    }
    /**
     * 操作装车，修改状态
     */
    @Transactional(rollbackFor = Throwable.class)
    public void operateEntruck(RwEntruckRecordEditBo bo, Boolean isAuto) {
        List<RwEntruckGoods> entityList = new ArrayList<>();
        Map<Long, Integer> editIdMap = new HashMap<>();
        if (CollUtil.isNotEmpty(bo.getGoodsList())) {
            editIdMap = bo.getGoodsList().stream().collect(Collectors.toMap(RwEntruckGoodsEditBo::getId, RwEntruckGoodsEditBo::getEntruckQuantity, (key1, key2) -> key2));
        }
        Map<Long, Integer> surplusQuantityMap = deliveryGoodsService.customSumListByDeliveryId(bo.getDeliveryId())
                .stream().collect(Collectors.toMap(SupDeliveryGoodsSumVo::getSupplierSkuId, SupDeliveryGoodsSumVo::getSurplusQuantity, (key1, key2) -> key2));
        List<RwEntruckGoodsVo> goodsVoList = entruckGoodsService.queryListByRecordIds(CollectionUtil.toList(bo.getId()));
        for (RwEntruckGoodsVo goodsVo : goodsVoList) {
            Integer entruckQuantity = editIdMap.getOrDefault(goodsVo.getId(), 0);
            if (isAuto) {
                if (surplusQuantityMap.get(goodsVo.getSupplierSkuId()) - entruckQuantity < 0) {
                    entruckQuantity = surplusQuantityMap.get(goodsVo.getSupplierSkuId());
                }
            } else {
                if (surplusQuantityMap.get(goodsVo.getSupplierSkuId()) - entruckQuantity < 0) {
                    throw new ServiceException(goodsVo.getSpuName() + "，装车超过质检合格件数");
                }
                if (entruckQuantity + goodsVo.getEntruckQuantity() > goodsVo.getDeliveryQuantity()) {
                    throw new ServiceException(goodsVo.getSpuName() + "，装车合计提交超过应装数量");
                }
            }
            RwEntruckGoods entity = new RwEntruckGoods();
            entity.setId(goodsVo.getId());
            entity.setEntruckQuantity(goodsVo.getEntruckQuantity() + entruckQuantity);
            entity.setDiffStatus(DeliveryStatusEnum.ENTRUCK_NO_DIFF.getCode());
            if (goodsVo.getDeliveryQuantity() - entity.getEntruckQuantity() > 0) {
                entity.setDiffStatus(DeliveryStatusEnum.ENTRUCK_HAVE_DIFF.getCode());
            }
            entityList.add(entity);
        }
        this.autoUpdateDeliveryEntruckStatus(goodsVoList, entityList, 3, true);

//        if (entruckRecordService.updateEntruck(bo) <= 0) {
//            throw new ServiceException("装车操作失败2");
//        }
//        if (!entruckGoodsService.updateEntruck(entityList)) {
//            throw new ServiceException("装车操作失败1");
//        }
//        if (!entruckRecordService.isExistEntruckRecordStatus(bo.getDeliveryId(), DeliveryStatusEnum.COMPLETE_INSPECT.getCode())) {
//            if (entruckGoodsService.isExistEntruckDiffStatus(bo.getDeliveryId(), DeliveryStatusEnum.ENTRUCK_HAVE_DIFF.getCode())) {
//                if (deliveryService.updateStatusAndDiffStatus(bo.getDeliveryId(), DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode(), DeliveryStatusEnum.ENTRUCK_HAVE_DIFF.getCode()) <= 0) {
//                    throw new ServiceException("装车操作失败3");
//                }
//                DeliveryDiffMessageBo msgBo = new DeliveryDiffMessageBo();
//                msgBo.setDeliveryId(bo.getDeliveryId());
//                msgBo.setDeliveryNo(deliveryService.selectAndCheckNullById(bo.getDeliveryId()).getDeliveryNo());
//                msgBo.setStatusTime(DateTime.now());
//                msgBo.setSource(3);
//                deliveryDiffHandleProducer.send(msgBo);
//            } else {
//                if (deliveryService.updateStatus(bo.getDeliveryId(), DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode()) <= 0) {
//                    throw new ServiceException("装车操作失败4");
//                }
//            }
//        }
    }

    /**
     * 拣货单 - 完成操作装车，修改状态
     */
    @Transactional(rollbackFor = Throwable.class)
    public void operateEntruckPicking(RwEntruckRecordEditPickingBo bo) {
        RwEntruckRecordGoodsSumVo vo = entruckGoodsService.pickingInfo(bo.getPickingNo(), false);
        if (vo.getStatus().intValue() != DeliveryStatusEnum.COMPLETE_INSPECT.getCode()) {
            throw new ServiceException("非待装车状态，不允许装车操作");
        }
        Map<Long, Integer> editIdMap = new HashMap<>();
        if (CollUtil.isNotEmpty(bo.getGoodsList())) {
            editIdMap = bo.getGoodsList().stream().collect(Collectors.toMap(RwEntruckGoodsEditBo::getId, RwEntruckGoodsEditBo::getEntruckQuantity, (key1, key2) -> key2));
        }
        // 查询装车记录，筛选待装车状态20
        List<RwEntruckRecord> recordList = entruckRecordService.selectListByIds(vo.getRecordIdList())
                .stream().filter(f -> f.getStatus().intValue() == DeliveryStatusEnum.COMPLETE_INSPECT.getCode()).collect(Collectors.toList());

        List<Long> recordIds = recordList.stream().map(RwEntruckRecord::getId).collect(Collectors.toList());
        List<RwEntruckGoodsVo> goodsVoList = entruckGoodsService.queryListByRecordIds(recordIds);

        List<RwEntruckGoods> entityList = new ArrayList<>();
        for (RwEntruckGoodsVo goodsVo : goodsVoList) {
            // 已经是 应装车=已装车， 不处理
            if (goodsVo.getEntruckQuantity() == goodsVo.getDeliveryQuantity()) {
                continue;
            }
            int entruckQuantity = editIdMap.getOrDefault(goodsVo.getId(), 0);
            if (entruckQuantity + goodsVo.getEntruckQuantity() > goodsVo.getDeliveryQuantity()) {
                throw new ServiceException(goodsVo.getSpuName() + "，装车合计提交超过应装数量");
            }
            RwEntruckGoods entity = new RwEntruckGoods();
            entity.setId(goodsVo.getId());
            entity.setEntruckQuantity(goodsVo.getEntruckQuantity() + entruckQuantity);
            entity.setDiffStatus(DeliveryStatusEnum.ENTRUCK_NO_DIFF.getCode());
            if (goodsVo.getDeliveryQuantity() - entity.getEntruckQuantity() > 0) {
                entity.setDiffStatus(DeliveryStatusEnum.ENTRUCK_HAVE_DIFF.getCode());
            }
            goodsVo.setDiffStatus(entity.getDiffStatus());
            entityList.add(entity);
        }
        // 批量修改装车记录状态
        entruckGoodsService.updateEntruck(entityList);
        entruckRecordService.updateEntruck(recordIds);
        // 批量修改送货单状态
        List<Long> deliveryIds = goodsVoList.stream().map(RwEntruckGoodsVo::getDeliveryId).distinct().toList();
        deliveryService.updateStatus(deliveryIds, DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode());
        // 有装车差异的送货单id
        List<Long> diffDeliveryIds = goodsVoList.stream().filter(f -> f.getDiffStatus().intValue() == DeliveryStatusEnum.ENTRUCK_HAVE_DIFF.getCode())
                .map(RwEntruckGoodsVo::getDeliveryId).distinct().toList();
        if (diffDeliveryIds.size() > 0) {
            // 批量修改送货单 差异状态
            deliveryService.updateDiffStatus(diffDeliveryIds, DeliveryStatusEnum.ENTRUCK_HAVE_DIFF.getCode());
            // 发送差异待处理消息
            Map<Long, String> deliveryNoMap = deliveryService.selectListById(diffDeliveryIds).stream().collect(Collectors.toMap(SupDeliveryVo::getId, SupDeliveryVo::getDeliveryNo, (key1, key2) -> key2));
            LoginUser loginUser = LoginHelper.getLoginUser();
            diffDeliveryIds.forEach(deliveryId -> {
                DeliveryDiffMessageBo msgBo = new DeliveryDiffMessageBo();
                msgBo.setDeliveryId(deliveryId);
                msgBo.setDeliveryNo(deliveryNoMap.get(deliveryId));
                msgBo.setStatusTime(DateTime.now());
                msgBo.setDelayTimeLevel(6);
                msgBo.setSource(4);
                msgBo.setUserId(loginUser.getUserId());
                msgBo.setUserCode(loginUser.getUserCode());
                msgBo.setUserName(loginUser.getRealName());
                deliveryDiffHandleProducer.send(msgBo);
            });
        }
        // 直属仓 自动接车
        List<Long> affiliatedRecordIds = recordList.stream().filter(f -> f.getIsAffiliated() == YNStatusEnum.ENABLE.getCode())
                .map(RwEntruckRecord::getId).collect(Collectors.toList());
        if (affiliatedRecordIds.size() > 0) {
            affiliatedReceiveBatchProducer.send(affiliatedRecordIds);
        }
        // 调用云仓出库单
//        this.createCwStock(vo.getRegionWhId(),vo.getSaleDate(), bo.getPickingNo(), entruckGoodsService.queryListByRecordIds(vo.getRecordIdList()));
    }

    /**
     * 撤销装车
     */
    @Transactional(rollbackFor = Throwable.class)
    public void revokeEntruck(Long recordId) {
        RwEntruckRecordVo recordVo = entruckRecordService.selectAndCheckNullById(recordId);
        if (recordVo.getStatus().intValue() != DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode()) {
            throw new ServiceException("非已装车状态，不允许撤销");
        }
        if (StringUtils.isNotBlank(recordVo.getEntruckNo())) {
            if (recordVo.getIsAffiliated().intValue() == YNStatusEnum.ENABLE.getCode()) {
                throw new ServiceException("直属城市仓已接货，不允许撤销");
            }
            throw new ServiceException("已生成装车单，不允许撤销");
        }
        if (entruckRecordService.revokeEntruck(recordId) <= 0) {
            throw new ServiceException("撤销装车失败1");
        }
        if (entruckGoodsService.revokeEntruck(recordId) <= 0) {
            throw new ServiceException("撤销装车失败2");
        }
        if (deliveryService.updateStatus(recordVo.getDeliveryId(), DeliveryStatusEnum.COMPLETE_INSPECT.getCode()) <= 0) {
            throw new ServiceException("撤销装车失败3");
        }
    }

    /**
     * 自动创建装车单
     */
    public void autoCreateEntruck(LocalDate saleDate, Long regionWhId) {
        RwWaitEntruckQueryBo bo = new RwWaitEntruckQueryBo();
        bo.setSaleDate(saleDate);
        bo.setRegionWhId(regionWhId);
        List<RwEntruckRecordSumVo> recordSumVos = entruckRecordService.completeEntruckLogisticsCount(bo);
        for (RwEntruckRecordSumVo vo : recordSumVos) {
            RwEntruckAddBo entruckBo = new RwEntruckAddBo();
            entruckBo.setSaleDate(vo.getSaleDate());
            entruckBo.setRegionWhId(vo.getRegionWhId());
            entruckBo.setLogisticsId(vo.getLogisticsId());
            this.createEntruck(entruckBo, false);
        }
    }

    /**
     * 创建装车单
     */
    @Lock4j(name = OrderCacheNames.OPERATE_PICKING_ENTRUCK_LOCK, keys = "#bo.regionWhId + '_' + #bo.saleDate + '_' + #bo.logisticsId", expire = 5000, acquireTimeout = 1000)
    @Transactional(rollbackFor = Throwable.class)
    public void createEntruck(RwEntruckAddBo bo, boolean isRelatedDepart) {
        List<RwEntruckRecordVo> recordVos = entruckRecordService.completeEntruckList(bo.getRegionWhId(), bo.getLogisticsId(), bo.getSaleDate(), DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode());
        if (recordVos.size() == 0) {
            throw new ServiceException("无装车记录，不能创建装车单");
        }
        if (isRelatedDepart) {
            // 查询未关联的车辆信息-发车单
            List<RwDepartVo> departVos = departService.getWaitRelatedList(bo.getRegionWhId(), bo.getLogisticsId(), bo.getSaleDate());
            if (departVos.size() > 0) {
                bo.setDepartId(departVos.get(0).getId());
                bo.setDepartNo(departVos.get(0).getDepartNo());
                bo.setDepartDate(LocalDate.now());
            }
        }

        List<RwEntruckRecordEditEntruckBo> editBoList = new ArrayList<>();
        int totalEntruckQuantity = 0;
        BigDecimal totalSpuGrossWeight = null;
        for (RwEntruckRecordVo recordVo : recordVos) {
            RwEntruckRecordEditEntruckBo editBo = new RwEntruckRecordEditEntruckBo();
            editBo.setId(recordVo.getId());
            editBoList.add(editBo);
            totalEntruckQuantity += recordVo.getGoodsList().stream().mapToInt(RwEntruckGoodsVo::getEntruckQuantity).sum();
            totalSpuGrossWeight = NumberUtil.add(totalSpuGrossWeight, recordVo.getGoodsList().stream().map(p -> NumberUtil.mul(p.getEntruckQuantity(), p.getSpuGrossWeight())).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        bo.setTotalEntruckQuantity(totalEntruckQuantity);
        bo.setTotalSpuGrossWeight(totalSpuGrossWeight);
        bo.setCityWhId(recordVos.get(0).getCityWhId());
        bo.setParkingNo(recordVos.get(0).getParkingNo());

        RemoteWhNexusVo vo = remoteRegionWhService.queryWhNexusById(bo.getRegionWhId(), bo.getCityWhId());
        if (vo != null) {
            bo.setArrivalDate(LocalDate.now().plusDays(vo.getArrivalDays()));
        }
        entruckService.insert(bo);
        editBoList.forEach(e -> {
            e.setEntruckId(bo.getId());
            e.setEntruckNo(bo.getEntruckNo());
        });
        entruckRecordService.updateEntruckNo(editBoList);
    }

    /**
     * 分货装车--操作装车，修改状态
     */
    public void operateEntruckSimplify(RwEntruckGoodsBatchEditBo bo) {
        List<Long> ids = Arrays.stream(bo.getIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
        // 这里有批量提交ids, 会存在已装车完成id
        List<RwEntruckGoodsVo> goodsVoList = entruckGoodsService.queryListByIds(ids);
        List<RwEntruckGoods> entityList = this.getRwEntruckGoodsList(bo, goodsVoList);
        if (entityList.size() > 0) {
            this.autoUpdateDeliveryEntruckStatus(goodsVoList, entityList, 3, false);
        }
    }
    private List<RwEntruckGoods> getRwEntruckGoodsList(RwEntruckGoodsBatchEditBo bo, List<RwEntruckGoodsVo> goodsVoList) {
        List<RwEntruckGoods> entityList = new ArrayList<>();
        for (RwEntruckGoodsVo goodsVo : goodsVoList) {
            if (bo.getEntruckQuantity() <= 0) {
                break;
            }
            // 有差异 或 应装车=已装车， 不处理
            if (goodsVo.getDiffStatus().intValue() > DeliveryStatusEnum.ENTRUCK_NO_DIFF.getCode()
                    || goodsVo.getEntruckQuantity().intValue() == goodsVo.getDeliveryQuantity().intValue()) {
                continue;
            }
            RwEntruckGoods entity = new RwEntruckGoods();
            entity.setId(goodsVo.getId());
            // 剩余分配数量 = 提交装车数量 + 部分已装车数量 - 商品应装车数量
            int surplusQuantity = bo.getEntruckQuantity() + goodsVo.getEntruckQuantity() - goodsVo.getDeliveryQuantity();
            if (surplusQuantity >= 0) {
                entity.setEntruckQuantity(goodsVo.getDeliveryQuantity());
                bo.setEntruckQuantity(surplusQuantity);
            } else {
                entity.setEntruckQuantity(bo.getEntruckQuantity() + goodsVo.getEntruckQuantity());
                bo.setEntruckQuantity(0);
            }
            entityList.add(entity);
        }
        return entityList;
    }

    /**
     * 分货装车--完成分货装车
     */
    public void completeEntruckSimplify(RwWaitEntruckQueryBo bo) {
        List<RwEntruckGoodsVo> goodsVoList = entruckGoodsService.customWaitEntruckGoodsListBySkuId(bo);
        if (goodsVoList.size() == 0) {
            return;
        }
        // 更新装车记录-商品差异状态
        List<RwEntruckGoods> entityList = goodsVoList.stream().map(e -> {
            RwEntruckGoods entity = new RwEntruckGoods();
            entity.setId(e.getId());
            entity.setDiffStatus(DeliveryStatusEnum.ENTRUCK_HAVE_DIFF.getCode());
            entity.setDiffDealTime(DateTime.now());
            return entity;
        }).collect(Collectors.toList());

        this.autoUpdateDeliveryEntruckStatus(goodsVoList, entityList, 4, false);
    }

    /**
     * 定时任务 - 20待装车状态 自动完成装车
     */
    @Transactional(rollbackFor = Throwable.class)
    public void autoCompleteEntruck(LocalDate saleDate, Long regionWhId) {
        RwWaitEntruckQueryBo bo = new RwWaitEntruckQueryBo();
        bo.setSaleDate(saleDate);
        bo.setRegionWhId(regionWhId);
        bo.setEntruckType(null);
        List<RwEntruckGoodsVo> goodsVoList = entruckGoodsService.customWaitEntruckGoodsListBySkuId(bo);
        if (goodsVoList.size() == 0) {
            return;
        }
        // 更新装车记录
        List<RwEntruckGoods> entityList = goodsVoList.stream().map(e -> {
            RwEntruckGoods entity = new RwEntruckGoods();
            entity.setId(e.getId());
            entity.setEntruckQuantity(e.getDeliveryQuantity());
            return entity;
        }).collect(Collectors.toList());
        this.autoUpdateDeliveryEntruckStatus(goodsVoList, entityList, 3, false);
    }

    @Transactional(rollbackFor = Throwable.class)
    public void autoUpdateDeliveryEntruckStatus(List<RwEntruckGoodsVo> goodsVoList, List<RwEntruckGoods> entityList, Integer source, Boolean isDelayHandleDiff) {
        // 更新商品装车件数
        entruckGoodsService.updateEntruck(entityList);

        // 商品对应的装车记录id，判断装车记录下的商品是否都装车完成，是：自动更新装车记录状态=30已装车
        Map<Long, Long> recordIdMap = goodsVoList.stream().collect(Collectors.toMap(RwEntruckGoodsVo::getRecordId, RwEntruckGoodsVo::getDeliveryId, (key1, key2) -> key2));
        // 查询明细存在未装车的 recordId
        List<Long> waitEntruckRecordIds = entruckGoodsService.getExistWaitEntruckGoodsMap(recordIdMap.keySet().stream().toList());
        // 剔除未装车完的记录，保留已装车完的recordIdAndDeliveryIds
        waitEntruckRecordIds.forEach(recordId -> recordIdMap.remove(recordId));
        if (recordIdMap.size() == 0) {
            return;
        }
        // 查询装车记录，筛选待装车状态20
        List<RwEntruckRecord> recordList = entruckRecordService.selectListByIds(recordIdMap.keySet().stream().toList())
                .stream().filter(f -> f.getStatus().intValue() == DeliveryStatusEnum.COMPLETE_INSPECT.getCode()).collect(Collectors.toList());
        if (recordList.size() == 0) {
            return;
        }
        // 批量修改状态
        entruckRecordService.updateEntruck(recordList.stream().map(RwEntruckRecord::getId).collect(Collectors.toList()));
        // 直属仓 自动接车
        List<Long> affiliatedRecordIds = recordList.stream().filter(f -> f.getIsAffiliated() == YNStatusEnum.ENABLE.getCode()).map(RwEntruckRecord::getId).distinct().toList();
        if (affiliatedRecordIds.size() > 0) {
            affiliatedReceiveBatchProducer.send(affiliatedRecordIds);
        }
        // 基采、地产 调用云仓出库单
//        if (isCreateCwStock) {
//            List<Long> cwStockRecordIds = recordList.stream()
//                    .filter(f -> f.getBusinessType().intValue() == OrderBusinessTypeEnum.BUSINESS_TYPE20.getCode() || f.getBusinessType().intValue() == OrderBusinessTypeEnum.BUSINESS_TYPE30.getCode())
//                    .map(RwEntruckRecord::getId).collect(Collectors.toList());
//            if (cwStockRecordIds.size() > 0) {
//                RwEntruckRecord rwEntruckRecord = recordList.get(0);
//                this.createCwStock(rwEntruckRecord.getRegionWhId(),rwEntruckRecord.getSaleDate(), "FH", entruckGoodsService.queryListByRecordIds(cwStockRecordIds));
//            }
//        }

        // 商品对应的送货单id，判断送货单下的装车记录是否都30已装车，是：自动更新送货单状态=30已装车
        Map<Long, Long> deliveryIdMap = recordList.stream().collect(Collectors.toMap(RwEntruckRecord::getDeliveryId, RwEntruckRecord::getId, (key1, key2) -> key2));
        // 查询存在装车记录待装车的 送货单id
        List<Long> waitEntruckDeliveryIds = entruckRecordService.getExistEntruckRecordStatus(deliveryIdMap.keySet().stream().toList(), DeliveryStatusEnum.COMPLETE_INSPECT.getCode());
        // 剔除未待装车的送货单id，保留已装车的deliveryIds 批量修改状态
        waitEntruckDeliveryIds.forEach(deliveryId -> deliveryIdMap.remove(deliveryId));
        if (deliveryIdMap.size() == 0) {
            return;
        }
        // 批量修改状态
        List<Long> deliveryIds = deliveryIdMap.keySet().stream().toList();
        deliveryService.updateStatus(deliveryIds, DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode());
        // 有装车差异的送货单id
        List<Long> diffDeliveryIds = entruckGoodsService.getExistEntruckDiffStatus(deliveryIds, DeliveryStatusEnum.ENTRUCK_HAVE_DIFF.getCode());
        if (diffDeliveryIds.size() == 0) {
            return;
        }
        // 批量修改 差异状态
        deliveryService.updateDiffStatus(diffDeliveryIds, DeliveryStatusEnum.ENTRUCK_HAVE_DIFF.getCode());
        // 发送差异待处理消息
        Map<Long, String> deliveryNoMap =  deliveryService.selectListById(diffDeliveryIds).stream().collect(Collectors.toMap(SupDeliveryVo::getId, SupDeliveryVo::getDeliveryNo, (key1, key2) -> key2));
        diffDeliveryIds.forEach(deliveryId -> {
            DeliveryDiffMessageBo msgBo = new DeliveryDiffMessageBo();
            msgBo.setDeliveryId(deliveryId);
            msgBo.setDeliveryNo(deliveryNoMap.get(deliveryId));
            msgBo.setStatusTime(DateTime.now());
            if (!isDelayHandleDiff) {
                msgBo.setDelayTimeLevel(6);
            }
            msgBo.setSource(source);
            deliveryDiffHandleProducer.send(msgBo);
        });
    }

    /**
     * 装车数据异常的缺货申请作废 - 修复装车件数（实际货物已上车）
     */
    @Transactional(rollbackFor = Throwable.class)
    public RwEntruckVo repairEntruckDiffQuantity(Long recordId, Long supplierSkuId, Integer repairQuantity) {
        log.keyword("repairEntruckDiffQuantity").info("recordId={}, supplierSkuId={}, repairQuantity={}", recordId, supplierSkuId, repairQuantity);
        RwEntruckVo entruckVo = new RwEntruckVo();
        if (repairQuantity == null || repairQuantity <= 0) {
            log.keyword("repairEntruckDiffQuantity").info("恢复件数必须大于0");
            return entruckVo;
        }
        RwEntruckRecordVo recordVo = entruckRecordService.selectAndCheckNullById(recordId);
        // 非已装车状态，无需执行处理
        if (recordVo.getStatus().intValue() != DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode()) {
            log.keyword("repairEntruckDiffQuantity").info("status={}, 装车记录状态必须=30", recordVo.getStatus());
            return entruckVo;
        }
        Map<Long, RwEntruckGoodsVo> goodsVoMap = entruckGoodsService.queryListByRecordIds(ListUtil.toList(recordVo.getId()))
                .stream().collect(Collectors.toMap(RwEntruckGoodsVo::getSupplierSkuId, Function.identity(), (key1, key2) -> key2));
        RwEntruckGoodsVo goodsVo = goodsVoMap.get(supplierSkuId);
        // 商品skuid不存在
        if (goodsVo == null) {
            log.keyword("repairEntruckDiffQuantity").info("商品明细不存在");
            return entruckVo;
        }
        BigDecimal repairSpuGrossWeight = NumberUtil.mul(goodsVo.getSpuGrossWeight(), repairQuantity);
        // 修改明细: 1差异状态，2装车数量
        RwEntruckGoods goodsBo = new RwEntruckGoods();
        goodsBo.setId(goodsVo.getId());
        goodsBo.setEntruckQuantity(goodsVo.getEntruckQuantity() + repairQuantity);
        goodsBo.setDiffStatus(DeliveryStatusEnum.ENTRUCK_NO_DIFF.getCode());
        if (goodsVo.getDeliveryQuantity().intValue() < goodsBo.getEntruckQuantity()) {
            throw new ServiceException("恢复件数+装车件数，超过应装件数");
        }
        if (goodsVo.getDeliveryQuantity().intValue() > goodsBo.getEntruckQuantity()) {
            goodsBo.setDiffStatus(DeliveryStatusEnum.DIFF_NO_DELIVERY.getCode());
        }
        log.keyword("repairEntruckDiffQuantity").info("修改装车记录明细：id={}, entruckQuantity={}, diffStatus={}, 原entruckQuantity={}",
                goodsBo.getId(), goodsBo.getEntruckQuantity(), goodsBo.getDiffStatus(), goodsVo.getEntruckQuantity());
        entruckGoodsService.updateEntruck(ListUtil.toList(goodsBo));
        // 已生成装车单
        if (StrUtil.isNotBlank(recordVo.getEntruckNo())) {
            // 是否直属仓
            if (recordVo.getIsAffiliated().intValue() == YNStatusEnum.ENABLE.getCode()) {
                entruckVo.setId(recordVo.getId());
                entruckVo.setRegionWhId(recordVo.getRegionWhId());
                entruckVo.setStatus(DeliveryStatusEnum.COMPLETE_RECEIVE.getCode());
            } else {
                // 修改装车单: 1装车总件数，2装车毛重合计(斤)
                entruckVo = entruckService.selectAndCheckNullById(recordVo.getEntruckId());
                RwEntruck entruckBo = new RwEntruck();
                entruckBo.setId(entruckVo.getId());
                entruckBo.setTotalEntruckQuantity(entruckVo.getTotalEntruckQuantity() + repairQuantity);
                entruckBo.setTotalSpuGrossWeight(entruckVo.getTotalSpuGrossWeight().add(repairSpuGrossWeight));
                log.keyword("repairEntruckDiffQuantity").info("修改装车单：id={}, totalEntruckQuantity={}, totalSpuGrossWeight={}, 原totalEntruckQuantity={}, 原totalSpuGrossWeight={}, ",
                        entruckBo.getId(), entruckBo.getTotalEntruckQuantity(), entruckBo.getTotalSpuGrossWeight(), entruckVo.getTotalEntruckQuantity(), entruckVo.getTotalSpuGrossWeight());
                entruckService.update(entruckBo);

                // 已发车
                if (entruckVo.getStatus().intValue() == DeliveryStatusEnum.COMPLETE_DEPART.getCode()) {
                    // 修改发车单 毛重合计(斤)
                    RwDepartVo departVo = departService.selectAndCheckNullById(entruckVo.getDepartId());
                    RwDepart departBo = new RwDepart();
                    departBo.setId(departVo.getId());
                    departBo.setTotalSpuGrossWeight(departVo.getTotalSpuGrossWeight().add(repairSpuGrossWeight));
                    log.keyword("repairEntruckDiffQuantity").info("修改发车单：id={}, totalSpuGrossWeight={}, 原totalSpuGrossWeight={}, ",
                            departBo.getId(), departBo.getTotalSpuGrossWeight(), departVo.getTotalSpuGrossWeight());
                    departService.update(departBo);
                }
            }
            entruckVo.setIsAffiliated(recordVo.getIsAffiliated());
        }
//        // 基采、产地：调用云仓出库单
//        if (recordVo.getBusinessType().intValue() == OrderBusinessTypeEnum.BUSINESS_TYPE20.getCode()
//                || recordVo.getBusinessType().intValue() == OrderBusinessTypeEnum.BUSINESS_TYPE30.getCode()) {
//            goodsVo.setEntruckQuantity(repairQuantity);
//            String pickingNo = StrUtil.isNotBlank(recordVo.getPickingNo()) ? recordVo.getPickingNo() : "FH";
//            this.createCwStock(recordVo.getRegionWhId(),recordVo.getSaleDate(), pickingNo + "-QHZF", ListUtil.toList(goodsVo));
//        }
        return entruckVo;
    }

    /**
     * 创建缺货单
     */
    public void createStockout(List<BatchCreateStockoutBO> stockoutBOList) {
        stockoutSkuRecordService.insterBatchByBo(stockoutBOList);
        Map<Long, List<BatchCreateStockoutBO>> collect = stockoutBOList.stream().collect(Collectors.groupingBy(BatchCreateStockoutBO::getLogisticsId));
        collect.forEach((k, v) -> createStockoutProducer.send(v));
    }

    /**
     * 创建出库单 - 基采、产地 商品
     */
    public void createCwStock(Long regionWhId,LocalDate saleDate, String pickingNo, List<RwEntruckGoodsVo> goodsVos) {
        // 调用云仓出库单
        Map<Long, Long> skuIdMap = customToolService.getSkuIdMap(goodsVos.stream().map(RwEntruckGoodsVo::getSupplierSkuId).toList());

        RemoteCwTransHeadDTO cwBo = new RemoteCwTransHeadDTO();
        cwBo.setRegionWhId(regionWhId);
        cwBo.setIoFlag(TransTypeIoFlagEnum.STATUS1.getCode());
        cwBo.setTransDate(new Date());
        cwBo.setTransCode(TransTypeCodeEnum.FJCK.getCode());
        cwBo.setRemark("打印拣货单出库");
        cwBo.setSourceType(TransSourceTypeEnum.STATUS30.getCode().longValue());
        cwBo.setSourceId(pickingNo);

        List<RemoteCwTransDetailDTO> skuList = goodsVos.stream().filter(f -> f.getDeliveryQuantity() > 0).map(e -> {
            RemoteCwTransDetailDTO sku = new RemoteCwTransDetailDTO();
            BeanUtils.copyProperties(cwBo, sku);
            sku.setSourceDetailId(e.getId().toString());
            sku.setSkuId(skuIdMap.get(e.getSupplierSkuId()));
            sku.setIoQty(BigDecimal.valueOf(e.getDeliveryQuantity()));
            sku.setSaleDate(saleDate);
            return sku;
        }).collect(Collectors.toList());

        if (skuList.size() == 0) {
            return;
        }
        cwBo.setCwTransDetailDTOList(skuList);
        remoteCwStockService.insertByBo(cwBo);
    }
}
