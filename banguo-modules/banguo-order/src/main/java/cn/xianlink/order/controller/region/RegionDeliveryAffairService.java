package cn.xianlink.order.controller.region;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.basic.api.RemoteCityWhService;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.vo.RemoteCityWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhParkingVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.common.api.enums.order.*;
import cn.xianlink.common.api.enums.product.SupplierSkuSaleTypeEnum;
import cn.xianlink.common.api.util.RoleUtil;
import cn.xianlink.common.api.vo.RemoteSupplierSkuFileVo;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.json.utils.JsonUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.constant.OrderCacheNames;
import cn.xianlink.order.controller.CustomToolService;
import cn.xianlink.order.controller.sup.SupDeliveryAffairService;
import cn.xianlink.order.domain.RwEntruckGoodsOrder;
import cn.xianlink.order.domain.RwEntruckRecord;
import cn.xianlink.order.domain.bo.stockOut.BatchCreateStockoutBO;
import cn.xianlink.order.domain.delivery.bo.*;
import cn.xianlink.order.domain.delivery.vo.RwWaitDeliveryVo;
import cn.xianlink.order.domain.delivery.vo.SupDeliveryGoodsVo;
import cn.xianlink.order.domain.delivery.vo.SupDeliveryVo;
import cn.xianlink.order.domain.entruck.bo.*;
import cn.xianlink.order.domain.entruck.vo.*;
import cn.xianlink.order.domain.order.bo.QueryDeliverBo;
import cn.xianlink.order.domain.order.vo.*;
import cn.xianlink.order.domain.vo.stockOut.StockoutSkuRecordVO;
import cn.xianlink.order.service.*;
import cn.xianlink.order.util.CustomNoUtil;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuStockVo;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.model.LoginUser;
import com.baomidou.lock.annotation.Lock4j;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
@CustomLog
public class RegionDeliveryAffairService {
    private final transient ISupDeliveryService deliveryService;
    private final transient ISupDeliveryGoodsService deliveryGoodsService;
    private final transient IRwEntruckRecordService entruckRecordService;
    private final transient IRwEntruckGoodsService entruckGoodsService;
    private final transient IOrderService orderService;
    private final transient CustomToolService customToolService;
    private final transient IStockoutSkuRecordService stockoutSkuRecordService;
    private final ISortGoodsService sortGoodsService;
    private final transient RegionEntruckAffairService regionEntruckAffairService;
    private final transient SupDeliveryAffairService supDeliveryAffairService;

    @DubboReference
    private final transient RemoteSupplierService remoteSupplierService;
    @DubboReference
    private final transient RemoteDeptService remoteDeptService;
    @DubboReference
    private final transient RemoteCityWhService remoteCityWhService;
    @DubboReference
    private final transient RemoteRegionWhService remoteRegionWhService;
    @DubboReference
    private final transient RemoteSupplierSkuService remoteSupplierSkuService;

    /**
     * 查询-待送货列表
     *
     * @param bo
     * @return
     */
    public List<RwWaitDeliveryVo> waitDeliveryList(RwNotEntruckQueryBo bo) {
        // 查询总仓物流线
        LinkedHashMap<Long, RemoteRegionWhParkingVo> logisticsVoMap = customToolService.getDeliveryLogistics(bo.getRegionWhId(), true);
        if (logisticsVoMap.size() == 0) {
            return new ArrayList<>();
        }
        List<Long> logisticsIdList = logisticsVoMap.keySet().stream().toList();
        String buyerCode = bo.getOwn() ? LoginHelper.getLoginUser().getUserCode() : null;
        Map<Long, Integer> logisticsIsEarlyEndMap = customToolService.queryLogisticsIsEarlyEndMap(logisticsIdList, bo.getSaleDate());
        // 查询条件  提前发车
        if (YNStatusEnum.ENABLE.getCode().equals(bo.getIsEarlyEnd())) {
            logisticsIdList = logisticsIsEarlyEndMap.entrySet().stream().filter(entry -> YNStatusEnum.ENABLE.getCode().equals(entry.getValue())).map(Map.Entry::getKey).collect(Collectors.toList());
        }

        // 查询订单商品项
        QueryDeliverBo orderBo = new QueryDeliverBo();
        orderBo.setSource(OrderDeliverSourceEnum.REGION.getCode());
        orderBo.setSaleDate(bo.getSaleDate());
        orderBo.setRegionWhId(bo.getRegionWhId());
        orderBo.setSupplierId(bo.getSupplierId());
        orderBo.setLogisticsIdList(logisticsIdList);
        orderBo.setBuyerCodeList(buyerCode != null ? ListUtil.toList(buyerCode) : null);
        orderBo.setNotBusinessTypes(ListUtil.toList(OrderBusinessTypeEnum.BUSINESS_TYPE40.getCode()));
        List<QueryRegionDeliverVo> supplierOrderVos = orderService.queryRegionDeliver(orderBo);
        if (CollectionUtil.isEmpty(supplierOrderVos)) {
            return new ArrayList<>();
        }

        // 供应商送货中、已装车、差异补送
        SupWaitDeliveryQueryBo deliveryQueryBo = new SupWaitDeliveryQueryBo();
        deliveryQueryBo.setSaleDate(bo.getSaleDate());
        deliveryQueryBo.setRegionWhId(bo.getRegionWhId());
        deliveryQueryBo.setSupplierId(bo.getSupplierId());
        deliveryQueryBo.setBuyerCode(buyerCode);
        deliveryQueryBo.setLogisticsIdList(orderBo.getLogisticsIdList());
        Map<String, RwEntruckGoodsSumVo> entruckListMap = entruckGoodsService.customSupplierSumListByRegionWhId(deliveryQueryBo)
                .stream().collect(Collectors.toMap(RwEntruckGoodsSumVo::getSupplierIdKey, Function.identity(), (key1, key2) -> key2));
        // 不补送、不采购：生成缺货单
        List<Long> buyerSkuIds = StrUtil.isNotBlank(buyerCode) ? customToolService.getSkuIdList(bo.getSaleDate(), bo.getRegionWhId(), buyerCode) : null;
        Map<String, Integer> stockoutListMap = stockoutSkuRecordService.queryStockoutSkuCountByRegionWhId(bo.getSaleDate(), bo.getRegionWhId(), bo.getSupplierId(), buyerSkuIds, orderBo.getLogisticsIdList())
                .stream().collect(Collectors.toMap(StockoutSkuRecordVO::getSkuSupplierIdKey, StockoutSkuRecordVO::getStockoutCount, (key1, key2) -> key2));
        // 供应商名称
        List<Long> supplierIds = supplierOrderVos.stream().map(QueryRegionDeliverVo::getSupplierId).distinct().toList();
        List<Long> supplierDeptIds = supplierOrderVos.stream().filter(f -> f.getSupplierDeptId() > 0).map(QueryRegionDeliverVo::getSupplierDeptId).distinct().toList();
        Map<Long, RemoteSupplierVo> supplierMap = remoteSupplierService.getSupplierByIds(supplierIds).stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
        Map<Long, String> deptNameMap = supplierDeptIds.size() > 0 ? remoteDeptService.getDeptNameMap(supplierDeptIds) : new HashMap<>();
        // 组装待送货列表
        List<RwWaitDeliveryVo> list = new ArrayList<>();
        supplierOrderVos.forEach(e -> {
            RwWaitDeliveryVo supVo = new RwWaitDeliveryVo();
            supVo.setSaleDate(bo.getSaleDate());
            supVo.setSupplierId(e.getSupplierId());
            supVo.setSupplierName(supplierMap.getOrDefault(e.getSupplierId(), new RemoteSupplierVo()).getName());
            supVo.setSupplierAlias(supplierMap.getOrDefault(e.getSupplierId(), new RemoteSupplierVo()).getAlias());
            supVo.setSupplierDeptId(e.getSupplierDeptId());
            supVo.setSupplierDeptName(deptNameMap.get(e.getSupplierDeptId()));
            supVo.setWaitDeliveryQuantity(e.getCount());
            RwEntruckGoodsSumVo sumVo = entruckListMap.get(e.getSupplierIdKey());
            if (sumVo != null) {
                supVo.setAlreadyDeliveryQuantity(sumVo.getDeliveryQuantity());
                supVo.setAlreadyEntruckQuantity(sumVo.getEntruckQuantity());
                supVo.setWaitDeliveryQuantity(supVo.getWaitDeliveryQuantity() - sumVo.totalQuantity());
            }
            Integer stockoutCount = stockoutListMap.get(e.getSupplierIdKey());
            if (stockoutCount != null) {
                supVo.setWaitDeliveryQuantity(supVo.getWaitDeliveryQuantity() - stockoutCount);
            }
            // 待送货数量大于0 显示
            if (supVo.getWaitDeliveryQuantity() > 0) {
                list.add(supVo);
            }
        });
        return list;
    }

    /**
     * 拣货装车列表（待装车、已装车）
     */
    public List<RwWaitEntruckLogisticsVo> waitEntruckLogisticsList(RwWaitEntruckQueryBo bo) {
        List<Long> supplierSkuIdList = CollUtil.isNotEmpty(bo.getSupplierSkuIds()) ? bo.getSupplierSkuIds() : new ArrayList<>();
        LoginUser user = LoginHelper.getLoginUser();
        //如果是管理员则查询所有
        Long basPortageTeamId = null;
        if (RoleUtil.isRegionAdmin(user.getRolePermission())) {
            if (ObjectUtil.isNotNull(bo.getBasPortageTeamId())) {
                basPortageTeamId = bo.getBasPortageTeamId();
            }
        } else {
            // 用户装卸队查询权限
            basPortageTeamId = customToolService.getBasPortageTeamIdAndCheck(bo.getRegionWhId(), user.getUserId());
            // 返回-用于创建拣货单
            bo.setBasPortageTeamId(basPortageTeamId);
        }
        if (ObjectUtil.isNotNull(basPortageTeamId)) {
            // 查询装卸队商品详情
            List<Long> buyerSkuIds = customToolService.getSkuIdList(bo.getSaleDate(), bo.getRegionWhId(), basPortageTeamId);
            if (buyerSkuIds.size() == 0) {
                return new ArrayList<>();
            }
            // 装车操作-校验skuid权限
            if (supplierSkuIdList.size() > 0) {
                Map<Long, Long> skuIdMap = buyerSkuIds.stream().collect(Collectors.toMap(key -> key, value -> value));
                bo.getSupplierSkuIds().forEach(skuId -> {
                    if (!skuIdMap.containsKey(skuId)) {
                        throw new ServiceException("所属装卸队无权限操作该商品装车" + skuId);
                    }
                });
            } else {
                // 按权限查
                supplierSkuIdList.addAll(buyerSkuIds);
            }
        }
        // 查询总仓物流线
        LinkedHashMap<Long, RemoteRegionWhParkingVo> lmap = customToolService.getDeliveryLogistics(bo.getRegionWhId(), true);
        if (lmap.size() == 0) {
            return new ArrayList<>();
        }
        LinkedHashMap<Long, RemoteRegionWhParkingVo> logisticsVoMap = new LinkedHashMap<>();
        if (ObjectUtil.isNull(bo.getLogisticsId())) {
            logisticsVoMap.putAll(lmap);
        } else if (lmap.containsKey(bo.getLogisticsId())) {
            logisticsVoMap.put(bo.getLogisticsId(), lmap.get(bo.getLogisticsId()));
        } else {
            return new ArrayList<>();
        }
        List<Integer> businessTypes = ListUtil.toList(OrderBusinessTypeEnum.BUSINESS_TYPE20.getCode(), OrderBusinessTypeEnum.BUSINESS_TYPE30.getCode());
        // 查询订单商品项
        QueryDeliverBo orderBo = new QueryDeliverBo();
        orderBo.setSource(OrderDeliverSourceEnum.REGION.getCode());
        orderBo.setPassAudit(1);
        orderBo.setBusinessTypes(businessTypes);
        orderBo.setSaleDate(bo.getSaleDate());
        orderBo.setRegionWhId(bo.getRegionWhId());
        orderBo.setLogisticsIdList(logisticsVoMap.keySet().stream().toList());
        orderBo.setSupplierSkuId(supplierSkuIdList);
        if (ObjectUtil.isNotNull(bo.getBusinessType())) {
            orderBo.setBusinessTypes(ListUtil.toList(bo.getBusinessType()));
            if (bo.getBusinessType().intValue() == OrderBusinessTypeEnum.BUSINESS_TYPE30.getCode()) {
                if (CollUtil.isNotEmpty(bo.getBuyerCodeList())) {
                    orderBo.setBuyerCodeList(bo.getBuyerCodeList());
                }
            } else {
                orderBo.setProvideRegionWhIdList(bo.getProvideRegionWhIdList());
            }
        }
        List<QueryDeliverVo> orderItemVos = orderService.queryDeliver(orderBo);
        if (CollectionUtil.isEmpty(orderItemVos)) {
            return new ArrayList<>();
        }
        List<Long> supplierSkuIds = orderItemVos.stream().map(QueryDeliverVo::getSupplierSkuId).distinct().collect(Collectors.toList());
        // 供应商送货中、已装车、差异补送
        SupWaitDeliveryQueryBo deliveryBo = new SupWaitDeliveryQueryBo();
        deliveryBo.setSaleDate(bo.getSaleDate());
        deliveryBo.setRegionWhId(bo.getRegionWhId());
        deliveryBo.setBusinessTypes(businessTypes);
        deliveryBo.setSupplierSkuIds(supplierSkuIds);
        deliveryBo.setLogisticsIdList(orderBo.getLogisticsIdList());
        Map<String, RwEntruckGoodsSumVo> entruckListMap = entruckGoodsService.customLogisticsSumListBySupplierId(deliveryBo)
                .stream().collect(Collectors.toMap(RwEntruckGoodsSumVo::getLogisticsIdAndSupplierSkuIdKey, Function.identity()));
        // 不补送、不采购：生成缺货单
        Map<String, Integer> stockoutListMap = stockoutSkuRecordService.queryStockoutSkuCountByLogisticsId(bo.getSaleDate(), bo.getRegionWhId(), null, orderBo.getLogisticsIdList())
                .stream().collect(Collectors.toMap(StockoutSkuRecordVO::getLogisticsIdAndSupplierSkuIdKey, StockoutSkuRecordVO::getStockoutCount));
        // 商品库存信息  && 商品送货中-用于有效库存计算
        Map<Long, BigDecimal> skuStockMap = remoteSupplierSkuService.queryYunStock(supplierSkuIds)
                .stream().collect(Collectors.toMap(RemoteSupplierSkuStockVo::getSupplierSkuId, RemoteSupplierSkuStockVo::getOnhandQty));
        // 新需求拣货单打印及出库，所以此处无需再计算库存占用问题（送货未装车）
//        deliveryBo.setLogisticsIdList(null);
//        Map<Long, Integer> deliveryQtyMap = entruckGoodsService.customSkuSumListBySupplierId(deliveryBo)
//                .stream().collect(Collectors.toMap(RwEntruckGoodsSumVo::getSupplierSkuId, RwEntruckGoodsSumVo::getDeliveryQuantity));
//        deliveryQtyMap.forEach((skuId, deliveryQty) -> {
//            if (skuStockMap.containsKey(skuId)) {
//                BigDecimal stock = NumberUtil.sub(skuStockMap.get(skuId), deliveryQty);
//                log.keyword("stcokSku",skuId).info(String.format("sku：%s,云仓库存:%s,操作库存:%s",skuId, skuStockMap.get(skuId), deliveryQty));
//                skuStockMap.put(skuId, stock.intValue() > 0 ? stock : BigDecimal.ZERO);
//            }
//        });
        // 城市仓名称、供应商名称、商品文件列表
        Map<Long, Long> cityWhIdMap = logisticsVoMap.values().stream().collect(Collectors.toMap(RemoteRegionWhParkingVo::getCityWhId, RemoteRegionWhParkingVo::getCityWhId, (key1, key2) -> key2));
        Map<Long, String> cityWhNameMap = remoteCityWhService.queryList(cityWhIdMap.keySet().stream().toList())
                .stream().collect(Collectors.toMap(RemoteCityWhVo::getId, RemoteCityWhVo::getName));
        Map<Long, Long> supplierIdMap = orderItemVos.stream().collect(Collectors.toMap(QueryDeliverVo::getSupplierId, QueryDeliverVo::getSupplierId, (key1, key2) -> key2));
        Map<Long, RemoteSupplierVo> supplierMap = remoteSupplierService.getSupplierByIds(supplierIdMap.keySet().stream().toList())
                .stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
        Map<Long, List<RemoteSupplierSkuFileVo>> fileListMap = customToolService.getSkuFileListMap(supplierSkuIds);
        // 装卸队
        Map<Long, Long> portageTeaIdMap = orderItemVos.stream().collect(Collectors.toMap(QueryDeliverVo::getBasPortageTeamId, QueryDeliverVo::getBasPortageTeamId, (key1, key2) -> key2));
        Map<Long, String> teamNameMap = customToolService.getTeamNameMap(portageTeaIdMap.keySet().stream().toList());
        // 按商品Logisticid分组
        Map<Long, List<QueryDeliverVo>> orderLogisticListMap = orderItemVos.stream().collect(Collectors.groupingBy(QueryDeliverVo::getLogisticsId));
        // 总仓数据
        Map<Long, String> regionWhNameMap = remoteRegionWhService.queryList().stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, RemoteRegionWhVo::getRegionWhName));
        // 组装待分拣列表
        List<RwWaitEntruckLogisticsVo> list = new ArrayList<>();
        orderLogisticListMap.forEach((logisticId, skuList) -> {
            List<RwWaitEntruckGoodsVo> goodsList = new ArrayList<>();
            skuList.forEach(e -> {
                RwWaitEntruckGoodsVo goodsVo = new RwWaitEntruckGoodsVo();
                goodsVo.setDiffQuantity(stockoutListMap.getOrDefault(e.getLogisticsIdAndSupplierSkuIdKey(), 0));
                goodsVo.setWaitEntruckQuantity(e.getCount() - goodsVo.getDiffQuantity());
                goodsVo.setStockQuantity(skuStockMap.getOrDefault(e.getSupplierSkuId(), BigDecimal.ZERO).intValue());
                RwEntruckGoodsSumVo sumVo = entruckListMap.get(e.getLogisticsIdAndSupplierSkuIdKey());
                if (sumVo != null) {
                    goodsVo.setAlreadyDeliveryQuantity(sumVo.getDeliveryQuantity());
                    goodsVo.setAlreadyEntruckQuantity(sumVo.getEntruckQuantity());
                    goodsVo.setWaitEntruckQuantity(goodsVo.getWaitEntruckQuantity() - sumVo.totalQuantity());
                }
                // 待拣货 = 0，不返回
                if (goodsVo.getWaitEntruckQuantity() <= 0) {
                    return;
                }
                goodsVo.setSkuQuantity(e.getCount());
                goodsVo.setImgUrl(e.getImgUrl());
                // 列表页不加载商品详情
                if (!bo.getIsListViewQuery()) {
                    goodsVo.setSaleDate(bo.getSaleDate());
                    goodsVo.setSupplierSkuId(e.getSupplierSkuId());
                    goodsVo.setSupplierId(e.getSupplierId());
                    if (supplierMap.containsKey(e.getSupplierId())) {
                        goodsVo.setSupplierName(supplierMap.get(e.getSupplierId()).getName());
                        goodsVo.setSupplierAlias(supplierMap.get(e.getSupplierId()).getAlias());
                    }
                    goodsVo.setSupplierDeptId(e.getSupplierDeptId());
                    goodsVo.setSupplierDeptName(e.getSupplierDeptName());
                    goodsVo.setIsExemptInspect(e.getIsCheck());
                    goodsVo.setBatchType(e.getBatchType());
                    goodsVo.setBatchPrice(e.getPrice());
                    goodsVo.setBuyerCode(e.getBuyerCode());
                    goodsVo.setBuyerName(e.getBuyerName());
                    goodsVo.setProvideRegionWhId(e.getProvideRegionWhId());
                    goodsVo.setProvideRegionWhName(regionWhNameMap.get(e.getProvideRegionWhId()));
                    goodsVo.setSpuName(e.getSpuName());
                    goodsVo.setSpuGrossWeight(e.getSpuGrossWeight());
                    goodsVo.setSpuNetWeight(e.getSpuNetWeight());
                    goodsVo.setFreightRatio(e.getFreightRatio());
                    goodsVo.setSpuGrade(e.getSpuGrade());
                    goodsVo.setSpuStandards(e.getSpuStandards());
                    goodsVo.setProducer(e.getProducer());
                    goodsVo.setPackageWord(e.getPackageWord());
                    goodsVo.setBasPortageTeamName(teamNameMap.get(e.getBasPortageTeamId()));
                    goodsVo.setFileList(fileListMap.get(e.getSupplierSkuId()));
                    goodsVo.setBusinessType(e.getBusinessType());
                    // 产地简称
                    goodsVo.setAreaCode(e.getAreaCode());
                    goodsVo.setBrand(e.getBrand());
                    goodsVo.setShortProducer(e.getShortProducer());
                }
                goodsList.add(goodsVo);
            });
            // 无待拣货商品，不返回
            if (goodsList.size() == 0) {
                return;
            }

            RwWaitEntruckLogisticsVo logisticsVo = new RwWaitEntruckLogisticsVo();
            logisticsVo.setSaleDate(bo.getSaleDate());

            logisticsVo.setRegionWhId(bo.getRegionWhId());
            logisticsVo.setLogisticsId(logisticId);
            logisticsVo.setLogisticsName(logisticsVoMap.get(logisticId).getLogisticsName());
            logisticsVo.setCityWhId(skuList.get(0).getCityWhId());
            logisticsVo.setCityWhName(cityWhNameMap.get(logisticsVo.getCityWhId()));
            logisticsVo.setParkingNo(logisticsVoMap.get(logisticId).getParkingNo());
            logisticsVo.setIsAffiliated(logisticsVoMap.get(logisticId).getIsAffiliated());
            logisticsVo.setSkuQuantity(goodsList.stream().mapToInt(RwWaitEntruckGoodsVo::getSkuQuantity).sum());
            logisticsVo.setAlreadyDeliveryQuantity(goodsList.stream().mapToInt(RwWaitEntruckGoodsVo::getAlreadyDeliveryQuantity).sum());
            logisticsVo.setAlreadyEntruckQuantity(goodsList.stream().mapToInt(RwWaitEntruckGoodsVo::getAlreadyEntruckQuantity).sum());
            logisticsVo.setDiffQuantity(goodsList.stream().mapToInt(RwWaitEntruckGoodsVo::getDiffQuantity).sum());
            logisticsVo.setWaitEntruckQuantity(goodsList.stream().mapToInt(RwWaitEntruckGoodsVo::getWaitEntruckQuantity).sum());
            logisticsVo.setStockQuantity(goodsList.stream().mapToInt(RwWaitEntruckGoodsVo::getStockQuantity).sum());
            logisticsVo.setGoodsCount(goodsList.size());
            if (bo.getIsListViewQuery()) {
                logisticsVo.setGoodsList(goodsList.size() > 6 ? goodsList.subList(0, 6) : goodsList);
            } else {
                logisticsVo.setGoodsList(goodsList);
            }
            list.add(logisticsVo);
        });
//        list.sort(Comparator.comparingInt(RwWaitEntruckLogisticsVo::getPickingStatus));
        return list;
    }

    /**
     * 查询-暂不装车列表(物流线列表)【停用】
     *
     * @param bo
     * @return
     */
    public List<RwNotEntruckLogisticsVo> notEntruckLogisticsList(RwNotEntruckQueryBo bo) {
        // 查询总仓物流线
        LinkedHashMap<Long, RemoteRegionWhParkingVo> logisticsVoMap = customToolService.getDeliveryLogistics(bo.getRegionWhId(), false);
        if (logisticsVoMap.size() == 0) {
            return new ArrayList<>();
        }
        QueryDeliverBo queryBo = new QueryDeliverBo();
        queryBo.setSource(OrderDeliverSourceEnum.REGION.getCode());
        queryBo.setSaleDate(bo.getSaleDate());
        if (ObjectUtil.isNotNull(bo.getLogisticsId())) {
            if (logisticsVoMap.get(bo.getLogisticsId()) == null) {
                return new ArrayList<>();
            }
            queryBo.setLogisticsIdList(ListUtil.toList(bo.getLogisticsId()));
        } else {
            queryBo.setLogisticsIdList(logisticsVoMap.keySet().stream().toList());
        }
        queryBo.setNotBusinessTypes(ListUtil.toList(OrderBusinessTypeEnum.BUSINESS_TYPE40.getCode()));
        List<QueryDeliverLogisticsVo> logisticsItemVos = orderService.queryDeliverLogistics(queryBo);
        if (CollectionUtil.isEmpty(logisticsItemVos)) {
            return new ArrayList<>();
        }
        Map<Long, String> logisticsNameMap = logisticsVoMap.values().stream().collect(Collectors.toMap(RemoteRegionWhParkingVo::getLogisticsId, RemoteRegionWhParkingVo::getLogisticsName, (key1, key2) -> key2));
        Map<Long, String> cityWhNameMap = customToolService.queryCityNameMap(logisticsVoMap.values().stream().map(RemoteRegionWhParkingVo::getCityWhId).collect(Collectors.toList()));
        return logisticsItemVos.stream().map(e -> {
            RwNotEntruckLogisticsVo vo = new RwNotEntruckLogisticsVo();
            BeanUtils.copyProperties(e, vo);
            vo.setSaleDate(bo.getSaleDate());
            vo.setLogisticsName(logisticsNameMap.get(e.getLogisticsId()));
            vo.setCityWhName(cityWhNameMap.get(e.getCityWhId()));
            vo.setSkuQuantity(e.getCount().intValue());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 查询-暂不装车列表(明细-供应商列表)【停用】
     *
     * @param bo
     * @return
     */
    public List<RwNotEntruckSupplierVo> notEntruckSupplierIdList(RwNotEntruckQueryBo bo) {
        // 查询物流线订单项汇总
        QueryDeliverBo queryBo = new QueryDeliverBo();
        queryBo.setSource(OrderDeliverSourceEnum.REGION.getCode());
        queryBo.setSaleDate(bo.getSaleDate());
        queryBo.setLogisticsIdList(CollectionUtil.toList(bo.getLogisticsId()));
        queryBo.setNotBusinessTypes(ListUtil.toList(OrderBusinessTypeEnum.BUSINESS_TYPE40.getCode()));
        List<QueryDeliverLogisticsItemVo> logisticsItemVos = orderService.queryDeliverLogisticsItem(queryBo);
        if (CollectionUtil.isEmpty(logisticsItemVos)) {
            return new ArrayList<>();
        }
        List<Long> supplierIds = logisticsItemVos.stream().map(QueryDeliverLogisticsItemVo::getSupplierId).distinct().toList();
        List<Long> supplierDeptIds = logisticsItemVos.stream().filter(f -> f.getSupplierDeptId() > 0).map(QueryDeliverLogisticsItemVo::getSupplierDeptId).distinct().toList();
        Map<Long, RemoteSupplierVo> supplierMap = remoteSupplierService.getSupplierByIds(supplierIds).stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
        Map<Long, String> deptNameMap = supplierDeptIds.size() > 0 ? remoteDeptService.getDeptNameMap(supplierDeptIds) : new HashMap<>();

        return logisticsItemVos.stream().map(e -> {
            RwNotEntruckSupplierVo vo = new RwNotEntruckSupplierVo();
            BeanUtils.copyProperties(e, vo);
            vo.setSupplierName(supplierMap.getOrDefault(vo.getSupplierId(), new RemoteSupplierVo()).getName());
            vo.setSupplierAlias(supplierMap.getOrDefault(vo.getSupplierId(), new RemoteSupplierVo()).getAlias());
            vo.setSupplierDeptName(deptNameMap.get(vo.getSupplierDeptId()));
            vo.setSkuCount(e.getSupplierSkuCount());
            vo.setSkuQuantity(e.getCount());
            vo.setIsShowNoPurchase(1);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 查询-暂不装车列表(物流线列表+供应商列表)
     *
     * @param bo
     * @return
     */
    public List<RwNotEntruckLogisticsVo> notEntruckList(RwNotEntruckQueryBo bo) {
        // 查询总仓物流线
        LinkedHashMap<Long, RemoteRegionWhParkingVo> logisticsVoMap = customToolService.getDeliveryLogistics(bo.getRegionWhId(), false);
        if (logisticsVoMap.size() == 0) {
            return new ArrayList<>();
        }
        QueryDeliverBo orderBo = new QueryDeliverBo();
        orderBo.setSource(OrderDeliverSourceEnum.REGION.getCode());
        orderBo.setSaleDate(bo.getSaleDate());
        orderBo.setRegionWhId(bo.getRegionWhId());
        if (ObjectUtil.isNotNull(bo.getLogisticsId())) {
            if (logisticsVoMap.get(bo.getLogisticsId()) == null) {
                return new ArrayList<>();
            }
            orderBo.setLogisticsIdList(ListUtil.toList(bo.getLogisticsId()));
        } else {
            orderBo.setLogisticsIdList(logisticsVoMap.keySet().stream().toList());
        }
        orderBo.setNotBusinessTypes(ListUtil.toList(OrderBusinessTypeEnum.BUSINESS_TYPE40.getCode()));
        List<QueryDeliverLogisticsItemVo> logisticsItemVos = orderService.queryDeliverLogisticsItem(orderBo);
        if (CollectionUtil.isEmpty(logisticsItemVos)) {
            return new ArrayList<>();
        }
        // 供应商送货中、已装车、差异补送
        SupWaitDeliveryQueryBo queryBo = BeanUtil.copyProperties(orderBo, SupWaitDeliveryQueryBo.class);
        Map<String, RwEntruckGoodsSumVo> entruckListMap = entruckGoodsService.customSupplierSumListByLogisticsId(queryBo)
                .stream().collect(Collectors.toMap(RwEntruckGoodsSumVo::getLogisticsIdAndSupplierDeptIdKey, Function.identity()));
        // 不补送、不采购：生成缺货单
        Map<String, Integer> stockoutListMap = stockoutSkuRecordService.queryStockoutSkuCountByLogisticsIdDeptId(bo.getSaleDate(), bo.getRegionWhId(), orderBo.getLogisticsIdList())
                .stream().collect(Collectors.toMap(StockoutSkuRecordVO::getLogisticsIdAndSupplierDeptIdKey, StockoutSkuRecordVO::getStockoutCount));
        // 计算待送货= 需求数-已装车-缺货记录
        logisticsItemVos.forEach(item ->
            item.setCount(NumberUtil.sub(item.getCount(),
                    entruckListMap.get(item.getLogisticsIdAndSupplierDeptIdKey()) != null ? entruckListMap.get(item.getLogisticsIdAndSupplierDeptIdKey()).totalQuantity() : 0,
                    stockoutListMap.getOrDefault(item.getLogisticsIdAndSupplierDeptIdKey(), 0)
            ).intValue())
        );
        List<Long> supplierIds = logisticsItemVos.stream().map(QueryDeliverLogisticsItemVo::getSupplierId).distinct().toList();
        List<Long> supplierDeptIds = logisticsItemVos.stream().filter(f -> f.getSupplierDeptId() > 0).map(QueryDeliverLogisticsItemVo::getSupplierDeptId).distinct().toList();
        Map<Long, RemoteSupplierVo> supplierMap = remoteSupplierService.getSupplierByIds(supplierIds).stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
        Map<Long, String> deptNameMap = supplierDeptIds.size() > 0 ? remoteDeptService.getDeptNameMap(supplierDeptIds) : new HashMap<>();
        Map<Long, String> logisticsNameMap = logisticsVoMap.values().stream().collect(Collectors.toMap(RemoteRegionWhParkingVo::getLogisticsId, RemoteRegionWhParkingVo::getLogisticsName, (key1, key2) -> key2));
        Map<Long, String> cityWhNameMap = customToolService.queryCityNameMap(logisticsVoMap.values().stream().map(RemoteRegionWhParkingVo::getCityWhId).collect(Collectors.toList()));

        Map<Long, List<QueryDeliverLogisticsItemVo>> listMap  = logisticsItemVos.stream().filter(f -> f.getCount() > 0).collect(Collectors.groupingBy(QueryDeliverLogisticsItemVo::getLogisticsId));
        return listMap.values().stream().map(list -> {
            QueryDeliverLogisticsItemVo logisticsVo = list.get(0);
            RwNotEntruckLogisticsVo vo = new RwNotEntruckLogisticsVo();
            BeanUtils.copyProperties(logisticsVo, vo);
            vo.setSaleDate(bo.getSaleDate());
            vo.setLogisticsName(logisticsNameMap.get(logisticsVo.getLogisticsId()));
            vo.setCityWhName(cityWhNameMap.get(logisticsVo.getCityWhId()));
            vo.setSupplierCount(list.stream().collect(Collectors.groupingBy(QueryDeliverLogisticsItemVo::getSupplierId)).size());
            vo.setSkuQuantity(list.stream().mapToInt(QueryDeliverLogisticsItemVo::getCount).sum());
            vo.setSupplierList(list.stream().map(e -> {
                RwNotEntruckSupplierVo supplierVo = new RwNotEntruckSupplierVo();
                BeanUtils.copyProperties(e, supplierVo);
                supplierVo.setSupplierName(supplierMap.getOrDefault(supplierVo.getSupplierId(), new RemoteSupplierVo()).getName());
                supplierVo.setSupplierAlias(supplierMap.getOrDefault(supplierVo.getSupplierId(), new RemoteSupplierVo()).getAlias());
                supplierVo.setSupplierDeptName(deptNameMap.get(supplierVo.getSupplierDeptId()));
                supplierVo.setSkuCount(e.getSupplierSkuCount());
                supplierVo.setSkuQuantity(e.getCount());
                supplierVo.setIsShowNoPurchase(1);
                return supplierVo;
            }).collect(Collectors.toList()));
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 二维码扫码校验（质检、装车、拣货、配货）
     *
     * @return 装车记录id 或 送货单id 或 拣货单号
     */
    public String scanCodeCheck(SupDeliveryScanCodeCheckBo bo) {
        // 拣货单
        if (bo.getScanType() == 4) {
            if (StrUtil.isBlank(bo.getDeliveryId())) {
                throw new ServiceException("请扫正确的拣货单二维码");
            }
            RwEntruckRecordGoodsSumVo vo;
            try {
                vo = entruckGoodsService.pickingInfo(bo.getDeliveryId(), false);
            } catch (ServiceException se) {
                throw new ServiceException("请扫正确的拣货单二维码");
            }
            if (vo.getRegionWhId().longValue() != bo.getRegionWhId()) {
                throw new ServiceException("当前总仓无操作权限");
            }
            LocalDate saleDate = customToolService.getLastSaleDate(vo.getRegionWhId(), null);
            if (saleDate.compareTo(vo.getSaleDate()) < 0) {
                throw new ServiceException("当前无法作业下个销售日期单据");
            }
            if (saleDate.compareTo(vo.getSaleDate()) > 0) {
                throw new ServiceException("二维码已过期");
            }
            return "0";
        }

        // 配货订单
        if (bo.getScanType() == 5) {
            String orderCode = "186A0" + bo.getDeliveryId();
            try {
                QueryDeliveryVo vo = orderService.getDeliveryInfoByCode(orderCode, false);
                if (vo.getRegionWhId().longValue() != bo.getRegionWhId()) {
                    throw new ServiceException("当前总仓无操作权限");
                }
                LocalDate saleDate = customToolService.getLastSaleDate(vo.getRegionWhId(), null);
                if (saleDate.compareTo(vo.getSaleDate()) > 0) {
                    throw new ServiceException("二维码已过期");
                }
            } catch (ServiceException se) {
                throw new ServiceException(se.getMessage().replace("订单号不存在","请扫正确的配货订单二维码"));
            }
            return orderCode;
        }

        // 质检、装车
        if (!NumberUtil.isLong(bo.getDeliveryId())) {
            throw new ServiceException("请扫正确的送货单二维码");
        }
        SupDeliveryVo vo;
        try {
            vo = deliveryService.selectAndCheckNullById(Long.parseLong(bo.getDeliveryId()));
        } catch (ServiceException se) {
            throw new ServiceException("请扫正确的送货单二维码");
        }
        if (bo.getScanType() != 3) {
            // 1扫码质检，2扫码装车
            if (vo.getRegionWhId().longValue() != bo.getRegionWhId()) {
                throw new ServiceException("当前总仓无操作权限");
            }
        }
        LocalDate saleDate = customToolService.getLastSaleDate(vo.getRegionWhId(), null);
        if (saleDate.compareTo(vo.getSaleDate()) < 0) {
            throw new ServiceException("当前无法作业下个销售日期单据");
        }
        if (saleDate.compareTo(vo.getSaleDate()) > 0) {
            throw new ServiceException("二维码已过期");
        }
        if (vo.getStatus().intValue() == DeliveryStatusEnum.CANCEL_DELIVERY.getCode()) {
            throw new ServiceException("送货单已取消");
        }
        // 2扫码装车 3扫码收货
        if (bo.getScanType() != 1) {
            if (vo.getStatus().intValue() == DeliveryStatusEnum.WAIT_INSPECT.getCode()) {
                throw new ServiceException("送货单未质检");
            }
            List<RwEntruckRecordVo> list = entruckRecordService.selectInfoListByDeliveryId(vo.getId());
            if (list.size() == 0) {
                throw new ServiceException("数据异常，无待装车记录");
            }
            // 2扫码装车
            if (bo.getScanType() == 2) {
                for (RwEntruckRecordVo recordVo : list) {
                    if (recordVo.getStatus().intValue() == DeliveryStatusEnum.COMPLETE_INSPECT.getCode()) {
                        // 装车记录id
                        return recordVo.getId().toString();
                    }
                }
                // 装车记录id
                return list.get(0).getId().toString();
            }
            // 3扫码收货
            if (bo.getScanType() == 3) {
                List<RwEntruckRecordVo> filterList = list.stream().filter(f -> f.getCityWhId().intValue() == bo.getCityWhId() && f.getIsAffiliated() == 1).collect(Collectors.toList());
                if (filterList.size() == 0) {
                    throw new ServiceException("当前城市仓无操作权限");
                }
                RwEntruckRecordVo recordVo = filterList.get(0);
                if (recordVo.getStatus().intValue() == DeliveryStatusEnum.COMPLETE_INSPECT.getCode()) {
                    throw new ServiceException("送货单未装车操作");
                }
                if (recordVo.getStatus().intValue() == DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode()) {
                    if (ObjectUtil.isNotNull(recordVo.getEntruckId())) {
                        throw new ServiceException("送货单已接货");
                    }
                    // 装车记录id
                    return recordVo.getId().toString();
                } else {
                    throw new ServiceException("送货单状态异常：" + recordVo.getStatus());
                }
            }
        }
        // 送货单id
        return vo.getId().toString();
    }

    /**
     * 完成质检，修改状态
     */
    @Transactional(rollbackFor = Throwable.class)
    public void completeInspect(Long deliveryId) {
        SupDeliveryVo deliveryVo = deliveryService.selectAndCheckNullById(deliveryId);
        if (deliveryVo.getStatus().intValue() != DeliveryStatusEnum.WAIT_INSPECT.getCode()) {
            throw new ServiceException("送货单非待质检状态，不允许完成质检");
        }
        List<SupDeliveryGoodsVo> goodsVoList = deliveryGoodsService.queryListByDeliveryId(deliveryId);
        Map<Integer, List<SupDeliveryGoodsVo>> statusListMap = goodsVoList.stream().collect(Collectors.groupingBy(SupDeliveryGoodsVo::getStatus));
        // 状态；10待质检，21合格，22不合格
        if (CollUtil.isNotEmpty(statusListMap.get(DeliveryStatusEnum.WAIT_INSPECT.getCode()))) {
            throw new ServiceException("部分商品未质检，请继续质检");
        }
        List<SupDeliveryGoodsVo> status22List = statusListMap.get(DeliveryStatusEnum.INSPECT_UNQUALIFIED.getCode());
        if (CollUtil.isEmpty(status22List)) {
            deliveryService.updateStatus(deliveryId, DeliveryStatusEnum.COMPLETE_INSPECT.getCode());
            entruckRecordService.updateStatusByDeliveryId(deliveryId, DeliveryStatusEnum.COMPLETE_INSPECT.getCode());
        } else {
            if(status22List.size() == goodsVoList.size()) {
                SupDeliveryCancelBo bo = new SupDeliveryCancelBo();
                bo.setId(deliveryId);
                bo.setCancelReason("全部质检不合格");
                deliveryService.cancelDelivery(bo);
                entruckRecordService.updateStatusByDeliveryId(bo.getId(), DeliveryStatusEnum.CANCEL_DELIVERY.getCode());
            } else {
                Map<Long, Long> skuIdMap = status22List.stream().collect(Collectors.toMap(SupDeliveryGoodsVo::getSupplierSkuId, SupDeliveryGoodsVo::getSupplierSkuId));
                // 根据不合格商品，标记装车记录商品差异状态
                List<RwEntruckRecordVo> recordVos = entruckRecordService.selectListByDeliveryId(deliveryId, null);
                recordVos.forEach(recordVo -> {
                    recordVo.getGoodsList().forEach(goodsVo -> {
                        if (skuIdMap.containsKey(goodsVo.getSupplierSkuId())) {
                            goodsVo.setDiffStatus(DeliveryStatusEnum.DIFF_DELIVERY.getCode());
                        }
                    });
                });
                // 根据标记差异状态 处理数据
                List<RwEntruckRecord> completeRecordList = new ArrayList<>();
                List<Long> cancelRecordIds = new ArrayList<>();
                List<Long> diffGoodsIds = new ArrayList<>();
                for (RwEntruckRecordVo recordVo : recordVos) {
                    List<RwEntruckGoodsVo> diffGoodsList = recordVo.getGoodsList().stream().filter(f -> f.getDiffStatus().intValue() == DeliveryStatusEnum.DIFF_DELIVERY.getCode()).collect(Collectors.toList());
                    if (diffGoodsList.size() == recordVo.getGoodsList().size()) {
                        cancelRecordIds.add(recordVo.getId());
                    } else {
                        RwEntruckRecord entity = new RwEntruckRecord();
                        entity.setId(recordVo.getId());
                        entity.setStatus(DeliveryStatusEnum.COMPLETE_INSPECT.getCode());
                        if (diffGoodsList.size() > 0) {
                            entity.setTotalDeliveryQuantity(recordVo.getTotalDeliveryQuantity() - diffGoodsList.stream().mapToInt(RwEntruckGoodsVo::getDeliveryQuantity).sum());
                            diffGoodsIds.addAll(diffGoodsList.stream().map(RwEntruckGoodsVo::getId).collect(Collectors.toList()));
                        }
                        completeRecordList.add(entity);
                    }
                }
                deliveryService.updateStatus(deliveryId, DeliveryStatusEnum.COMPLETE_INSPECT.getCode());
                if (completeRecordList.size() > 0) {
                    entruckRecordService.updateById(completeRecordList);
                    if (diffGoodsIds.size() > 0) {
                        entruckGoodsService.deleteByIds(diffGoodsIds);
                    }
                }
                if (cancelRecordIds.size() > 0) {
                    entruckRecordService.deleteByIds(cancelRecordIds);
                }
            }
        }
        // 直属城市仓物流 自动装车完成，自动接货
//        this.affiliatedAutoEntruckAndReceive(deliveryId);
    }

    /**
     * 修改质检
     * 限制：已质检无装车记录
     */
    @Transactional(rollbackFor = Throwable.class)
    public void updateInspect(SupDeliveryGoodsEditBo goodsBo) {
        SupDeliveryVo deliveryVo = deliveryService.selectAndCheckNullById(goodsBo.getDeliveryId());
        if (deliveryVo.getStatus().intValue() != DeliveryStatusEnum.COMPLETE_INSPECT.getCode()) {
            throw new ServiceException("送货单非已待质检状态，不允许修改质检");
        }
        if (entruckGoodsService.isExistEntruckQuantity(goodsBo.getDeliveryId())) {
            throw new ServiceException("部分商品已有装车记录，不允许修改质检");
        }
        SupDeliveryGoodsVo goodsVo = deliveryGoodsService.queryById(goodsBo.getId());
        if (goodsVo.getStatus().intValue() == DeliveryStatusEnum.INSPECT_UNQUALIFIED.getCode()) {
            throw new ServiceException("商品状态已是不合格，不支持变更");
        }
        if (goodsBo.getStatus().intValue() == DeliveryStatusEnum.INSPECT_QUALIFIED.getCode()) {
            throw new ServiceException("仅允许商品合格状态修改为不合格");
        }
        customToolService.inspectOssSave(goodsBo);
        goodsBo.setReturnQuantity(goodsVo.getDeliveryQuantity());
        goodsBo.setActualQuantity(0);
        goodsBo.setInspectCount(goodsVo.getInspectCount() + 1);
        deliveryGoodsService.updateInspect(goodsBo);

        if (!deliveryGoodsService.isExistWaitInspectRecord(goodsBo.getDeliveryId(), DeliveryStatusEnum.INSPECT_QUALIFIED.getCode())) {
            SupDeliveryCancelBo bo = new SupDeliveryCancelBo();
            bo.setId(goodsBo.getDeliveryId());
            bo.setCancelReason("全部质检不合格");
            deliveryService.cancelDelivery(bo);
            entruckRecordService.updateStatusByDeliveryId(bo.getId(), DeliveryStatusEnum.CANCEL_DELIVERY.getCode());
        } else {
            // 根据不合格商品，标记装车记录商品差异状态
            List<RwEntruckRecordVo> recordVos = entruckRecordService.selectListByDeliveryId(goodsBo.getDeliveryId(), null);
            recordVos.forEach(recordVo -> {
                recordVo.getGoodsList().forEach(goods -> {
                    if (goods.getSupplierSkuId().equals(goodsVo.getSupplierSkuId())) {
                        goods.setDiffStatus(DeliveryStatusEnum.DIFF_DELIVERY.getCode());
                    }
                });
            });
            // 根据标记差异状态 处理数据
            List<RwEntruckRecord> updateRecordList = new ArrayList<>();
            List<Long> cancelRecordIds = new ArrayList<>();
            List<Long> diffGoodsIds = new ArrayList<>();
            for (RwEntruckRecordVo recordVo : recordVos) {
                List<RwEntruckGoodsVo> diffGoodsList = recordVo.getGoodsList().stream().filter(f -> f.getDiffStatus().intValue() == DeliveryStatusEnum.DIFF_DELIVERY.getCode()).collect(Collectors.toList());
                if (diffGoodsList.size() == 0) {
                    continue;
                }
                if (diffGoodsList.size() == recordVo.getGoodsList().size()) {
                    cancelRecordIds.add(recordVo.getId());
                } else {
                    RwEntruckRecord entity = new RwEntruckRecord();
                    entity.setId(recordVo.getId());
                    entity.setTotalDeliveryQuantity(recordVo.getTotalDeliveryQuantity() - diffGoodsList.stream().mapToInt(RwEntruckGoodsVo::getDeliveryQuantity).sum());
                    updateRecordList.add(entity);
                    diffGoodsIds.addAll(diffGoodsList.stream().map(RwEntruckGoodsVo::getId).collect(Collectors.toList()));
                }
            }
            if (updateRecordList.size() > 0) {
                entruckRecordService.updateById(updateRecordList);
                entruckGoodsService.deleteByIds(diffGoodsIds);
            }
            if (cancelRecordIds.size() > 0) {
                entruckRecordService.deleteByIds(cancelRecordIds);
            }
        }
    }

    /**
     * 直属城市仓物流 自动装车完成，自动接货
     */
    public void affiliatedAutoEntruckAndReceive(Long deliveryId) {
        List<RwEntruckRecordVo> recordVos = entruckRecordService.selectListByDeliveryId(deliveryId, YNStatusEnum.ENABLE.getCode());
        if (recordVos.size() == 0) {
            return;
        }
        Map<Long, Integer> actualQuantityMap = deliveryGoodsService.queryListByDeliveryId(deliveryId)
                .stream().collect(Collectors.toMap(SupDeliveryGoodsVo::getSupplierSkuId, SupDeliveryGoodsVo::getActualQuantity, (key1, key2) -> key2));

        List<RwEntruckRecordEditBo> recordBoList = recordVos.stream().map(recordVo -> {
            RwEntruckRecordEditBo recordBo = new RwEntruckRecordEditBo();
            recordBo.setId(recordVo.getId());
            recordBo.setDeliveryId(recordVo.getDeliveryId());
            recordBo.setBoardQuantity(0);
            recordBo.setGoodsList(recordVo.getGoodsList().stream().map(goodsVo -> {
                RwEntruckGoodsEditBo goodsBo = new RwEntruckGoodsEditBo();
                goodsBo.setId(goodsVo.getId());
                // 剩余合格数量 = 质检合格数 - 应装车数量
                int surplusQuantity = NumberUtil.sub(actualQuantityMap.get(goodsVo.getSupplierSkuId()), goodsVo.getDeliveryQuantity()).intValue();
                if (surplusQuantity >= 0) {
                    actualQuantityMap.put(goodsVo.getSupplierSkuId(), surplusQuantity);
                    goodsBo.setEntruckQuantity(goodsVo.getDeliveryQuantity());
                } else {
                    actualQuantityMap.put(goodsVo.getSupplierSkuId(), 0);
                    goodsBo.setEntruckQuantity(actualQuantityMap.get(goodsVo.getSupplierSkuId()));
                }
                return goodsBo;
            }).collect(Collectors.toList()));
            return recordBo;
        }).collect(Collectors.toList());

        for (RwEntruckRecordEditBo recordBo : recordBoList) {
            regionEntruckAffairService.operateEntruck(recordBo, true);
        }
    }

    /**
     * 撤销质检
     */
    @Transactional(rollbackFor = Throwable.class)
    public void revokeInspect(Long deliveryId) {
        SupDeliveryVo deliveryVo = deliveryService.selectAndCheckNullById(deliveryId);
        if (deliveryVo.getStatus().intValue() != DeliveryStatusEnum.COMPLETE_INSPECT.getCode()) {
            throw new ServiceException("送货单非已待质检状态，不允许撤销质检");
        }
        if (entruckRecordService.isExistEntruckRecordStatus(deliveryId, DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode())) {
            throw new ServiceException("部分商品已装车，请先撤销装车操作");
        }
        if (deliveryService.updateStatus(deliveryId, DeliveryStatusEnum.WAIT_INSPECT.getCode()) <= 0) {
            throw new ServiceException("质检撤销失败1");
        }
        if (deliveryGoodsService.revokeInspect(deliveryId) <= 0) {
            throw new ServiceException("质检撤销失败2");
        }
        if (entruckRecordService.updateStatusByDeliveryId(deliveryId, DeliveryStatusEnum.WAIT_INSPECT.getCode()) <= 0) {
            throw new ServiceException("质检撤销失败3");
        }
    }

    /**
     * 待送货商品-不采购
     */
    @Transactional(rollbackFor = Throwable.class)
    public void waitDeliveryNoPurchase(SupWaitDeliveryNoPurchaseBo bo) {
        SupDeliveryGoodsAddBo goodsAddBo = new SupDeliveryGoodsAddBo();
        goodsAddBo.setSupplierSkuId(bo.getSupplierSkuId());
        goodsAddBo.setDeliveryQuantity(bo.getNoPurchaseQuantity());

        SupDeliveryAddBo addBo = new SupDeliveryAddBo();
        addBo.setSaleDate(bo.getSaleDate());
        addBo.setRegionWhId(bo.getRegionWhId());
        addBo.setSupplierId(bo.getSupplierId());
        addBo.setSupplierDeptId(remoteSupplierSkuService.getSimpleById(bo.getSupplierSkuId()).getSupplierDeptId());
        addBo.setGoodsList(ListUtil.toList(goodsAddBo));
        List<RwEntruckRecordAddBo> recordBos = regionEntruckAffairService.getEntruckRecordAddBoList(addBo, OrderDeliverSourceEnum.REGION.getCode(), bo.getIsDelivery());
        // 加载关联订单项数据
        regionEntruckAffairService.loadDeliveryOrderItemList(recordBos);
        this.waitDeliveryNoPurchase(recordBos, false);
    }

    @Transactional(rollbackFor = Throwable.class)
    public void waitDeliveryNoPurchase(List<RwEntruckRecordAddBo> recordBos, boolean systemAuto) {
        LoginUser user = LoginHelper.getLoginUser();
        List<BatchCreateStockoutBO> stockoutBOList = new ArrayList<>();
        for (RwEntruckRecordAddBo recordBo : recordBos) {
            BatchCreateStockoutBO stockout = new BatchCreateStockoutBO();
            stockout.setSaleDate(recordBo.getSaleDate());
            stockout.setRegionWhId(recordBo.getRegionWhId());
            stockout.setSkuSupplierId(recordBo.getSupplierId());
            stockout.setSkuSupplierDeptId(recordBo.getSupplierDeptId());
            stockout.setLogisticsId(recordBo.getLogisticsId());
            stockout.setType(BlameSourceTypeEnum.STOCKOUT.getCode());
            stockout.setSystemAuto(systemAuto);
            if (systemAuto) {
                stockout.setCreateScene(StockOutCreateSceneEnum.AUTO_NO_BUY_GOODS.getCode());
            } else {
                stockout.setCreateScene(StockOutCreateSceneEnum.NO_BUY_GOODS.getCode());
                stockout.setBuyerId(user.getUserId());
            }
            if (CollUtil.isNotEmpty(recordBo.getOrderItemList())) {
                for (RwEntruckGoodsOrder order : recordBo.getOrderItemList()) {
                    BatchCreateStockoutBO stockoutBo = BeanUtil.copyProperties(stockout, BatchCreateStockoutBO.class);
                    stockoutBo.setOrderId(order.getOrderId());
                    stockoutBo.setOrderItemId(order.getOrderItemId());
                    stockoutBo.setSupplierSkuId(order.getSupplierSkuId());
                    stockoutBo.setSpuName(order.getSpuName());
                    stockoutBo.setStockoutCount(order.getDeliveryQuantity());
                    stockoutBOList.add(stockoutBo);
                }
            } else {
                for (RwEntruckGoodsAddBo goodsBo : recordBo.getGoodsList()) {
                    BatchCreateStockoutBO stockoutBo = BeanUtil.copyProperties(stockout, BatchCreateStockoutBO.class);
                    stockoutBo.setSupplierSkuId(goodsBo.getSupplierSkuId());
                    stockoutBo.setSpuName(goodsBo.getSpuName());
                    stockoutBo.setStockoutCount(goodsBo.getDeliveryQuantity());
                    stockoutBOList.add(stockoutBo);
                }
            }
        }
        if (stockoutBOList.size() == 0) {
            throw new ServiceException("没有可以操作不采购的数量");
        }
        // 创建缺货单
        log.keyword("waitDeliveryNoPurchase").info("createStockout {}", JsonUtils.toJsonString(stockoutBOList));
        regionEntruckAffairService.createStockout(stockoutBOList);
    }

    /**
     * 取消送货单&装车记录
     */
    @Transactional(rollbackFor = Throwable.class)
    public void autoCancelDeliveryOrCompleteInspect(LocalDate saleDate, Long regionWhId) {
        SupDeliveryQueryBo bo = new SupDeliveryQueryBo();
        bo.setRegionWhId(regionWhId);
        bo.setSaleDate(saleDate);
        List<SupDeliveryVo> deliveryVos = deliveryService.waitInspectList(bo);
        if (deliveryVos.size() == 0) {
            return;
        }
        List<Long> deliveryIds = deliveryVos.stream().map(SupDeliveryVo::getId).collect(Collectors.toList());
        Map<Long, Long> existInspectMap = deliveryGoodsService.queryListByDeliveryIds(deliveryIds)
                .stream().filter(f -> !f.getStatus().equals(DeliveryStatusEnum.WAIT_INSPECT.getCode()))
                .collect(Collectors.toMap(SupDeliveryGoodsVo::getDeliveryId, SupDeliveryGoodsVo::getDeliveryId, (key1, key2) -> key2));
        // 取消送货单
        List<Long> cancelIds = deliveryVos.stream().filter(f -> !existInspectMap.containsKey(f.getId())).map(SupDeliveryVo::getId).collect(Collectors.toList());
        if (cancelIds.size() > 0) {
            SupDeliveryCancelBo cancelBo = new SupDeliveryCancelBo();
            cancelBo.setIds(cancelIds);
            cancelBo.setCancelReason("送货结束自动取消");
            log.keyword("SupDeliveryAffairService").warn("autoCancelDeliveryOrCompleteInspect cancel: {}", cancelBo);
            deliveryService.cancelDelivery(cancelBo);
            entruckRecordService.updateStatusByDeliveryIds(cancelIds, DeliveryStatusEnum.CANCEL_DELIVERY.getCode());
        }
        // 送货单完成质检
        List<Long> completeIds = deliveryVos.stream().filter(f -> existInspectMap.containsKey(f.getId())).map(SupDeliveryVo::getId).collect(Collectors.toList());
        if (completeIds.size() > 0) {
            deliveryGoodsService.autoCompleteInspect(completeIds);
            for (Long deliveryId : completeIds) {
                this.completeInspect(deliveryId);
            }
        }
    }

    /**
     * 待送货商品-自动不采购
     */
    @Transactional(rollbackFor = Throwable.class)
    public void autoWaitDeliveryNoPurchase(LocalDate saleDate, Long regionWhId) {
        SupWaitDeliveryQueryBo bo = new SupWaitDeliveryQueryBo();
        bo.setSaleDate(saleDate);
        bo.setRegionWhId(regionWhId);
        List<RwWaitDeliveryGoodsVo> list = entruckGoodsService.rwWaitDeliveryList(bo);
        if (list.size() == 0) {
            return;
        }
        List<RwEntruckRecordAddBo> recordBos = new ArrayList<>();
        for (RwWaitDeliveryGoodsVo vo : list) {
            RwEntruckGoodsAddBo goodsBo = new RwEntruckGoodsAddBo();
            goodsBo.setSupplierSkuId(vo.getSupplierSkuId());
            goodsBo.setSpuName(vo.getSpuName());
            goodsBo.setDeliveryQuantity(vo.getWaitDeliveryQuantity());

            RwEntruckRecordAddBo recordBo = new RwEntruckRecordAddBo();
            recordBo.setSaleDate(vo.getSaleDate());
            recordBo.setRegionWhId(vo.getRegionWhId());
            recordBo.setSupplierId(vo.getSupplierId());
            recordBo.setSupplierDeptId(vo.getSupplierDeptId());
            recordBo.setLogisticsId(vo.getLogisticsId());
            recordBo.setGoodsList(ListUtil.toList(goodsBo));

            recordBos.add(recordBo);
        }
        this.waitDeliveryNoPurchase(recordBos, true);
    }

//    /**
//     * 拣货装车--操作拣货装车（创建送货单、装车记录）
//     */
//    @Lock4j(name = OrderCacheNames.OPERATE_PICKING_ENTRUCK_LOCK, keys = "#bo.regionWhId + '_' + #bo.saleDate + '_' + #bo.logisticsId", expire = 5000, acquireTimeout = 3000)
//    @Transactional(rollbackFor = Throwable.class)
//    public void operateEntruck30(RwEntruckPickingLogisticsAddBo bo) {
//        List<RwEntruckPickingGoodsAddBo> goodsList = bo.getGoodsList().stream().filter(g -> g.getEntruckQuantity() > 0).collect(Collectors.toList());
//        if (goodsList.size() == 0) {
//            return;
//        }
//        Map<Long, Integer> entruckSkuMap = goodsList.stream().collect(Collectors.toMap(RwEntruckPickingGoodsAddBo::getSupplierSkuId, RwEntruckPickingGoodsAddBo::getEntruckQuantity));
//        // 查询待拣货数
//        RwWaitEntruckQueryBo queryBo = new RwWaitEntruckQueryBo();
//        queryBo.setSaleDate(bo.getSaleDate());
//        queryBo.setRegionWhId(bo.getRegionWhId());
//        queryBo.setLogisticsId(bo.getLogisticsId());
//        queryBo.setSupplierSkuIds(entruckSkuMap.keySet().stream().toList());
//        List<RwWaitEntruckLogisticsVo> list = this.waitEntruckLogisticsList(queryBo);
//        if (list.size() == 0) {
//            throw new ServiceException("无待装车商品列表");
//        }
//        RwWaitEntruckLogisticsVo vo = list.get(0);
//        if (vo.getGoodsList().size() != goodsList.size()) {
//            throw new ServiceException(String.format("装车商品数【%d】与待装车商品数【%d】不匹配", goodsList.size(), list.size()));
//        }
//        vo.getGoodsList().forEach(goods -> {
//            if (goods.getWaitEntruckQuantity() < entruckSkuMap.get(goods.getSupplierSkuId())) {
//                throw new ServiceException(String.format("商品【%s】装车数大于待装车数(%d)", goods.getSpuName(), goods.getWaitEntruckQuantity()));
//            }
//        });
//        this.operateEntruck(vo, entruckSkuMap, DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode());
//    }

    /**
     * 创建拣货单-送货单&装车记录
     */
    @Lock4j(name = OrderCacheNames.OPERATE_PICKING_ENTRUCK_LOCK, keys = "#bo.regionWhId + '_' + #bo.saleDate + '_' + #bo.logisticsId", expire = 5000, acquireTimeout = 3000)
    @Transactional(rollbackFor = Throwable.class)
    public void operateEntruck20(RwWaitEntruckQueryBo bo) {
        List<RwWaitEntruckLogisticsVo> list = this.waitEntruckLogisticsList(bo);
        if (list.size() == 0) {
            throw new ServiceException("该物流线暂无待拣货需求数-" + bo.getLogisticsId());
        }
        String pickingNo = bo.getEntruckType() == 1 ? CustomNoUtil.getPickingNo(bo.getSaleDate()) : "";

        RwWaitEntruckLogisticsVo vo = list.get(0);
        vo.setPickingNo(pickingNo);
        vo.setBasPortageTeamId(bo.getBasPortageTeamId());

        bo.setPickingNo(pickingNo);
        bo.setIsCreateNoSuccess(this.operateEntruck(vo, null, DeliveryStatusEnum.COMPLETE_INSPECT.getCode()));
    }

    @Transactional(rollbackFor = Throwable.class)
    public boolean operateEntruck(RwWaitEntruckLogisticsVo vo, Map<Long, Integer> entruckSkuMap, Integer status) {
        boolean isCreateNoSuccess = false;
        boolean isEntruck = status.intValue() == DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode();
        List<Long> cwStockRecordIds = new ArrayList<>();
        for (RwWaitEntruckGoodsVo goods : vo.getGoodsList()) {
            Integer entruckQuantity = isEntruck ? entruckSkuMap.get(goods.getSupplierSkuId()) : goods.getWaitEntruckQuantity();
            // 库存=0，不打印生成单据
            if (goods.getStockQuantity() <= 0) {
                continue;
            }
            // 库存 < 待送货， 单据数 = 库存数
            if (goods.getStockQuantity() < entruckQuantity) {
                entruckQuantity = goods.getStockQuantity();
            }
            // 送货单 商品
            SupDeliveryAddBo deliveryBo = new SupDeliveryAddBo();
            deliveryBo.setBusinessType(goods.getBusinessType());
            deliveryBo.setStatus(status);
            deliveryBo.setSaleDate(vo.getSaleDate());
            deliveryBo.setRegionWhId(vo.getRegionWhId());
            deliveryBo.setSupplierId(goods.getSupplierId());
            deliveryBo.setSupplierName(goods.getSupplierName());
            deliveryBo.setSupplierDeptId(goods.getSupplierDeptId());
            deliveryBo.setSupplierDeptName(goods.getSupplierDeptName());
            deliveryBo.setTotalDeliveryQuantity(entruckQuantity);
            deliveryBo.setRemark(isEntruck ? "分拣装车" : "待拣打印");
            deliveryBo.setPickingNo(vo.getPickingNo());
            deliveryBo.setBasPortageTeamId(vo.getBasPortageTeamId());

            SupDeliveryGoodsAddBo deliveryGoodsBo = new SupDeliveryGoodsAddBo();
            BeanUtils.copyProperties(goods, deliveryGoodsBo);
            deliveryGoodsBo.setIsExemptInspect(YNStatusEnum.ENABLE.getCode());
            deliveryGoodsBo.setDeliveryQuantity(entruckQuantity);
            deliveryBo.setGoodsList(ListUtil.toList(deliveryGoodsBo));

            // 装车记录 商品
            RwEntruckRecordAddBo recordBo = new RwEntruckRecordAddBo();
            recordBo.setBusinessType(goods.getBusinessType());
            recordBo.setStatus(status);
            recordBo.setSaleDate(vo.getSaleDate());
            recordBo.setRegionWhId(vo.getRegionWhId());
            recordBo.setSupplierId(goods.getSupplierId());
            recordBo.setSupplierName(goods.getSupplierName());
            recordBo.setSupplierDeptId(goods.getSupplierDeptId());
            recordBo.setSupplierDeptName(goods.getSupplierDeptName());
            recordBo.setLogisticsId(vo.getLogisticsId());
            recordBo.setCityWhId(vo.getCityWhId());
            recordBo.setParkingNo(vo.getParkingNo());

            if (remoteRegionWhService.queryById(recordBo.getRegionWhId()).getCityWhIdList().contains(recordBo.getCityWhId())) {
                recordBo.setIsAffiliated(1);
            }
            recordBo.setPickingNo(vo.getPickingNo());
            recordBo.setBasPortageTeamId(vo.getBasPortageTeamId());

            RwEntruckGoodsAddBo recordGoodsBo = new RwEntruckGoodsAddBo();
            BeanUtils.copyProperties(goods, recordGoodsBo);
            recordGoodsBo.setDeliveryQuantity(entruckQuantity);
            if (isEntruck) {
                recordGoodsBo.setEntruckQuantity(entruckQuantity);
            }
            recordBo.setGoodsList(ListUtil.toList(recordGoodsBo));

            // 加载关联订单项数据
            List<RwEntruckRecordAddBo> recordBos = ListUtil.toList(recordBo);
            regionEntruckAffairService.loadDeliveryOrderItemList(recordBos);
            // 创建送货单
            int rows = deliveryService.insert(deliveryBo);
            if (rows <= 0) {
                throw new ServiceException("操作拣货装车失败" + status);
            }
            // 创建装车记录
            entruckRecordService.insert(deliveryBo.getId(), deliveryBo.getDeliveryNo(), recordBos);
            // 直属仓 自动接车
            if (recordBo.getIsAffiliated() == 1 && isEntruck) {
                sortGoodsService.directImmediateReceive(recordBo.getId());
            }
            cwStockRecordIds.add(recordBo.getId());
            isCreateNoSuccess = true;
        }
        if (cwStockRecordIds.size() > 0) {
            String pickingNo = StrUtil.isNotBlank(vo.getPickingNo()) ? vo.getPickingNo() : "FH";
            regionEntruckAffairService.createCwStock(vo.getRegionWhId(), vo.getSaleDate(), pickingNo, entruckGoodsService.queryListByRecordIds(cwStockRecordIds));
        }
        return isCreateNoSuccess;
    }

//    /**
//     * 拣货装车--操作拣货装车、并完成拣货装车（创建送货单、装车记录、差异不采）
//     * SaleDate、RegionWhId、LogisticsId（可为空）
//     */
//    @Transactional(rollbackFor = Throwable.class)
//    public void completeEntruckPicking(RwWaitEntruckQueryBo bo) {
//        // 查询待拣货数
//        List<RwWaitEntruckLogisticsVo> list = this.waitEntruckLogisticsList(bo);
//        if (list.size() == 0) {
//            return;
//        }
//        List<BatchCreateStockoutBO> stockoutBOList = new ArrayList<>();
//        for (RwWaitEntruckLogisticsVo vo : list) {
//            for (RwWaitEntruckGoodsVo goods : vo.getGoodsList()) {
//                if (goods.getWaitEntruckQuantity() <= 0) {
//                    continue;
//                }
//                BatchCreateStockoutBO stockoutBo = new BatchCreateStockoutBO();
//                stockoutBo.setSaleDate(bo.getSaleDate());
//                stockoutBo.setRegionWhId(bo.getRegionWhId());
//                stockoutBo.setSkuSupplierId(goods.getSupplierId());
//                stockoutBo.setSkuSupplierDeptId(goods.getSupplierDeptId());
//                stockoutBo.setLogisticsId(vo.getLogisticsId());
//                stockoutBo.setType(BlameSourceTypeEnum.STOCKOUT.getCode());
//                stockoutBo.setSupplierSkuId(goods.getSupplierSkuId());
//                stockoutBo.setSpuName(goods.getSpuName());
//                stockoutBo.setStockoutCount(goods.getWaitEntruckQuantity());
//                stockoutBo.setCreateScene(StockOutCreateSceneEnum.PICKING_ENTRUCK_GOODS.getCode());
//                stockoutBo.setBuyerId(LoginHelper.getLoginUser().getUserId());
//                stockoutBOList.add(stockoutBo);
//            }
//        }
//        // 创建缺货单
//        if (stockoutBOList.size() > 0) {
//            log.keyword("operateEntruckPicking").info("createStockout {}", JsonUtils.toJsonString(stockoutBOList));
//            regionEntruckAffairService.createStockout(stockoutBOList);
//        }
//    }
}
