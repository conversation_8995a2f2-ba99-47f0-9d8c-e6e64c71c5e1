package cn.xianlink.order.controller.region;

import cn.xianlink.common.api.enums.order.AccountTypeStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.blameRecord.AddBlameRecordBO;
import cn.xianlink.order.domain.bo.blameRecord.BlameRecordPageBO;
import cn.xianlink.order.domain.vo.blameRecord.BlameRecordInfoVO;
import cn.xianlink.order.domain.vo.blameRecord.BlameRecordPageVO;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.service.IBlameRecordService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 总仓助手-判责单
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@Validated
@RequiredArgsConstructor
@RestController("regionBlameRecordController")
@RequestMapping("/order/region/blame")
public class BlameRecordController extends BaseController {

    private final IBlameRecordService blameRecordService;

    @PostMapping("/blameRecordPage")
    @ApiOperation(value = "判责单列表")
    public R<TableDataInfo<BlameRecordPageVO>> blameRecordPage(@RequestBody BlameRecordPageBO bo){
        return R.ok(blameRecordService.blameRecordPage(bo));
    }

    @GetMapping("/getBlameById/{id}")
    @ApiOperation(value = "判责单详情")
    public R<BlameRecordInfoVO> getBlameById(@PathVariable("id") Long id){
        return R.ok(blameRecordService.getBlameById(id,null));
    }

    @PostMapping("/addBlame")
    @RepeatSubmit()
    @ApiOperation(value = "新增判责")
    public R<Void> addBlame(@RequestBody AddBlameRecordBO bo){
        bo.setRegionWhId(LoginHelper.getLoginUser().getRelationId());
        blameRecordService.addBlame(bo);
        return R.ok();
    }

    @GetMapping("/cancel/{id}")
    @RepeatSubmit()
    @ApiOperation(value = "撤回判责")
    public R<Void> cancel(@PathVariable("id") Long id){
        blameRecordService.cancel(id);
        return R.ok();
    }

    @GetMapping("/adminCancel")
    @RepeatSubmit()
    @ApiOperation(value = "运维-撤回判责")
    public R<Void> adminCancel(@RequestParam("code") String code){
        blameRecordService.adminCancel(code);
        return R.ok();
    }

    @PostMapping("/updateBlame")
    @RepeatSubmit()
    @ApiOperation(value = "修改判责单信息-介入/驳回")
    public R<Void> updateBlame(@RequestBody AddBlameRecordBO bo){
        blameRecordService.changBlame(bo);
        return R.ok();
    }

    @GetMapping("/getBlameCount")
    @ApiOperation(value = "获取判责单各状态数量")
    public R<List<CommonStatusCountVO>> getBlameCount(){
        return R.ok(blameRecordService.getBlameCount(AccountTypeStatusEnum.REGION.getCode(), null));
    }

}
