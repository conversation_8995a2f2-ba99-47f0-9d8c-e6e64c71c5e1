package cn.xianlink.order.controller.admin;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.xianlink.basic.api.RemoteBasCustomerService;
import cn.xianlink.basic.api.RemoteCityWhPlaceService;
import cn.xianlink.basic.api.RemoteCityWhService;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.basic.api.domain.bo.RemoteRegionLogisticsQueryBo;
import cn.xianlink.basic.api.domain.vo.RemoteCityWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteCustomerVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionLogisticsVo;
import cn.xianlink.common.api.enums.order.OrderCancelTypeEnum;
import cn.xianlink.common.api.enums.order.RefundStatusEnum;
import cn.xianlink.common.api.enums.order.RefundTypeEnum;
import cn.xianlink.common.api.enums.order.StockOutStatusEnum;
import cn.xianlink.common.api.enums.product.CategoryAfterTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.config.OrderProperties;
import cn.xianlink.order.domain.*;
import cn.xianlink.order.domain.excel.ReceiveGoodsDetailExportDto;
import cn.xianlink.order.domain.excel.ReceiveGoodsExportBo;
import cn.xianlink.order.domain.excel.ReceiveGoodsExportDto;
import cn.xianlink.order.domain.excel.SortGoodsExportBo;
import cn.xianlink.order.mapper.OrderMapper;
import cn.xianlink.order.service.*;
import cn.xianlink.order.util.KeyComparator;
import cn.xianlink.order.util.excel.ExcelFileUtil;
import cn.xianlink.order.util.excel.TemplatePathEnum;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuFundsInfoVo;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.resource.api.domain.RemoteFile;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.swagger.annotations.ApiOperation;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 管理台-提货单
 * <AUTHOR>
 * @date 2024-05-25
 */
@CustomLog
@Validated
@RequiredArgsConstructor
@RestController("adminReceiveGoodsController")
@RequestMapping("/order/admin/receive")
public class ReceiveGoodsController extends BaseController {

    private final IReceiveGoodsService receiveGoodsService;

    private final ISortGoodsService sortGoodsService;
    private final IOrderItemService orderItemService;
    private final IStockoutRecordService stockoutRecordService;
    private final IRefundRecordService refundRecordService;
    private final OrderMapper orderMapper;
    @DubboReference(timeout = 300000)
    private final transient RemoteFileService remoteFileService;
    @DubboReference
    private final transient RemoteCityWhService remoteCityWhService;
    @DubboReference
    private final transient RemoteRegionLogisticsService remoteRegionLogisticsService;
    @DubboReference
    private final transient RemoteCityWhPlaceService remoteCityWhPlaceService;
    @DubboReference
    private final transient RemoteBasCustomerService remoteBasCustomerService;
    @DubboReference
    private final transient RemoteUserService remoteUserService;
    @DubboReference
    private final transient RemoteSupplierSkuService remoteSupplierSkuService;

    private final OrderProperties orderProperties;



//    @PostMapping("/createReceiveGoodsRecord")
//    public R<List<CommonStatusCountVO>> createReceiveGoodsRecord(@RequestBody List<Long> orderIds){
//        receiveGoodsService.createReceiveGoodsRecord(orderIds);
//        return R.ok();
//    }

    @PostMapping("/exportDeliveryNumber")
    @ApiOperation(value = "导出拍子号")
    @RepeatSubmit()
    public R<String> exportDeliveryNumber(@RequestBody SortGoodsExportBo bo) {
        return R.ok(null, sortGoodsService.exportDeliveryNumber(bo));
    }

    /**
     * 导出提货单
     * <AUTHOR> on 2024/11/9:15:52
     * @param bo
     * @return cn.xianlink.common.core.domain.R<java.lang.String>
     */
    @PostMapping("/exportReceive")
    @RepeatSubmit()
    public R<String> exportReceive(@RequestBody ReceiveGoodsExportBo bo) {
        if (CollectionUtil.isNotEmpty(bo.getPlaceIdList())) {
            bo.setPlaceIdList(remoteCityWhPlaceService.getPlaceIdsByUser(LoginHelper.getLoginUser().getUserId()));
        }else if (CollectionUtil.isEmpty(bo.getPlaceIdList()) && CollectionUtil.isEmpty(bo.getPlaceIdLevel2List())){
            bo.setPlaceIdList(remoteRegionLogisticsService.queryAllPlaceIdByCUser(LoginHelper.getLoginUser().getUserId()));
            bo.setPlaceIdLevel2List(remoteCityWhPlaceService.getPlaceIdsByUser(LoginHelper.getLoginUser().getUserId()));
        }
        //前端改完必传物流线
        List<ReceiveGoodsRecord> receiveRecords = receiveGoodsService.getReceiveGoodsRecord(LoginHelper.getLoginUser().getRelationId(),
                bo.getLogisticsId(), bo.getSaleDate(), bo.getPlaceIdLevel2List(), bo.getPlaceIdList());
        if (CollUtil.isEmpty(receiveRecords)) {
            return R.fail("无数据");
        }
        Set<Long> customerIdSet = new HashSet<>();
        Set<Long> orderIdSet = new HashSet<>();
        //拍子号对应的订单列表，list可能是多个客户的订单
        Map<String, List<Long>> numToOrderMap = new HashMap();
        List<ReceiveGoodsRecord> list1 = receiveRecords.stream().sorted(Comparator.comparing(ReceiveGoodsRecord::getDeliveryNumber)).toList();
        for (ReceiveGoodsRecord receiveRecord : list1) {
            customerIdSet.add(receiveRecord.getCustomerId());
            orderIdSet.add(receiveRecord.getOrderId());

            var list = numToOrderMap.getOrDefault(receiveRecord.getPlaceLevel2Code() + receiveRecord.getDeliveryNumber(), new ArrayList<>());
            list.add(receiveRecord.getOrderId());
            numToOrderMap.put(receiveRecord.getPlaceLevel2Code() + receiveRecord.getDeliveryNumber(), list);
        }
        //物流线
        String logisticsName = "";
        RemoteRegionLogisticsQueryBo bo1 = new RemoteRegionLogisticsQueryBo();
        bo1.setLogisticsId(bo.getLogisticsId());
        RemoteRegionLogisticsVo logisticsVo = remoteRegionLogisticsService.queryList(bo1).get(0);
        logisticsName = logisticsVo.getLogisticsName();
        //城市仓
        RemoteCityWhVo cityWhVo = remoteCityWhService.queryById(logisticsVo.getCityWhId());

        //查客户
        Map<Long,RemoteCustomerVo> customerVoMap = remoteBasCustomerService.getByIds(Lists.newArrayList(customerIdSet)).stream().collect(Collectors.toMap(RemoteCustomerVo::getId, Function.identity()));

        Map<String, RemoteUserBo> customerPhoneMap = remoteUserService.getUsersByUserCodes(customerVoMap.values().stream().map(RemoteCustomerVo::getUserCode).toList());

        //查订单详情
        List<String> cancelTypes = Arrays.asList(OrderCancelTypeEnum.FEW.getCode(), OrderCancelTypeEnum.OUT.getCode());
        List<OrderItem> allItems = orderItemService.getByOrderIds(orderIdSet, cancelTypes);
        List<Long> itemIds = new ArrayList<>();
        Map<Long,List<OrderItem>> orderItemMap = new HashMap<>();
        List<Long> supplierSkuIds = new ArrayList<>();
        for (OrderItem item : allItems) {
            itemIds.add(item.getId());

            var t1 = orderItemMap.getOrDefault(item.getOrderId(), new ArrayList<>());
            t1.add(item);
            orderItemMap.put(item.getOrderId(), t1);

            supplierSkuIds.add(item.getSupplierSkuId());
        }
        Map<Long,RemoteSupplierSkuFundsInfoVo> skuMap = remoteSupplierSkuService.queryInfoListByFunds(supplierSkuIds).stream().collect(Collectors.toMap(RemoteSupplierSkuFundsInfoVo::getSkuId, Function.identity()));
        //查询待确认和已确认的缺货少货记录
        List<StockoutRecordDetail> stockoutRecordDetails = stockoutRecordService.getStockoutByItemIdList(itemIds, Arrays.asList(StockOutStatusEnum.STATUS_CONFIRM.getCode(), StockOutStatusEnum.HAS_CONFIRM.getCode()));
        //统计每个商品对应的缺货少货数量
        Map<Long, Integer> stockoutCountMap = stockoutRecordDetails.stream().collect(Collectors.groupingBy(StockoutRecordDetail::getOrderItemId, Collectors.summingInt(StockoutRecordDetail::getStockoutCount)));

        LambdaQueryWrapper<Order> orderQuery = Wrappers.lambdaQuery();
        orderQuery.in(Order::getId, orderItemMap.keySet())
                .select(Order::getId, Order::getCustomerId, Order::getFinancialServiceAmount);
        Map<Long, List<Order>> customerOrderMap = orderMapper.selectList(orderQuery).stream().collect(Collectors.groupingBy(Order::getCustomerId));

        //根据itemId查询缺货少货的退款记录
        List<RefundProductDetail> refunded = refundRecordService.getRefundByItemIdList(itemIds);
        Set<Integer> refundSet = Sets.newHashSet(RefundTypeEnum.LACK.getCode(), RefundTypeEnum.LESS.getCode(),RefundTypeEnum.DIFFERENCE.getCode());
        //只查缺货少货的非退款中的记录
        refunded = refunded.stream()
                .filter(o-> refundSet.contains(o.getRefundType()) && !o.getRefundStatus().equals(RefundStatusEnum.IN_REFUND.getCode()))
                .collect(Collectors.toList());
        Map<Long, List<RefundProductDetail>> refundMap = refunded.stream().collect(Collectors.groupingBy(RefundProductDetail::getOrderItemId));
        Map<String, List<Long>> sortedMap = new TreeMap<>(new KeyComparator());
        sortedMap.putAll(numToOrderMap);
        List<ReceiveGoodsExportDto> reports = new ArrayList<>();
        for (Map.Entry<String, List<Long>> longListEntry : sortedMap.entrySet()) {
            var deliveryNumber = longListEntry.getKey();
            var orderIds = longListEntry.getValue();
            //同一拍子上可能会有多个客户的商品
            Map<Long, List<OrderItem>> customerItemMap = new HashMap<>();
            for (Long orderId : orderIds) {
                List<OrderItem> items = orderItemMap.get(orderId);
                var t = customerItemMap.getOrDefault(items.get(0).getCustomerId(), new ArrayList<>());
                t.addAll(orderItemMap.get(orderId));
                customerItemMap.put(items.get(0).getCustomerId(), t);
            }
            for (Map.Entry<Long, List<OrderItem>> listEntry : customerItemMap.entrySet()) {
                Long customerId = listEntry.getKey();
                List<OrderItem> items = listEntry.getValue();
                if (CollUtil.isEmpty(items)) {
                    continue;
                }
                String customerPhone = "";
                if (customerVoMap.containsKey(customerId)) {
                    if (customerPhoneMap.containsKey(customerVoMap.get(customerId).getUserCode())) {
                        customerPhone = customerPhoneMap.get(customerVoMap.get(customerId).getUserCode()).getPhoneNo();
                    }
                }
                BigDecimal financialServiceAmount = customerOrderMap.get(customerId).stream().map(Order::getFinancialServiceAmount).reduce(BigDecimal::add).get();
                reports.add(this.buildReceiveGoodsExportDto(logisticsName, bo.getSaleDate(), customerVoMap.get(customerId),
                        customerPhone, deliveryNumber, items, stockoutCountMap, skuMap, financialServiceAmount, refundMap));
            }
        }
        if (reports.isEmpty()) {
            return R.fail("无数据");
        }
        String title = cityWhVo.getName() + "城市仓，" + logisticsName + "物流 销售日:" + bo.getSaleDate().toString();
        //先填充数据
        ExcelFileUtil excelFileUtil = new ExcelFileUtil();
        ByteArrayOutputStream tempOutputStream = new ByteArrayOutputStream();
        excelFileUtil.exportExcel(title, reports, TemplatePathEnum.RECEIVE_GOODS_RESULT.getValue(), tempOutputStream);
        //outputStream是最终的输出流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        //数据填充完后，找出换页符的位置，并设置换页符，最终设置打印区域
        try (ByteArrayInputStream bais = new ByteArrayInputStream(tempOutputStream.toByteArray());
             XSSFWorkbook workbook = new XSSFWorkbook(bais);
        ) {
            // 获取填充后的工作表
            XSSFSheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                Cell firstCell = row.getCell(0);
                if (firstCell != null
                        && firstCell.getCellType().equals(CellType.STRING)
                        && firstCell.getStringCellValue().equals(ExcelFileUtil.BREAK_FLAG)) {
                    firstCell.setCellValue("");
                    sheet.setRowBreak(row.getRowNum());
                }
                row.getRowNum();
            }
            //设置打印区域，如果增加了导出列，需要修改endColumn
            workbook.setPrintArea(0, 0, 15, 1, sheet.getLastRowNum());
            workbook.write(outputStream);
        } catch (Exception e) {
            throw new ServiceException(e);
        } finally {
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("close stream error", e);
            }
        }


//        String filePath = "example2.xlsx"; // 要创建的文件路径
//        FileOutputStream fileOutputStream = null;
//        try {
//            fileOutputStream = new FileOutputStream(filePath);
//            fileOutputStream.write(outputStream.toByteArray());
//            fileOutputStream.close();
//            System.out.println("文件创建成功，内容已写入。");
//        } catch (FileNotFoundException e) {
//            throw new RuntimeException(e);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//        R<String> r =  R.ok();
//        r.setData("aaa");
//        return r;

        RemoteFile remoteFile = remoteFileService.uploadTempFile("提货单导出", "提货单导出.xlsx", ContentType.OCTET_STREAM.getValue(), outputStream.toByteArray(),1);
        R<String> r =  R.ok();
        r.setData(remoteFile.getUrl());
        return r;
    }

    private ReceiveGoodsExportDto buildReceiveGoodsExportDto(String logisticsName, LocalDate saleDate, RemoteCustomerVo customerVo, String phoneNumber, String deliveryNumber,
                                                             List<OrderItem> items, Map<Long, Integer> stockoutCountMap, Map<Long,RemoteSupplierSkuFundsInfoVo> skuMap,
                                                             BigDecimal financialServiceAmount, Map<Long, List<RefundProductDetail>> refundMap){
        ReceiveGoodsExportDto export = new ReceiveGoodsExportDto();

        Integer totalNumber = 0;
        BigDecimal totalGrossWeight = BigDecimal.ZERO;
        BigDecimal totalServiceAmount = BigDecimal.ZERO;
        BigDecimal totalFreightAmount = BigDecimal.ZERO;
        BigDecimal totalProductAmount = BigDecimal.ZERO;
        BigDecimal refundProductAmount = BigDecimal.ZERO;
        BigDecimal refundServiceAmount = BigDecimal.ZERO;
        BigDecimal refundFreightAmount = BigDecimal.ZERO;
        BigDecimal refundFinancialServiceAmount = BigDecimal.ZERO;
        BigDecimal baseFreightAmount = BigDecimal.ZERO;
        BigDecimal freightTotalFreeAmount = BigDecimal.ZERO;
        BigDecimal platformServiceFreeAmount = BigDecimal.ZERO;
        BigDecimal platformFreightAmount = BigDecimal.ZERO;
        BigDecimal freightTotalAmount = BigDecimal.ZERO;
        BigDecimal level2FreightAmount = BigDecimal.ZERO;

        List<ReceiveGoodsDetailExportDto> goods = new ArrayList<>();
        //根据supplierSkuId排序
        items.sort(Comparator.comparing(OrderItem::getSupplierSkuId));
        // 使用Map来存储根据supplierSkuId和price分组后的数据
        Map<String, List<OrderItem>> groupedItems = new HashMap<>();
        for (OrderItem item : items) {
            String key = item.getSupplierSkuId() + "-" + item.getPrice() + "-" + item.getCancelType();
            groupedItems.computeIfAbsent(key, k -> new ArrayList<>()).add(item);
        }
        int sortNumber = 1;
        for (List<OrderItem> group : groupedItems.values()) {
            OrderItem firstItem = group.get(0);
            int stockoutCount = 0;
            Integer quantity = 0;
            BigDecimal productAmount = BigDecimal.ZERO;
            BigDecimal serviceAmount = BigDecimal.ZERO;
            BigDecimal serviceFreeAmount = BigDecimal.ZERO;
            BigDecimal freightAmount = BigDecimal.ZERO;
            BigDecimal freightFreeAmount = BigDecimal.ZERO;
            BigDecimal productFreeAmount = BigDecimal.ZERO;
            for (OrderItem orderItem : group) {
                stockoutCount = stockoutCount + stockoutCountMap.getOrDefault(firstItem.getId(), 0);
                quantity += orderItem.getCount();
                productAmount = productAmount.add(orderItem.getProductAmount());
                productFreeAmount = productFreeAmount.add(orderItem.getProductFreeAmount());
                serviceAmount = serviceAmount.add(orderItem.getPlatformServiceAmount());
                serviceFreeAmount = serviceFreeAmount.add(orderItem.getPlatformServiceFreeAmount());
                freightAmount = freightAmount.add(orderItem.getFreightTotalAmount());
                freightFreeAmount = freightFreeAmount.add(orderItem.getFreightTotalFreeAmount());
                baseFreightAmount = baseFreightAmount.add(orderItem.getBaseFreightAmount());
                freightTotalFreeAmount = freightTotalFreeAmount.add(orderItem.getFreightTotalFreeAmount());
                platformServiceFreeAmount = platformServiceFreeAmount.add(orderItem.getPlatformServiceFreeAmount());
                platformFreightAmount = platformFreightAmount.add(orderItem.getPlatformFreightAmount());
                freightTotalAmount = freightTotalAmount.add(orderItem.getFreightTotalAmount());
                level2FreightAmount = level2FreightAmount.add(orderItem.getPlatformFreightAmountLevel2());
                if (refundMap.containsKey(orderItem.getId())) {
                    for (RefundProductDetail refundProductDetail : refundMap.get(orderItem.getId())) {
                        refundProductAmount = refundProductAmount.add(refundProductDetail.getRefundProductAmount());
                        refundFinancialServiceAmount = refundFinancialServiceAmount.add(refundProductDetail.getRefundFinancialServicePrice());
                        refundServiceAmount = refundServiceAmount.add(refundProductDetail.getRefundServiceAmount());
                        refundFreightAmount = refundFreightAmount.add(refundProductDetail.getRefundFreightAmount());
                    }
                }
            }
            //int转BigDecimal
            BigDecimal countB = BigDecimal.valueOf(quantity);
//            if (stockoutCount > 0) {
//                BigDecimal t = BigDecimal.valueOf(stockoutCount);
////                refundProductAmount = refundProductAmount.add(firstItem.getPrice().multiply(t));
//                refundServiceAmount = refundServiceAmount.add(firstItem.getPlatformServiceAmount().subtract(firstItem.getPlatformServiceFreeAmount()).multiply(t).divide(countB, 2, RoundingMode.HALF_DOWN));
//                refundFreightAmount = refundFreightAmount.add(firstItem.getFreightTotalAmount().subtract(firstItem.getFreightTotalFreeAmount()).multiply(t).divide(countB, 2, RoundingMode.HALF_DOWN));
//            }

            RemoteSupplierSkuFundsInfoVo skuInfo = skuMap.get(firstItem.getSupplierSkuId());
            String producer = "";
            //产地根据-分割取最后一个
            if (StrUtil.isNotBlank(skuInfo.getProducer())) {
                String[] split = skuInfo.getProducer().split(StrPool.DASHED);
                producer = split[split.length-1] + "-";
            }
            String brand = StrUtil.isNotBlank(skuInfo.getBrand()) ? skuInfo.getBrand() + "-":"";

            ReceiveGoodsDetailExportDto goodsDto =  new ReceiveGoodsDetailExportDto();
            goodsDto.setName((firstItem.getProdType().equals(2)?"[赠品]":"") + brand + producer + skuInfo.getSkuName() + "-" + skuInfo.getSpuStandards());
            goodsDto.setSortNumber(sortNumber);
            goodsDto.setPrice(firstItem.getPrice());
            goodsDto.setServiceAmount(serviceAmount);
            goodsDto.setServiceFreeAmount(serviceFreeAmount);
            goodsDto.setLogisticsName(logisticsName);
            goodsDto.setQuantity(quantity);
            goodsDto.setOutQuantity(stockoutCount);
            goodsDto.setWarehouseInspectionr("否");
            if (firstItem.getAfterSaleType() != null) {
                if (CategoryAfterTypeEnum.INSPECTION.getCode().equals(firstItem.getAfterSaleType())) {
                    goodsDto.setWarehouseInspectionr("是");
                }
            }
            goodsDto.setTotalGrossWeight(firstItem.getSpuGrossWeight().multiply(countB));
            goodsDto.setTotalNetWeight(firstItem.getSpuNetWeight().multiply(countB));
            goodsDto.setNetPrice(firstItem.getPrice().divide(firstItem.getSpuNetWeight(), 2, RoundingMode.HALF_UP));
            goodsDto.setProductAmount(productAmount);
            goodsDto.setProductFreeAmount(productFreeAmount);
            goodsDto.setProductTotalAmount(productAmount.subtract(productFreeAmount));
            goodsDto.setFreightAmount(freightAmount);
            goodsDto.setFreightFreeAmount(freightFreeAmount);
            if (StrUtil.isNotBlank(firstItem.getCancelType())) {
                if (OrderCancelTypeEnum.OUT.getCode().equals(firstItem.getCancelType())) {
                    goodsDto.setRemark(OrderCancelTypeEnum.OUT.getDesc());
                } else {
                    goodsDto.setRemark(OrderCancelTypeEnum.FEW.getDesc());
                }
            }
            goods.add(goodsDto);

            totalNumber += goodsDto.getQuantity();
            totalGrossWeight = totalGrossWeight.add(goodsDto.getTotalGrossWeight());
            totalServiceAmount = totalServiceAmount.add(goodsDto.getServiceAmount()).subtract(goodsDto.getServiceFreeAmount());
            totalProductAmount = totalProductAmount.add(goodsDto.getProductAmount()).subtract(goodsDto.getProductFreeAmount());
            totalFreightAmount = totalFreightAmount.add(goodsDto.getFreightAmount()).subtract(goodsDto.getFreightFreeAmount());

            sortNumber++;
        }
        String head = deliveryNumber + " ";
        if (StrUtil.isNotBlank(customerVo.getAlias())) {
            head = head + "客户名： " + customerVo.getAlias() + "【" + customerVo.getName() + "】 【" + phoneNumber + "】";
        } else {
            head = head + "客户名： 【" + customerVo.getName() + "】 【" + phoneNumber + "】 ";
        }
        if (ObjectUtil.isNotEmpty(customerVo.getAddress())) {
            head = head  + "【"  + customerVo.getAddress()  + "】 ";
        }
        if (ObjectUtil.isNotEmpty(saleDate)) {
            head = head + " 【" + saleDate.toString() + "】";
        }

        export.setHead(head);
        export.setTotalNumber(totalNumber);
        export.setTotalGrossWeight(totalGrossWeight);
        export.setTotalServiceAmount(totalServiceAmount);
        export.setTotalFreightAmount(totalFreightAmount);
        export.setTotalProductAmount(totalProductAmount);
        export.setFinancialServiceAmount(financialServiceAmount.subtract(refundFinancialServiceAmount));
        export.setRefundServiceAmount(refundServiceAmount);
        export.setRefundProductAmount(refundProductAmount);
        export.setRefundFreightAmount(refundFreightAmount);
        export.setBaseFreightAmount(baseFreightAmount);
        export.setFreightTotalFreeAmount(freightTotalFreeAmount);
        export.setPlatformServiceFreeAmount(platformServiceFreeAmount);
        export.setPlatformFreightAmount(platformFreightAmount);
        export.setFreightTotalAmount(freightTotalAmount);
        export.setLevel2FreightAmount(level2FreightAmount);

        BigDecimal totalAmount = totalProductAmount.add(totalFreightAmount).add(totalServiceAmount).add(financialServiceAmount)
                .subtract(refundServiceAmount).subtract(refundProductAmount).subtract(refundFreightAmount);
        export.setTotalAmount(totalAmount);
        export.setGoods(goods);
        return export;
    }

}
