package cn.xianlink.order.controller.region;


import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.report.StatSaleSearchBo;
import cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo;
import cn.xianlink.order.domain.bo.report.SupplierRankSearchBo;
import cn.xianlink.order.domain.vo.refundRecord.RefundPageVO;
import cn.xianlink.order.domain.vo.report.StatBoardVo;
import cn.xianlink.order.domain.vo.report.SupplierRankVo;
import cn.xianlink.order.service.IOrderService;
import cn.xianlink.order.service.IStatBoardService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 总仓小程序-看板
 *
 * <AUTHOR>
 * @date 2024-10-15
 * @folder 总仓助手(小程序)/看板
 */
@Validated
@RequiredArgsConstructor
@RestController("regionStatBoardController")
@RequestMapping("/order/region/stat_board/")
public class StatBoardController extends BaseController {
    private final IStatBoardService statBoardService;

    /**
     * 统计商品，金额
     *
     * @param bo
     * @return
     */
    @PostMapping("/region_sale")
    public R<StatBoardVo> statRegionSale(@RequestBody StatSaleSearchBo bo) {
        return R.ok(statBoardService.statRegionSale(bo));
    }


    /**
     *商品统计列表
     * @param bo
     * @return
     */
    @PostMapping("/commodity_statistics_region")
    public R<TableDataInfo<CommodityStatisticsInfoVo>> commodityStatistics(@RequestBody StatSaleSearchBo bo) {
        return R.ok(statBoardService.commodityStatisticsRegionSale(bo));
    }

    /**
     *商品统计合计
     * @param bo
     * @return
     */
    @PostMapping("/commodity_statistics_region_total")
    public R<CommodityStatisticsInfoVo> commodityStatisticsTotal(@RequestBody StatSaleSearchBo bo) {
        return R.ok(statBoardService.commodityStatisticsRegionTotal(bo));
    }

    /**
     * 供应商排行榜
     *
     * @param bo
     * @return
     */
    @PostMapping("/supplier_rank")
    public R<TableDataInfo<SupplierRankVo>> supplierRank(@RequestBody SupplierRankSearchBo bo) {
        return R.ok(statBoardService.supplierRank(bo));
    }
}