package cn.xianlink.order.controller.admin;

import cn.xianlink.common.api.enums.order.OrderCancelTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.domain.PublicId;
import cn.xianlink.order.domain.order.bo.OrderSearchBo;
import cn.xianlink.order.domain.order.bo.QueryAdminPageBo;
import cn.xianlink.order.domain.order.vo.GetInfoVo;
import cn.xianlink.order.domain.order.vo.OrderItemAggregateVO;
import cn.xianlink.order.domain.order.vo.QueryAdminPageVo;
import cn.xianlink.order.domain.vo.order.AdminOrderInfoVo;
import cn.xianlink.order.service.IOrderItemService;
import cn.xianlink.order.service.IOrderService;
import cn.xianlink.order.service.IRefundRecordService;
import cn.xianlink.order.service.OrderExportService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 订单
 * 前端访问路由地址为:/order/admin/order
 * <AUTHOR>
 * @date 2024-05-28
 * @folder 般果管理中心/订单/订单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/admin/order")
public class AOrderController {

    private final IOrderService orderService;
    private final IOrderItemService orderItemService;
    private final OrderExportService refundExportService;
    private final IRefundRecordService refundRecordService;

    @PostMapping("/queryAdminPage")
    @Operation(summary = "订单列表查询")
    public R<TableDataInfo<QueryAdminPageVo>> queryAdminPage(@RequestBody QueryAdminPageBo bo) {
        return R.ok(orderService.queryAdminPage(bo));
    }

//    @PostMapping("/queryItemPage")
//    @Operation(summary = "订单项列表查询")
//    public R<TableDataInfo<QueryAdminItemPageVo>> queryItemPage(@RequestBody QueryAdminPageBo bo) {
//        return R.ok();
//    }

    @PostMapping("/getInfo")
    @Operation(summary = "获取订单详细信息")
    public R<GetInfoVo> getInfo(@RequestBody PublicId id) {
        return R.ok(orderService.queryById(id.getId()));
    }

    @GetMapping("/listOrderInfo")
    @Operation(summary = "获取订单明细列表")
    public R<List<OrderItemAggregateVO>> listOrderInfo(@RequestParam("orderCode") String orderCode) {
        OrderSearchBo bo = new OrderSearchBo();
        bo.setOrderCode(orderCode);
        List<OrderItemAggregateVO> orderInfoPage = orderService.listOrderInfo(bo);
        return R.ok(orderInfoPage);
    }

    @PostMapping("/getOrderInfoPage")
    @Operation(summary = "获取订单明细列表分页")
    public R<TableDataInfo<OrderItemAggregateVO>> getOrderInfoPage(@RequestBody OrderSearchBo searchBo) {
        TableDataInfo<OrderItemAggregateVO> orderInfoPage = orderService.getOrderInfoPage(searchBo);
        return R.ok(orderInfoPage);
    }



    @GetMapping("/listByOrderCode")
    @Operation(summary = "根据订单编号模糊查询订单号")
    public R<List<String>> listByOrderCode(@RequestParam(value = "orderCode", required = false) String orderCode,
                                           @RequestParam(value = "supplierId", required = false) Long supplierId,
                                           @RequestParam(value = "cityWhId", required = false)  Long cityWhId,
                                           @RequestParam(value = "saleDate", required = false)  String saleDate,
                                           @RequestParam(value = "regionWhId", required = false)  Long regionWhId){
        return R.ok(orderService.listByOrderCode(orderCode, supplierId, cityWhId, saleDate, regionWhId));
    }

    @GetMapping("/order_info/{orderCode}")
    @Operation(summary = "订单信息")
    public R<AdminOrderInfoVo> adminOrderInfo(@PathVariable("orderCode") String orderCode) {
        return R.ok(orderService.adminOrderInfo(orderCode));
    }

    /**
     * 导出订单明细
     * <AUTHOR> on 2025/1/9:15:29
     * @param searchBo
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     */
    @PostMapping("/exportOrder")
    @RepeatSubmit()
    public R<Void> orderExport(@Validated @RequestBody OrderSearchBo searchBo) {
        refundExportService.submitOrderExport(searchBo);
        return R.ok();
    }

    @RepeatSubmit()
    @PostMapping("/exceptionRefund")
    @Operation(summary = "支付成功取消订单报错数据修复接口")
    public R<Void> exceptionRefund(@RequestBody PublicId id) {
        refundRecordService.exceptionRefund(id.getIdList());
        return R.ok();
    }

}
