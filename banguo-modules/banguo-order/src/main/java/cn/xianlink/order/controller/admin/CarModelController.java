package cn.xianlink.order.controller.admin;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.CarModelBo;
import cn.xianlink.order.domain.vo.CarModelVo;
import cn.xianlink.order.service.ICarModelService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 车型
 * 前端访问路由地址为:/system/model
 *
 * <AUTHOR>
 * @date 2024-06-11
 * @folder 般果管理中心/订单/车型表
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/admin/model")
public class CarModelController extends BaseController {

    private final ICarModelService carModelService;

    /**
     * 查询车型列表
     */
    @PostMapping("/list")
    @ApiOperation("分页查询")
    public R<TableDataInfo<CarModelVo>> list(@RequestBody CarModelBo bo, PageQuery pageQuery) {
        return R.ok(carModelService.queryPageList(bo, pageQuery));
    }

    /**
     * 查询车型列表
     */
    @PostMapping("/getList")
    @ApiOperation("车型list集合")
    public R<List<CarModelVo>> getList(@RequestBody CarModelBo bo) {
        return R.ok(carModelService.queryList(bo));
    }


    /**
     * 获取车型详细信息
     *
     * @param id 主键
     */
    @GetMapping("/getInfo")
    @ApiOperation("查询详情")
    public R<CarModelVo> getInfo(@RequestParam("id") Long id) {
        return R.ok(carModelService.queryById(id));
    }

    /**
     * 新增车型
     */
    @PostMapping("/add")
    @RepeatSubmit()
    @ApiOperation("新增车型")
    public R<Boolean> add(@RequestBody CarModelBo bo) {
        return R.ok(carModelService.insertByBo(bo));
    }

    /**
     * 修改车型
     */
    @PostMapping("/edit")
    @RepeatSubmit()
    @ApiOperation("修改车型")
    public R<Boolean> edit(@RequestBody CarModelBo bo) {
        return R.ok(carModelService.updateByBo(bo));
    }

    /**
     * 删除车型
     *
     * @param ids 主键串
     */
    @PostMapping("/deleteByIds")
    @RepeatSubmit()
    @ApiOperation("删除车型")
    public R<Boolean> remove(@RequestBody List<Long> ids) {
        return R.ok(carModelService.deleteWithValidByIds(ids));
    }
}
