package cn.xianlink.order.controller.platform;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.bo.BaseIdBo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.report.ReportLossMiniListSearchBo;
import cn.xianlink.order.domain.bo.report.ReportLossOrderBo;
import cn.xianlink.order.domain.bo.report.ReportLossOrderFlowBo;
import cn.xianlink.order.domain.vo.report.ReportLossMiniDetailVo;
import cn.xianlink.order.domain.vo.report.ReportLossMiniListVo;
import cn.xianlink.order.domain.vo.report.ReportNumStatisticsVo;
import cn.xianlink.order.service.IReportLossService;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 报损单
 *
 * <AUTHOR> xiaodaibing on 2024-05-30 17:29
 * @folder 采集平台(小程序)/报损单
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/platform/report")
public class PReportLossController extends BaseController {

    private final IReportLossService reportLossService;

    /**
     * 列表
     * @param bo
     * @return cn.xianlink.common.core.domain.R<TableDataInfo < ReportLossMiniListVo>>
     * <AUTHOR> on 2024/6/11:15:24
     */
    @PostMapping("/list")
    public R<TableDataInfo<ReportLossMiniListVo>> list(@RequestBody ReportLossMiniListSearchBo bo) {
        bo.setCustomerId(LoginHelper.getLoginUser().getRelationId());
        return R.ok(reportLossService.miniPage(bo));
    }


    /**
     * 获取详情
     *
     * @param id
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.order.domain.vo.report.ReportLossMiniDetailVo>
     * <AUTHOR> on 2024/6/6:15:22
     */
    @GetMapping("/detail/{id}")
    public R<ReportLossMiniDetailVo> detail(@PathVariable("id") Long id) {
        ReportLossMiniDetailVo vo = reportLossService.detail(id);
        if (!vo.getCustomerId().equals(LoginHelper.getLoginUser().getRelationId())){
            return R.fail("无权限查看");
        }
        return R.ok(vo);
    }

    /**
     * 创建报损单
     * <AUTHOR> on 2024/6/6:15:51
     * @param bo
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     */
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@RequestBody @Valid ReportLossOrderBo bo) {
        reportLossService.create(bo);
        return toAjax(Boolean.TRUE);
    }

    /**
     * 报损流程处理
     * 供应商审核、申诉、客服介入
     * <AUTHOR> on 2024/6/6:17:16
     * @param bo
     * @return void
     */
    @RepeatSubmit()
    @GlobalTransactional
    @PostMapping("/processing")
    public R<Void> processing(@RequestBody @Valid ReportLossOrderFlowBo bo) {
        bo.setReportLossId(reportLossService.getReportLossId(bo.getOrderItemId()));
        reportLossService.processing(bo);
        return toAjax(Boolean.TRUE);
    }



    /**
     * 撤销
     * 供应商未处理的情况下，直接删除
     * 撤销申诉
     * <AUTHOR> on 2024/6/6:15:54
     * @param bo
     * @return void
     */
    @Transactional(rollbackFor = Exception.class)
    @RepeatSubmit()
    @PostMapping("/revoke")
    public R<Void> revoke(@RequestBody @Valid BaseIdBo bo) {
        Long reportId = reportLossService.getReportLossId(bo.getId());
        reportLossService.revoke( reportId, bo.getId());
        return toAjax(Boolean.TRUE);
    }



    /**
     * 各状态数量统计
     * 目前只统计待审核、驳回、申诉中
     * <AUTHOR> on 2024/6/11:9:47
     * @return cn.xianlink.common.core.domain.R<java.util.List<cn.xianlink.order.domain.vo.report.ReportNumStatisticsVo>>
     */
    @GetMapping("/numStatistics")
    public R<List<ReportNumStatisticsVo>> numStatistics() {
        return R.ok(reportLossService.numStatistics(LoginHelper.getLoginUser().getRelationId(),null,null,null, null));
    }



    /**
     * IM报损单列表
     */
    @PostMapping("/lossOrderlist")
    public R<TableDataInfo<ReportLossMiniListVo>> lossOrderlist(@RequestBody ReportLossMiniListSearchBo bo) {
        return R.ok(reportLossService.lossOrderlist(bo));
    }

}
