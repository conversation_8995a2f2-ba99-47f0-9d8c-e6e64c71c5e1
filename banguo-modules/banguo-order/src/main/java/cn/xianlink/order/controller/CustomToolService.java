package cn.xianlink.order.controller;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.basic.api.RemoteCityWhService;
import cn.xianlink.basic.api.RemotePortageTeamService;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.domain.bo.RemotePortageTeamQueryBo;
import cn.xianlink.basic.api.domain.bo.RemotePortageWorkerQueryBo;
import cn.xianlink.basic.api.domain.bo.RemoteRegionLogisticsQueryBo;
import cn.xianlink.basic.api.domain.vo.*;
import cn.xianlink.common.api.enums.order.DeliveryStatusEnum;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.api.vo.RemoteSupplierSkuFileVo;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.domain.delivery.bo.SupDeliveryGoodsEditBo;
import cn.xianlink.order.domain.delivery.vo.SupDeliveryGoodsVo;
import cn.xianlink.order.domain.delivery.vo.SupDeliveryVo;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.bo.RemoteQueryInfoListBo;
import cn.xianlink.product.api.domain.bo.RemoteQuerySkuIdBo;
import cn.xianlink.product.api.domain.bo.RemoteQuerySupplierSkuFileBo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.resource.api.domain.RemoteOssBo;
import cn.xianlink.resource.api.domain.RemoteOssVo;
import cn.xianlink.system.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
public class CustomToolService {
    @DubboReference
    private final transient RemoteRegionLogisticsService remoteRegionLogisticsService;
    @DubboReference
    private final transient RemoteCityWhService remoteCityWhService;
    @DubboReference
    private final transient RemoteRegionWhService remoteRegionWhService;
    @DubboReference
    private final transient RemoteSupplierSkuService remoteSupplierSkuService;
    @DubboReference
    private final transient RemotePortageTeamService remotePortageTeamService;

    @DubboReference
    private final transient RemoteFileService remoteFileService;
    public final String ossBusinessType = "sup_delivery_goods";
    public final String ossTagPic = "pic";
    public final String ossTagVideo = "video";

    /**
     * 获取最新销售日期
     * @param regionWhId
     * @param date
     * @return
     */
    public LocalDate getLastSaleDate(Long regionWhId, LocalDate date) {
        if (date == null) {
            RemoteRegionWhVo vo = remoteRegionWhService.queryById(regionWhId);
            return SaleDateUtil.getSaleDate(vo.getDeliveryTimeEnd());
        }
        return date;
    }

    /**
     * 计算加载 送货单处理处理倒计时时间戳
     * @param vo
     * @param hour
     */
    public void loadDiffDealTimestamp(SupDeliveryVo vo) {
        if (vo.getDiffStatus().intValue() == DeliveryStatusEnum.ENTRUCK_HAVE_DIFF.getCode()) {
            vo.setDiffDealTimeEnd(DateUtil.offsetHour(vo.getStatusTime(), 1));
            vo.setDiffDealTimestamp(vo.getDiffDealTimeEnd().getTime() - DateTime.now().getTime());
        }
    }


    /**
     * 查询基础物流列表
     * @param logisticsIds
     * @return
     */
    public List<RemoteRegionLogisticsVo> queryAllLogisticsList(List<Long> logisticsIds) {
        RemoteRegionLogisticsQueryBo queryBo = new RemoteRegionLogisticsQueryBo();
        queryBo.setLogisticsIds(logisticsIds);
        queryBo.setAllStatus(true);
        return remoteRegionLogisticsService.queryList(queryBo);
    }
    private List<RemoteRegionLogisticsVo> queryLogisticsList(Long regionWhId, List<Long> cityWhIds) {
        RemoteRegionLogisticsQueryBo queryBo = new RemoteRegionLogisticsQueryBo();
        queryBo.setRegionWhId(regionWhId);
        queryBo.setCityWhIds(cityWhIds);
        return remoteRegionLogisticsService.queryList(queryBo);
    }
    /**
     * 查询基础物流名称Map
     * @param logisticsIds
     * @return
     */
    public Map<Long, String> queryLogisticsNameMap(List<Long> logisticsIds) {
        return queryAllLogisticsList(logisticsIds).stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, RemoteRegionLogisticsVo::getLogisticsName, (key1, key2) -> key2));
    }
    public String queryLogisticsName(Long logisticsId) {
        List<RemoteRegionLogisticsVo> list = queryAllLogisticsList(CollectionUtil.toList(logisticsId));
        if (list.size() > 0) {
            return list.get(0).getLogisticsName();
        }
        return null;
    }
    public Map<Long, Integer> queryLogisticsIsEarlyEndMap(List<Long> logisticsIds, LocalDate saleDate) {
        List<RemoteRegionLogisticsVo> list = queryAllLogisticsList(logisticsIds);
        Map<Long, Integer> map = new HashMap<>();
        for (RemoteRegionLogisticsVo vo : list) {
            map.put(vo.getId(), vo.getCurrentIsEarlyEnd(saleDate));
        }
        return map;
    }

    /**
     * 查询城市仓名称Map
     * @param cityIds
     * @return
     */
    public Map<Long, String> queryCityNameMap(List<Long> cityIds) {
        return remoteCityWhService.queryList(cityIds).stream().collect(Collectors.toMap(RemoteCityWhVo::getId, RemoteCityWhVo::getName, (key1, key2) -> key2));
    }
    public String queryCityName(Long cityId) {
        return remoteCityWhService.queryById(cityId).getName();
    }

    /**
     * 获取总仓物流线（配置装车位 或 直属城市仓）
     * @param regionWhId
     * @return
     */
    public LinkedHashMap<Long, RemoteRegionWhParkingVo> getDeliveryLogistics(Long regionWhId, boolean isDelivery) {
        // 查询总仓+车位列表
        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryParkingListById(regionWhId);
        List<RemoteRegionWhParkingVo> logisticsIdVoList = new ArrayList<>();
        Map<Long, String> parkingVoMap = regionWhVo.getParkingList().stream().collect(Collectors.toMap(RemoteRegionWhParkingVo::getLogisticsId, RemoteRegionWhParkingVo::getParkingNo, (key1, key2) -> key2));
        if (isDelivery) {
            if (CollUtil.isNotEmpty(regionWhVo.getCityWhIdList())) {
                List<RemoteRegionLogisticsVo> list = queryLogisticsList(regionWhVo.getId(), regionWhVo.getCityWhIdList());
                if (list.size() > 0) {
                    // 直属城市仓物流线
                    logisticsIdVoList.addAll(list.stream().map(e -> {
                        RemoteRegionWhParkingVo entity = new RemoteRegionWhParkingVo();
                        entity.setLogisticsId(e.getId());
                        entity.setLogisticsName(e.getLogisticsName());
                        entity.setParkingNo(parkingVoMap.containsKey(e.getId()) ? parkingVoMap.get(e.getId()) : "属地仓");
                        entity.setCityWhId(e.getCityWhId());
                        entity.setIsAffiliated(1);
                        return entity;
                    }).collect(Collectors.toList()));
                }
            }
            // 有车位的物流线
            logisticsIdVoList.addAll(regionWhVo.getParkingList());
        } else {
            // 查询总仓基础物流线, 并对比车位物流线，或直属城市仓，过滤后剩余物流线为暂不装车
            List<RemoteRegionLogisticsVo> list = queryLogisticsList(regionWhVo.getId(), null)
                    .stream().filter(p -> StringUtils.isEmpty(parkingVoMap.get(p.getId())) && !regionWhVo.getCityWhIdList().contains(p.getCityWhId())).collect(Collectors.toList());
            if (list.size() > 0) {
                logisticsIdVoList.addAll(list.stream().map(e -> {
                    RemoteRegionWhParkingVo entity = new RemoteRegionWhParkingVo();
                    entity.setLogisticsId(e.getId());
                    entity.setLogisticsName(e.getLogisticsName());
                    entity.setCityWhId(e.getCityWhId());
                    return entity;
                }).collect(Collectors.toList()));
            }
        }
        if (logisticsIdVoList.size() == 0) {
            return new LinkedHashMap();
        }
        // 去重
        return logisticsIdVoList.stream().collect(Collectors.toMap(RemoteRegionWhParkingVo::getLogisticsId, Function.identity(), (key1, key2) -> key1, LinkedHashMap::new));
    }

    /**
     * 获取供应商商品sku包装图片列表
     * @param supplierSkuIds
     * @return
     */
    public Map<Long, List<RemoteSupplierSkuFileVo>> getSkuFileListMap(List<Long> supplierSkuIds) {
        if (supplierSkuIds.size() == 0) {
            return new HashMap<>();
        }
        RemoteQuerySupplierSkuFileBo fileBo = new RemoteQuerySupplierSkuFileBo();
        fileBo.setSupplierSkuIdList(supplierSkuIds);
        List<RemoteSupplierSkuFileVo> fileVos = remoteSupplierSkuService.querySupplierSkuFile(fileBo);
        return fileVos.stream().collect(Collectors.groupingBy(RemoteSupplierSkuFileVo::getSupplierSkuId, Collectors.toList()));
    }
    public Map<Long, RemoteSupplierSkuInfoVo> getSkuInfoMap(List<Long> supplierSkuIds) {
        if (supplierSkuIds.size() == 0) {
            return new HashMap<>();
        }
        RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
        listBo.setSupplierSkuIdList(supplierSkuIds);
        List<RemoteSupplierSkuInfoVo> skuInfoVos = remoteSupplierSkuService.queryInfoList(listBo);
        return skuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
    }

    public List<Long> getSkuIdList(LocalDate saleDate, Long regionWhId, String buyerCode) {
        RemoteQuerySkuIdBo skuBo = new RemoteQuerySkuIdBo();
        skuBo.setSaleDate(saleDate);
        skuBo.setRegionWhId(regionWhId);
        skuBo.setBuyerCode(buyerCode);
        return remoteSupplierSkuService.querySkuIdList(skuBo);
    }
    public List<Long> getSkuIdList(LocalDate saleDate, Long regionWhId, Long basPortageTeamId) {
        RemoteQuerySkuIdBo skuBo = new RemoteQuerySkuIdBo();
        skuBo.setSaleDate(saleDate);
        skuBo.setRegionWhId(regionWhId);
        skuBo.setBasPortageTeamId(basPortageTeamId);
        return remoteSupplierSkuService.querySkuIdList(skuBo);
    }

    public Map<Long, String> getTeamNameMap(List<Long> basPortageTeamIds) {
        if (basPortageTeamIds.size() == 0) {
            return new HashMap<>();
        }
        RemotePortageTeamQueryBo teamBo = new RemotePortageTeamQueryBo();
        teamBo.setTeamIds(basPortageTeamIds);
        teamBo.setAllStatus(true);
        return remotePortageTeamService.queryList(teamBo)
                .stream().collect(Collectors.toMap(RemotePortageTeamVo::getId, RemotePortageTeamVo::getTeamName, (key1, key2) -> key2));
    }

    public Long getBasPortageTeamIdAndCheck(Long regionWhId, Long userId) {
        // 用户装卸队查询权限
        RemotePortageWorkerQueryBo workerBo = new RemotePortageWorkerQueryBo();
        workerBo.setWorkerId(userId);
        workerBo.setRegionWhId(regionWhId);
        workerBo.setAllStatus(true);
        List<RemotePortageWorkerVo> workerVos = remotePortageTeamService.queryWorkers(workerBo);
        if (workerVos.size() == 0) {
            throw new ServiceException("无所属装卸队成员身份");
        }
        RemotePortageWorkerVo workerVo = workerVos.get(0);
        if (workerVo.getStatus().intValue() == YNStatusEnum.DISABLE.getCode()) {
            throw new ServiceException("您的装卸队成员身份已被禁用");
        }
        return workerVo.getTeamId();
    }

    public Map<Long, Long> getSkuIdMap(List<Long> supplierSkuIds) {
        if (supplierSkuIds.size() == 0) {
            return new HashMap<>();
        }
        return remoteSupplierSkuService.querySimpleInfoList(supplierSkuIds)
                .stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, RemoteSupplierSkuInfoVo::getSkuId));
    }

    public void loadInspectOssUrl(SupDeliveryGoodsVo vo) {
        String ossKeyword = vo.getId().toString();
        List<RemoteOssVo> ossVos = remoteFileService.select(ossBusinessType, ossKeyword);
        vo.setPicUrlList(ossVos.stream().filter(p -> ossTagPic.equals(p.getTag())).map(RemoteOssVo::getUrl).collect(Collectors.toList()));
        vo.setVideoUrlList(ossVos.stream().filter(p -> ossTagVideo.equals(p.getTag())).map(RemoteOssVo::getUrl).collect(Collectors.toList()));
    }

    public void inspectOssSave(SupDeliveryGoodsEditBo bo) {
        final String ossKeyword = String.valueOf(bo.getId());
        List<RemoteOssBo> ossBos = new ArrayList<>();
        if (CollUtil.isNotEmpty(bo.getPicUrlList())) {
            ossBos.addAll(bo.getPicUrlList().stream().map(url -> {
                RemoteOssBo ossBo = new RemoteOssBo();
                ossBo.setBusinessType(ossBusinessType);
                ossBo.setKeyword(ossKeyword);
                ossBo.setTag(ossTagPic);
                ossBo.setUrl(url);
                return ossBo;
            }).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(bo.getVideoUrlList())) {
            ossBos.addAll(bo.getVideoUrlList().stream().map(url -> {
                RemoteOssBo ossBo = new RemoteOssBo();
                ossBo.setBusinessType(ossBusinessType);
                ossBo.setKeyword(ossKeyword);
                ossBo.setTag(ossTagVideo);
                ossBo.setUrl(url);
                return ossBo;
            }).collect(Collectors.toList()));
        }
        remoteFileService.delete(ossBusinessType, ossKeyword);
        if (ossBos.size() > 0) {
            remoteFileService.batchInsert(ossBos);
        }
    }

    public LoginUser getLoginUser() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            loginUser = new LoginUser();
            loginUser.setUserCode("000");
            loginUser.setRealName("系统用户");
        }
        return loginUser;
    }
}
