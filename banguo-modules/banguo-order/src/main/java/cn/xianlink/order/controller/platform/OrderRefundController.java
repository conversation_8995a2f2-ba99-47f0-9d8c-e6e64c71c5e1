package cn.xianlink.order.controller.platform;

import cn.xianlink.common.api.enums.order.AccountTypeStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.domain.bo.refundRecord.DifferenceRefundPageBO;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.domain.vo.refundRecord.DifferenceRefundDetailVO;
import cn.xianlink.order.domain.vo.refundRecord.DifferenceRefundVO;
import cn.xianlink.order.domain.vo.refundRecord.RefundRecordDetailVO;
import cn.xianlink.order.domain.vo.refundRecord.RefundRecordVO;
import cn.xianlink.order.service.IRefundRecordService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import cn.xianlink.common.web.core.BaseController;

import java.util.List;

/**
 * 集采平台-退货退款/差额退款
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@Validated
@RequiredArgsConstructor
@RestController("platformOrderRefundController")
@RequestMapping("/order/platform/refund")
public class OrderRefundController extends BaseController {

    private final IRefundRecordService refundRecordService;

    @PostMapping("/difPage")
    @ApiOperation(value = "分页查询差额退款列表")
    public R<TableDataInfo<DifferenceRefundVO>> difPage(@RequestBody DifferenceRefundPageBO bo){
        if (bo.getCustomerId() == null && LoginHelper.getLoginUser().getRelationId() == null) {
            return R.ok(TableDataInfo.build());
        }else {
            bo.setCustomerId(LoginHelper.getLoginUser().getRelationId());
        }
        return R.ok(refundRecordService.difPage(bo));
    }

    @GetMapping("/getDifById/{id}")
    @ApiOperation(value = "获取差额退款单项详情")
    public R<DifferenceRefundDetailVO> getDifById(@PathVariable("id") Long id){
        return R.ok(refundRecordService.getDifById(id));
    }

    @GetMapping("/getDifRefundCount")
    @ApiOperation(value = "获取差额退款单各状态数量")
    public R<CommonStatusCountVO> getDifRefundCount(){
        return R.ok(refundRecordService.getDifRefundCount(AccountTypeStatusEnum.CUSTOMER.getCode()));
    }

    @PostMapping("/refundPage")
    @ApiOperation(value = "分页查询退货退款列表")
    public R<TableDataInfo<RefundRecordVO>> refundPage(@RequestBody DifferenceRefundPageBO bo){
        bo.setCustomerId(LoginHelper.getLoginUser().getRelationId());
        return R.ok(refundRecordService.refundPage(bo));
    }

    @GetMapping("/getRefundById/{id}")
    @ApiOperation(value = "获取退货退款单项详情")
    public R<RefundRecordDetailVO> getRefundById(@PathVariable("id") Long id){
        return R.ok(refundRecordService.getRefundById(id));
    }

    @GetMapping("/getRefundCount")
    @ApiOperation(value = "获取退货退款单各状态数量")
    public R<List<CommonStatusCountVO>> getRefundCount(){
        return R.ok(refundRecordService.getRefundCount(AccountTypeStatusEnum.CUSTOMER.getCode()));
    }

}
