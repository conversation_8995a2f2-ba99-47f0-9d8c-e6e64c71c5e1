package cn.xianlink.order.controller.city;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.RemoteCityWhPlaceService;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.api.constant.DeductionInfoStatusEnum;
import cn.xianlink.order.api.constant.DeductionInfoEnum;
import cn.xianlink.order.domain.bo.DeductionInfoBackStageBO;
import cn.xianlink.order.domain.bo.DeductionInfoBo;
import cn.xianlink.order.domain.vo.DeductionInfoVo;
import cn.xianlink.order.domain.vo.DeductionReasonVO;
import cn.xianlink.order.domain.vo.DeductionStatusCountVO;
import cn.xianlink.order.service.IDeductionInfoService;
import cn.xianlink.system.api.model.LoginUser;
import io.swagger.annotations.ApiOperation;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 扣款单
 *
 * <AUTHOR>
 * @date 2024-06-01
 * @folder 城市仓端(小程序)/订单/扣款单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/city/deduction")
@CustomLog
public class DeductionInfoCityController extends BaseController {

    private final IDeductionInfoService deductionInfoService;

    @DubboReference
    private final transient RemoteRegionLogisticsService remoteRegionLogisticsService;

    @DubboReference
    private final transient RemoteCityWhPlaceService remoteCityWhPlaceService;

    /**
     * 查询扣款单列表
     */
    @PostMapping("/list")
    @ApiOperation("分页查询扣款单列表")
    public R<TableDataInfo<DeductionInfoVo>> list(@RequestBody DeductionInfoBackStageBO bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isEmpty(loginUser) || ObjectUtil.isEmpty(loginUser.getRelationId())) {
            throw new ServiceException("用户未登录");
        }

        DeductionInfoBo infoBo = new DeductionInfoBo();
        if (ObjectUtil.isNotEmpty(bo) || ObjectUtil.isNotEmpty(bo.getCreateTimeStart()) || ObjectUtil.isNotEmpty(bo.getCreateTimeEnd())
                || ObjectUtil.isNotEmpty(bo.getSaleTimeStart()) || ObjectUtil.isNotEmpty(bo.getSaleTimeEnd())) {
            infoBo = BeanUtil.toBean(bo, DeductionInfoBo.class);
            infoBo.setStartTime(bo.getCreateTimeStart());
            infoBo.setEndTime(bo.getCreateTimeEnd());
        }
        if (CollectionUtil.isNotEmpty(bo.getPlaceIdList())) {
            infoBo.setLogisticsIdList(remoteRegionLogisticsService.queryIdByCityUserFiled(LoginHelper.getLoginUser().getUserId(), bo.getPlaceIdList()));
        }else if (CollectionUtil.isEmpty(bo.getPlaceIdList()) && CollectionUtil.isEmpty(bo.getPlaceIdLevel2List())){
            infoBo.setLogisticsIdList(remoteRegionLogisticsService.queryIdByCityUser(LoginHelper.getLoginUser().getUserId()));
            bo.setPlaceIdLevel2List(remoteCityWhPlaceService.getPlaceIdsByUser(LoginHelper.getLoginUser().getUserId()));
        }
        infoBo.setType(DeductionInfoEnum.CITY.getCode());
        infoBo.setCommonId(loginUser.getRelationId());
        infoBo.setPlaceIdLevel2List(bo.getPlaceIdLevel2List());
        return R.ok(deductionInfoService.queryPageList(infoBo, pageQuery));
    }



    /**
     * 获取扣款单详细信息
     *
     * @param id 主键
     */
    @GetMapping("/getDeductionInfoById")
    @ApiOperation("查询扣款单详情")
    public R<DeductionInfoVo> getDeductionInfoById(@RequestParam("id") Long id) {
        return R.ok(deductionInfoService.queryById(id));
    }

    @GetMapping("/listDeductionReasonVO")
    @ApiOperation("扣款单原因数据字典列表")
    public R<List<DeductionReasonVO>> listDeductionReasonVO() {
        return R.ok(deductionInfoService.listDeductionReasonVO("gyskkyy"));
    }


    @GetMapping("/getBillInfoByTypeAndCode")
    @ApiOperation("根据关联单据类型和关联单据查询不同得单据信息")
    public R<Object> getBillInfoByTypeAndCode(@RequestParam("billType") Integer billType,
                                              @RequestParam("billCode") String billCode) {
        return R.ok(deductionInfoService.getBillInfoByTypeAndCode(billType, billCode));
    }



    /**
     * 获取不同扣款单待结算状态的数量
     *
     */
    @GetMapping("/getDeductionStatusCount")
    @ApiOperation("获取不同扣款单待结算状态的数量")
    public R<DeductionStatusCountVO> getDeductionStatusCount(@RequestParam(value = "regionWhId", required = false) Long regionWhId,
                                                             @RequestParam("type") Integer type) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isEmpty(loginUser) || ObjectUtil.isEmpty(loginUser.getRelationId())) {
            throw new ServiceException("用户未登录");
        }
        DeductionInfoBo bo = new DeductionInfoBo();
        bo.setType(DeductionInfoEnum.loadByCode(type).getCode());
        if (ObjectUtil.isNotEmpty(regionWhId)) {
            bo.setRegionWhId(regionWhId);
        }
        bo.setCommonId(loginUser.getRelationId());
        bo.setStatus(DeductionInfoStatusEnum.COMMITTED.getCode());

        log.info("获取不同扣款单待结算状态的数量bo:{}",bo);
        System.err.println(loginUser);
        return R.ok(deductionInfoService.getDeductionStatusCount(bo));
    }
}
