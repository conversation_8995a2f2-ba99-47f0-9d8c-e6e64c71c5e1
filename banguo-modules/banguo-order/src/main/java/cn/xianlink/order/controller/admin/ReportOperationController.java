package cn.xianlink.order.controller.admin;

import cn.hutool.core.collection.CollUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.order.domain.bo.refundRecord.CreateRefundRecordBO;
import cn.xianlink.order.domain.operation.ReportRefundDto;
import cn.xianlink.order.service.IRefundRecordService;
import cn.xianlink.order.service.IReportLossService;
import com.baomidou.lock.annotation.Lock4j;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 * 报损运维接口
 * <AUTHOR> xiaodaibing on 2024-12-12 11:03
 * @folder 报损运维接口
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/report/operation")
@RefreshScope
public class ReportOperationController {

    private final IReportLossService reportLossService;
    private final IRefundRecordService refundRecordService;

    /**
     * 运维开关，默认为0,开启为1，每次运维前需要手动开启
     */
    @Value("${operatorSwitch:0}")
    private String operatorSwitch;

    /**
     * 报损退钱运维接口
     * <AUTHOR> on 2024/12/12:11:05
     * @param dto
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     */
    @Lock4j(keys = "#dto.reportId")
    @RepeatSubmit
    @PostMapping("/refund")
    public R<Void> refund(@RequestBody ReportRefundDto dto) {
        if (!enable()) {
            return R.warn("请先开启运维开关");
        }
        reportLossService.refundAgain(dto);
        return R.ok();
    }

    /**
     * 释放占用
     * <AUTHOR> on 2024/12/12:15:26
     * @param refundCode
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     */
    @Lock4j(keys = "#refundCode")
    @GetMapping("/releaseOccupy/{refundCode}")
    public R<Void> releaseOccupy(@PathVariable String refundCode) {
        if (!enable()) {
            return R.warn("请先开启运维开关");
        }
        refundRecordService.releaseOccupy(refundCode);
        return R.ok();
    }

    /**
     * 批量释放占用
     * <AUTHOR> on 2024/12/12:15:26
     * @param values
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     */
    @PostMapping("/batchReleaseOccupy")
    public R<Void> batchReleaseOccupy(@RequestBody List<String> values) {
        if (!enable()) {
            return R.warn("请先开启运维开关");
        }
        if(CollUtil.isNotEmpty(values)) {
            for (String value : values) {
                refundRecordService.releaseOccupy(value);
            }
        }
        return R.ok();
    }

    /**
     * 关闭退款
     * <AUTHOR> on 2024/12/12:15:26
     * @param refundCode
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     */
    @PostMapping("/batchCloseRefund")
    public R<Void> closeRefund(@RequestBody List<String> values) {
        if (!enable()) {
            return R.warn("请先开启运维开关");
        }
        if(CollUtil.isNotEmpty(values)) {
            for (String value : values) {
                refundRecordService.closeRefund(value);
            }
        }
        return R.ok();
    }


    private boolean enable() {
        return "1".equals(operatorSwitch);
    }

    /**
     * 报损退钱运维接口
     */
    @Lock4j(keys = "#bo.orderItemId")
    @RepeatSubmit
    @PostMapping("/adminRefund")
    public R<Void> adminRefund(@RequestBody CreateRefundRecordBO bo) {
        if (!enable()) {
            return R.warn("请先开启运维开关");
        }
        refundRecordService.refundByAdmin(bo);
        return R.ok();
    }

}
