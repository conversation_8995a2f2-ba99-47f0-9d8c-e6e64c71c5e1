package cn.xianlink.order.controller.region;

import cn.xianlink.common.api.enums.order.AccountTypeStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.stockOut.StockoutPageBO;
import cn.xianlink.order.domain.bo.stockOut.StockoutUpdateBO;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutDetailVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutPageVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutRecordDetailVO;
import cn.xianlink.order.service.IStockoutRecordService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 总仓助手-缺货少货
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@Validated
@RequiredArgsConstructor
@RestController("regionStockoutController")
@RequestMapping("/order/region/stockout")
public class StockoutController extends BaseController {

    private final IStockoutRecordService stockoutRecordService;

    @PostMapping("/stockoutPage")
    @ApiOperation(value = "缺货少货列表")
    public R<TableDataInfo<StockoutPageVO>> stockoutPage(@RequestBody StockoutPageBO bo){
        return R.ok(stockoutRecordService.stockoutPage(bo));
    }

    @GetMapping("/getStockoutById/{id}")
    @ApiOperation(value = "缺货少货详情")
    public R<StockoutDetailVO> getStockoutById(@PathVariable("id") Long id){
        return R.ok(stockoutRecordService.getStockoutById(id));
    }

    @GetMapping("/getStockoutByCode/{code}")
    @ApiOperation(value = "缺货少货详情")
    public R<StockoutDetailVO> getStockoutByCode(@PathVariable("code") String code){
        return R.ok(stockoutRecordService.getStockoutByCode(code));
    }

    @GetMapping("/getStockoutOrder/{id}")
    @ApiOperation(value = "查询关联订单")
    public R<List<StockoutRecordDetailVO>> getStockoutOrder(@PathVariable("id") Long id){
        return R.ok(stockoutRecordService.getStockoutOrder(id,AccountTypeStatusEnum.REGION.getCode()));
    }

    @GetMapping("/getStockoutCount")
    @ApiOperation(value = "获取缺货少货单各状态数量")
    public R<List<CommonStatusCountVO>> getStockoutCount(){
        return R.ok(stockoutRecordService.getStockoutCount(AccountTypeStatusEnum.REGION.getCode()));
    }

    @GetMapping("/stockoutConfirm/{id}")
    @RepeatSubmit()
    @ApiOperation(value = "缺货少货单确认")
    public R<Void> stockoutConfirm(@PathVariable("id") Long id){
        stockoutRecordService.stockoutConfirm(id);
        return R.ok();
    }

    @GetMapping("/stockoutInvalid/{id}")
    @RepeatSubmit()
    @ApiOperation(value = "缺货少货单作废")
    public R<Void> stockoutInvalid(@PathVariable("id") Long id){
        stockoutRecordService.stockoutInvalid(id);
        return R.ok();
    }

    @PostMapping("/updateStockout")
    @RepeatSubmit()
    @ApiOperation(value = "更新缺货少货单数量")
    public R<Void> updateStockout(@RequestBody StockoutUpdateBO bo){
        stockoutRecordService.updateStockout(bo);
        return R.ok();
    }

//    @PostMapping("/createStockout")
//    @RepeatSubmit()
//    @ApiOperation(value = "(不采购)创建缺货单")
//    public R<Void> createStockout(@RequestBody List<BatchCreateStockoutBO> bo){
//        stockoutRecordService.createStockout(bo);
//        return R.ok();
//    }

}
