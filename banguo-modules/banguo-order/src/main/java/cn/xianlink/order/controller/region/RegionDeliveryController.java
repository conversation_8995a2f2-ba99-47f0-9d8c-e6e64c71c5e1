package cn.xianlink.order.controller.region;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.bi.api.IRemoteBiSkuService;
import cn.xianlink.bi.api.domain.bo.RemoteSkuLossBo;
import cn.xianlink.bi.api.domain.vo.RemoteSkuLossVo;
import cn.xianlink.common.api.enums.order.DeliveryStatusEnum;
import cn.xianlink.common.api.enums.order.OrderBusinessTypeEnum;
import cn.xianlink.common.api.enums.order.OrderDeliverSourceEnum;
import cn.xianlink.common.api.vo.RemoteSupplierSkuFileVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.config.SkuGlobalProperties;
import cn.xianlink.order.controller.CustomToolService;
import cn.xianlink.order.controller.sup.SupDeliveryAffairService;
import cn.xianlink.order.domain.delivery.bo.*;
import cn.xianlink.order.domain.delivery.vo.RwWaitDeliveryVo;
import cn.xianlink.order.domain.delivery.vo.SupDeliveryGoodsVo;
import cn.xianlink.order.domain.delivery.vo.SupDeliveryVo;
import cn.xianlink.order.domain.delivery.vo.SupWaitDeliveryGoodsVo;
import cn.xianlink.order.domain.entruck.bo.RwEntruckGoodsOrderQueryBo;
import cn.xianlink.order.domain.entruck.bo.RwNotEntruckQueryBo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsOrderSimplifyVo;
import cn.xianlink.order.domain.entruck.vo.RwNotEntruckLogisticsVo;
import cn.xianlink.order.domain.entruck.vo.RwNotEntruckSupplierVo;
import cn.xianlink.order.service.IRwEntruckGoodsService;
import cn.xianlink.order.service.ISupDeliveryGoodsService;
import cn.xianlink.order.service.ISupDeliveryService;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.system.api.RemoteDeptService;
import jakarta.validation.constraints.NotNull;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 总仓-送货单
 *
 * <AUTHOR>
 * @date 2024-05-28
 * @folder 总仓助手(小程序)/送货单
 */
@Validated
@CustomLog
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/region/delivery")
public class RegionDeliveryController extends BaseController {

    private final transient ISupDeliveryService deliveryService;
    private final transient ISupDeliveryGoodsService deliveryGoodsService;
    private final transient IRwEntruckGoodsService entruckGoodsService;
    private final transient CustomToolService customToolService;
    private final transient RegionDeliveryAffairService regionDeliveryAffairService;
    private final transient SupDeliveryAffairService supDeliveryAffairService;
    @DubboReference
    private final transient RemoteSupplierService remoteSupplierService;
    @DubboReference
    private final transient IRemoteBiSkuService remoteBiSkuService;
    @DubboReference
    private final transient RemoteDeptService remoteDeptService;
    @Autowired
    private SkuGlobalProperties skuGlobalProperties;

//    /**
//     * 查询-暂不装车列表-计数(商品需求件数合计)
//     */
//    @GetMapping("/notEntruckLogisticsCount/{regionWhId}")
//    public R<Integer> notEntruckLogisticsCount(@NotNull(message = "总仓id不能为空") @PathVariable Long regionWhId) {
//        RwNotEntruckQueryBo bo = new RwNotEntruckQueryBo();
//        bo.setRegionWhId(regionWhId);
//        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), null));
//        List<RwNotEntruckLogisticsVo> list = regionDeliveryAffairService.notEntruckLogisticsList(bo);
//        return R.ok(list.stream().mapToInt(RwNotEntruckLogisticsVo::getSkuQuantity).sum());
//    }
//    /**
//     * 查询-待送货列表-计数(待送货件数合计)
//     */
//    @GetMapping("/waitDeliverySupCount/{regionWhId}")
//    public R<Integer> waitDeliverySupCount(@NotNull(message = "总仓id不能为空") @PathVariable Long regionWhId) {
//        RwNotEntruckQueryBo bo = new RwNotEntruckQueryBo();
//        bo.setRegionWhId(regionWhId);
//        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), null));
//        List<RwWaitDeliveryVo> list = regionDeliveryAffairService.waitDeliveryList(bo);
//        return R.ok(list.stream().mapToInt(RwWaitDeliveryVo::getWaitDeliveryQuantity).sum());
//    }
//    /**
//     * 首页-送货单-计数(差异待处理单据数)
//     */
//    @GetMapping("/deliveryCount/{regionWhId}")
//    public R<Long> deliveryCount(@NotNull(message = "总仓id不能为空") @PathVariable Long regionWhId) {
//        SupDeliveryQueryBo bo = new SupDeliveryQueryBo();
//        bo.setRegionWhId(regionWhId);
//        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), null));
//        return R.ok(deliveryService.deliveryCount(bo));
//    }
//    /**
//     * 首页-质检记录-计数(待质检单据数)
//     */
//    @GetMapping("/waitInspectCount/{regionWhId}")
//    public R<Long> waitInspectCount(@NotNull(message = "总仓id不能为空") @PathVariable Long regionWhId) {
//        SupDeliveryQueryBo bo = new SupDeliveryQueryBo();
//        bo.setRegionWhId(regionWhId);
//        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), null));
//        return R.ok(deliveryService.waitInspectCount(bo));
//    }

    /**
     * 查询-暂不装车列表(物流线列表)【停用】
     */
    @PostMapping("/notEntruckLogisticsList")
    public R<List<RwNotEntruckLogisticsVo>> notEntruckLogisticsList(@Validated @RequestBody RwNotEntruckQueryBo bo) {
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        return R.ok(regionDeliveryAffairService.notEntruckLogisticsList(bo));
    }

    /**
     * 查询-暂不装车列表(明细-供应商列表)【停用】
     */
    @PostMapping("/notEntruckSupplierIdList")
    public R<List<RwNotEntruckSupplierVo>> notEntruckSupplierIdList(@Validated @RequestBody RwNotEntruckQueryBo bo) {
        if (ObjectUtil.isNull(bo.getSaleDate())) {
            throw new ServiceException("销售日期不能为空");
        }
        if (ObjectUtil.isNull(bo.getLogisticsId())) {
            throw new ServiceException("物流线id不能为空");
        }
        return R.ok(regionDeliveryAffairService.notEntruckSupplierIdList(bo));
    }

    /**
     * 查询-暂不装车列表(物流线列表+供应商列表)
     */
    @PostMapping("/notEntruckList")
    public R<List<RwNotEntruckLogisticsVo>> notEntruckList(@Validated @RequestBody RwNotEntruckQueryBo bo) {
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        return R.ok(regionDeliveryAffairService.notEntruckList(bo));
    }

    /**
     * 查询-待送货供应商列表
     */
    @PostMapping("/waitDeliverySupList")
    public R<List<RwWaitDeliveryVo>> waitDeliverySupList(@Validated @RequestBody RwNotEntruckQueryBo bo) {
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        return R.ok(regionDeliveryAffairService.waitDeliveryList(bo));
    }

    /**
     * 查询-待送货商品列表
     */
    @PostMapping("/waitDeliveryGoodsList")
    public R<List<SupWaitDeliveryGoodsVo>> waitDeliveryGoodsList(@Validated @RequestBody SupWaitDeliveryQueryBo bo) {
        if (ObjectUtil.isNull(bo.getSaleDate())) {
            throw new ServiceException("销售日期不能为空");
        }
        if (ObjectUtil.isNull(bo.getSupplierId())) {
            throw new ServiceException("供应商id不能为空");
        }
        bo.setSource(OrderDeliverSourceEnum.REGION.getCode());
        bo.setNotBusinessTypes(ListUtil.toList(OrderBusinessTypeEnum.BUSINESS_TYPE40.getCode()));
        List<SupWaitDeliveryGoodsVo> list = supDeliveryAffairService.waitDeliveryList(bo, bo.getIsDelivery());
        list.sort(Comparator.comparingInt(SupWaitDeliveryGoodsVo::getBusinessType));
        return R.ok(list);
    }

    /**
     * 待送货商品-不采购
     */
    @RepeatSubmit()
    @PostMapping("/waitDeliveryNoPurchase")
    public R<Void> waitDeliveryNoPurchase(@Validated @RequestBody SupWaitDeliveryNoPurchaseBo bo) {
        regionDeliveryAffairService.waitDeliveryNoPurchase(bo);
        return R.ok();
    }

    /**
     * 查询-送货单列表
     */
    @PostMapping("/page")
    public R<TableDataInfo<SupDeliveryVo>> page(@Validated @RequestBody SupDeliveryQueryBo bo) {
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        if (bo.getOwn() || StrUtil.isNotBlank(bo.getSpuName())) {
            bo.setBuyerCode(bo.getOwn() ? LoginHelper.getLoginUser().getUserCode() : null);
            List<Long> deliveryIds = deliveryGoodsService.deliveryIdListBySpuName(bo);
            if (deliveryIds.size() == 0) {
                return R.ok(TableDataInfo.build());
            }
            bo.setDeliveryIds(deliveryIds);
        }
        TableDataInfo<SupDeliveryVo> table = deliveryService.pageList(bo);
        if (CollUtil.isNotEmpty(table.getRows())) {
            for (SupDeliveryVo vo : table.getRows()) {
                if (vo.getCreateCode().equals(LoginHelper.getLoginUser().getUserCode())) {
                    vo.setIsShowCancel(1);
                }
            }
        }
        return R.ok(table);
    }

    /**
     * 查询-送货单详情
     */
    @GetMapping("/info/{deliveryId}")
    public R<SupDeliveryVo> info(@NotNull(message = "送货单id不能为空") @PathVariable Long deliveryId) {
        return R.ok(deliveryService.queryById(deliveryId));
    }

    /**
     * 查询-质检记录列表
     */
    @PostMapping("/inspectPage")
    public R<TableDataInfo<SupDeliveryVo>> inspectPage(@Validated @RequestBody SupDeliveryQueryBo bo) {
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        if (CollectionUtil.isNotEmpty(bo.getStatusList())) {
            // 已质检包括已装车
            if (bo.getStatusList().get(0).intValue() == DeliveryStatusEnum.COMPLETE_INSPECT.getCode()) {
                bo.setStatusList(CollectionUtil.toList(DeliveryStatusEnum.COMPLETE_INSPECT.getCode(),
                        DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode()));
            }
        } else {
            // 查全部
            bo.setStatusList(CollectionUtil.toList(DeliveryStatusEnum.WAIT_INSPECT.getCode(),
                    DeliveryStatusEnum.COMPLETE_INSPECT.getCode(),
                    DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode()));
        }
        bo.setQueryApi(3);
        bo.setInspectCode(bo.getOwn() ? LoginHelper.getLoginUser().getUserCode() : null);
        if (StrUtil.isNotBlank(bo.getSpuName())) {
            List<Long> deliveryIds = deliveryGoodsService.deliveryIdListBySpuName(bo);
            if (deliveryIds.size() == 0) {
                return R.ok(TableDataInfo.build());
            }
            bo.setDeliveryIds(deliveryIds);
        }
        TableDataInfo<SupDeliveryVo> table = deliveryService.pageList(bo);
        if (CollUtil.isNotEmpty(table.getRows())) {
            for (SupDeliveryVo vo : table.getRows()) {
                if (vo.getStatus().intValue() != DeliveryStatusEnum.WAIT_INSPECT.getCode()) {
                    vo.setStatus(DeliveryStatusEnum.COMPLETE_INSPECT.getCode());
                }
            }
        }
        return R.ok(table);
    }

    /**
     * 查询-质检记录详情
     */
    @GetMapping("/inspectInfo/{deliveryId}")
    public R<SupDeliveryVo> inspectInfo(@NotNull(message = "送货单id不能为空") @PathVariable Long deliveryId) {
        SupDeliveryVo vo = deliveryService.queryInfoById(deliveryId);
        try {
            Map<Long, Long> skuIdMap = customToolService.getSkuIdMap(vo.getGoodsList().stream().map(SupDeliveryGoodsVo::getSupplierSkuId).toList());
            if (skuIdMap.size() == 0) {
                R.ok(vo);
            }
            LocalDate saleDate = customToolService.getLastSaleDate(vo.getRegionWhId(), null);
            RemoteSkuLossBo skuBo = new RemoteSkuLossBo();
            skuBo.setSkuIdList(skuIdMap.values().stream().toList());
//            // 近七日报损率
//            skuBo.setSaleDateStart(saleDate.plusDays(-7));
//            skuBo.setSaleDateEnd(saleDate.plusDays(-1));
//            Map<Long, RemoteSkuLossVo> skuLossMap = remoteBiSkuService.getSkuLossByRemoteBI(skuBo)
//                    .stream().collect(Collectors.toMap(RemoteSkuLossVo::getSkuId, Function.identity()));
            // 今日销售件数
            skuBo.setSaleDateStart(saleDate);
            skuBo.setSaleDateEnd(saleDate);
            Map<Long, RemoteSkuLossVo> skuSaleMap = remoteBiSkuService.getSkuLossByRemoteBI(skuBo)
                    .stream().collect(Collectors.toMap(RemoteSkuLossVo::getSkuId, Function.identity()));
//            vo.getGoodsList().forEach(e -> {
//                if (skuIdMap.containsKey(e.getSupplierSkuId())) {
//                    Long skuId = skuIdMap.get(e.getSupplierSkuId());
//                    e.setTotalReportLossRate(skuLossMap.getOrDefault(skuId, new RemoteSkuLossVo()).getLossRate());
//                    e.setTodaySaleQuantity(skuSaleMap.getOrDefault(skuId, new RemoteSkuLossVo()).getSold());
//                    e.setCardVo(skuLossMap.getOrDefault(skuId, new RemoteSkuLossVo()).getCardVo());
//                }
//            });
            Map<Long, RemoteSkuLossVo> lossMap = this.getRemoteSkuLossVoMap(skuIdMap.values().stream().toList(), saleDate);

            Boolean isShowEdit;
            if (vo.getStatus().intValue() == DeliveryStatusEnum.COMPLETE_INSPECT.getCode()
                    && !entruckGoodsService.isExistEntruckQuantity(deliveryId)) {
                isShowEdit = true;
            } else {
                isShowEdit = false;
            }
            vo.getGoodsList().forEach(e -> {
                if (skuIdMap.containsKey(e.getSupplierSkuId())) {
                    Long skuId = skuIdMap.get(e.getSupplierSkuId());
                    e.setRemoteSkuLossVo(lossMap.get(skuId));
                    e.setRemoteSkuCard(lossMap.getOrDefault(skuId, new RemoteSkuLossVo()).getCardVo());
                    e.setTodaySaleQuantity(skuSaleMap.getOrDefault(skuId, new RemoteSkuLossVo()).getSold());
                }
                if (isShowEdit && e.getStatus().intValue() == DeliveryStatusEnum.INSPECT_QUALIFIED.getCode()) {
                    e.setIsShowEdit(1);
                }
            });
        } catch (Exception e) {
            log.keyword("inspectInfo", deliveryId).warn("获取bi报错", e);
        }
        return R.ok(vo);
    }

    /**
     * 二维码扫码校验（质检、装车、配货）
     * @return
     */
    @PostMapping("/scanCodeCheck")
    public R<String> scanCodeCheck(@Validated @RequestBody SupDeliveryScanCodeCheckBo bo) {
        if (ObjectUtil.isNull(bo.getRegionWhId())) {
            throw new ServiceException("总仓id不能为空");
        }
        return R.ok("成功", regionDeliveryAffairService.scanCodeCheck(bo));
    }

    /**
     * 查询-送货单商品详情
     */
    @GetMapping("/goodsInfo/{goodsId}")
    public R<SupDeliveryGoodsVo> goodsInfo(@NotNull(message = "商品id为空") @PathVariable Long goodsId) {
        SupDeliveryGoodsVo vo = deliveryGoodsService.queryById(goodsId);

        List<Long> skuIds = ListUtil.toList(vo.getSupplierSkuId());
        Map<Long, List<RemoteSupplierSkuFileVo>> fileListMap = customToolService.getSkuFileListMap(skuIds);
        Map<Long, RemoteSupplierSkuInfoVo> skuInfoMap = customToolService.getSkuInfoMap(skuIds);
        vo.setFileList(fileListMap.get(vo.getSupplierSkuId()));
        RemoteSupplierSkuInfoVo skuInfoVo = skuInfoMap.get(vo.getSupplierSkuId());
        if (ObjectUtil.isNotNull(skuInfoVo)) {
            vo.setSpuGrade(skuInfoVo.getSpuGrade());
            vo.setSpuStandards(skuInfoVo.getSpuStandards());
            vo.setProducer(skuInfoVo.getProducer());
            vo.setPackageWord(skuInfoVo.getPackageWord());
            // 产地简称
            vo.setAreaCode(skuInfoVo.getAreaCode());
            vo.setBrand(skuInfoVo.getBrand());
            vo.setShortProducer(skuInfoVo.getShortProducer());
            vo.setSpuGradeDesc(skuInfoVo.getSpuGradeDesc());
            vo.setSnapshot(skuInfoVo.getSnapshot());
            vo.setSaleType(skuInfoVo.getSaleType());
        }
        SupDeliveryVo deliveryVo = deliveryService.selectAndCheckNullById(vo.getDeliveryId());
        vo.setSupplierDeptName(deliveryVo.getSupplierDeptName());
        customToolService.loadInspectOssUrl(vo);

        LocalDate saleDate = customToolService.getLastSaleDate(deliveryVo.getRegionWhId(), null);
        try {
            Map<Long, Long> skuIdMap = customToolService.getSkuIdMap(ListUtil.toList(vo.getSupplierSkuId()));
//            RemoteSkuLossBo skuBo = new RemoteSkuLossBo();
//            skuBo.setSkuIdList(skuIdMap.values().stream().toList());
//            skuBo.setSaleDateEnd(saleDate.plusDays(-1));
//            // 近七日报损率
//            skuBo.setSaleDateStart(saleDate.plusDays(-7));
//            List<RemoteSkuLossVo> skuInfoList = remoteBiSkuService.getSkuLossByRemoteBI(skuBo);
//            if (CollUtil.isNotEmpty(skuInfoList)) {
//                vo.setTotalReportLossRate(skuInfoList.get(0).getLossRate());
//                vo.setTotalReportLossOrderRate(skuInfoList.get(0).getLossOrderRate());
//                vo.setCardVo(skuInfoList.get(0).getCardVo());
//            }
//            // 半年内不合格次数
//            skuBo.setSaleDateStart(saleDate.plusDays(-180));
//            skuInfoList = remoteBiSkuService.getSkuInspectByRemoteBI(skuBo);
//            if (CollUtil.isNotEmpty(skuInfoList)) {
//                vo.setTotalInspectCount(skuInfoList.get(0).getInspectCount());
//            }
            Long skuId = skuIdMap.get(vo.getSupplierSkuId());
            if (skuId != null) {
                Map<Long, RemoteSkuLossVo> lossMap = this.getRemoteSkuLossVoMap(ListUtil.toList(skuId), saleDate);
                vo.setRemoteSkuLossVo(lossMap.get(skuId));
                vo.setRemoteSkuCard(lossMap.getOrDefault(skuId, new RemoteSkuLossVo()).getCardVo());

            }
        } catch (Exception e) {
            log.keyword("goodsInfo", goodsId).warn("获取bi报错", e);
        }
        return R.ok(vo);
    }

    /**
     * 质检操作
     */
    @Log(title = "质检操作", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/goodsInspect")
    public R<Void> goodsInspect(@Validated @RequestBody SupDeliveryGoodsEditBo bo) {
        SupDeliveryVo deliveryVo = deliveryService.selectAndCheckNullById(bo.getDeliveryId());
        if (deliveryVo.getStatus().intValue() != DeliveryStatusEnum.WAIT_INSPECT.getCode()) {
            return R.warn("送货单非待质检状态，不允许质检操作");
        }
        SupDeliveryGoodsVo vo = deliveryGoodsService.queryById(bo.getId());
        if (bo.getStatus().intValue() == DeliveryStatusEnum.INSPECT_UNQUALIFIED.getCode()) {
            bo.setReturnQuantity(vo.getDeliveryQuantity());
            bo.setActualQuantity(0);
            bo.setInspectCount(vo.getInspectCount() + 1);
        } else {
            bo.setReturnQuantity(0);
            bo.setActualQuantity(vo.getDeliveryQuantity());
        }
        customToolService.inspectOssSave(bo);
        return toAjax(deliveryGoodsService.updateInspect(bo));
    }

    /**
     * 完成质检
     */
    @Log(title = "完成质检", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/completeInspect/{deliveryId}")
    public R<Void> completeInspect(@NotNull(message = "送货单id为空") @PathVariable Long deliveryId) {
        regionDeliveryAffairService.completeInspect(deliveryId);
        return R.ok();
    }

    /**
     * 修改质检
     * 限制：已质检无装车记录
     */
    @Log(title = "修改质检", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateInspect")
    public R<Void> updateInspect(@Validated @RequestBody SupDeliveryGoodsEditBo bo) {
        regionDeliveryAffairService.updateInspect(bo);
        return R.ok();
    }

    /**
     * 撤销质检
     */
//    @Log(title = "撤销质检", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PostMapping("/revokeInspect/{deliveryId}")
//    public R<Void> revokeInspect(@NotNull(message = "送货单id为空") @PathVariable Long deliveryId) {
//        regionDeliveryAffairService.revokeInspect(deliveryId);
//        return R.ok();
//    }

    /**
     * 差异处理-差异商品列表查询
     */
    @PostMapping("/diffGoodsList/{deliveryId}")
    public R<SupDeliveryVo> diffGoodsList(@NotNull(message = "主键不能为空") @PathVariable Long deliveryId) {
        SupDeliveryVo vo = deliveryService.selectAndCheckNullById(deliveryId);
        customToolService.loadDiffDealTimestamp(vo);
        vo.setDiffGoodsList(entruckGoodsService.customSumListByDeliveryId(vo.getId()));
        vo.getDiffGoodsList().forEach(g -> g.setSupplierDeptName(vo.getSupplierDeptName()));
        RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(vo.getSupplierId());
        if (supplierVo != null) {
            vo.setSupplierAlias(supplierVo.getAlias());
        }
        return R.ok(vo);
    }

    /**
     * 差异处理-提交
     */
    @Log(title = "已装车-差异处理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/diffHandle")
    public R<Void> diffHandle(@Validated @RequestBody SupDeliveryDiffBo bo) {
        Map<Long, Integer> diffStatusMap = bo.getGoodsList().stream().collect(Collectors.toMap(SupDeliveryDiffGoodsBo::getSupplierSkuId, SupDeliveryDiffGoodsBo::getDiffStatus, (key1, key2) -> key2));
        return toAjax(supDeliveryAffairService.diffHandle(2, bo.getDeliveryId(), diffStatusMap));
    }


    /**
     * 获取报损信息
     * @param skuIdList
     * @param saleDate
     * @return
     */
    private Map<Long, RemoteSkuLossVo> getRemoteSkuLossVoMap(List<Long> skuIdList, LocalDate saleDate) {
        RemoteSkuLossBo remoteSkuLossBo = new RemoteSkuLossBo();
        remoteSkuLossBo.setSkuIdList(skuIdList);
        remoteSkuLossBo.setSaleDateEnd(saleDate.plusDays(-ObjectUtil.defaultIfNull(skuGlobalProperties.getEndDay(), 1)));
        remoteSkuLossBo.setSaleDateStart(saleDate.plusDays(-ObjectUtil.defaultIfNull(skuGlobalProperties.getLossSkuDay(), 7)));
        List<RemoteSkuLossVo> skuLossByRemoteBI = remoteBiSkuService.getSkuLossByRemoteBI(remoteSkuLossBo);
        remoteSkuLossBo.setSaleDateStart(saleDate.plusDays(-ObjectUtil.defaultIfNull(skuGlobalProperties.getInspectDay(), 180)));
        List<RemoteSkuLossVo> skuInspectByRemoteBI = remoteBiSkuService.getSkuInspectByRemoteBI(remoteSkuLossBo);
        return RemoteSkuLossVo.mergeToMap(skuLossByRemoteBI, skuInspectByRemoteBI);
    }

    /**
     * 创建-送货单&装车记录
     */
    @Log(title = "创建-送货单&装车记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<SupDeliveryAddBo> create(@Validated @RequestBody SupDeliveryAddBo bo) {
        if (ObjectUtil.isNull(bo.getSupplierId())) {
            return R.warn("供应商id不能为空");
        }
        Map<Long, List<SupDeliveryGoodsAddBo>> deptGoodsListMap = bo.getGoodsList().stream().collect(Collectors.groupingBy(SupDeliveryGoodsAddBo::getSupplierDeptId, Collectors.toList()));
        if (deptGoodsListMap.size() > 1) {
            return R.warn("不可以混合送货，只能送供应商或者单个档口的货-"+deptGoodsListMap.keySet());
        }
        bo.setSupplierName(remoteSupplierService.getSupplierById(bo.getSupplierId()).getName());
        bo.setSupplierDeptId(bo.getGoodsList().get(0).getSupplierDeptId());
        if (bo.getSupplierDeptId() != null && bo.getSupplierDeptId() > 0) {
            bo.setSupplierDeptName(remoteDeptService.selectDeptNameByIds(bo.getSupplierDeptId().toString()));
        }
        supDeliveryAffairService.createDelivery(bo, null);
        SupDeliveryAddBo data = new SupDeliveryAddBo();
        data.setId(bo.getId());
        data.setDeliveryNo(bo.getDeliveryNo());
        return R.ok(data);
    }

    /**
     * 取消送货单
     */
    @Log(title = "取消", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/cancel")
    public R<Void> cancel(@Validated @RequestBody SupDeliveryCancelBo bo) {
        return toAjax(supDeliveryAffairService.cancelDelivery(bo, OrderDeliverSourceEnum.REGION.getCode()));
    }

    /**
     * 送货单商品标签打印查询
     */
    @PostMapping("/getDeliveryGoodsOrderLabelList")
    public R<List<RwEntruckGoodsOrderSimplifyVo>> getDeliveryGoodsOrderLabelList(@RequestBody RwEntruckGoodsOrderQueryBo bo) {
        if (ObjectUtil.isNull(bo.getDeliveryId())) {
            return R.warn("送货单id不能为空");
        }
        return R.ok(entruckGoodsService.getDeliveryGoodsOrderLabelSimplifyList(bo));
    }

}
