package cn.xianlink.order.controller.admin;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.service.DictService;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.api.constant.DeductionInfoEnum;
import cn.xianlink.order.api.constant.DeductionInfoStatusEnum;
import cn.xianlink.order.domain.bo.DeductionInfoAdminBO;
import cn.xianlink.order.domain.bo.DeductionInfoBackStageBO;
import cn.xianlink.order.domain.bo.DeductionInfoBo;
import cn.xianlink.order.domain.vo.DeductionInfoVo;
import cn.xianlink.order.domain.vo.DeductionReasonVO;
import cn.xianlink.order.domain.vo.DeductionStatusCountVO;
import cn.xianlink.order.domain.vo.SettlementRelationVo;
import cn.xianlink.order.mq.producer.DeductionInfoCompleteProducer;
import cn.xianlink.order.service.IDeductionInfoService;
import cn.xianlink.system.api.model.LoginUser;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 扣款单
 * 前端访问路由地址为:/system/info
 *
 * <AUTHOR>
 * @date 2024-06-01
 * @folder 般果管理中心/订单/扣款单
 */
@Validated
@RequiredArgsConstructor
@RestController("adminDeductionInfoController")
@RequestMapping("/order/admin/deduction")
public class DeductionInfoController extends BaseController {

    private final IDeductionInfoService deductionInfoService;

    @Resource
    private transient DeductionInfoCompleteProducer deductionInfoCompleteProducer;

    private final transient DictService dictService;

    /**
     * 查询扣款单列表
     */
    @PostMapping("/list")
    @ApiOperation("分页查询扣款单列表")
    public R<TableDataInfo<DeductionInfoVo>> list(@RequestBody DeductionInfoBackStageBO bo, @RequestBody PageQuery pageQuery) {
        DeductionInfoBo infoBo = new DeductionInfoBo();
        if (ObjectUtil.isNotEmpty(bo) || ObjectUtil.isNotEmpty(bo.getCreateTimeStart()) || ObjectUtil.isNotEmpty(bo.getCreateTimeEnd())
                || ObjectUtil.isNotEmpty(bo.getSaleTimeStart()) || ObjectUtil.isNotEmpty(bo.getSaleTimeEnd())
                || ObjectUtil.isNotEmpty(bo.getSaleDayStart()) || ObjectUtil.isNotEmpty(bo.getSaleDayEnd())) {
            infoBo = BeanUtil.toBean(bo, DeductionInfoBo.class);
            infoBo.setStartTime(bo.getCreateTimeStart());
            infoBo.setEndTime(bo.getCreateTimeEnd());
        }

        return R.ok(deductionInfoService.queryPageList(infoBo, pageQuery));
    }

    /**
     * 导出加扣款单列表
     */
    @PostMapping("/export")
    @ApiOperation("导出加扣款单列表")
    public R<Void> exportDeductionRecord(@RequestBody DeductionInfoBackStageBO bo, HttpServletResponse response) {
        DeductionInfoBo infoBo = new DeductionInfoBo();
        if (ObjectUtil.isNotEmpty(bo) || ObjectUtil.isNotEmpty(bo.getCreateTimeStart()) || ObjectUtil.isNotEmpty(bo.getCreateTimeEnd())
                || ObjectUtil.isNotEmpty(bo.getSaleTimeStart()) || ObjectUtil.isNotEmpty(bo.getSaleTimeEnd())
                || ObjectUtil.isNotEmpty(bo.getSaleDayStart()) || ObjectUtil.isNotEmpty(bo.getSaleDayEnd())) {
            infoBo = BeanUtil.toBean(bo, DeductionInfoBo.class);
            infoBo.setStartTime(bo.getCreateTimeStart());
            infoBo.setEndTime(bo.getCreateTimeEnd());
        }
        deductionInfoService.exportDeductionRecord(infoBo);
        return R.ok();
    }


    /**
     * 获取扣款单详细信息
     *
     * @param id 主键
     */
    @GetMapping("/getDeductionInfoById")
    @ApiOperation("查询扣款单详情")
    public R<DeductionInfoVo> getDeductionInfoById(@RequestParam("id") Long id) {
        return R.ok(deductionInfoService.queryById(id));
    }

    /**
     * 新增扣款单
     */
    @PostMapping("/add")
    @RepeatSubmit()
    @ApiOperation("新增扣款单")
    public R<Boolean> add(@RequestBody DeductionInfoAdminBO bo) {
        //todo 用户登录
        return R.ok(deductionInfoService.insertByBo(BeanUtil.toBean(bo, DeductionInfoBo.class)));
    }

    /**
     * 修改扣款单
     */
    @PostMapping("/edit")
    @RepeatSubmit()
    @ApiOperation("修改扣款单")
    public R<Boolean> edit(@RequestBody DeductionInfoAdminBO bo) {
        return R.ok(deductionInfoService.updateByBo(BeanUtil.toBean(bo, DeductionInfoBo.class)));
    }

    /**
     * 根据扣款单id删除数据
     *
     * @param id
     * @return
     */
    @GetMapping("/deleteDeductionInfoById")
    @RepeatSubmit()
    @ApiOperation("根据扣款单id删除数据")
    public R<Boolean> deleteDeductionInfoById(@RequestParam("id") Long id) {
        return R.ok(deductionInfoService.deleteDeductionInfoById(id));
    }

    @GetMapping("/listDeductionReasonVO")
    @ApiOperation("扣款单原因数据字典列表")
    public R<List<DeductionReasonVO>> listDeductionReasonVO() {
        System.err.println(dictService.getDictLabel("gyskkyy", "1", ","));

        Map<String, String> map = dictService.getAllDictByDictType("gyskkyy");
        List<DeductionReasonVO> list = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(map)) {
            map.forEach((key, value) -> {
                DeductionReasonVO vo = new DeductionReasonVO();
                List<String> tmpList = Arrays.asList(value.split(StringUtils.SEPARATOR));
                if (tmpList.size() != 2) {
                    return;
                }
                vo.setName(tmpList.get(0));
                vo.setDesc(tmpList.get(1));
                vo.setValue(Integer.valueOf(key));
                list.add(vo);
            });
        }
        System.err.println(list);
        return R.ok(list);
    }


    @GetMapping("/getBillInfoByTypeAndCode")
    @ApiOperation("根据关联单据类型和关联单据查询不同得单据信息")
    public R<Object> getBillInfoByTypeAndCode(@RequestParam("billType") Integer billType,
                                              @RequestParam("billCode") String billCode) {
        return R.ok(deductionInfoService.getBillInfoByTypeAndCode(billType, billCode));
    }

    /**
     * 获取不同扣款单待结算状态的数量
     */
    @GetMapping("/getDeductionStatusCount")
    @ApiOperation("获取不同扣款单待结算状态的数量")
    public R<DeductionStatusCountVO> getDeductionStatusCount(@RequestParam(value = "regionWhId", required = false) Long regionWhId,
                                                             @RequestParam(value = "commonId", required = false) Long commonId,
                                                             @RequestParam("type") Integer type) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isEmpty(loginUser) || ObjectUtil.isEmpty(loginUser.getRelationId())) {
            throw new ServiceException("用户未登录");
        }
        DeductionInfoBo bo = new DeductionInfoBo();
        bo.setType(DeductionInfoEnum.loadByCode(type).getCode());
        //todo 登录用户的城市仓id 如何获取
        //loginUser.getRelationId()
        if (ObjectUtil.isNotEmpty(regionWhId)) {
            bo.setRegionWhId(regionWhId);
        }
        if (ObjectUtil.isNotEmpty(commonId)) {
            bo.setCommonId(commonId);
        }
        bo.setStatus(DeductionInfoStatusEnum.COMMITTED.getCode());

        System.err.println(loginUser);
        return R.ok(deductionInfoService.getDeductionStatusCount(bo));
    }

    /**
     * 手动结算城市仓加款单
     */
    @PostMapping("/divide")
    @ApiOperation("手动结算城市仓加款单")
    public R<Integer> divide(@RequestBody List<Long> ids) {
        return R.ok(deductionInfoService.divide(ids));
    }
}
