package cn.xianlink.order.controller.platform;

import cn.xianlink.common.api.enums.order.AccountTypeStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.domain.bo.receiveGoods.ReceiveGoodsConfirmBO;
import cn.xianlink.order.domain.bo.receiveGoods.ReceiveGoodsPageBO;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.domain.vo.receiveGoods.ReceiveGoodsPageVO;
import cn.xianlink.order.domain.vo.receiveGoods.ReceiveGoodsVO;
import cn.xianlink.order.service.IReceiveGoodsService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import cn.xianlink.common.web.core.BaseController;

import java.util.List;


/**
 * 集采平台-提货单
 * <AUTHOR>
 * @date 2024-05-25
 */
@Validated
@RequiredArgsConstructor
@RestController("platformReceiveGoodsController")
@RequestMapping("/order/platform/receive")
public class ReceiveGoodsController extends BaseController {

    private final IReceiveGoodsService receiveGoodsService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询提货单列表")
    public R<TableDataInfo<ReceiveGoodsPageVO>> page(@RequestBody ReceiveGoodsPageBO bo){
        if (bo.getCustomerId() == null && LoginHelper.getLoginUser().getRelationId() == null) {
            return R.ok(TableDataInfo.build());
        }else {
            bo.setCustomerId(LoginHelper.getLoginUser().getRelationId());
        }
        return R.ok(receiveGoodsService.page(bo));
    }

    @GetMapping("/getById/{id}")
    @ApiOperation(value = "获取提货单详情")
    public R<ReceiveGoodsVO> getById(@PathVariable("id") Long id){
        return R.ok(receiveGoodsService.getById(id, AccountTypeStatusEnum.CUSTOMER.getCode()));
    }

    @PostMapping("/confirm")
    @RepeatSubmit()
    @ApiOperation(value = "确认提货")
    public R<Void> confirm(@RequestBody ReceiveGoodsConfirmBO bo){
        receiveGoodsService.confirm(bo);
        return R.ok();
    }

    @GetMapping("/getReceiveCount")
    @ApiOperation(value = "获取提货单各状态数量")
    public R<List<CommonStatusCountVO>> getReceiveCount(){
        return R.ok(receiveGoodsService.getReceiveCount(AccountTypeStatusEnum.CUSTOMER.getCode()));
    }

}
