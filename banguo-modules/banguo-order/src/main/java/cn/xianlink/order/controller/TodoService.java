package cn.xianlink.order.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.RemoteCityWhPlaceService;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.vo.RemoteCityWhPlaceVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionLogisticsVo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.common.api.enums.order.*;
import cn.xianlink.order.domain.ReportLossOrder;
import cn.xianlink.order.domain.RwEntruck;
import cn.xianlink.order.domain.SortGoods;
import cn.xianlink.order.domain.StockoutRecord;
import cn.xianlink.order.domain.delivery.bo.SupWaitDeliveryQueryBo;
import cn.xianlink.order.domain.entruck.bo.RwWaitEntruckQueryBo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckRecordSumVo;
import cn.xianlink.order.domain.todo.bo.TodoBo;
import cn.xianlink.order.domain.todo.vo.TodoGoodsVo;
import cn.xianlink.order.domain.todo.vo.TodoInfo;
import cn.xianlink.order.mapper.*;
import cn.xianlink.order.service.IOrderService;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.bo.RemoteDeliveryAuditBo;
import cn.xianlink.product.api.domain.bo.RemoteQueryInfoListBo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@RefreshScope
public class TodoService {
    private final transient CustomToolService customToolService;

    private final transient IOrderService orderService;
    private final transient SupDeliveryGoodsMapper supDeliveryGoodsMapper;
    private final transient RwEntruckGoodsMapper rwEntruckGoodsMapper;
    private final transient RwEntruckRecordMapper rwEntruckRecordMapper;
    private final transient RwEntruckMapper entruckMapper;
    private final transient ReportLossOrderMapper reportLossOrderMapper;
    private final transient StockoutRecordMapper stockoutRecordMapper;
    private final transient SortGoodsMapper sortGoodsMapper;

    @DubboReference
    private final transient RemoteSupplierService remoteSupplierService;
    @DubboReference
    private final transient RemoteSupplierSkuService remoteSupplierSkuService;
    @DubboReference
    private final transient RemoteCityWhPlaceService remoteCityWhPlaceService;

    /**
     * 是否检验供应商待送货需要总仓审核通过
     */
    @Value("${banguo-order.todo_days.wait_delivery:7}")
    private Integer waitDeliveryDays;

    public Integer getWaitDeliveryDays() {
        return waitDeliveryDays > 0 ? waitDeliveryDays - 1 : 1;
    }

    public void loadSupplierInfo(List<TodoGoodsVo> list) {
        if (list.size() == 0) {
            return;
        }
        // 供应商名称
        List<Long> supplierIds = list.stream().map(TodoGoodsVo::getSupplierId).distinct().toList();
        Map<Long, RemoteSupplierVo> supplierMap = remoteSupplierService.getSupplierByIds(supplierIds)
                .stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
        list.forEach(goodsVo -> {
            if (supplierMap.containsKey(goodsVo.getSupplierId())) {
                goodsVo.setSupplierName(supplierMap.get(goodsVo.getSupplierId()).getName());
                goodsVo.setSupplierAlias(supplierMap.get(goodsVo.getSupplierId()).getAlias());
            }
        });
    }

    public void loadLogisticsInfo(List<TodoGoodsVo> list) {
        if (list.size() == 0) {
            return;
        }
        // 物流
        List<Long> logisticsIds = list.stream().map(TodoGoodsVo::getLogisticsId).distinct().toList();
        Map<Long, RemoteRegionLogisticsVo> logisticsMap = customToolService.queryAllLogisticsList(logisticsIds).stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, Function.identity()));
        // 提货点、城市仓
        List<Long> placeIds = logisticsMap.values().stream().map(RemoteRegionLogisticsVo::getPlaceId).distinct().toList();
        Map<Long, RemoteCityWhPlaceVo> placeMap = remoteCityWhPlaceService.queryList(placeIds).stream().collect(Collectors.toMap(RemoteCityWhPlaceVo::getId, Function.identity()));
        list.forEach(goodsVo -> {
            RemoteRegionLogisticsVo logisticsVo = logisticsMap.get(goodsVo.getLogisticsId());
            if (logisticsVo != null) {
                goodsVo.setLogisticsName(logisticsVo.getLogisticsName());
                goodsVo.setCityWhName(placeMap.getOrDefault(logisticsVo.getPlaceId(), new RemoteCityWhPlaceVo()).getCityWhName());
                goodsVo.setPlaceName(placeMap.getOrDefault(logisticsVo.getPlaceId(), new RemoteCityWhPlaceVo()).getPlaceName());
            }
        });
    }

    public void loadSkuInfo(List<TodoGoodsVo> list) {
        this.loadSkuInfo(list, null);
    }

    public void loadSkuInfo(List<TodoGoodsVo> list, Integer passAudit) {
        if (list.size() == 0) {
            return;
        }
        RemoteQueryInfoListBo skuBo = new RemoteQueryInfoListBo();
        skuBo.setSupplierSkuIdList(list.stream().map(TodoGoodsVo::getSupplierSkuId).distinct().toList());
        // 获取不需要审核或审核通过的数据: 0-否，1-是
        skuBo.setPassAudit(passAudit);
        Map<Long, RemoteSupplierSkuInfoVo> skuInfoMap = remoteSupplierSkuService.queryInfoList(skuBo)
                .stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
        list.forEach(e -> {
            RemoteSupplierSkuInfoVo skuInfoVo = skuInfoMap.get(e.getSupplierSkuId());
            if (ObjectUtil.isNotNull(skuInfoVo)) {
                e.setQuerySkuIsNotNull(true);
                e.setSpuGrade(skuInfoVo.getSpuGrade());
                e.setSpuStandards(skuInfoVo.getSpuStandards());
                e.setProducer(skuInfoVo.getProducer());
                e.setPackageWord(skuInfoVo.getPackageWord());
                // 产地简称
                e.setBrand(skuInfoVo.getBrand());
                e.setShortProducer(skuInfoVo.getShortProducer());
                if (e.getSpuNetWeight() == null) {
                    e.setSpuNetWeight(skuInfoVo.getSpuNetWeight());
                    e.setSpuGrossWeight(skuInfoVo.getSpuGrossWeight());
                }
                if (e.getBusinessType() == null) {
                    e.setBusinessType(skuInfoVo.getBusinessType());
                }
                if (e.getSupplierId() == null) {
                    e.setSupplierId(skuInfoVo.getSupplierId());
                }
                if (e.getBuyerName() == null) {
                    e.setBuyerName(skuInfoVo.getBuyerName());
                }
            }
        });
    }


//    /**
//     * 待送货
//     */
//    public List<TodoGoodsVo> waitDeliveryList(TodoBo bo) {
//        LocalDate saleDate = customToolService.getLastSaleDate(bo.getRegionWhId(), null);
//        SupWaitDeliveryQueryBo queryBo = new SupWaitDeliveryQueryBo();
//        BeanUtils.copyProperties(bo, queryBo);
//        queryBo.setSaleDateStart(saleDate.plusDays(-getWaitDeliveryDays()));
//        queryBo.setSaleDateEnd(saleDate);
//        if (bo.getSource().equals(OrderDeliverSourceEnum.SUPPLIER.getCode())) {
//            queryBo.setBusinessTypes(ListUtil.toList(OrderBusinessTypeEnum.BUSINESS_TYPE1.getCode()));
//            queryBo.setPassAudit(1);
//        }
//        List<TodoGoodsVo> list = this.waitDeliveryList(queryBo);
//        this.loadSupplierInfo(list);
//        return list;
//    }
//    private List<TodoGoodsVo> waitDeliveryList(SupWaitDeliveryQueryBo bo) {
//        // 查询订单商品项
//        QueryDeliverBo orderBo = new QueryDeliverBo();
//        orderBo.setSource(bo.getSource());
//        orderBo.setBusinessTypes(bo.getBusinessTypes());
//        orderBo.setPassAudit(bo.getPassAudit());
//        orderBo.setRegionWhId(bo.getRegionWhId());
//        orderBo.setSaleDateStart(bo.getSaleDateStart());
//        orderBo.setSaleDateEnd(bo.getSaleDateEnd());
//        orderBo.setSupplierId(bo.getSupplierId());
//        orderBo.setIsTodoQuery(true);
//        orderBo.setBuyerCodeList(bo.getBuyerCode() != null ? ListUtil.toList(bo.getBuyerCode()) : null);
//        List<QueryDeliverVo> orderItemVos = orderService.queryDeliver(orderBo);
//        if (CollectionUtil.isEmpty(orderItemVos)) {
//            return new ArrayList<>();
//        }
//        // 供应商送货中、已装车、差异补送
//        Map<Long, RwEntruckGoodsSumVo> entruckListMap = rwEntruckGoodsMapper.customSkuSumListBySupplierId(bo)
//                .stream().collect(Collectors.toMap(RwEntruckGoodsSumVo::getSupplierSkuId, Function.identity(), (key1, key2) -> key2));
//        // 不补送、不采购：生成缺货单
//        Map<Long, Integer> stockoutListMap = stockoutSkuRecordService.queryStockoutSkuCountBySupplierId(bo.getSaleDateStart(), bo.getSaleDateEnd(), bo.getRegionWhId(), bo.getSupplierId())
//                .stream().collect(Collectors.toMap(StockoutSkuRecordVO::getSupplierSkuId, StockoutSkuRecordVO::getStockoutCount, (key1, key2) -> key2));
//        // 组装待送货列表
//        List<TodoGoodsVo> list = new ArrayList<>();
//        orderItemVos.forEach(itemVo -> {
//            TodoGoodsVo goodsVo = new TodoGoodsVo();
//            goodsVo.setSaleDate(itemVo.getSaleDate());
//            goodsVo.setSupplierId(itemVo.getSupplierId());
//            goodsVo.setSupplierSkuId(itemVo.getSupplierSkuId());
//            goodsVo.setBusinessType(itemVo.getBusinessType());
//            goodsVo.setBuyerName(itemVo.getBuyerName());
//            goodsVo.setSpuName(itemVo.getSpuName());
//            goodsVo.setSpuGrossWeight(itemVo.getSpuGrossWeight());
//            goodsVo.setSpuNetWeight(itemVo.getSpuNetWeight());
//            goodsVo.setSpuGrade(itemVo.getSpuGrade());
//            goodsVo.setSpuStandards(itemVo.getSpuStandards());
//            goodsVo.setProducer(itemVo.getProducer());
//            goodsVo.setPackageWord(itemVo.getPackageWord());
//            goodsVo.setBrand(itemVo.getBrand());
//            goodsVo.setShortProducer(itemVo.getShortProducer());
//            goodsVo.setSkuQuantity(itemVo.getCount());
//            RwEntruckGoodsSumVo sumVo = entruckListMap.get(itemVo.getSupplierSkuId());
//            if (sumVo != null) {
//                goodsVo.setSkuQuantity(NumberUtil.sub(goodsVo.getSkuQuantity(), sumVo.totalQuantity()).intValue());
//            }
//            if (stockoutListMap.get(itemVo.getSupplierSkuId()) != null) {
//                goodsVo.setSkuQuantity(NumberUtil.sub(goodsVo.getSkuQuantity(), stockoutListMap.get(itemVo.getSupplierSkuId())).intValue());
//            }
//            // 待送货数量大于0 显示
//            if (goodsVo.getSkuQuantity() > 0) {
//                list.add(goodsVo);
//            }
//        });
//        return list;
//    }

    /**
     * 待送货、待分拣
     */
    @Cacheable(value = "statistics:todo:new:waitdeliverylist:cache#5m", key = "#bo.cacheUserId + '_' + #bo.regionWhId + '_' + #bo.source")
    public TodoInfo waitDeliveryList(TodoBo bo) {
        LocalDate saleDate = customToolService.getLastSaleDate(bo.getRegionWhId(), null);
        SupWaitDeliveryQueryBo queryBo = new SupWaitDeliveryQueryBo();
        BeanUtils.copyProperties(bo, queryBo);
        queryBo.setSaleDateStart(saleDate.plusDays(-getWaitDeliveryDays()));
        queryBo.setSaleDateEnd(saleDate);
        queryBo.setPayTime(orderService.getDelayQueryTime());
        if (bo.getSource().equals(OrderDeliverSourceEnum.SUPPLIER.getCode())) {
            queryBo.setBusinessTypes(ListUtil.toList(OrderBusinessTypeEnum.BUSINESS_TYPE1.getCode()));
            queryBo.setPassAudit(1);
        } else if (bo.getSource().equals(OrderDeliverSourceEnum.REGION_PICKING.getCode())) {
            queryBo.setBusinessTypes(ListUtil.toList(OrderBusinessTypeEnum.BUSINESS_TYPE20.getCode(), OrderBusinessTypeEnum.BUSINESS_TYPE30.getCode()));
            queryBo.setPassAudit(1);
        }
        List<TodoGoodsVo> list = rwEntruckGoodsMapper.waitDeliveryList(queryBo);
        if (CollUtil.isNotEmpty(list)) {
            this.loadSkuInfo(list, queryBo.getPassAudit());
            this.loadSupplierInfo(list);
            list = list.stream().filter(f -> f.getQuerySkuIsNotNull()).collect(Collectors.toList());
        }
        return new TodoInfo(list, waitDeliveryDays);
    }


    /**
     * 送货待审核
     */
    @Cacheable(value = "statistics:todo:new:regiondeliveryauditlist:cache#5m", key = "#bo.cacheUserId + '_' + #bo.regionWhId")
    public TodoInfo regionDeliveryAuditList(TodoBo bo) {
        LocalDate saleDate = customToolService.getLastSaleDate(bo.getRegionWhId(), null);
        RemoteDeliveryAuditBo queryBo = new RemoteDeliveryAuditBo();
        queryBo.setRegionWhId(bo.getRegionWhId());
        queryBo.setSaleDateStart(saleDate);
        queryBo.setSaleDateEnd(saleDate);
        List<RemoteSupplierSkuInfoVo> skuInfoVos = remoteSupplierSkuService.queryDeliveryAuditList(queryBo);
        List<TodoGoodsVo> list = new ArrayList<>();
        skuInfoVos.forEach(skuVo -> {
            TodoGoodsVo goodsVo = new TodoGoodsVo();
            BeanUtils.copyProperties(skuVo, goodsVo);
            list.add(goodsVo);
        });
        this.loadSupplierInfo(list);
        return new TodoInfo(list, 1);
    }

    /**
     * 待质检
     */
    @Cacheable(value = "statistics:todo:new:waitinspectgoodslist:cache#5m", key = "#bo.cacheUserId + '_' + #bo.regionWhId + '_' + #bo.source")
    public TodoInfo waitInspectGoodsList(TodoBo bo) {
        LocalDate saleDate = customToolService.getLastSaleDate(bo.getRegionWhId(), null);
        SupWaitDeliveryQueryBo queryBo = new SupWaitDeliveryQueryBo();
        BeanUtils.copyProperties(bo, queryBo);
        queryBo.setSaleDateStart(saleDate.plusDays(-getWaitDeliveryDays()));
        queryBo.setSaleDateEnd(saleDate);
        List<TodoGoodsVo> list = supDeliveryGoodsMapper.waitInspectGoodsList(queryBo);
        this.loadSkuInfo(list);
        this.loadSupplierInfo(list);
        return new TodoInfo(list, waitDeliveryDays);
    }

    /**
     * 待装车
     * entruckType类型； 1嘉兴分货(市采)， 2天津分货(基采、产地),  3天津分拣(基采、产地)
     */
    @Cacheable(value = "statistics:todo:new:waitentruckgoodslist:cache#5m", key = "#bo.cacheUserId + '_' + #bo.regionWhId + '_' + #bo.source + '_' + #entruckType")
    public TodoInfo waitEntruckGoodsList(TodoBo bo, Integer entruckType) {
        LocalDate saleDate = customToolService.getLastSaleDate(bo.getRegionWhId(), null);
        RwWaitEntruckQueryBo queryBo = new RwWaitEntruckQueryBo();
        BeanUtils.copyProperties(bo, queryBo);
        queryBo.setSaleDateStart(saleDate.plusDays(-getWaitDeliveryDays()));
        queryBo.setSaleDateEnd(saleDate);
        queryBo.setEntruckType(entruckType);
        List<TodoGoodsVo> list = rwEntruckGoodsMapper.waitEntruckGoodsList(queryBo);
        this.loadSkuInfo(list);
        this.loadSupplierInfo(list);
        return new TodoInfo(list, waitDeliveryDays);
    }

//    /**
//     * 待分拣
//     */
//    @Cacheable(value = "statistics:todo:new:waitpickingprintlist:cache#5m", key = "#bo.cacheUserId + '_' + #bo.regionWhId")
//    public List<TodoGoodsVo> waitPickingPrintList(TodoBo bo) {
//        LocalDate saleDate = customToolService.getLastSaleDate(bo.getRegionWhId(), null);
//        SupWaitDeliveryQueryBo queryBo = new SupWaitDeliveryQueryBo();
//        BeanUtils.copyProperties(bo, queryBo);
//        queryBo.setSaleDateStart(saleDate.plusDays(-getWaitDeliveryDays()));
//        queryBo.setSaleDateEnd(saleDate);
//        queryBo.setBusinessTypes(ListUtil.toList(OrderBusinessTypeEnum.BUSINESS_TYPE20.getCode(), OrderBusinessTypeEnum.BUSINESS_TYPE30.getCode()));
//        queryBo.setPassAudit(1);
//        List<TodoGoodsVo> list = this.waitDeliveryList(queryBo);
//        this.loadSupplierInfo(list);
//        return list;
//    }

    @Cacheable(value = "statistics:todo:new:waitcreateentrucknocount:cache#5m", key = "#bo.cacheUserId + '_' + #bo.regionWhId")
    public TodoInfo waitCreateEntruckNoCount(TodoBo bo) {
        LocalDate saleDate = customToolService.getLastSaleDate(bo.getRegionWhId(), null);
        RwWaitEntruckQueryBo queryBo = new RwWaitEntruckQueryBo();
        queryBo.setRegionWhId(bo.getRegionWhId());
        queryBo.setSaleDateStart(saleDate.plusDays(-getWaitDeliveryDays()));
        queryBo.setSaleDateEnd(saleDate);
        List<RwEntruckRecordSumVo> recordList = rwEntruckRecordMapper.waitCreateEntruckNoList(queryBo);
        List<TodoGoodsVo> list = new ArrayList<>();
        for (RwEntruckRecordSumVo record : recordList) {
            TodoGoodsVo goodsVo = new TodoGoodsVo();
            BeanUtils.copyProperties(record, goodsVo);
            list.add(goodsVo);
        }
        return new TodoInfo(list, waitDeliveryDays);
    }

    @Cacheable(value = "statistics:todo:new:waitcreateentrucknocount:cache#5m", key = "#bo.cacheUserId + '_' + #bo.regionWhId + '_' + #bo.cityWhId + '_' + #status")
    public TodoInfo waitDepartCount(TodoBo bo, Integer status) {
        LambdaQueryWrapper<RwEntruck> lqw = Wrappers.lambdaQuery();
        lqw.eq(RwEntruck::getStatus, status);
        if (status.intValue() == DeliveryStatusEnum.WAIT_DEPART.getCode()) {
            lqw.eq(RwEntruck::getRegionWhId, bo.getRegionWhId());
        } else if (status.intValue() == DeliveryStatusEnum.COMPLETE_DEPART.getCode()) {
            lqw.eq(RwEntruck::getCityWhId, bo.getCityWhId());
        }
        List<RwEntruck> rwEntruckList = entruckMapper.selectList(lqw);
        List<TodoGoodsVo> list = new ArrayList<>();
        for (RwEntruck entruck : rwEntruckList) {
            TodoGoodsVo goodsVo = new TodoGoodsVo();
            BeanUtils.copyProperties(entruck, goodsVo);
            list.add(goodsVo);
        }
        return new TodoInfo(list, null);
    }

    @Cacheable(value = "statistics:todo:new:supreportlossauditcount:cache#5m", key = "#bo.cacheUserId + '_' + #bo.regionWhId")
    public TodoInfo supReportLossAuditCount(TodoBo bo) {
        LambdaQueryWrapper<ReportLossOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(ReportLossOrder::getLossStatus, ReportLossStatusEnum.WAIT_AUDIT.getCode());
        lqw.eq(ReportLossOrder::getRegionWhId, bo.getRegionWhId());
        lqw.eq(ReportLossOrder::getSupplierId, bo.getSupplierId());
        lqw.eq(ObjectUtil.isNotNull(bo.getSupplierDeptId()), ReportLossOrder::getSupplierDeptId, bo.getSupplierDeptId());
        List<ReportLossOrder> reportLossList = reportLossOrderMapper.selectList(lqw);
        List<TodoGoodsVo> list = new ArrayList<>();
        for (ReportLossOrder loss : reportLossList) {
            TodoGoodsVo goodsVo = new TodoGoodsVo();
            BeanUtils.copyProperties(loss, goodsVo);
            list.add(goodsVo);
        }
        return new TodoInfo(list, null);
    }

    /**
     * 获取未确认缺货少货单列表
     *
     * @param type 类型 1缺货 2少货
     */
    @Cacheable(value = "statistics:todo:new:waitconfirmstockoutlist:cache#5m", key = "#bo.cacheUserId + '_' + #bo.regionWhId + '_' + #bo.cashKey + '_' + #bo.source + '_' + #type")
    public TodoInfo waitConfirmStockOutList(TodoBo bo, Integer type) {
        LambdaQueryWrapper<StockoutRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StockoutRecord::getDelFlag, 0);
        lqw.eq(StockoutRecord::getAuditStatus, StockoutAuditStatusEnum.STATE_CONFIRM.getCode());
        lqw.eq(StockoutRecord::getType, type);
        // 总仓查询
        lqw.eq(ObjectUtil.isNotNull(bo.getRegionWhId()), StockoutRecord::getRegionWhId, bo.getRegionWhId());
        lqw.eq(ObjectUtil.isNotNull(bo.getBuyerId()), StockoutRecord::getBuyerId, bo.getBuyerId());
        // 城市仓查询
        lqw.eq(ObjectUtil.isNotNull(bo.getCityWhId()), StockoutRecord::getCityWhId, bo.getCityWhId());

        lqw.select(StockoutRecord::getSupplierId, StockoutRecord::getSaleDate, StockoutRecord::getLogisticsId,
                StockoutRecord::getSupplierSkuId, StockoutRecord::getSpuName, StockoutRecord::getBuyerName,
                StockoutRecord::getStockoutCount);
        if (CollUtil.isNotEmpty(bo.getLogisticsIds()) && CollUtil.isNotEmpty(bo.getPlaceIdLevel2List())){
            lqw.in(StockoutRecord::getLogisticsId, bo.getLogisticsIds());
            bo.getPlaceIdLevel2List().add(0L);
            lqw.in(StockoutRecord::getPlaceIdLevel2, bo.getPlaceIdLevel2List());
        }else if (CollUtil.isNotEmpty(bo.getLogisticsIds()) && CollUtil.isEmpty(bo.getPlaceIdLevel2List())){
            lqw.in(StockoutRecord::getLogisticsId, bo.getLogisticsIds());
        }else if (CollUtil.isEmpty(bo.getLogisticsIds()) && CollUtil.isNotEmpty(bo.getPlaceIdLevel2List())){
            lqw.in(StockoutRecord::getPlaceIdLevel2, bo.getPlaceIdLevel2List());
        }
        lqw.orderByDesc(StockoutRecord::getId);
        List<StockoutRecord> stockOutList = stockoutRecordMapper.selectList(lqw);
        List<TodoGoodsVo> list = new ArrayList<>();
        for (StockoutRecord record : stockOutList) {
            TodoGoodsVo goodsVo = new TodoGoodsVo();
            BeanUtils.copyProperties(record, goodsVo);
            goodsVo.setSkuQuantity(record.getStockoutCount());
            list.add(goodsVo);
        }
        this.loadSkuInfo(list);
        this.loadLogisticsInfo(list);
        this.loadSupplierInfo(list);
        return new TodoInfo(list, null);
    }

    /**
     * 获取未判责少货单列表
     */
    @Cacheable(value = "statistics:todo:new:waitblamestockoutlist:cache#5m", key = "#bo.cacheUserId + '_' + #bo.supplierId + '_' + #bo.cashKey + '_' + #bo.source")
    public TodoInfo waitBlameStockOutList(TodoBo bo) {
        LambdaQueryWrapper<StockoutRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StockoutRecord::getDelFlag, 0);
        lqw.eq(StockoutRecord::getBlameStatus, BlameStatusEnum.STATE_BLAME.getCode());
        lqw.eq(StockoutRecord::getBusinessType, OrderBusinessTypeEnum.BUSINESS_TYPE1.getCode());
        lqw.eq(StockoutRecord::getType, 2).eq(StockoutRecord::getBusinessType, OrderBusinessTypeEnum.BUSINESS_TYPE1.getCode());
        // 总仓查询
        lqw.eq(ObjectUtil.isNotNull(bo.getRegionWhId()), StockoutRecord::getRegionWhId, bo.getRegionWhId());
        lqw.eq(ObjectUtil.isNotNull(bo.getBuyerId()), StockoutRecord::getBuyerId, bo.getBuyerId());
        // 城市仓查询
        lqw.eq(ObjectUtil.isNotNull(bo.getCityWhId()), StockoutRecord::getCityWhId, bo.getCityWhId());
        // 供应商查询
        lqw.eq(ObjectUtil.isNotNull(bo.getSupplierId()), StockoutRecord::getSupplierId, bo.getSupplierId());

        lqw.select(StockoutRecord::getSupplierId, StockoutRecord::getSaleDate, StockoutRecord::getLogisticsId,
                StockoutRecord::getSupplierSkuId, StockoutRecord::getSpuName, StockoutRecord::getBuyerName,
                StockoutRecord::getStockoutCount);
        if (CollUtil.isNotEmpty(bo.getLogisticsIds()) && CollUtil.isNotEmpty(bo.getPlaceIdLevel2List())){
            lqw.in(StockoutRecord::getLogisticsId, bo.getLogisticsIds());
            bo.getPlaceIdLevel2List().add(0L);
            lqw.in(StockoutRecord::getPlaceIdLevel2, bo.getPlaceIdLevel2List());
        }else if (CollUtil.isNotEmpty(bo.getLogisticsIds()) && CollUtil.isEmpty(bo.getPlaceIdLevel2List())){
            lqw.in(StockoutRecord::getLogisticsId, bo.getLogisticsIds());
        }else if (CollUtil.isEmpty(bo.getLogisticsIds()) && CollUtil.isNotEmpty(bo.getPlaceIdLevel2List())){
            lqw.in(StockoutRecord::getPlaceIdLevel2, bo.getPlaceIdLevel2List());
        }
        lqw.orderByDesc(StockoutRecord::getId);
        List<StockoutRecord> stockOutList = stockoutRecordMapper.selectList(lqw);
        List<TodoGoodsVo> list = new ArrayList<>();
        for (StockoutRecord record : stockOutList) {
            TodoGoodsVo goodsVo = new TodoGoodsVo();
            BeanUtils.copyProperties(record, goodsVo);
            goodsVo.setSkuQuantity(record.getStockoutCount());
            list.add(goodsVo);
        }
        this.loadSkuInfo(list);
        this.loadLogisticsInfo(list);
        this.loadSupplierInfo(list);
        return new TodoInfo(list, null);
    }

    /**
     * 获取未完成分货列表
     *
     * @param status 1待分货 2分货中 3已分货
     */
    @Cacheable(value = "statistics:todo:new:waitconfirmsortlist:cache#5m", key = "#bo.cashKey + '_' + #status")
    public TodoInfo waitConfirmSortList(TodoBo bo, Integer status) {
        List<SortGoods> sortGoodsList = sortGoodsMapper.waitConfirmSortList(bo, status);
        List<TodoGoodsVo> list = new ArrayList<>();
        for (SortGoods goods : sortGoodsList) {
            TodoGoodsVo goodsVo = new TodoGoodsVo();
            BeanUtils.copyProperties(goods, goodsVo);
            //可操作的数量 未收 > 已收-已分 ? 已收-已分 : 未收
            int count = goods.getReceived() - goods.getActualReceived() > goods.getUnreceived() ? goods.getUnreceived() : goods.getReceived() - goods.getActualReceived();
            if (count <=0) {
                continue;
            }
            goodsVo.setSkuQuantity(count);
            goodsVo.setSortStatus(goods.getStatus());
            list.add(goodsVo);
        }
        this.loadSkuInfo(list);
        this.loadSupplierInfo(list);
        return new TodoInfo(list, null);
    }
}
