package cn.xianlink.order.controller.region;

import cn.xianlink.basic.api.domain.vo.RemoteRcsVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.report.CustomerStatisticsSearchBo;
import cn.xianlink.order.domain.bo.report.ReportLossMiniListSearchBo;
import cn.xianlink.order.domain.bo.report.ReportLossOrderFlowBo;
import cn.xianlink.order.domain.vo.report.ReportLossMiniDetailVo;
import cn.xianlink.order.domain.vo.report.ReportLossMiniListVo;
import cn.xianlink.order.domain.vo.report.ReportNumStatisticsVo;
import cn.xianlink.order.service.ICustomerStatisticsService;
import cn.xianlink.order.service.IReportLossService;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 报损单
 *
 * <AUTHOR> xiaodaibing on 2024-05-30 17:29
 * @folder 总仓助手(小程序)/报损单
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/region/report")
public class RReportLossController extends BaseController {

    private final IReportLossService reportLossService;

    private final ICustomerStatisticsService customerStatisticsService;

    /**
     * 列表
     * 需要传 regionWhId
     * @param bo
     * @return cn.xianlink.common.core.domain.R<TableDataInfo < ReportLossMiniListVo>>
     * <AUTHOR> on 2024/6/11:15:24
     */
    @PostMapping("/list")
    public R<TableDataInfo<ReportLossMiniListVo>> list(@RequestBody ReportLossMiniListSearchBo bo) {
        return R.ok(reportLossService.miniPage(bo));
    }


    /**
     * 获取详情
     *
     * @param id
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.order.domain.vo.report.ReportLossMiniDetailVo>
     * <AUTHOR> on 2024/6/6:15:22
     */
    @GetMapping("/detail/{id}")
    public R<ReportLossMiniDetailVo> detail(@PathVariable("id") Long id) {
        return R.ok(reportLossService.detail(id));
    }


    /**
     * 报损流程处理
     * 供应商审核、申诉、客服介入
     * <AUTHOR> on 2024/6/6:17:16
     * @param bo
     * @return void
     */
    @RepeatSubmit()
    @GlobalTransactional
    @PostMapping("/processing")
    public R<Void> processing(@RequestBody @Valid ReportLossOrderFlowBo bo) {
        bo.setReportLossId(reportLossService.getReportLossId(bo.getOrderItemId()));
        reportLossService.processing(bo);
        return toAjax(Boolean.TRUE);
    }



    /**
     * 各状态数量统计
     * 目前只统计待审核、驳回、申诉中
     * <AUTHOR> on 2024/6/11:9:47
     * @param  
     * @return cn.xianlink.common.core.domain.R<java.util.List<cn.xianlink.order.domain.vo.report.ReportNumStatisticsVo>>
     */
    @GetMapping("/numStatistics")
    public R<List<ReportNumStatisticsVo>> numStatistics() {
        return R.ok(reportLossService.numStatistics(null,null,null, LoginHelper.getLoginUser().getRelationId(), null));
    }

    /**
     * 客户报损统计
     * <AUTHOR> on 2024/7/29:15:27
     * @param searchBo
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.basic.api.domain.vo.RemoteRcsVo>
     */
    @PostMapping("/customerStatistics")
    public R<RemoteRcsVo> customerStatistics(@Valid @RequestBody CustomerStatisticsSearchBo searchBo) {
        if (searchBo.getCycle() > 12) {
            return R.ok(customerStatisticsService.getYearReportLoss(searchBo.getCustomerId(), searchBo.getCycle()));
        } else {
            return R.ok(customerStatisticsService.getMonthReportLoss(searchBo.getCustomerId(), searchBo.getCycle()));
        }
    }
}
