package cn.xianlink.order.controller.city;

import cn.hutool.core.collection.CollectionUtil;
import cn.xianlink.basic.api.RemoteCityWhPlaceService;
import cn.xianlink.common.api.enums.order.AccountTypeStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.refundRecord.DifferenceRefundPageBO;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.domain.vo.refundRecord.DifferenceRefundDetailVO;
import cn.xianlink.order.domain.vo.refundRecord.DifferenceRefundVO;
import cn.xianlink.order.service.IRefundRecordService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 城市仓小程序-订单退款&&差额退款
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@Validated
@RequiredArgsConstructor
@RestController("cityOrderRefundController")
@RequestMapping("/order/city/refund")
public class OrderRefundController extends BaseController {

    private final IRefundRecordService refundRecordService;
    @DubboReference
    private final transient RemoteCityWhPlaceService remoteCityWhPlaceService;

    @PostMapping("/difPage")
    @ApiOperation(value = "分页查询差额退款列表")
    public R<TableDataInfo<DifferenceRefundVO>> difPage(@RequestBody DifferenceRefundPageBO bo){
        if (bo.getCityWhId() == null && LoginHelper.getLoginUser().getRelationId() == null) {
            return R.ok(TableDataInfo.build());
        }else {
            bo.setCityWhId(LoginHelper.getLoginUser().getRelationId());
        }
        if (CollectionUtil.isNotEmpty(bo.getPlaceIdList())) {
            bo.setPlaceIdList(remoteCityWhPlaceService.getPlaceIdsByUser(LoginHelper.getLoginUser().getUserId()));
        }else if (CollectionUtil.isEmpty(bo.getPlaceIdList()) && CollectionUtil.isEmpty(bo.getPlaceIdLevel2List())){
            bo.setPlaceIdList(remoteCityWhPlaceService.getPlaceIdsByUser(LoginHelper.getLoginUser().getUserId()));
            bo.setPlaceIdLevel2List(bo.getPlaceIdList());
        }
        return R.ok(refundRecordService.difPage(bo));
    }

    @GetMapping("/getDifById/{id}")
    @ApiOperation(value = "获取差额退款单项详情")
    public R<DifferenceRefundDetailVO> getDifById(@PathVariable("id") Long id){
        return R.ok(refundRecordService.getDifById(id));
    }

    @GetMapping("/getDifRefundCount")
    @ApiOperation(value = "获取差额退款单各状态数量")
    public R<CommonStatusCountVO> getDifRefundCount(){
        return R.ok(refundRecordService.getDifRefundCount(AccountTypeStatusEnum.CITY.getCode()));
    }

}
