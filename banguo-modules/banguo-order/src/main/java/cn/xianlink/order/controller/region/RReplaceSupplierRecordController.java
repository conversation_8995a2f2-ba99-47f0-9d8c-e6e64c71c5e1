package cn.xianlink.order.controller.region;

import cn.xianlink.order.domain.PublicId;
import cn.xianlink.order.domain.replaceSupplierRecord.bo.QueryReplacePageBo;
import cn.xianlink.order.domain.replaceSupplierRecord.vo.ReplaceSupplierRecordVo;
import cn.xianlink.order.service.IReplaceSupplierRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;

/**
 * 换供应商转单记录
 * 前端访问路由地址为:/order/region/replaceSupplierRecord
 * <AUTHOR>
 * @date 2024-06-17
 * @folder 总仓助手(小程序)/订单/转单记录
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/region/replaceSupplierRecord")
public class RReplaceSupplierRecordController extends BaseController {

    private final IReplaceSupplierRecordService replaceSupplierRecordService;

    /**
     * 查询换供应商转单记录列表
     */
//    @SaCheckPermission("order:replaceSupplierRecord:queryPage")
    @PostMapping("/queryPage")
    public R<TableDataInfo<ReplaceSupplierRecordVo>> queryPage(@RequestBody QueryReplacePageBo bo) {
        return R.ok(replaceSupplierRecordService.queryPage(bo));
    }

    /**
     * 获取换供应商转单记录详细信息
     *
     * @param id 主键
     */
//    @SaCheckPermission("order:replaceSupplierRecord:getInfo")
    @PostMapping("/getInfo")
    public R<ReplaceSupplierRecordVo> getInfo(@RequestBody PublicId id) {
        return R.ok(replaceSupplierRecordService.queryById(id.getId()));
    }
}
