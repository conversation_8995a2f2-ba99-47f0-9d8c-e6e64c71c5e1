package cn.xianlink.order.controller.region;

import java.util.List;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.service.DictService;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.api.constant.DeductionInfoStatusEnum;
import cn.xianlink.order.api.constant.DeductionInfoEnum;
import cn.xianlink.order.domain.bo.DeductionInfoBo;
import cn.xianlink.order.api.bo.DeductionInfoParamBo;
import cn.xianlink.order.domain.vo.DeductionInfoVo;
import cn.xianlink.order.domain.vo.DeductionReasonVO;
import cn.xianlink.order.domain.vo.DeductionStatusCountVO;
import cn.xianlink.order.mq.producer.DeductionInfoCompleteProducer;
import cn.xianlink.order.service.IDeductionInfoService;
import cn.xianlink.system.api.model.LoginUser;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;

/**
 * 扣款单
 * 前端访问路由地址为:/system/info
 *
 * <AUTHOR>
 * @date 2024-06-01
 * @folder 总仓助手(小程序)/订单/扣款单
 */
@Validated
@RequiredArgsConstructor
@RestController("regionDeductionInfoController")
@RequestMapping("/order/region/deduction")
public class DeductionInfoController extends BaseController {

    private final IDeductionInfoService deductionInfoService;

    @Resource
    private transient DeductionInfoCompleteProducer deductionInfoCompleteProducer;

    private final transient DictService dictService;

    /**
     * 查询扣款单列表
     */
    @PostMapping("/list")
    @ApiOperation("分页查询扣款单列表")
    public R<TableDataInfo<DeductionInfoVo>> list(@RequestBody DeductionInfoBo bo, PageQuery pageQuery) {
        return R.ok(deductionInfoService.queryPageList(bo, pageQuery));
    }



    /**
     * 获取扣款单详细信息
     *
     * @param id 主键
     */
    @GetMapping("/getDeductionInfoById")
    @ApiOperation("查询扣款单详情")
    public R<DeductionInfoVo> getDeductionInfoById(@RequestParam("id") Long id) {
        return R.ok(deductionInfoService.queryById(id));
    }

    /**
     * 新增扣款单
     */
    @PostMapping("/add")
    @RepeatSubmit()
    @ApiOperation("新增扣款单")
    public R<Boolean> add(@RequestBody DeductionInfoBo bo) {
        //todo 用户登录
        return R.ok(deductionInfoService.insertByBo(bo));
    }

    /**
     * 修改扣款单
     */
    @PostMapping("/edit")
    @RepeatSubmit()
    @ApiOperation("修改扣款单")
    public R<Boolean> edit(@RequestBody DeductionInfoBo bo) {
        return R.ok(deductionInfoService.updateByBo(bo));
    }

    /**
     * 根据扣款单id删除数据
     * @param id
     * @return
     */
    @GetMapping("/deleteDeductionInfoById")
    @RepeatSubmit()
    @ApiOperation("根据扣款单id删除数据")
    public R<Boolean> deleteDeductionInfoById(@RequestParam("id") Long id) {
        return R.ok(deductionInfoService.deleteDeductionInfoById(id));
    }

    @GetMapping("/getBillInfoByTypeAndCode")
    @ApiOperation("根据关联单据类型和关联单据查询不同得单据信息")
    public R<Object> getBillInfoByTypeAndCode(@RequestParam("billType") Integer billType,
                                              @RequestParam("billCode") String billCode) {
        return R.ok(deductionInfoService.getBillInfoByTypeAndCode(billType, billCode));
    }

    /**
     * 删除扣款单
     *
     * @param ids 主键串
     */
    @DeleteMapping("/{ids}")
    @RepeatSubmit()
    public R<Boolean> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return R.ok(deductionInfoService.deleteWithValidByIds(List.of(ids), true));
    }

    @GetMapping("/saveUseDeductionInfo")
    @RepeatSubmit()
    public R<Boolean> saveUseDeductionInfo(@RequestParam("date") String date,
                                           @RequestParam(value = "code", required = false) String code) {
        return R.ok(deductionInfoService.saveUseDeductionInfo(date, Lists.newArrayList(code)));
    }


    @PostMapping("/testSendMq")
    @ApiOperation("测试发送mq接口（测试接口）")
    public R<Void> testSendMq(@RequestBody DeductionInfoBo bo) {
        deductionInfoCompleteProducer.send(BeanUtil.toBean(bo, DeductionInfoParamBo.class));
        return R.ok();
    }

    @GetMapping("/listDeductionReasonVO")
    @ApiOperation("扣款单原因数据字典列表")
    public R<List<DeductionReasonVO>> listDeductionReasonVO() {
        return R.ok(deductionInfoService.listDeductionReasonVO("gyskkyy"));
    }

    /**
     * 获取不同扣款单待结算状态的数量
     *
     */
    @PostMapping("/getDeductionStatusCount")
    @ApiOperation("获取不同扣款单待结算状态的数量")
    public R<DeductionStatusCountVO> getDeductionStatusCount(@RequestParam(value = "regionWhId", required = false) Long regionWhId,
                                                             @RequestParam("type") Integer type) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isEmpty(loginUser) || ObjectUtil.isEmpty(loginUser.getRelationId())) {
            throw new ServiceException("用户未登录");
        }
        DeductionInfoBo bo = new DeductionInfoBo();
        bo.setType(DeductionInfoEnum.loadByCode(type).getCode());
        //todo 登录用户的城市仓id 如何获取
        //loginUser.getRelationId()
        if (ObjectUtil.isNotEmpty(regionWhId)) {
            bo.setRegionWhId(regionWhId);
        }
        bo.setStatus(DeductionInfoStatusEnum.COMMITTED.getCode());

        System.err.println(loginUser);
        return R.ok(deductionInfoService.getDeductionStatusCount(bo));
    }
}
