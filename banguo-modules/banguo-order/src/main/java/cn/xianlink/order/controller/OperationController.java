package cn.xianlink.order.controller;

import cn.xianlink.common.api.enums.order.BlameSourceTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.StockoutRecord;
import cn.xianlink.order.domain.operation.ShortageCreateBlameBo;
import cn.xianlink.order.mapper.StockoutRecordDetailMapper;
import cn.xianlink.order.mapper.StockoutRecordMapper;
import cn.xianlink.order.service.IStockoutRecordService;
import com.baomidou.lock.annotation.Lock4j;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 订单运维接口
 * <AUTHOR> xiaodaibing on 2024-12-02 16:37
 * @folder 订单运维接口
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/operation")
public class OperationController extends BaseController {

    private final StockoutRecordMapper stockoutRecordMapper;

    private final StockoutRecordDetailMapper stockoutRecordDetailMapper;

    private final IStockoutRecordService stockoutRecordService;


    /**
     * 少货单创建判责单
     * <AUTHOR> on 2024/12/2:16:42
     * @param bo
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     */
//    @RepeatSubmit()
//    @Lock4j(keys = "1")
//    @PostMapping("/shortageCreateBlame")
//    public R<Void> shortageCreateBlame(@Validated ShortageCreateBlameBo bo) {
//        for (Long id : bo.getIds()) {
//            StockoutRecord stockoutRecord =  stockoutRecordMapper.selectById(id);
//            if (stockoutRecord == null) {
//                return R.fail(id + "不存在");
//            }
//            if (stockoutRecord.getType().equals(BlameSourceTypeEnum.LESS_GOODS.getCode())) {
//                stockoutRecordService.stockoutCreateBlame(stockoutRecord, stockoutRecordDetailMapper.selectByRecordId(id));
//            } else {
//                return R.fail(id + "不是少货单");
//            }
//        }
//        return R.ok();
//    }


}
