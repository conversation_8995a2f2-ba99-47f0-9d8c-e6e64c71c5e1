package cn.xianlink.order.controller.admin;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.refundRecord.DifferenceRefundPageBO;
import cn.xianlink.order.domain.bo.refundRecord.RefundPageAuditBO;
import cn.xianlink.order.domain.vo.refundRecord.DifferenceRefundDetailVO;
import cn.xianlink.order.domain.vo.refundRecord.DifferenceRefundPageVO;
import cn.xianlink.order.domain.vo.refundRecord.RefundPageVO;
import cn.xianlink.order.domain.vo.refundRecord.RefundRecordPageVO;
import cn.xianlink.order.service.OrderExportService;
import cn.xianlink.order.service.IRefundRecordService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 般果管理中心-订单退款&&差额退款
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@Validated
@RequiredArgsConstructor
@RestController("adminOrderRefundController")
@RequestMapping("/order/admin/refund")
public class OrderRefundController extends BaseController {

    private final IRefundRecordService refundRecordService;
    private final OrderExportService refundExportService;

    @PostMapping("/difErrorPage")
    @ApiOperation(value = "异常管理-分页查询差额退款单")
    public R<TableDataInfo<DifferenceRefundDetailVO>> difErrorPage(@RequestBody DifferenceRefundPageBO bo){
        return R.ok(refundRecordService.difErrorPage(bo));
    }

    @PostMapping("/difPage")
    @ApiOperation(value = "供应商结算-分页查询差额退款列表")
    public R<TableDataInfo<DifferenceRefundPageVO>> difPage(@RequestBody DifferenceRefundPageBO bo){
        return R.ok(refundRecordService.adminDifPage(bo));
    }

    @PostMapping("/refundPage")
    @ApiOperation(value = "供应商结算-分页查询退货退款列表")
    public R<TableDataInfo<RefundRecordPageVO>> refundPage(@RequestBody DifferenceRefundPageBO bo){
        return R.ok(refundRecordService.adminRefundPage(bo));
    }

    @PostMapping("/auditPage")
    @ApiOperation(value = "退款审核-分页查询退货退款列表")
    public R<TableDataInfo<RefundPageVO>> auditPage(@RequestBody RefundPageAuditBO bo){
        return R.ok(refundRecordService.auditPage(bo));
    }

    /**
     * 导出退款列表
     * <AUTHOR> on 2025/1/9:15:41
     * @param bo
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     */
    @PostMapping("/exportAuditPage")
    @RepeatSubmit()
    public R<Void> exportAuditPage(@RequestBody RefundPageAuditBO bo){
        refundExportService.submitRefundPage(bo);
        return R.ok();
    }


    @GetMapping("/auditPass/{id}")
    @RepeatSubmit()
    @ApiOperation(value = "审核通过")
    public R<Void> auditPass(@PathVariable("id") Long id){
        refundRecordService.auditPass(id);
        return R.ok();
    }

    @GetMapping("/auditReturn/{id}")
    @RepeatSubmit()
    @ApiOperation(value = "审核不通过")
    public R<Void> auditReturn(@PathVariable("id") Long id){
        refundRecordService.auditReturn(id);
        return R.ok();
    }
}
