package cn.xianlink.order.controller.admin;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.api.enums.system.SysUserTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.domain.bo.pay.SupOrderItermSearchBo;
import cn.xianlink.order.domain.bo.report.ReportLossPcListSearchBo;
import cn.xianlink.order.domain.vo.pay.OrderPayAccountTransVo;
import cn.xianlink.order.domain.vo.report.ReportLossPcListVo;
import cn.xianlink.order.service.IOrderPayService;
import cn.xianlink.order.service.IReportLossService;
import cn.xianlink.system.api.model.LoginUser;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 * @date 2024-05-28
 * @folder 般果管理中心/供应商结算
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/admin/sup")
@CustomLog
public class ASupController {

    private final IReportLossService reportLossService;

    private final IOrderPayService iOrderPayService;

    
    /**
     * 订货收款明细
     * <AUTHOR> on 2024/6/12:11:43
     * @param bo 
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.common.mybatis.core.page.TableDataInfo<cn.xianlink.order.domain.vo.pay.OrderPayAccountTransVo>>
     */
    @PostMapping("/orderItermPage")
    public R<TableDataInfo<OrderPayAccountTransVo>> orderIterm(@RequestBody SupOrderItermSearchBo bo) {
        return R.ok(iOrderPayService.orderItermPage(bo));
    }

    /**
     * 报损退款明细
     * <AUTHOR> on 2024/6/12:11:31
     * @param bo
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.common.mybatis.core.page.TableDataInfo<cn.xianlink.order.domain.vo.report.ReportLossPcListVo>>
     */
    @PostMapping("/refund/rlList")
    public R<TableDataInfo<ReportLossPcListVo>> list(@RequestBody ReportLossPcListSearchBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isNotEmpty(loginUser) && ObjectUtil.isEmpty(bo.getSupplierDeptId())) {
            if (SysUserTypeEnum.SUPPLIER_USER.getType().equals(loginUser.getUserType()) && loginUser.getDeptId() != 0L) {
                bo.setSupplierDeptId(loginUser.getDeptId());
                log.keyword("ASupController", "list","报损单查询档口信息refundPage")
                        .info("报损单查询档口信息，供应商用户查询，入参：{}，loginUser：{}", bo, loginUser);
            }
        } else {
            if (bo.getSupplierDeptId() == 0L) {
                bo.setSupplierDeptId(null);
            }
            log.keyword("ASupController", "list","报损单查询档口信息refundPage")
                    .info("报损单查询档口信息无用户信息，入参：{}", bo);
        }
        return R.ok(reportLossService.refundPage(bo));
    }

}
