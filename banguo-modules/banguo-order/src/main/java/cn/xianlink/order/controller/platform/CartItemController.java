package cn.xianlink.order.controller.platform;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.RemoteCityWhPlaceService;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.basic.api.domain.vo.RemoteCityWhPlaceVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteRuleFreightPriceVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.service.DictService;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.CartItemBo;
import cn.xianlink.order.domain.bo.CartItemParamBo;
import cn.xianlink.order.domain.vo.CartItemCountVO;
import cn.xianlink.order.domain.vo.CartItemVo;
import cn.xianlink.order.service.ICartItemService;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.bo.RemoteQueryInfoListBo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.system.api.RemoteDictService;
import cn.xianlink.system.api.model.LoginUser;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 购物车
 * 前端访问路由地址为:/product/item
 *
 * <AUTHOR>
 * @date 2024-05-25
 * @folder 采集平台(小程序)/订单/集采平台-购物车
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/platform/cart")
@Api(tags = "集采平台-购物车")
public class CartItemController extends BaseController {

    private final ICartItemService cartItemService;

    @DubboReference
    private final RemoteSupplierSkuService remoteSupplierSkuService;

    @DubboReference
    private final RemoteDictService remoteDictService;

    private final DictService dictService;
    @DubboReference
    private final RemoteRegionLogisticsService remoteRegionLogisticsService;

    @DubboReference
    private final RemoteCityWhPlaceService remoteCityWhPlaceService;


    /**
     * 查询购物车列表
     */
    @PostMapping("/list")
    public List<CartItemVo> list(@RequestBody CartItemBo bo) {
        return cartItemService.queryList(bo);
    }

    /**
     * 小程序查询购物车列表
     */
    @GetMapping("/listCartItemList")
    @ApiOperation("小程序查询购物车列表")
    public R<List<CartItemVo>> listCartItemList(@RequestParam("regionWhId") Long regionWhId,
                                                @RequestParam(value = "cityWhId", required = false) Long cityWhId,
                                                @RequestParam("placeId") Long placeId) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isEmpty(loginUser) || ObjectUtil.isEmpty(loginUser.getUserId())) {
            throw new ServiceException("登录用户信息异常");
        }
        Long parentPlaceId = null;
        RemoteCityWhPlaceVo remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(placeId);
        if (Objects.nonNull(remoteCityWhPlaceVo)){
            parentPlaceId = remoteCityWhPlaceVo.getParentPlaceId();
        }
        List<CartItemVo> cartItemVos = cartItemService.listCartItemList(regionWhId, loginUser.getUserId(), cityWhId ,placeId,parentPlaceId);
        //根据总仓&&提货点查询物流线及基础吨位是否配置
        placeId = Objects.nonNull(parentPlaceId) ? parentPlaceId : placeId;
        List<Long> isConfig = remoteRegionLogisticsService.checkForSalePage(placeId, Collections.singletonList(regionWhId));
        if (CollectionUtils.isEmpty(isConfig) && CollectionUtils.isNotEmpty(cartItemVos)){
            cartItemVos.forEach(l->l.setHasLogistics(0));
        }
        return R.ok(cartItemVos);
    }

    /**
     * 小程序查询购物车列表
     */
    @GetMapping("/list_cart_item_list_v2")
    @ApiOperation("小程序查询购物车列表V2")
    public R<List<CartItemVo>> listCartItemListV2(@RequestParam("regionWhId") Long regionWhId,
                                                @RequestParam(value = "cityWhId", required = false) Long cityWhId,
                                                @RequestParam("placeId") Long placeId) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isEmpty(loginUser) || ObjectUtil.isEmpty(loginUser.getUserId())) {
            throw new ServiceException("登录用户信息异常");
        }
        //根据总仓&&提货点查询物流线及基础吨位是否配置
        Long parentPlaceId = null;
        RemoteCityWhPlaceVo remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(placeId);
        if (Objects.nonNull(remoteCityWhPlaceVo)){
            parentPlaceId = remoteCityWhPlaceVo.getParentPlaceId();
        }
        List<CartItemVo> cartItemVos = cartItemService.listCartItemList(regionWhId, loginUser.getUserId(), cityWhId ,placeId,parentPlaceId);
        placeId = Objects.nonNull(parentPlaceId) ? parentPlaceId : placeId;
        List<Long> isConfig = remoteRegionLogisticsService.checkForSalePage(placeId, Collections.singletonList(regionWhId));
        if (CollectionUtils.isEmpty(isConfig) && CollectionUtils.isNotEmpty(cartItemVos)){
            cartItemVos.forEach(l->l.setHasLogistics(0));
        }
        cartItemVos.forEach(l->{
            BigDecimal skuSubsidy = l.getSkuSubsidy();
            l.setPrice(l.getPrice().subtract(skuSubsidy));
        });
        cartItemVos = cartItemVos.stream().filter(item -> Objects.equals(item.getIsExpire(),0)).collect(Collectors.toList());
        return R.ok(cartItemVos);
    }

    @PostMapping("/testCacheData")
    public Boolean testCacheData(@RequestBody CartItemBo bo) {
        return cartItemService.cacheCartItemBo(bo);
    }

    @PostMapping("/testInfo")
    @ApiOperation("测试接口，勿联调")
    public List<RemoteSupplierSkuInfoVo> testInfo(@RequestBody CartItemBo bo) {
        RemoteQueryInfoListBo queryInfoListBo = new RemoteQueryInfoListBo();
        queryInfoListBo.setSupplierSkuIdList(Lists.newArrayList(bo.getSupplierSkuId()));
        return remoteSupplierSkuService.queryInfoList(queryInfoListBo);
    }

    @GetMapping("/getCartItemCount")
    @ApiOperation("根据用户id查询购物车角标数字")
    public R<List<CartItemCountVO>> getCartItemCount(@RequestParam(value = "cityWhId", required = false) Long cityWhId) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isEmpty(loginUser) || ObjectUtil.isEmpty(loginUser.getUserId())) {
            throw new ServiceException("登录用户信息异常");
        }
        return R.ok(cartItemService.getCartItemCountByUserId(loginUser.getUserId(), cityWhId));
    }


    @GetMapping("/batchDeleteCartItemByRegionWhId")
    @ApiOperation("根据regionWhId批量删除购物车列表中的失效商品数据")
    public R<Boolean> batchDeleteCartItemByIds(@RequestParam("regionWhId") Long regionWhId,@RequestParam(value = "cityWhId",required = false) Long cityWhId) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isEmpty(loginUser) || ObjectUtil.isEmpty(loginUser.getUserId())) {
            throw new ServiceException("用户未登录");
        }
        Boolean aBoolean = cartItemService.batchDeleteExpireCartItemByIds(regionWhId, loginUser.getUserId(),cityWhId);
        return R.ok(aBoolean);
    }

    /**
     * 新增购物车
     */
    @PostMapping("/addCartItem")
    @ApiOperation(value = "添加、修改用户购物车物品", notes = "添加/修改用户购物车商品，并传入改变的商品个数(count)" +
            "当count为正值时，增加商品数量，当count为负值时，将减去商品的数量，当最终count值等于0时，会将商品从购物车里面删除")
    public R<CartItemVo> add(@RequestBody CartItemParamBo bo) {
        if (ObjectUtil.isEmpty(bo)) {
            throw new ServiceException("添加购物车失败，入参为空");
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isEmpty(loginUser) || ObjectUtil.isEmpty(loginUser.getUserId())) {
            throw new ServiceException("用户未登录");
        }
        bo.setUserId(loginUser.getUserId());
        RemoteCityWhPlaceVo remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(bo.getPlaceId());
        Long placeId = bo.getPlaceId();
        if (Objects.nonNull(remoteCityWhPlaceVo)){
            Long parentPlaceId = remoteCityWhPlaceVo.getParentPlaceId();
            placeId = Objects.nonNull(parentPlaceId) ? parentPlaceId :bo.getPlaceId();
        }
        List<Long> isConfig = remoteRegionLogisticsService.checkForSalePage(placeId, Collections.singletonList(bo.getRegionWhId()));
        if (CollectionUtils.isEmpty(isConfig)){
            throw new ServiceException("城市仓未设置吨位，请联系城市仓工作人员");
        }
        return R.ok(cartItemService.insertByBo(bo));
    }

    /**
     * 新增购物车
     */
    @GetMapping("/batchAddCartItem")
    @ApiOperation(value = "批量添加购物商品数据", notes = "批量添加购物商品数据")
    public R<Boolean> batchAddCartItem(@RequestParam("orderId") Long orderId) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isEmpty(loginUser) || ObjectUtil.isEmpty(loginUser.getUserId())) {
            throw new ServiceException("用户未登录");
        }
        if (ObjectUtil.isEmpty(orderId)) {
            throw new ServiceException("添加购物车失败，入参为空");
        }
        return R.ok(cartItemService.batchAddCartItem(orderId));
    }

    @GetMapping("/deleteCacheCartItemCountAndList")
    @ApiOperation("（后端使用）删除用户下面购物车缓存和角标数量缓存")
    public R<Void> deleteCacheCartItemCountAndList(@RequestParam("regionWhId") Long regionWhId,
                                                   @RequestParam("userId") Long userId) {
        cartItemService.deleteCacheCartItemCountAndList(userId, regionWhId);
        return R.ok();
    }





}
