package cn.xianlink.order.controller.platform;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.PublicId;
import cn.xianlink.order.domain.order.vo.GetWhiteInfoVo;
import cn.xianlink.order.service.IOrderService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 订单
 * 前端访问路由地址为:/order/platform/white
 * <AUTHOR>
 * @date 2024-05-27
 * @folder 采集平台(小程序)/订单/白名单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/platform/white")
public class PWhiteController extends BaseController {

    private final IOrderService orderService;

//    @SaCheckPermission("system:order:query")
    @PostMapping("/getWhiteInfo")
    @Operation(summary = "获取订单详细信息(白名单)")
    public R<GetWhiteInfoVo> getWhiteInfo(@RequestBody PublicId id) {
        return R.ok(orderService.getWhiteInfo(id.getId()));
    }
}
