package cn.xianlink.order.controller.admin;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.blameRecord.BlameRecordPageBO;
import cn.xianlink.order.domain.vo.blameRecord.BlameRecordAdminVO;
import cn.xianlink.order.service.IBlameRecordService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 般果管理中心-判责单
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@Validated
@RequiredArgsConstructor
@RestController("adminBlameRecordController")
@RequestMapping("/order/admin/blame")
public class BlameRecordController extends BaseController {

    private final IBlameRecordService blameRecordService;

    @PostMapping("/blameRecordPage")
    @ApiOperation(value = "判责单列表")
    public R<TableDataInfo<BlameRecordAdminVO>> blameRecordPage(@RequestBody BlameRecordPageBO bo){
        return R.ok(blameRecordService.blameAdminPage(bo));
    }

}
