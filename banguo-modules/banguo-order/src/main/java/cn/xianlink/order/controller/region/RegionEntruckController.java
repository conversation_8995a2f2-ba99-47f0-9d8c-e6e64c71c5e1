package cn.xianlink.order.controller.region;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.domain.vo.RemoteWhNexusVo;
import cn.xianlink.common.api.enums.order.DeliveryStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.controller.CustomToolService;
import cn.xianlink.order.domain.depart.bo.RwWaitDepartQueryBo;
import cn.xianlink.order.domain.entruck.bo.*;
import cn.xianlink.order.domain.entruck.vo.RwEntruckRecordSumVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckRecordVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckVo;
import cn.xianlink.order.service.IRwEntruckRecordService;
import cn.xianlink.order.service.IRwEntruckService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 总仓-装车单
 *
 * <AUTHOR>
 * @date 2024-05-28
 * @folder 总仓助手(小程序)/装车单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/region/entruck")
public class RegionEntruckController extends BaseController {

    private final transient IRwEntruckRecordService entruckRecordService;
    private final transient IRwEntruckService entruckService;
    private final transient RegionEntruckAffairService regionEntruckAffairService;
    private final transient CustomToolService customToolService;
    @DubboReference
    private final transient RemoteRegionWhService remoteRegionWhService;


//    /**
//     * 查询-装车记录-计数(待装车记录单据数)
//     */
//    @GetMapping("/waitEntruckRecordCount/{regionWhId}")
//    public R<Long> waitEntruckRecordCount(@NotNull(message = "总仓id不能为空") @PathVariable Long regionWhId) {
//        RwWaitEntruckQueryBo bo = new RwWaitEntruckQueryBo();
//        bo.setRegionWhId(regionWhId);
//        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), null));
//        return R.ok(entruckRecordService.waitEntruckRecordCount(bo));
//    }
//    /**
//     * 查询-待装车列表-计数(待生成装车单物流数)
//     */
//    @GetMapping("/waitEntruckListCount/{regionWhId}")
//    public R<Long> waitEntruckListCount(@NotNull(message = "总仓id不能为空") @PathVariable Long regionWhId) {
//        RwWaitEntruckQueryBo bo = new RwWaitEntruckQueryBo();
//        bo.setRegionWhId(regionWhId);
//        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), null));
//        return R.ok((long) entruckRecordService.completeEntruckLogisticsCount(bo).size());
//    }
//    /**
//     * 首页-装车单-计数(待发车单据数)
//     */
//    @GetMapping("/entruckCount/{regionWhId}")
//    public R<Long> entruckCount(@NotNull(message = "总仓id不能为空") @PathVariable Long regionWhId) {
//        RwEntruckQueryBo bo = new RwEntruckQueryBo();
//        bo.setRegionWhId(regionWhId);
//        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), null));
//        return R.ok(entruckService.entruckCount(bo, DeliveryStatusEnum.WAIT_DEPART.getCode()));
//    }

    /**
     * 查询-装车记录
     */
    @PostMapping("/recordPage")
    public R<TableDataInfo<RwEntruckRecordVo>> recordPage(@Validated @RequestBody RwEntruckRecordQueryBo bo) {
        if (ObjectUtil.isNull(bo.getRegionWhId())) {
            return R.warn("总仓id不能为空");
        }
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        // 查全部
        if (CollectionUtil.isEmpty(bo.getStatusList())) {
            bo.setStatusList(CollectionUtil.toList(DeliveryStatusEnum.COMPLETE_INSPECT.getCode(),
                    DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode()));
        }
        TableDataInfo<RwEntruckRecordVo> table = entruckRecordService.pageList(bo);
        if (CollUtil.isNotEmpty(table.getRows())) {
            List<Long> basPortageTeamIds = table.getRows().stream().filter(f -> ObjectUtil.isNotNull(f.getBasPortageTeamId())).map(RwEntruckRecordVo::getBasPortageTeamId).distinct().toList();
            Map<Long, String> teamNameMap = customToolService.getTeamNameMap(basPortageTeamIds);
            for (RwEntruckRecordVo vo : table.getRows()) {
                if (ObjectUtil.isNotNull(vo.getBasPortageTeamId())) {
                    vo.setBasPortageTeamName(teamNameMap.get(vo.getBasPortageTeamId()));
                }
                if (StrUtil.isNotBlank(vo.getEntruckName()) || CollUtil.isEmpty(vo.getGoodsList())) {
                    continue;
                }
                for (int i = vo.getGoodsList().size() - 1; i >= 0; i--) {
                    String createName = vo.getGoodsList().get(i).getCreateName();
                    if (StrUtil.isNotBlank(createName)) {
                        vo.setEntruckName(createName);
                        break;
                    }
                }
            }
        }
        return R.ok(table);
    }

    /**
     * 查询-装车记录详情
     */
    @GetMapping("/recordInfo/{recordId}")
    public R<RwEntruckRecordVo> recordInfo(@NotNull(message = "装车记录id不能为空") @PathVariable Long recordId) {
        return R.ok(entruckRecordService.queryById(recordId));
    }

    /**
     * 装车操作-单条商品-提交
     */
    @Log(title = "装车操作-单条商品-提交", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/operateEntruckGoods")
    public R<Void> operateEntruckGoods(@Validated @RequestBody RwEntruckGoodsEditBo bo) {
        regionEntruckAffairService.operateEntruckGoods(bo);
        return R.ok();
    }

    /**
     * 装车操作-提交
     */
    @Log(title = "装车操作-提交", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/operateEntruck")
    public R<Void> operateEntruck(@Validated @RequestBody RwEntruckRecordEditBo bo) {
        RwEntruckRecordVo vo = entruckRecordService.selectAndCheckNullById(bo.getId());
        if (vo.getStatus().intValue() != DeliveryStatusEnum.COMPLETE_INSPECT.getCode()) {
            throw new ServiceException("非待装车状态，不允许装车操作");
        }
        bo.setDeliveryId(vo.getDeliveryId());
        regionEntruckAffairService.operateEntruck(bo, false);
        return R.ok();
    }

    /**
     * 装车操作-撤销
     */
    @Log(title = "装车操作-撤销", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/revokeEntruck/{recordId}")
    public R<Void> revokeEntruck(@NotNull(message = "装车记录id不能为空") @PathVariable Long recordId) {
        regionEntruckAffairService.revokeEntruck(recordId);
        return R.ok();
    }

    /**
     * 查询-待装车列表
     */
    @PostMapping("/waitEntruckList")
    public R<List<RwEntruckRecordSumVo>> waitEntruckList(@Validated @RequestBody RwWaitEntruckQueryBo bo) {
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        return R.ok(entruckRecordService.completeEntruckSumList(bo));
    }

    /**
     * 创建装车单
     */
    @Log(title = "创建装车单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Long> create(@Validated @RequestBody RwEntruckAddBo bo) {
        regionEntruckAffairService.createEntruck(bo, true);
        return R.ok(bo.getId());
    }

    /**
     * 查询-装车单列表
     */
    @PostMapping("/page")
    public R<TableDataInfo<RwEntruckVo>> page(@Validated @RequestBody RwEntruckQueryBo bo) {
        if (ObjectUtil.isNull(bo.getRegionWhId())) {
            return R.warn("总仓id不能为空");
        }
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        // 查全部
        if (CollectionUtil.isEmpty(bo.getStatusList())) {
            bo.setStatusList(CollectionUtil.toList(DeliveryStatusEnum.WAIT_DEPART.getCode(),
                    DeliveryStatusEnum.COMPLETE_DEPART.getCode(),
                    DeliveryStatusEnum.COMPLETE_RECEIVE.getCode()));
        } else {
            if (bo.getStatusList().get(0).intValue() == DeliveryStatusEnum.COMPLETE_DEPART.getCode()) {
                bo.setStatusList(CollectionUtil.toList(DeliveryStatusEnum.COMPLETE_DEPART.getCode(),
                        DeliveryStatusEnum.COMPLETE_RECEIVE.getCode()));
            }
        }
        TableDataInfo<RwEntruckVo> table = entruckService.customPageList(bo);
        // 待发车状态，加载预计倒仓日期
        Map<Long, Integer> arrivalDaysMap = null;
        for (RwEntruckVo vo : table.getRows()) {
            if (vo.getStatus().intValue() == DeliveryStatusEnum.WAIT_DEPART.getCode()) {
                if (arrivalDaysMap == null) {
                    arrivalDaysMap = remoteRegionWhService.queryCityListById(bo.getRegionWhId()).stream().collect(Collectors.toMap(RemoteWhNexusVo::getCityWhId, RemoteWhNexusVo::getArrivalDays, (key1, key2) -> key2));
                }
                vo.setArrivalDate(LocalDate.now().plusDays(arrivalDaysMap.getOrDefault(vo.getCityWhId(), 0)));
            }
        }
        return R.ok(table);
    }

    /**
     * 查询-发车合车-未被选中的待发车的装车单
     */
    @PostMapping("/waitDepartList")
    public R<List<RwEntruckVo>> waitDepartList(@Validated @RequestBody RwWaitDepartQueryBo bo) {
        return R.ok(entruckService.waitDepartList(bo));
    }

    /**
     * 修改装车单-预计到仓日期
     */
    @Log(title = "修改装车单-预计到仓日期", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateArrivalDate")
    public R<Void> updateArrivalDate(@Validated @RequestBody RwEntruckEditArrivalDateBo bo) {
        RwEntruckVo vo = entruckService.selectAndCheckNullById(bo.getId());
        if (vo.getStatus().intValue() != DeliveryStatusEnum.COMPLETE_DEPART.getCode()) {
            return R.warn("非已发车状态，不允许修改");
        }
        if (bo.getArrivalDate().compareTo(vo.getArrivalDate()) == 0) {
            return R.ok();
        }
        if (bo.getArrivalDate().compareTo(vo.getDepartDate()) < 0) {
            return R.warn("到仓日期不能小于发货日期");
        }
        return toAjax(entruckService.updateArrivalDate(bo));
    }

}
