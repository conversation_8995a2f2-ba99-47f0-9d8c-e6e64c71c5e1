package cn.xianlink.order.controller.admin;

import java.util.List;

import cn.hutool.core.bean.BeanUtil;
import cn.xianlink.order.domain.bo.SettlementRelationAddBo;
import cn.xianlink.order.domain.bo.SettlementRelationBo;
import cn.xianlink.order.domain.bo.SettlementRelationPageBo;
import cn.xianlink.order.domain.vo.SettlementRelationOrderInfoVo;
import cn.xianlink.order.domain.vo.SettlementRelationVo;
import cn.xianlink.order.service.ISettlementRelationService;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;

/**
 * 结算关联
 * 前端访问路由地址为:/system/relation
 * @folder 般果管理中心/订单/城市仓结算
 *
 * <AUTHOR>
 * @date 2024-11-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/admin/relation")
public class SettlementRelationController extends BaseController {

    private final ISettlementRelationService settlementRelationService;

    /**
     * 查询结算关联列表
     */
    @GetMapping("/list")
    public TableDataInfo<SettlementRelationVo> list(SettlementRelationBo bo, PageQuery pageQuery) {
        return settlementRelationService.queryPageList(bo, pageQuery);
    }

    /**
     * 分页查询城市仓结算
     */
    @PostMapping("/getPage")
    public TableDataInfo<SettlementRelationOrderInfoVo> getPage( @RequestBody SettlementRelationPageBo bo, PageQuery pageQuery) {
        return settlementRelationService.getPage(bo, pageQuery);
    }

    /**
     * 导出结算关联列表
     */
    @Log(title = "结算关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SettlementRelationBo bo, HttpServletResponse response) {
        List<SettlementRelationVo> list = settlementRelationService.queryList(bo);
        ExcelUtil.exportExcel(list, "结算关联", SettlementRelationVo.class, response);
    }

    /**
     * 获取结算关联详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<SettlementRelationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(settlementRelationService.queryById(id));
    }

    /**
     * 新增结算关联
     */
    @Log(title = "结算关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@RequestBody SettlementRelationAddBo bo) {

        return toAjax(settlementRelationService.insertByBo(bo));
    }

    /**
     * 修改结算关联
     */
    @Log(title = "结算关联", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@RequestBody SettlementRelationBo bo) {
        return toAjax(settlementRelationService.updateByBo(bo));
    }

    /**
     * 删除结算关联
     *
     * @param ids 主键串
     */
    @Log(title = "结算关联", businessType = BusinessType.DELETE)
    @RepeatSubmit()
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(settlementRelationService.deleteWithValidByIds(List.of(ids), true));
    }
}
