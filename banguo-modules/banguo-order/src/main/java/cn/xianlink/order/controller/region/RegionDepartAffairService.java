package cn.xianlink.order.controller.region;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.domain.vo.RemoteWhNexusVo;
import cn.xianlink.common.api.enums.order.DeliveryStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.order.domain.depart.bo.RwDepartAddBo;
import cn.xianlink.order.domain.depart.bo.RwDepartEditBo;
import cn.xianlink.order.domain.depart.bo.RwDepartLogisticsBo;
import cn.xianlink.order.domain.depart.bo.RwWaitDepartQueryBo;
import cn.xianlink.order.domain.depart.vo.RwDepartLogisticsVo;
import cn.xianlink.order.domain.depart.vo.RwDepartVo;
import cn.xianlink.order.domain.entruck.bo.RwEntruckEditDepartBo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckVo;
import cn.xianlink.order.service.IRwDepartService;
import cn.xianlink.order.service.IRwEntruckService;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
@CustomLog
public class RegionDepartAffairService {
    private final transient IRwEntruckService entruckService;
    private final transient IRwDepartService departService;
    @DubboReference
    private final transient RemoteRegionWhService remoteRegionWhService;

    /**
     * 自动创建创建发车单，并发车
     */
    @Transactional(rollbackFor = Throwable.class)
    public void autoCreateDepart(LocalDate saleDate, Long regionWhId) {
        RwWaitDepartQueryBo bo = new RwWaitDepartQueryBo();
        bo.setSaleDate(saleDate);
        bo.setRegionWhId(regionWhId);
        bo.setIsLoadName(false);
        List<RwEntruckVo> list = entruckService.waitDepartList(bo);
        if (list.size() == 0) {
            return;
        }
        List<Long> cityWhIds = list.stream().map(RwEntruckVo::getCityWhId).distinct().collect(Collectors.toList());
        Map<String, Integer> arrivalDaysMap = remoteRegionWhService.selectListByCityWhId(cityWhIds)
                .stream().collect(Collectors.toMap(RemoteWhNexusVo::getRegionWhIdAndCityWhIdKey, RemoteWhNexusVo::getArrivalDays));
        for (RwEntruckVo vo : list) {
            // 创建发车单
            RwDepartLogisticsBo logisticsBo = new RwDepartLogisticsBo();
            logisticsBo.setLogisticsId(vo.getLogisticsId());
            logisticsBo.setCityWhId(vo.getCityWhId());
            logisticsBo.setEntruckId(vo.getId());

            RwDepartAddBo departBo = new RwDepartAddBo();
            departBo.setSaleDate(vo.getSaleDate());
            departBo.setRegionWhId(vo.getRegionWhId());
            departBo.setLicencePlate("补数据");
            departBo.setCarTypeId(1L);
            departBo.setLogisticsList(ListUtil.toList(logisticsBo));
            departBo.setTotalSpuGrossWeight(vo.getTotalSpuGrossWeight());
            departBo.setStatus(DeliveryStatusEnum.COMPLETE_DEPART.getCode());
            departService.insert(departBo);

            // 更新绑定装车单
            RwEntruckEditDepartBo editBo = new RwEntruckEditDepartBo();
            editBo.setId(vo.getId());
            editBo.setDepartId(departBo.getId());
            editBo.setDepartNo(departBo.getDepartNo());
            editBo.setDepartDate(LocalDate.now());
            editBo.setArrivalDate(LocalDate.now().plusDays(arrivalDaysMap.getOrDefault(String.format("%s-%s", vo.getRegionWhId(), vo.getCityWhId()), 0)));
            entruckService.updateDepartNo(ListUtil.toList(editBo));
        }
    }

    /**
     * 创建发车单
     */
    @Transactional(rollbackFor = Throwable.class)
    public void departCar(RwDepartEditBo bo) {
        if (CollUtil.isEmpty(bo.getLogisticsList())) {
            throw new ServiceException("车辆物流列表不能为空");
        }
        Map<Long, RwDepartLogisticsBo> logisticsMap = bo.getLogisticsList().stream().collect(Collectors.toMap(RwDepartLogisticsBo::getLogisticsId, Function.identity(), (key1, key2) -> key2));
        if (bo.getLogisticsList().size() != logisticsMap.size()) {
            throw new ServiceException("合车物流不允许有重复");
        }
        for (RwDepartLogisticsBo logVo : bo.getLogisticsList()) {
            if (logVo.getEntruckId() == null) {
                throw new ServiceException("存在未生成装车单的物流线");
            }
        }
        boolean isAdd = true;
        RwDepartVo departVo = null;
        if (ObjectUtil.isNotNull(bo.getId())) {
            isAdd = false;
            departVo = departService.selectAndCheckNullById(bo.getId());
            if (departVo.getStatus().intValue() != DeliveryStatusEnum.WAIT_DEPART.getCode()) {
                throw new ServiceException("发车单非待发车状态，不允许操作");
            }
        }
        if (bo.getLogisticsList().size() > 1) {
            if (ObjectUtil.isNull(bo.getCarpoolFee())) {
                throw new ServiceException("合车费不能为空");
            }
        } else {
            bo.setCarpoolFee(null);
        }
        List<Long> entruckIdList = bo.getLogisticsList().stream().map(RwDepartLogisticsBo::getEntruckId).collect(Collectors.toList());
        List<RwEntruckVo> entruckVos = entruckService.queryListByIds(entruckIdList);
        if (entruckVos.size() == 0) {
            throw new ServiceException("装车单id不存在");
        }
        Map<Long, RwEntruckVo> entruckMap = entruckVos.stream().collect(Collectors.toMap(RwEntruckVo::getId, Function.identity(), (key1, key2) -> key2));
        for (Long entruckId : entruckIdList) {
            RwEntruckVo entruckVo = entruckMap.get(entruckId);
            if (entruckVo == null) {
                throw new ServiceException(String.format("装车单id[%d]不存在", entruckId));
            }
            if (entruckVo.getStatus().intValue() != DeliveryStatusEnum.WAIT_DEPART.getCode()) {
                throw new ServiceException(String.format("装车单id[%d]非待发车状态，不允许操作", entruckId));
            }
            if (ObjectUtil.isNotNull(departVo) && !entruckVo.getSaleDate().equals(departVo.getSaleDate())) {
                throw new ServiceException(String.format("装车单id[%d]销售日期不匹配", entruckId));
            }
        }
        RwEntruckVo entruckVo = entruckMap.get(entruckIdList.get(0));
        bo.setTotalSpuGrossWeight(entruckVos.stream().map(RwEntruckVo::getTotalSpuGrossWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
        bo.setStatus(DeliveryStatusEnum.COMPLETE_DEPART.getCode());
        if (isAdd) {
            RwDepartAddBo addBo = new RwDepartAddBo();
            BeanUtils.copyProperties(bo, addBo);
            addBo.setSaleDate(entruckVo.getSaleDate());
            addBo.setRegionWhId(entruckVo.getRegionWhId());
            addBo.setLogisticsList(bo.getLogisticsList());
            departService.insert(addBo);
            bo.setId(addBo.getId());
            bo.setDepartNo(addBo.getDepartNo());
        } else {
            List<Long> deleteIds = this.getDeleteLogisticsIds(departVo, logisticsMap);
            // 剩余为新增
            if (logisticsMap.size() > 0) {
                departService.insertBatchLogistics(departVo, logisticsMap.values().stream().toList());
            }
            if (deleteIds.size() > 0) {
                departService.deleteByIdLogistics(deleteIds);
            }
            departService.update(bo);
            bo.setDepartNo(departVo.getDepartNo());
        }
        // 更新绑定装车单
        Map<Long, Integer> arrivalDaysMap = remoteRegionWhService.queryCityListById(entruckVo.getRegionWhId()).stream().collect(Collectors.toMap(RemoteWhNexusVo::getCityWhId, RemoteWhNexusVo::getArrivalDays, (key1, key2) -> key2));
        List<RwEntruckEditDepartBo> editBoList = new ArrayList<>();
        for (int i = 0; i < entruckVos.size(); i++) {
            RwEntruckVo vo = entruckVos.get(i);
            RwEntruckEditDepartBo editBo = new RwEntruckEditDepartBo();
            editBo.setId(vo.getId());
            editBo.setDepartId(bo.getId());
            editBo.setDepartNo(bo.getDepartNo());
            editBo.setDepartDate(LocalDate.now());
            editBo.setArrivalDate(LocalDate.now().plusDays(arrivalDaysMap.getOrDefault(vo.getCityWhId(), 0)));
            if (i == 0) {
                editBo.setLogisticsFee(NumberUtil.add(bo.getEntruckFee(), bo.getParkingFee(), bo.getInsuranceFee(), bo.getFreightFee(), bo.getCarpoolFee()));
            }
            editBoList.add(editBo);
        }
        entruckService.updateDepartNo(editBoList);
    }

    /**
     * 修改发车单
     */
    @Transactional(rollbackFor = Throwable.class)
    public void updateDepart(RwDepartEditBo bo) {
        if (ObjectUtil.isNull(bo.getId())) {
            throw new ServiceException("发车单id不能为空");
        }
        RwDepartVo vo = departService.selectAndCheckNullById(bo.getId());
        if (vo.getStatus().intValue() == DeliveryStatusEnum.COMPLETE_RECEIVE.getCode()) {
            throw new ServiceException("发车单已接车，不允许修改");
        }
        List<RwEntruckVo> entruckVos = entruckService.selectByDepartId(vo.getId());
        if (vo.getStatus().intValue() == DeliveryStatusEnum.COMPLETE_DEPART.getCode()) {
            for (RwEntruckVo entruckVo : entruckVos) {
                if (entruckVo.getStatus().intValue() == DeliveryStatusEnum.COMPLETE_RECEIVE.getCode()) {
                    throw new ServiceException("装车单已接车，不允许修改");
                }
            }
            if (entruckVos.size() > 1) {
                if (ObjectUtil.isNull(bo.getCarpoolFee())) {
                    throw new ServiceException("合车费不能为空");
                }
            } else {
                bo.setCarpoolFee(null);
            }
            BigDecimal logisticsFee = NumberUtil.add(bo.getEntruckFee(), bo.getParkingFee(), bo.getInsuranceFee(), bo.getFreightFee(), bo.getCarpoolFee());
            RwEntruckVo entruckVo = entruckVos.stream().collect(Collectors.toMap(RwEntruckVo::getLogisticsId, Function.identity())).get(vo.getLogisticsId());
            if (entruckVo.getLogisticsFee().doubleValue() != logisticsFee.doubleValue()) {
                entruckService.updateLogisticsFee(entruckVo.getId(), logisticsFee);
            }
        } else if (vo.getStatus().intValue() == DeliveryStatusEnum.WAIT_DEPART.getCode()) {
            if (CollUtil.isEmpty(bo.getLogisticsList())) {
                throw new ServiceException("车辆物流列表不能为空");
            }
            Map<Long, RwDepartLogisticsBo> logisticsMap = bo.getLogisticsList().stream().collect(Collectors.toMap(RwDepartLogisticsBo::getLogisticsId, Function.identity(), (key1, key2) -> key2));
            if (bo.getLogisticsList().size() != logisticsMap.size()) {
                throw new ServiceException("合车物流不允许有重复");
            }
            List<Long> deleteIds = this.getDeleteLogisticsIds(vo, logisticsMap);
            // 剩余为新增
            if (logisticsMap.size() > 0) {
                departService.insertBatchLogistics(vo, logisticsMap.values().stream().toList());
            }
            if (deleteIds.size() > 0) {
                departService.deleteByIdLogistics(deleteIds);
            }
        }
        departService.update(bo);
    }

    private List<Long> getDeleteLogisticsIds(RwDepartVo vo, Map<Long, RwDepartLogisticsBo> logisticsMap) {
        List<Long> deleteIds = new ArrayList<>();
        // 计算需要修改的物流线
        List<RwDepartLogisticsVo> logisticsVos = departService.selectDepartLogisticsList(ListUtil.toList(vo.getId()));
        for (RwDepartLogisticsVo logVo : logisticsVos) {
            if (logisticsMap.containsKey(logVo.getLogisticsId())) {
                logisticsMap.remove(logVo.getLogisticsId());
            } else {
                if (logVo.getLogisticsId().intValue() == vo.getLogisticsId().intValue()) {
                    throw new ServiceException("车辆主物流线不允许删除");
                }
                deleteIds.add(logVo.getId());
            }
        }
        return deleteIds;
    }
}
