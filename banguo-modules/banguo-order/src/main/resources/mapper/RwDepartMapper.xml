<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.RwDepartMapper">

    <select id="customWaitRelatedList" resultType="cn.xianlink.order.domain.depart.vo.RwDepartVo">
        SELECT rd.depart_id as id,
               rd.depart_no
        FROM rw_depart_logistics rd
                 left join rw_entruck re on re.depart_id = rd.depart_id and re.logistics_id = rd.logistics_id and re.del_flag = 0
        where rd.del_flag = 0
          and re.id is null
          and rd.region_wh_id = #{regionWhId}
          and rd.logistics_id = #{logisticsId}
          and rd.sale_date = #{saleDate}
        order by rd.id
    </select>

    <select id="selectDepartTimeBySkuId" resultType="java.util.Date">
        select depart.create_time
        from order_item item
                 inner join rw_entruck entruck
                            on item.logistics_id = entruck.logistics_id and item.sale_date = entruck.sale_date
                 inner join rw_depart depart on entruck.depart_id = depart.id where item.supplier_sku_id = #{skuId,jdbcType=BIGINT} limit 1
    </select>

    <select id="getSettlementDepartInfo" resultType="cn.xianlink.order.domain.depart.vo.RwDepartVo">
        select
        coalesce(sum(a.entruck_fee),0) as entruckFee,
        coalesce(sum(a.parking_fee),0) as parkingFee,
        coalesce(sum(a.freight_fee),0) as freightFee,
        coalesce(sum(a.carpool_fee),0) as carpoolFee
        from rw_depart a
        left join rw_entruck b
        on a.id = b.depart_id and b.del_flag = 0
        <where>
            a.del_flag = 0
            <if test="dateInfo != null">
                and b.depart_date = #{dateInfo}
            </if>
            <if test="cityWhId != null">
                and b.city_wh_id = #{cityWhId}
            </if>
            <if test="regionWhId != null">
                and b.region_wh_id = #{regionWhId}
            </if>
        </where>
    </select>
</mapper>
