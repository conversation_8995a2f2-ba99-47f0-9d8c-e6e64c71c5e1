<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.ReceiveGoodsRecordMapper">

    <select id="newPage" resultType = "cn.xianlink.order.domain.vo.receiveGoods.ReceiveGoodsPageV2VO">
        select
        r.id,
        r.customer_id,
        r.customer_name,
        r.delivery_number,
        r.place_level2_code,
        count(r.order_id) as orderCount,
        sum(r.order_count) as orderProductCount,
        sum(r.stay_count) as stayCount
        from receive_goods_record r
        <where>
            r.del_flag = 0
            and r.sale_date = #{bo.saleDate}
            <if test="bo.cityWhId != null">
                and r.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.customerId != null">
                and r.customer_id = #{bo.customerId}
            </if>
            <if test="bo.customerName != null and bo.customerName != ''">
                and (r.customer_name like concat('%', #{bo.customerName},'%')
                or r.customer_alias like concat('%', #{bo.customerName},'%'))
            </if>
            <if test="bo.placeId != null">
                and ((r.place_id = #{bo.placeId} and r.place_id_level2 = 0 ) or r.place_id_level2 = #{bo.placeId})
            </if>
        </where>
        group by r.customer_id
        order by r.delivery_number
    </select>

    <select id="page" resultType="cn.xianlink.order.domain.vo.receiveGoods.ReceiveGoodsPageVO">
        select r.*
        from
        receive_goods_record r
        <where>
            r.del_flag = 0
            and r.status != 0
            <if test="bo.customerId != null">
                and r.customer_id = #{bo.customerId}
            </if>
            <if test="bo.cityWhId != null">
                and r.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.orderCode != null and bo.orderCode != ''">
                and r.order_code = #{bo.orderCode}
            </if>
            <if test="bo.deliveryNumber != null">
                and r.delivery_number = #{bo.deliveryNumber}
            </if>
            <if test="bo.strDeliveryNumber != null and bo.strDeliveryNumber != ''">
                and (r.delivery_number like  concat('%', #{bo.strDeliveryNumber},'%')
                or  r.place_level2_code like  concat('%', #{bo.strDeliveryNumber},'%') )
            </if>
            <if test="bo.status != null">
                and r.status = #{bo.status}
            </if>
            <if test="bo.orderType != null">
                and r.order_type = #{bo.orderType}
            </if>
            <if test="bo.orderTimeStart != null and bo.orderTimeEnd != null">
                and r.order_time between #{bo.orderTimeStart} and #{bo.orderTimeEnd}
            </if>
            <if test="bo.customerName != null and bo.customerName != ''">
                and (r.customer_name like concat('%', #{bo.customerName},'%')
                or r.customer_alias like concat('%', #{bo.customerName},'%'))
            </if>
            <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
                and r.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
            </if>
            <if test="bo.placeIdList != null and bo.placeIdList.size() >0">
                and r.place_id in
                <foreach collection="bo.placeIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and
                <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
                    (
                </if>
                r.place_id_level2 = 0
            </if>
            <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
                <if test="bo.placeIdList != null and bo.placeIdList.size() > 0">
                    or
                </if>
                <if test="bo.placeIdList == null or bo.placeIdList.size() == 0">
                    and
                </if>
                r.place_id_level2 in
                <foreach collection="bo.placeIdLevel2List" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <if test="bo.placeIdList != null and bo.placeIdList.size() > 0">
                    )
                </if>
            </if>
        </where>
        order by r.create_time desc, r.id
    </select>
    <select id="getReceiveRecord" resultType="cn.xianlink.order.domain.ReceiveGoodsRecord">
        select * from receive_goods_record
        where
        del_flag = 0
        and status = 2
        <if test="bo.customerId != null">
            and customer_id = #{bo.customerId}
        </if>
        <if test="bo.cityWhId != null">
            and city_wh_id = #{bo.cityWhId}
        </if>
        <if test="bo.placeId != null">
            and place_id = #{bo.placeId}
        </if>
    </select>

    <select id="selectByLogisticsIdAndSaleDate" resultType="cn.xianlink.order.domain.ReceiveGoodsRecord">
        select rgr.* from banguo_order.`order` as o inner join banguo_order.receive_goods_record rgr on o.id = rgr.order_id
        where
        <if test="logisticsId != null">
            (o.logistics_id = #{logisticsId,jdbcType=BIGINT} or o.logistics_id_level2 = #{logisticsId,jdbcType=BIGINT})
        </if>
        <if test="logisticsId == null">
            o.city_wh_id = #{cityId,jdbcType=BIGINT}
        </if>
        <if test="placeIdList != null and placeIdList.size() >0">
            and o.place_id in
            <foreach collection="placeIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and
            <if test="placeIdLevel2List != null and placeIdLevel2List.size() > 0">
                (
            </if>
            o.place_id_level2 = 0
        </if>
        <if test="placeIdLevel2List != null and placeIdLevel2List.size() > 0">
            <if test="placeIdList != null and placeIdList.size() > 0">
                or
            </if>
            <if test="placeIdList == null or placeIdList.size() == 0">
                and
            </if>
            o.place_id_level2 in
            <foreach collection="placeIdLevel2List" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="placeIdList != null and placeIdList.size() > 0">
                )
            </if>
        </if>
        and o.sale_date = '${saleDate}' and o.status in ('ALREADY', 'FINISH', 'CANCEL') and (o.cancel_type is null or o.cancel_type in ('OUT', 'FEW'))
    </select>

    <select id="getByOrderId" resultType="cn.xianlink.order.domain.ReceiveGoodsRecord">
        select * from receive_goods_record where order_id = #{orderId}
    </select>
</mapper>
