<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.SortGoodsDetailMapper">
    <update id="updateDetailBatchById">
        update sort_goods_detail
        set status = #{sortGoodsStatus}
        where sort_goods_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and status != 4
    </update>

    <select id="getDeliveryNumbe" resultType="cn.xianlink.order.domain.SortGoodsDetail">
        select
        sgd.*
        from sort_goods_detail sgd left join sort_goods sg on sgd.sort_goods_id = sg.id and sg.del_flag = 0
        where
        sgd.del_flag = 0
        <if test="customerIdList != null and customerIdList.size() > 0">
            and sgd.customer_id in
            <foreach collection="customerIdList" item="customerId" open="(" separator="," close=")">
                #{customerId}
            </foreach>
        </if>
        and sgd.create_time >= #{date}
        and sg.place_id = #{placeId}
    </select>
    <select id="sortListByIds" resultType="cn.xianlink.order.domain.SortGoodsDetail">
        select * from sort_goods_detail
        where
        del_flag = 0
        and sort_goods_id in
        <foreach collection="sortGoodsIds" item="sortGoodsId" open="(" separator="," close=")">
            #{sortGoodsId}
        </foreach>
        <if test="bo.logisticsIds != null and bo.logisticsIds.size() > 0">
            and
            <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
                (
            </if>
            place_id_level2 = 0
        </if>
        <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
            <if test="bo.logisticsIds != null and bo.logisticsIds.size() > 0">
                or
            </if>
            <if test="bo.logisticsIds == null  or bo.logisticsIds.size() == 0">
                and
            </if>
            place_id_level2 in
            <foreach collection="bo.placeIdLevel2List" item="placeId" open="(" separator="," close=")">
                #{placeId}
            </foreach>
            <if test="bo.logisticsIds != null and bo.logisticsIds.size() > 0">
                )
            </if>
        </if>
    </select>
    <select id="getIsMoreGoods" resultType="cn.xianlink.order.domain.SortGoodsDetail">
        select
        d.*
        from sort_goods_detail d
        left join sort_goods s on d.sort_goods_id = s.id and s.del_flag = 0
        where
        d.del_flag = 0
        and s.city_wh_id = #{cityWhId}
        and s.supplier_sku_id in
        <foreach collection="supplierSkuIds" item="supplierSkuId" open="(" separator="," close=")">
            #{supplierSkuId}
        </foreach>
        <if test="bo.saleDate != null">
            and s.sale_date = #{bo.saleDate}
        </if>
        <if test="logisticsList != null and logisticsList.size() > 0">
            and s.logistics_id in
            <foreach collection="logisticsList" item="logisticsId" open="(" separator="," close=")">
                #{logisticsId}
            </foreach>
            and
            <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
                (
            </if>
            d.place_id_level2 = 0
        </if>
        <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
            <if test="logisticsList != null and logisticsList.size() > 0">
                or
            </if>
            <if test="logisticsList == null or logisticsList.size() == 0">
                and
            </if>
            d.place_id_level2 in
            <foreach collection="bo.placeIdLevel2List" item="placeId" open="(" separator="," close=")">
                #{placeId}
            </foreach>
            <if test="logisticsList != null and logisticsList.size() > 0">
                )
            </if>
        </if>
    </select>
    <select id="selectByOrderItemId" resultType="cn.xianlink.order.domain.SortGoodsDetail">
        select * from sort_goods_detail where order_item_id = #{id} and del_flag = 0
    </select>
</mapper>
