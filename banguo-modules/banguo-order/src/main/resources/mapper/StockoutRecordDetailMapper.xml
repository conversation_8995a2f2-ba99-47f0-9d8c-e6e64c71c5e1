<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.StockoutRecordDetailMapper">


    <select id="getAuditStatusList" resultType="cn.xianlink.order.domain.StockoutRecordDetail">
        select
        d.*,sum(d.stockout_count) as stockoutCount
        from stockout_record_detail d left join stockout_record r on d.stockout_record_id = r.id
        where
        d.del_flag = 0
        and r.del_flag = 0
        and d.order_item_id in
        <foreach collection="orderItemList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and r.audit_status in
        <foreach collection="auditStatus" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
        group by d.order_item_id
    </select>

    <select id="selectByStockoutRecordCode" resultType="cn.xianlink.order.domain.StockoutRecordDetail">
        select srd.* from stockout_record sr
        left join stockout_record_detail srd  on sr.id = srd.stockout_record_id
        where sr.del_flag = 0 and srd.del_flag = 0
        <if test="code != null and code != ''">
            and sr.code = #{code}
        </if>
        <if test="supplierSkuId != null">
            and sr.supplier_sku_id = #{supplierSkuId}
        </if>
    </select>


    <select id="selectByOrderItemId" resultType="cn.xianlink.order.domain.StockoutRecordDetail">
        select detail.*
        from stockout_record head
        inner join stockout_record_detail detail on head.id = detail.stockout_record_id
        where detail.order_item_id = #{itemId,jdbcType=BIGINT}
        and head.del_flag = 0 and detail.del_flag = 0
        <if test="auditStatus != null and auditStatus.size() != 0" >
            and head.audit_status in
            <foreach collection="auditStatus" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </select>

    <select id="selectByUnConfirm" resultType="cn.xianlink.order.domain.StockoutRecordDetail">
        select detail.*
        from stockout_record head
        inner join stockout_record_detail detail on head.id = detail.stockout_record_id
        where detail.order_item_id = #{itemId,jdbcType=BIGINT}
        and head.audit_status = 1 and head.del_flag = 0 and detail.del_flag = 0
    </select>
    <select id="selectCreareList" resultType="cn.xianlink.order.domain.StockoutRecordDetail">
        select d.*
        from stockout_record_detail d
        left join stockout_record r on d.stockout_record_id = r.id
        where r.del_flag = 0 and d.del_flag = 0
        and r.audit_status in(1,2)
        and d.order_item_id in
        <foreach collection="orderItemIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and r.create_scene_code = #{code}
    </select>

    <update id="stockoutInvalid">
        update stockout_record_detail set status = 3 where stockout_record_id = #{stockoutRecordId,jdbcType=BIGINT}
    </update>
</mapper>
