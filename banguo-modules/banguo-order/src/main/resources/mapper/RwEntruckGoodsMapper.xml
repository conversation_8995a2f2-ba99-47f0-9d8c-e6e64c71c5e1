<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.RwEntruckGoodsMapper">

    <sql id="sumSql">
        sum(if(rer.status &lt; 30, reg.delivery_quantity, 0)) as delivery_quantity,
        sum(if(rer.status = 30, reg.entruck_quantity, 0)) as entruck_quantity,
        sum(if(reg.diff_status = 32, reg.delivery_quantity-reg.entruck_quantity, 0)) as diff_quantity
    </sql>
    <select id="customSkuSumListBySupplierId" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsSumVo">
        select reg.supplier_sku_id,
               <include refid="sumSql"/>
        from rw_entruck_record rer
                 inner join rw_entruck_goods reg on reg.record_id = rer.id and reg.del_flag = 0
        where rer.del_flag = 0
          and rer.status in (10, 20, 30)
          and rer.region_wh_id = #{bo.regionWhId}
        <if test="bo.supplierId != null">
            and rer.supplier_id = #{bo.supplierId}
        </if>
        <if test="bo.saleDate != null">
            and rer.sale_date = #{bo.saleDate}
        </if>
        <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
            and rer.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
        </if>
        <if test="bo.buyerCode != null and bo.buyerCode != ''">
            and reg.buyer_code = #{bo.buyerCode}
        </if>
        <if test="bo.businessTypes != null and bo.businessTypes.size() > 0">
            and rer.business_type in
            <foreach collection="bo.businessTypes" item="businessType" open="(" close=")" separator=",">
                #{businessType}
            </foreach>
        </if>
        <if test="bo.supplierSkuIds != null and bo.supplierSkuIds.size() > 0">
            and reg.supplier_sku_id in
            <foreach collection="bo.supplierSkuIds" item="supplierSkuId" open="(" close=")" separator=",">
                #{supplierSkuId}
            </foreach>
        </if>
        <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
            and rer.logistics_id in
            <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                #{logisticsId}
            </foreach>
        </if>
        group by reg.supplier_sku_id
    </select>

    <select id="customLogisticsSumListBySupplierId" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsSumVo">
        select rer.logistics_id, reg.supplier_sku_id, rer.supplier_id,
               <include refid="sumSql"/>
        from rw_entruck_record rer
                 inner join rw_entruck_goods reg on reg.record_id = rer.id and reg.del_flag = 0
        where rer.del_flag = 0
          and rer.status in (10, 20, 30)
          and rer.sale_date = #{bo.saleDate}
          and rer.region_wh_id = #{bo.regionWhId}
        <if test="bo.supplierId != null">
            and rer.supplier_id = #{bo.supplierId}
        </if>
        <if test="bo.businessTypes != null and bo.businessTypes.size() > 0">
            and rer.business_type in
            <foreach collection="bo.businessTypes" item="businessType" open="(" close=")" separator=",">
                #{businessType}
            </foreach>
        </if>
        <if test="bo.supplierSkuIds != null and bo.supplierSkuIds.size() > 0">
            and reg.supplier_sku_id in
            <foreach collection="bo.supplierSkuIds" item="supplierSkuId" open="(" close=")" separator=",">
                #{supplierSkuId}
            </foreach>
        </if>
        <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
            and rer.logistics_id in
            <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                #{logisticsId}
            </foreach>
        </if>
        group by rer.logistics_id, reg.supplier_sku_id
    </select>

    <select id="customSupplierSumListByRegionWhId" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsSumVo">
        select rer.supplier_id, rer.supplier_dept_id,
               <include refid="sumSql"/>
        from rw_entruck_record rer
                 inner join rw_entruck_goods reg on reg.record_id = rer.id and reg.del_flag = 0
        where rer.del_flag = 0
          and rer.status in (10, 20, 30)
          and rer.sale_date = #{bo.saleDate}
          and rer.region_wh_id = #{bo.regionWhId}
        <if test="bo.supplierId != null">
            and rer.supplier_id = #{bo.supplierId}
        </if>
        <if test="bo.buyerCode != null and bo.buyerCode != ''">
            and reg.buyer_code = #{bo.buyerCode}
        </if>
        <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
            and rer.logistics_id in
            <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                #{logisticsId}
            </foreach>
        </if>
        group by rer.supplier_id, rer.supplier_dept_id
    </select>

    <select id="customSupplierSumListByLogisticsId" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsSumVo">
        select rer.logistics_id, rer.supplier_id, rer.supplier_dept_id,
        <include refid="sumSql"/>
        from rw_entruck_record rer
        inner join rw_entruck_goods reg on reg.record_id = rer.id and reg.del_flag = 0
        where rer.del_flag = 0
        and rer.status in (10, 20, 30)
        and rer.sale_date = #{bo.saleDate}
        and rer.region_wh_id = #{bo.regionWhId}
        <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
            and rer.logistics_id in
            <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                #{logisticsId}
            </foreach>
        </if>
        <if test="bo.notBusinessTypes != null and bo.notBusinessTypes.size() > 0">
            and rer.business_type not in
            <foreach collection="bo.notBusinessTypes" item="businessType" open="(" close=")" separator=",">
                #{businessType}
            </foreach>
        </if>
        group by rer.logistics_id, rer.supplier_id, rer.supplier_dept_id
    </select>

    <select id="customEntruckSumListByDeliveryId" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsSumVo">
        select reg.delivery_id, sum(entruck_quantity) as entruck_quantity
        from rw_entruck_goods reg
        where reg.del_flag = 0
          and reg.delivery_id in
            <foreach collection="deliveryIds" item="deliveryId" open="(" close=")" separator=",">
                #{deliveryId}
            </foreach>
        group by reg.delivery_id
    </select>

    <select id="customSumListByDeliveryId" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsCountVo">
        select reg.supplier_sku_id,
               reg.spu_name,
               reg.img_url,
               reg.spu_gross_weight,
               reg.spu_net_weight,
               sum(reg.delivery_quantity) as delivery_quantity,
               sum(reg.entruck_quantity) as entruck_quantity,
               sum(reg.delivery_quantity - reg.entruck_quantity) as diff_quantity
        from rw_entruck_goods reg
        where reg.del_flag = 0
          and reg.diff_status = 32
          and reg.delivery_id = #{deliveryId}
        group by reg.supplier_sku_id
    </select>

    <select id="customEntruckGoodsSumListByRegionWhId" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsCountVo">
        select reg.supplier_sku_id,
               reg.spu_name,
               reg.img_url,
               reg.spu_gross_weight,
               reg.spu_net_weight,
               rer.supplier_id,
               rer.supplier_name,
               rer.supplier_dept_id,
               rer.supplier_dept_name,
               rer.sale_date,
               sum(reg.delivery_quantity) as delivery_quantity,
               sum(reg.entruck_quantity) as entruck_quantity,
               sum(if(reg.diff_status > 31, reg.delivery_quantity-reg.entruck_quantity, 0)) as diff_quantity
        from rw_entruck_record rer
                 inner join rw_entruck_goods reg on reg.record_id = rer.id and reg.del_flag = 0
        where rer.del_flag = 0
          and rer.status in (20, 30)
          and rer.sale_date = #{bo.saleDate}
          and rer.region_wh_id = #{bo.regionWhId}
          and rer.entruck_no = ''
        <if test="bo.entruckType == 1">
            and rer.business_type = 1
        </if>
        <if test="bo.entruckType == 2">
            and rer.business_type in (20, 30)
            and rer.picking_no = ''
        </if>
        <if test="bo.spuName != null and bo.spuName != ''">
            and reg.spu_name like concat('%', #{bo.spuName}, '%')
        </if>
        <if test="bo.supplierSkuId != null">
            and reg.supplier_sku_id = #{bo.supplierSkuId}
        </if>
        <if test="bo.supplierSkuIds != null and bo.supplierSkuIds.size() > 0">
            and reg.supplier_sku_id in
            <foreach collection="bo.supplierSkuIds" item="supplierSkuId" open="(" close=")" separator=",">
                #{supplierSkuId}
            </foreach>
        </if>
        <if test="bo.buyerCode != null and bo.buyerCode != ''">
            and reg.buyer_code = #{bo.buyerCode}
        </if>
        <if test="bo.supplierName != null and bo.supplierName != ''">
            and (rer.supplier_name like concat('%', #{bo.supplierName}, '%') or rer.supplier_dept_name like concat('%', #{bo.supplierName}, '%'))
        </if>
        group by reg.supplier_sku_id
        order by rer.supplier_id asc, reg.supplier_sku_id asc
    </select>

    <select id="customEntruckGoodsLogisticsSumListBySkuId" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckRecordGoodsSumVo">
        select reg.supplier_sku_id, rer.logistics_id,
               rer.parking_no,
               rer.city_wh_id,
               group_concat(reg.id) as ids,
               sum(reg.delivery_quantity) as delivery_quantity,
               sum(reg.entruck_quantity)  as entruck_quantity,
               sum(if(reg.diff_status > 31, reg.delivery_quantity-reg.entruck_quantity, 0)) as diff_quantity
        from rw_entruck_record rer
                 inner join rw_entruck_goods reg on reg.record_id = rer.id and reg.del_flag = 0
        where rer.del_flag = 0
          and rer.status in (20, 30)
          and rer.sale_date = #{bo.saleDate}
          and rer.region_wh_id = #{bo.regionWhId}
          and rer.entruck_no = ''
        <if test="bo.entruckType == 1">
            and rer.business_type = 1
        </if>
        <if test="bo.entruckType == 2">
            and rer.business_type in (20, 30)
            and rer.picking_no = ''
        </if>
        <if test="bo.supplierSkuId != null">
          and reg.supplier_sku_id = #{bo.supplierSkuId}
        </if>
        <if test="bo.supplierSkuIds != null and bo.supplierSkuIds.size() > 0">
            and reg.supplier_sku_id in
            <foreach collection="bo.supplierSkuIds" item="supplierSkuId" open="(" close=")" separator=",">
                #{supplierSkuId}
            </foreach>
        </if>
        group by reg.supplier_sku_id, rer.logistics_id
        order by rer.parking_no
    </select>

    <select id="customWaitEntruckGoodsListBySkuId" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsVo">
        select reg.id, reg.delivery_id, reg.record_id, reg.delivery_quantity, reg.entruck_quantity
        from rw_entruck_record rer
                 inner join rw_entruck_goods reg on reg.record_id = rer.id and reg.del_flag = 0
        where rer.del_flag = 0
          and rer.status = 20
          and rer.sale_date = #{bo.saleDate}
          and reg.delivery_quantity - reg.entruck_quantity > 0
        <if test="bo.regionWhId != null">
            and rer.region_wh_id = #{bo.regionWhId}
        </if>
        <if test="bo.entruckType != null and bo.entruckType == 1">
            and rer.business_type = 1
        </if>
        <if test="bo.entruckType != null and bo.entruckType == 2">
            and rer.business_type in (20, 30)
            and rer.picking_no = ''
        </if>
        <if test="bo.logisticsId != null">
            and rer.logistics_id = #{bo.logisticsId}
        </if>
    </select>

    <select id="customWaitEntruckGoodsLogisticsList" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsDiffVo">
        select reg.supplier_sku_id,
               reg.spu_name,
               reg.img_url,
               reg.spu_gross_weight,
               reg.spu_net_weight,
               rer.supplier_id,
               rer.supplier_name,
               rer.supplier_dept_id,
               rer.supplier_dept_name,
               rer.sale_date,
               rer.logistics_id,
               rer.parking_no,
               rer.city_wh_id,
               rer.business_type,
               sum(reg.delivery_quantity - reg.entruck_quantity) as diff_quantity
        from rw_entruck_record rer
                 inner join rw_entruck_goods reg on reg.record_id = rer.id and reg.del_flag = 0
        where rer.del_flag = 0
          and rer.status = 20
          and rer.sale_date = #{bo.saleDate}
          and rer.region_wh_id = #{bo.regionWhId}
          and reg.delivery_quantity - reg.entruck_quantity > 0
        <if test="bo.entruckType == 1">
            and rer.business_type = 1
        </if>
        <if test="bo.entruckType == 2">
            and rer.business_type in (20, 30)
            and rer.picking_no = ''
        </if>
        group by reg.supplier_sku_id, rer.logistics_id
        order by reg.supplier_sku_id
    </select>

    <select id="customSkuIdEntruckList" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsVo">
        select reg.entruck_time, reg.entruck_quantity, reg.delivery_quantity, rer.status
        from rw_entruck_record rer
                 inner join rw_entruck_goods reg on reg.record_id = rer.id and reg.del_flag = 0
        where rer.del_flag = 0
          and rer.status in (10, 20,30)
          and rer.sale_date = #{bo.saleDate}
          and rer.region_wh_id = #{bo.regionWhId}
          and rer.logistics_id = #{bo.logisticsId}
          and reg.supplier_sku_id = #{bo.supplierSkuId}
        order by reg.entruck_time
    </select>

    <select id="pagePicking" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckRecordGoodsSumVo">
        select rer.picking_no,rer.parking_no,
            rer.logistics_id,
            rer.region_wh_id,
            rer.city_wh_id,
            rer.sale_date,
            rer.bas_portage_team_id,
            if(rer.entruck_name ='', reg.update_name, rer.entruck_name) as entruck_name,
            min(rer.status) status,
            max(rer.create_time) create_time,
            sum(reg.delivery_quantity) as delivery_quantity,
            sum(reg.entruck_quantity) as entruck_quantity,
            sum(if(reg.diff_status > 31, reg.delivery_quantity-reg.entruck_quantity, 0)) as diff_quantity,
            group_concat(rer.id) as record_ids
        from rw_entruck_record rer
            inner join rw_entruck_goods reg on rer.id = reg.record_id and reg.del_flag = 0
        where rer.del_flag = 0
        and rer.picking_no != ''
        <if test="!bo.isPickingNoQuery ">
            and rer.sale_date = #{bo.saleDate}
            and rer.region_wh_id = #{bo.regionWhId}
        </if>
        <if test="bo.pickingNo != null and bo.pickingNo != '' ">
            and rer.picking_no = #{bo.pickingNo}
        </if>
        <if test="bo.pickingNoList != null and bo.pickingNoList.size() > 0 ">
            and rer.picking_no in
            <foreach collection="bo.pickingNoList" item="pickingNo" open="(" close=")" separator=",">
                #{pickingNo}
            </foreach>
        </if>
        <if test="bo.basPortageTeamId != null">
            and rer.bas_portage_team_id = #{bo.basPortageTeamId}
        </if>
        <if test="bo.logisticsId != null">
            and rer.logistics_id = #{bo.logisticsId}
        </if>
        <if test="bo.cityWhId != null">
            and rer.city_wh_id = #{bo.cityWhId}
        </if>
        <if test="bo.pickingStatus != null">
            and rer.status = #{bo.pickingStatus}
        </if>
        <if test="bo.pickingStatus == null">
            and rer.status in (20, 30)
        </if>
        <if test="bo.spuName != null and bo.spuName != ''">
            and reg.spu_name like concat('%', #{bo.spuName}, '%')
        </if>
        group by rer.picking_no
        order by status asc, create_time desc
    </select>

    <select id="waitEntruckGoodsList" resultType="cn.xianlink.order.domain.todo.vo.TodoGoodsVo">
        select rer.business_type, rer.supplier_id, rer.sale_date,
            reg.supplier_sku_id, reg.spu_name, reg.buyer_name, reg.spu_gross_weight, reg.spu_net_weight,
            sum(reg.delivery_quantity - reg.entruck_quantity) as sku_quantity
        from rw_entruck_record rer
            inner join rw_entruck_goods reg on reg.record_id = rer.id and reg.del_flag = 0
        where rer.del_flag = 0
            and rer.status = 20
            and rer.region_wh_id = #{bo.regionWhId}
            and reg.delivery_quantity - reg.entruck_quantity > 0
        <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
            and rer.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
        </if>
        <if test="bo.buyerCode != null and bo.buyerCode != '' ">
            and reg.buyer_code = #{bo.buyerCode}
        </if>
        <if test="bo.supplierId != null ">
            and rer.supplier_id = #{bo.supplierId}
        </if>
        <if test="bo.entruckType != null ">
            <if test="bo.entruckType == 1">
                and rer.business_type = 1
            </if>
            <if test="bo.entruckType == 2">
                and rer.business_type in (20, 30)
                and rer.picking_no = ''
            </if>
            <if test="bo.entruckType == 3">
                and rer.business_type in (20, 30)
                and rer.picking_no != ''
            </if>
        </if>
        group by reg.supplier_sku_id
    </select>

    <select id="waitDeliveryList" resultType="cn.xianlink.order.domain.todo.vo.TodoGoodsVo">
        select a.business_type, a.supplier_id, a.sale_date, a.supplier_sku_id, a.spu_gross_weight, a.spu_net_weight, a.spu_name, a.buyer_name,
            a.count - ifnull(b.delivery_quantity, 0) - ifnull(c.stockout_count, 0) sku_quantity
        from
            (select business_type, supplier_id, sale_date, supplier_sku_id, spu_gross_weight, spu_net_weight, spu_name, buyer_name, sum(count) as count
                from order_item
                where del_flag = 0 and ( status = 'ALREADY' or status = 'FINISH' or ( status = 'CANCEL' and (cancel_type = 'OUT' or cancel_type = 'FEW') ) )
                and pay_time &lt;= #{bo.payTime}
                and region_wh_id = #{bo.regionWhId}
                and sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
            <if test="bo.businessTypes != null and bo.businessTypes.size() > 0">
                and business_type in
                <foreach collection="bo.businessTypes" item="businessType" open="(" close=")" separator=",">
                    #{businessType}
                </foreach>
            </if>
            <if test="bo.buyerCode != null and bo.buyerCode != '' ">
                and buyer_code = #{bo.buyerCode}
            </if>
            <if test="bo.supplierId != null ">
                and supplier_id = #{bo.supplierId}
            </if>
                group by supplier_sku_id) a
            left join (select reg.supplier_sku_id, sum(if(reg.diff_status = 34, reg.entruck_quantity, reg.delivery_quantity)) delivery_quantity
                from rw_entruck_record rer
                    inner join rw_entruck_goods reg on reg.record_id = rer.id and reg.del_flag = 0
                where rer.del_flag = 0 and rer.status in (10, 20, 30)
                and rer.region_wh_id = #{bo.regionWhId}
                and rer.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
            <if test="bo.businessTypes != null and bo.businessTypes.size() > 0">
                and rer.business_type in
                <foreach collection="bo.businessTypes" item="businessType" open="(" close=")" separator=",">
                    #{businessType}
                </foreach>
            </if>
            <if test="bo.buyerCode != null and bo.buyerCode != '' ">
                and reg.buyer_code = #{bo.buyerCode}
            </if>
            <if test="bo.supplierId != null ">
                and rer.supplier_id = #{bo.supplierId}
            </if>
                group by reg.supplier_sku_id) b on a.supplier_sku_id = b.supplier_sku_id
            left join (select supplier_sku_id, sum(stockout_count) as stockout_count
                from stockout_sku_record
                where del_flag = 0 and type = 1
                and region_wh_id = #{bo.regionWhId}
                and sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
            <if test="bo.supplierId != null ">
                and sku_supplier_id = #{bo.supplierId}
            </if>
                group by supplier_sku_id) c on a.supplier_sku_id = c.supplier_sku_id
        where a.count - ifnull(b.delivery_quantity, 0) - ifnull(stockout_count, 0) > 0
    </select>

    <select id="rwWaitDeliveryList" resultType="cn.xianlink.order.domain.entruck.vo.RwWaitDeliveryGoodsVo">
        select a.business_type, a.region_wh_id, a.logistics_id, a.supplier_id, a.supplier_dept_id, a.sale_date, a.supplier_sku_id, a.spu_name,
               sum(a.count - ifnull(b.delivery_quantity, 0) - ifnull(c.stockout_count, 0)) as wait_delivery_quantity
        from
            (select oi.business_type, oi.region_wh_id, oi.logistics_id, oi.supplier_id, oi.supplier_dept_id, oi.sale_date, oi.supplier_sku_id, oi.spu_name, sum(oi.count) as count
                from order_item oi
                where oi.del_flag = 0 and (oi.status = 'ALREADY' or oi.status = 'FINISH' or (oi.status = 'CANCEL' and (oi.cancel_type = 'OUT' or oi.cancel_type = 'FEW')))
                and oi.sale_date = #{bo.saleDate}
                and oi.business_type != 40
            <if test="bo.regionWhId != null ">
                and oi.region_wh_id = #{bo.regionWhId}
            </if>
                group by oi.supplier_sku_id, oi.logistics_id) a
            left join (select reg.supplier_sku_id, rer.logistics_id, sum(if(reg.diff_status = 34, reg.entruck_quantity, reg.delivery_quantity)) delivery_quantity
                from rw_entruck_record rer
                    inner join rw_entruck_goods reg on reg.record_id = rer.id and reg.del_flag = 0
                where rer.del_flag = 0 and rer.status in (10, 20, 30)
                and rer.sale_date = #{bo.saleDate}
                and rer.business_type != 40
            <if test="bo.regionWhId != null ">
                and rer.region_wh_id = #{bo.regionWhId}
            </if>
                group by reg.supplier_sku_id, rer.logistics_id) b on a.supplier_sku_id = b.supplier_sku_id and a.logistics_id = b.logistics_id
            left join (select ssr.supplier_sku_id, ssr.logistics_id, sum(ssr.stockout_count) as stockout_count
                from stockout_sku_record ssr
                where ssr.del_flag = 0 and ssr.type = 1
                and ssr.sale_date = #{bo.saleDate}
            <if test="bo.regionWhId != null ">
                and ssr.region_wh_id = #{bo.regionWhId}
            </if>
                group by ssr.supplier_sku_id, ssr.logistics_id) c on a.supplier_sku_id = c.supplier_sku_id and a.logistics_id = c.logistics_id
        where a.count - ifnull(b.delivery_quantity, 0) - ifnull(c.stockout_count, 0) > 0
        group by a.supplier_sku_id, a.logistics_id;
    </select>


</mapper>
