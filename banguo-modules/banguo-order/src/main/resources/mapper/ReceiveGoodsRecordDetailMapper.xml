<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.ReceiveGoodsRecordDetailMapper">


    <select id="selectByItems" resultType="cn.xianlink.order.domain.ReceiveGoodsRecordDetail">
        select detail.*
        from receive_goods_record_detail detail
            inner join receive_goods_record record on record.id = detail.receive_goods_record_id
        where detail.order_item_id
                in
            <foreach collection="orderItemIds" item="orderItemId" open="(" separator="," close=")">
                #{orderItemId,jdbcType=BIGINT}
            </foreach>
            and detail.del_flag = 0
            and record.status = 3
            and record.del_flag = 0
    </select>

    <select id="getSaleDateList" resultType="cn.xianlink.order.domain.vo.receiveGoods.ReceiveGoodsDetailVO">
        select a.*,b.sale_date from
        receive_goods_record_detail a left join order_item b on a.order_item_id = b.id
        left join receive_goods_record c on a.receive_goods_record_id = c.id
        where
        c.customer_id = #{customerId} and b.sale_date &lt;= #{firstDay} and b.sale_date &gt;= #{lastSevenDay}
        order by b.sale_date
    </select>

    <select id="listBySaleDateAndCustomerId" resultType="cn.xianlink.order.domain.vo.receiveGoods.ReceiveGoodsDetailVO">
        select a.id as detailId, c.customer_id, a.*
        from
        receive_goods_record_detail a left join receive_goods_record c on a.receive_goods_record_id = c.id
        where
        a.sort_goods_status = 4
        <if test="bo.customerId != null">
            and c.customer_id = #{bo.customerId}
        </if>
        <if test="bo.saleDate != null">
            and c.sale_date = #{bo.saleDate}
        </if>
        <if test="bo.cityWhId != null">
            and c.city_wh_id = #{bo.cityWhId}
        </if>
        <if test="bo.orderItemId != null">
            and a.order_item_id = #{bo.orderItemId}
        </if>
        <if test="bo.supplierSkuId != null">
            and a.supplier_sku_id = #{bo.supplierSkuId}
        </if>
        <if test="bo.placeIdList != null and bo.placeIdList.size() >0">
            and c.place_id in
            <foreach collection="bo.placeIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and
            <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
                (
            </if>
            c.place_id_level2 = 0
        </if>
        <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
            <if test="bo.placeIdList != null and bo.placeIdList.size() > 0">
                or
            </if>
            <if test="bo.placeIdList == null or bo.placeIdList.size() == 0">
                and
            </if>
            c.place_id_level2 in
            <foreach collection="bo.placeIdLevel2List" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="bo.placeIdList != null and bo.placeIdList.size() > 0">
                )
            </if>
        </if>
        order by a.id desc
    </select>

    <select id="getByParam" resultType="cn.xianlink.order.domain.ReceiveGoodsRecordDetail">
        select a.* from
        receive_goods_record_detail a
        left join receive_goods_record c on a.receive_goods_record_id = c.id
        where
        a.id = #{bo.detailId}
    </select>

    <update id="updateAfterSale">
        update receive_goods_record_detail set after_sale_status = #{afterSaleStatus,jdbcType=INTEGER} where order_item_id = #{orderItemId,jdbcType=BIGINT}
    </update>

    <select id="queryCityId" resultType="java.lang.Long">
        select rgr.city_wh_id
        from banguo_order.receive_goods_record rgr
                 inner join banguo_order.receive_goods_record_detail detail on rgr.id = detail.receive_goods_record_id
        where detail.id = #{detailId,jdbcType=BIGINT}
    </select>
</mapper>
