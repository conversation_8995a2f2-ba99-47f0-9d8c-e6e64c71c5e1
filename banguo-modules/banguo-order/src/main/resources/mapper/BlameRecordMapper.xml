<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.BlameRecordMapper">

    <select id="blameRecordPage" resultType="cn.xianlink.order.domain.vo.blameRecord.BlameRecordPageVO">
        select
        br.id as blameRecordId, br.code, br.source_code, br.city_wh_id, br.supplier_id, br.region_wh_id,
        br.logistics_name,br.supplier_dept_id,
        br.supplier_sku_id as supplierSkuId,
        br.spu_name, br.blame_amount + br.subsidy_free_amount as blameAmount, br.source_type, br.refund_status, br.blame_status, br.update_time, br.sale_date
        from
        blame_record br
        where br.del_flag = 0
        <if test="bo.code != null and bo.code != ''">
            and br.code = #{bo.code}
        </if>
        <if test="bo.sourceCode != null and bo.sourceCode != ''">
            and br.source_code = #{bo.sourceCode}
        </if>
        <if test="bo.buyerId != null">
            and br.buyer_id = #{bo.buyerId}
        </if>
        <if test="bo.cityWhId != null">
            and br.city_wh_id = #{bo.cityWhId}
        </if>
        <if test="bo.supplierId != null">
            and br.supplier_id = #{bo.supplierId}
        </if>
        <if test="bo.regionWhId != null">
            and br.region_wh_id = #{bo.regionWhId}
        </if>
        <if test="bo.logisticsId != null">
            and br.logistics_id = #{bo.logisticsId}
        </if>
        <if test="bo.blameStatus != null">
            and br.blame_status = #{bo.blameStatus}
        </if>
        <if test="bo.sourceType != null">
            and br.source_type = #{bo.sourceType}
        </if>
        <if test="bo.orderType != null">
            and br.order_type = #{bo.orderType}
        </if>
        <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
            and br.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
        </if>
        <if test="bo.createTimeStart != null and bo.createTimeEnd != null">
            and br.create_time between #{bo.createTimeStart} and #{bo.createTimeEnd}
        </if>
        <if test="bo.spuName != null and bo.spuName != ''">
            and br.spu_name like concat('%', #{bo.spuName},'%')
        </if>
        <if test="bo.businessType != null">
            and br.business_type = #{bo.businessType}
        </if>
        order by br.update_time desc
    </select>

    <select id="blameRecordDeatilPage" resultType="cn.xianlink.order.domain.vo.blameRecord.BlameRecordPageVO">
        select
        br.id as blameRecordId, br.code, br.source_code, br.city_wh_id, br.supplier_id, br.region_wh_id, br.supplier_dept_id,
        br.logistics_name,sum(brd.amount) as blameAmount,
        br.supplier_sku_id as supplierSkuId,
        br.spu_name, br.source_type, br.refund_status, brd.blame_status as blameDetailStatus, br.update_time
        from
        blame_record_detail brd left join blame_record br on br.id = brd.blame_record_id and br.del_flag = 0
        where brd.del_flag = 0
        <if test="bo.code != null and bo.code != ''">
            and br.code = #{bo.code}
        </if>
        <if test="bo.supplierDeptId != null">
            and br.supplier_dept_id = #{bo.supplierDeptId}
        </if>
        <if test="bo.sourceCode != null and bo.sourceCode != ''">
            and br.source_code = #{bo.sourceCode}
        </if>
        <if test="bo.buyerId != null">
            and br.buyer_id = #{bo.buyerId}
        </if>
        <if test="bo.cityWhId != null">
            and br.city_wh_id = #{bo.cityWhId}
        </if>
        <if test="bo.supplierId != null">
            and br.supplier_id = #{bo.supplierId}
        </if>
        <if test="bo.regionWhId != null">
            and br.region_wh_id = #{bo.regionWhId}
        </if>
        <if test="bo.logisticsId != null">
            and br.logistics_id = #{bo.logisticsId}
        </if>
        <if test="bo.blameStatus != null">
            and brd.blame_status = #{bo.blameStatus}
        </if>
        <if test="bo.sourceType != null">
            and br.source_type = #{bo.sourceType}
        </if>
        <if test="bo.orderType != null">
            and br.other_type = #{bo.orderType}
        </if>
        <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
            and br.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
        </if>
        <if test="bo.createTimeStart != null and bo.createTimeEnd != null">
            and br.create_time between #{bo.createTimeStart} and #{bo.createTimeEnd}
        </if>
        <if test="bo.spuName != null and bo.spuName != ''">
            and br.spu_name like concat('%', #{bo.spuName},'%')
        </if>
        <if test="bo.productResType != null">
            and brd.responsibility_type = #{bo.productResType}
        </if>
        <if test="bo.businessType != null">
            and br.business_type = #{bo.businessType}
        </if>
        <if test="bo.logisticsIds != null and bo.logisticsIds.size() > 0">
            and br.logistics_id in
            <foreach collection="bo.logisticsIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and
            <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
                (
            </if>
            br.place_id_level2 = 0
        </if>
        <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
            <if test="bo.logisticsIds != null and bo.logisticsIds.size() > 0">
                or
            </if>
            <if test="bo.logisticsIds == null  or bo.logisticsIds.size() == 0">
                and
            </if>
            br.place_id_level2 in
            <foreach collection="bo.placeIdLevel2List" item="placeId" open="(" separator="," close=")">
                #{placeId}
            </foreach>
            <if test="bo.logisticsIds != null and bo.logisticsIds.size() > 0">
                )
            </if>
        </if>
        group by br.id
        order by br.update_time desc
    </select>

    <select id="blameAdminPage" resultType="cn.xianlink.order.domain.vo.blameRecord.BlameRecordAdminVO">
        select
        br.id as blameRecordId, br.code, br.source_code, br.city_wh_id, br.supplier_id, br.region_wh_id,
        br.logistics_name, br.spu_name, br.blame_amount, br.source_type, br.refund_status, br.blame_status, br.update_time,
        br.create_time, br.product_amount, br.other_amount,br.subsidy_free_amount
        from
        blame_record br left join blame_record_detail brd on br.id = brd.blame_record_id and brd.del_flag = 0
        where br.del_flag = 0
        <if test="bo.code != null and bo.code != ''">
            and br.code = #{bo.code}
        </if>
        <if test="bo.sourceCode != null and bo.sourceCode != ''">
            and br.source_code = #{bo.sourceCode}
        </if>
        <if test="bo.buyerId != null">
            and br.buyer_id = #{bo.buyerId}
        </if>
        <if test="bo.cityWhId != null">
            and br.city_wh_id = #{bo.cityWhId}
        </if>
        <if test="bo.supplierId != null">
            and br.supplier_id = #{bo.supplierId}
        </if>
        <if test="bo.regionWhId != null">
            and br.region_wh_id = #{bo.regionWhId}
        </if>
        <if test="bo.logisticsId != null">
            and br.logistics_id = #{bo.logisticsId}
        </if>
        <if test="bo.blameStatus != null">
            and br.blame_status = #{bo.blameStatus}
        </if>
        <if test="bo.sourceType != null">
            and br.source_type = #{bo.sourceType}
        </if>
        <if test="bo.orderType != null">
            and br.other_type = #{bo.orderType}
        </if>
        <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
            and br.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
        </if>
        <if test="bo.createTimeStart != null and bo.createTimeEnd != null">
            and br.create_time between #{bo.createTimeStart} and #{bo.createTimeEnd}
        </if>
        <if test="bo.updateTimeStart != null and bo.updateTimeEnd != null">
            and br.update_time between #{bo.updateTimeStart} and #{bo.updateTimeEnd}
        </if>
        <if test="bo.spuName != null and bo.spuName != ''">
            and br.spu_name like concat('%', #{bo.spuName},'%')
        </if>
        <if test="bo.productResType != null">
            and brd.amount_type = 10
            and brd.responsibility_type = #{bo.productResType}
        </if>
        <if test="bo.otherResType != null">
            and brd.amount_type = 20
            and brd.responsibility_type = #{bo.otherResType}
        </if>
        <if test="bo.productResTimeStart != null and bo.productResTimeEnd != null">
            and brd.amount_type = 10
            and brd.confirm_time between #{bo.productResTimeStart} and #{bo.productResTimeEnd}
        </if>
        <if test="bo.otherResTimeStart != null and bo.otherResTimeEnd != null">
            and brd.amount_type = 20
            and brd.confirm_time between #{bo.otherResTimeStart} and #{bo.otherResTimeEnd}
        </if>
        <if test="bo.productResName != null and bo.productResName != ''">
            and brd.amount_type = 10
            and brd.confirm_name like concat('%', #{bo.productResName},'%')
        </if>
        <if test="bo.otherResName != null and bo.otherResName != ''">
            and brd.amount_type = 20
            and brd.confirm_name like concat('%', #{bo.otherResName},'%')
        </if>
        <if test="bo.businessType != null">
            and br.business_type = #{bo.businessType}
        </if>
        group by br.id
        order by br.update_time desc
    </select>
</mapper>
