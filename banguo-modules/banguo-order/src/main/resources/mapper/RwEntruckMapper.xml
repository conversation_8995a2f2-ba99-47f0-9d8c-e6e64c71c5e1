<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.RwEntruckMapper">
    <select id="customPageList" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckVo">
        SELECT re.*,
               rd.licence_plate
        FROM rw_entruck re
                 left join rw_depart rd on re.depart_id = rd.id and rd.del_flag = 0
            ${ew.getCustomSqlSegment}
    </select>

    <select id="customListByCityWhId" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckVo">
        select re.id,
               re.logistics_id,
               re.region_wh_id,
               re.city_wh_id,
               re.receive_date,
               rd.licence_plate,
               rd.create_time
        from rw_entruck re
                 left join rw_depart rd on re.depart_id = rd.id and rd.del_flag = 0
        where re.del_flag = 0
          and re.city_wh_id = #{cityWhId}
          and re.status = 60
          and re.receive_date = #{receiveDate}
    </select>


    <select id="customListByCityWhIdAndRegionId" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckVo">
        select re.id,
        re.logistics_id,
        re.region_wh_id,
        re.city_wh_id,
        re.receive_date,
        rd.licence_plate,
        rd.create_time
        from rw_entruck re
        left join rw_depart rd on re.depart_id = rd.id and rd.del_flag = 0
        where re.del_flag = 0
        and re.region_wh_id = #{regionWhId}
        and re.city_wh_id = #{cityWhId}
        and re.status = 60
        and re.receive_date = #{receiveDate}
    </select>

    <select id="selectInfoBySupplierSkuId" resultType="cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo">
        select reg.supplier_sku_id  as supplierSkuId,
        coalesce(sum( if(rer.status &lt; 30, reg.delivery_quantity, 0) + if(rer.status = 30, reg.entruck_quantity, 0)
                          + if(reg.diff_status = 32, reg.delivery_quantity-reg.entruck_quantity, 0) ) ,0) as deliveredCount,
        coalesce(sum(if(rer.status = 30, reg.entruck_quantity, 0)),0) as loadingCount
        from rw_entruck_record rer
        inner join rw_entruck_goods reg on reg.record_id = rer.id and reg.del_flag = 0
        where rer.del_flag = 0
        and rer.status != 90
        and rer.sale_date = #{saleDate}
        and rer.region_wh_id = #{regionWhId}
        and reg.supplier_sku_id in
        <foreach collection="supplierSkuIds" separator="," open="(" close=")" item="supplierSkuId" >
            #{supplierSkuId}
        </foreach>
        group by reg.supplier_sku_id
    </select>

    <select id="selectInfoBySupplierSkuIdTotal" resultType="cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo">
        select
        coalesce(sum( if(rer.status &lt; 30, reg.delivery_quantity, 0) + if(rer.status = 30, reg.entruck_quantity, 0)
                + if(reg.diff_status = 32, reg.delivery_quantity-reg.entruck_quantity, 0) ) ,0) as deliveredCount,
        sum(reg.entruck_quantity) as loadingCount
        from rw_entruck_record rer
        inner join rw_entruck_goods reg on reg.record_id = rer.id and reg.del_flag = 0
        where rer.del_flag = 0
        and rer.status != 90
        and rer.sale_date = #{saleDate}
        and rer.region_wh_id = #{regionWhId}
        and reg.supplier_sku_id in
        <foreach collection="supplierSkuIds" separator="," open="(" close=")" item="supplierSkuId" >
            #{supplierSkuId}
        </foreach>
    </select>
</mapper>
