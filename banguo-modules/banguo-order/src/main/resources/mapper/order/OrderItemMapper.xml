<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.OrderItemMapper">
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into order_item(
        order_id, order_code, customer_id, category_id, category_path_name, spu_id, spu_name, supplier_spu_id, supplier_sku_id,
        region_wh_id, city_wh_id, supplier_id, supplier_dept_id, logistics_id, place_id, place_name, address, business_type,
        buyer_code, buyer_name, img_url, sale_date, count, spu_gross_weight, spu_net_weight, price, product_amount, platform_service_amount,
        platform_freight_amount, base_freight_amount, region_freight_amount, freight_total_amount, other_total_amount,
        platform_service_free_amount, platform_freight_free_amount, base_freight_free_amount, region_freight_free_amount,
        freight_total_free_amount, coin_count, coin_amount, subsidy_free_amount, free_total_amount, total_amount, after_sale_day,
        after_sale_type, after_sale_status, provide_region_wh_id, provide_logistics_id, final_price, sku_id, category_id_level1, category_id_level2
        ,place_id_level2,place_name_level2,address_level2,platform_freight_amount_level2,logistics_id_level2,place_path,product_free_amount
        ,platform_freight_free_amount_level2,prod_type,region_subsidy_amount,financial_service_amount,settle_number,sale_type,sku_subsidy_amount)
        values
        <foreach collection="items" item="item" separator=",">
            (
            #{item.orderId}, #{item.orderCode}, #{item.customerId} ,#{item.categoryId}, #{item.categoryPathName}, #{item.spuId},
            #{item.spuName}, #{item.supplierSpuId}, #{item.supplierSkuId}, #{item.regionWhId}, #{item.cityWhId}, #{item.supplierId},
            #{item.supplierDeptId}, #{item.logisticsId}, #{item.placeId}, #{item.placeName}, #{item.address}, #{item.businessType},
            #{item.buyerCode}, #{item.buyerName}, #{item.imgUrl}, #{item.saleDate}, #{item.count}, #{item.spuGrossWeight},
            #{item.spuNetWeight}, #{item.price}, #{item.productAmount}, #{item.platformServiceAmount},
            #{item.platformFreightAmount}, #{item.baseFreightAmount}, #{item.regionFreightAmount}, #{item.freightTotalAmount},
            #{item.otherTotalAmount}, #{item.platformServiceFreeAmount}, #{item.platformFreightFreeAmount},
            #{item.baseFreightFreeAmount}, #{item.regionFreightFreeAmount}, #{item.freightTotalFreeAmount},
            #{item.coinCount}, #{item.coinAmount}, #{item.subsidyFreeAmount}, #{item.freeTotalAmount}, #{item.totalAmount},
            #{item.afterSaleDay}, #{item.afterSaleType}, #{item.afterSaleStatus}, #{item.provideRegionWhId}, #{item.provideLogisticsId},
            #{item.finalPrice}, #{item.skuId}, #{item.categoryIdLevel1}, #{item.categoryIdLevel2}
            , #{item.placeIdLevel2}, #{item.placeNameLevel2}, #{item.addressLevel2}, #{item.platformFreightAmountLevel2}, #{item.logisticsIdLevel2}
            , #{item.placePath}, #{item.productFreeAmount}, #{item.platformFreightFreeAmountLevel2}, #{item.prodType}, #{item.regionSubsidyAmount}
            ,#{item.financialServiceAmount}, #{item.settleNumber}, #{item.saleType},#{item.skuSubsidyAmount}
            )
        </foreach>
    </insert>

    <sql id="orderInfoPageFilter">
        where
        oi.del_flag = 0 and ( oi.status = 'ALREADY' or oi.status = 'FINISH' or ( oi.status = 'CANCEL' and (oi.cancel_type = 'OUT' or oi.cancel_type = 'FEW') ) )
        <if test="bo.createTimeStart != null and bo.createTimeEnd != null">
            and oi.create_time between #{bo.createTimeStart} and #{bo.createTimeEnd}
        </if>
        <if test="bo.regionWhId != null">
            and oi.region_wh_id = #{bo.regionWhId}
        </if>
        <if test="bo.supplierId != null">
            and oi.supplier_id = #{bo.supplierId}
        </if>
        <if test="bo.logisticsId != null">
            and oi.logistics_id = #{bo.logisticsId}
        </if>
        <if test="bo.placeId != null">
            and oi.place_id = #{bo.placeId}
        </if>
        <if test="bo.businessType != null">
            and oi.business_type = #{bo.businessType}
        </if>
        <if test="bo.status != null">
            and oi.status = #{bo.status}
        </if>
        <if test="bo.orderCode != null">
            and oi.order_code = #{bo.orderCode}
        </if>
        <if test="bo.orderId != null">
            and oi.order_id = #{bo.orderId}
        </if>
        <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
            and oi.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
        </if>
        <if test="bo.payTimeStart != null and bo.payTimeEnd != null">
            and oi.pay_time between #{bo.payTimeStart} and #{bo.payTimeEnd}
        </if>
        <if test="bo.cityWhId != null">
            and oi.city_wh_id = #{bo.cityWhId}
        </if>
        <if test="bo.spuName != null and bo.spuName != ''">
            and oi.spu_name like concat('%', #{bo.spuName}, '%')
        </if>
        <if test="bo.customerId != null">
            and oi.customer_id = #{bo.customerId}
        </if>
        <if test="bo.customerName != null and bo.customerName != ''">
            and oi.create_name like concat('%', #{bo.customerName}, '%')
        </if>
        order by oi.create_time desc
    </sql>


    <select id="listOrderInfo" resultType="cn.xianlink.order.domain.order.vo.OrderItemAggregateVO" >
        select
        oi.id as orderItemId,
        oi.order_id,
        oi.order_code,
        oi.sale_date,
        oi.supplier_id,
        oi.business_type,
        oi.status,
        oi.city_wh_id,
        oi.logistics_id,
        oi.region_wh_id,
        oi.place_id,
        oi.place_name,
        oi.customer_id,
        oi.supplier_sku_id,
        oi.buyer_code,
        oi.buyer_name,
        oi.total_amount,
        oi.spu_name,
        oi.spu_gross_weight,
        oi.price,
        oi.count as goodsCount,
        oi.settle_number as checkCount,
        oi.product_amount,
        oi.platform_service_amount,
        oi.platform_freight_amount,
        oi.financial_service_amount,
        oi.free_total_amount,
        oi.platform_service_free_amount,
        oi.freight_total_free_amount as regionFreightFreeAmount
        from
        order_item oi left join refund_product_detail rpd on oi.id = rpd.order_item_id and rpd.del_flag = 0
        <where>
            oi.del_flag = 0 and ( oi.status = 'ALREADY' or oi.status = 'FINISH' or ( oi.status = 'CANCEL' and (oi.cancel_type = 'OUT' or cancel_type = 'FEW') ) )

            <if test="bo.createTimeStart != null and bo.createTimeEnd != null">
                and oi.create_time between #{bo.createTimeStart} and #{bo.createTimeEnd}
            </if>
            <if test="bo.regionWhId != null">
                and oi.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.supplierId != null">
                and oi.supplier_id = #{bo.supplierId}
            </if>
            <if test="bo.logisticsId != null">
                and oi.logistics_id = #{bo.logisticsId}
            </if>
            <if test="bo.placeId != null">
                and oi.place_id = #{bo.placeId}
            </if>
            <if test="bo.businessType != null">
                and oi.business_type = #{bo.businessType}
            </if>
            <if test="bo.status != null">
                and oi.status = #{bo.status}
            </if>
            <if test="bo.orderCode != null">
                and oi.order_code = #{bo.orderCode}
            </if>
            <if test="bo.orderId != null">
                and oi.order_id = #{bo.orderId}
            </if>
            <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
                and oi.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
            </if>
            <if test="bo.payTimeStart != null and bo.payTimeEnd != null">
                and oi.pay_time between #{bo.payTimeStart} and #{bo.payTimeEnd}
            </if>
        </where>
        group by rpd.order_item_id
        order by oi.create_time desc
    </select>

    <select id="queryDeliver" resultType="cn.xianlink.order.domain.order.vo.QueryDeliverVo" >
        select
            region_wh_id,
            city_wh_id,
            logistics_id,
            supplier_id,
            supplier_dept_id,
            supplier_sku_id,
            original_order_item_id,
            business_type,
            sale_date,
            provide_region_wh_id,
            sum(count) as count,
            sum((count - refund_out_count) * price) as productAmount
        from
            order_item
        <where>
            del_flag = 0 and ( status = 'ALREADY' or status = 'FINISH' or ( status = 'CANCEL' and (cancel_type = 'OUT' or cancel_type = 'FEW') ) )
            <if test="bo.payTime != null">
                and pay_time &lt;= #{bo.payTime}
            </if>
            <if test="bo.spuName != null and bo.spuName != ''">
                and spu_name like concat('%', #{bo.spuName}, '%')
            </if>
            <if test="bo.regionWhId != null">
                and region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.supplierId != null">
                and supplier_id = #{bo.supplierId}
            </if>
            <if test="bo.supplierDeptId != null">
                and supplier_dept_id = #{bo.supplierDeptId}
            </if>
            <if test="bo.saleDate != null">
                and sale_date = #{bo.saleDate}
            </if>
            <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
                and sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
            </if>
            <if test="bo.supplierSkuId != null and bo.supplierSkuId.size() > 0">
                and supplier_sku_id in
                <foreach collection="bo.supplierSkuId" item="supplierSkuId" open="(" close=")" separator=",">
                    #{supplierSkuId}
                </foreach>
            </if>
            <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
                and logistics_id in
                <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                    #{logisticsId}
                </foreach>
            </if>
            <if test="bo.buyerCodeList != null and bo.buyerCodeList.size() > 0">
                and buyer_code in
                <foreach collection="bo.buyerCodeList" item="buyerCode" open="(" close=")" separator=",">
                    #{buyerCode}
                </foreach>
            </if>
            <if test="bo.businessTypes != null and bo.businessTypes.size() > 0">
                and business_type in
                <foreach collection="bo.businessTypes" item="businessType" open="(" close=")" separator=",">
                    #{businessType}
                </foreach>
            </if>
            <if test="bo.provideRegionWhIdList != null and bo.provideRegionWhIdList.size() > 0">
                and provide_region_wh_id in
                <foreach collection="bo.provideRegionWhIdList" item="provideRegionWhId" open="(" close=")" separator=",">
                    #{provideRegionWhId}
                </foreach>
            </if>
            <if test="bo.notBusinessTypes != null and bo.notBusinessTypes.size() > 0">
                and business_type not in
                <foreach collection="bo.notBusinessTypes" item="businessType" open="(" close=")" separator=",">
                    #{businessType}
                </foreach>
            </if>
        </where>
        group by supplier_sku_id, logistics_id
    </select>

    <select id="queryDeliverItem" resultType="cn.xianlink.order.domain.order.vo.QueryDeliverVo" >
        select oi.sale_date, oi.order_id, oi.id as order_item_id, oi.delivery_number, oi.customer_id, oi.supplier_sku_id, oi.spu_name, oi.count,
               oi.region_wh_id, oi.city_wh_id, oi.logistics_id, oi.supplier_id, oi.supplier_dept_id, oi.business_type,
               oi.buyer_code, oi.buyer_name, oi.img_url, oi.spu_gross_weight, oi.spu_net_weight,
               oi.count - ifnull(b.delivery_quantity, 0) - ifnull(c.stockout_count, 0) as wait_delivery_quantity
        from order_item oi
            left join (select rego.order_item_id, sum(rego.delivery_quantity) as delivery_quantity
                        from rw_entruck_record rer
                            inner join rw_entruck_goods reg on rer.id = reg.record_id and reg.del_flag = 0
                            inner join rw_entruck_goods_order rego on reg.record_id = rego.record_id and reg.supplier_sku_id = rego.supplier_sku_id
                        where rer.del_flag = 0
                            and rer.status in (10, 20, 30)
                            and rer.sale_date = #{bo.saleDate}
                            and rer.region_wh_id = #{bo.regionWhId}
                            and reg.supplier_sku_id in
                            <foreach collection="bo.supplierSkuId" item="supplierSkuId" open="(" close=")" separator=",">
                                #{supplierSkuId}
                            </foreach>
                            and rer.logistics_id in
                            <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                                #{logisticsId}
                            </foreach>
                        group by rego.order_item_id) b on oi.id = b.order_item_id
            left join (select ssr.order_item_id, sum(ssr.stockout_count) as stockout_count
                        from stockout_sku_record ssr
                        where ssr.del_flag = 0 and ssr.type = 1 and ssr.stockout_count > 0
                        and ssr.sale_date = #{bo.saleDate}
                        and ssr.region_wh_id = #{bo.regionWhId}
                        and ssr.supplier_sku_id in
                        <foreach collection="bo.supplierSkuId" item="supplierSkuId" open="(" close=")" separator=",">
                            #{supplierSkuId}
                        </foreach>
                        and ssr.logistics_id in
                        <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                            #{logisticsId}
                        </foreach>
                        group by ssr.order_item_id) c on oi.id = c.order_item_id
        where oi.del_flag = 0 and (oi.status = 'ALREADY' or oi.status = 'FINISH' or (oi.status = 'CANCEL' and (oi.cancel_type = 'OUT' or oi.cancel_type = 'FEW')))
          and oi.sale_date = #{bo.saleDate}
          and oi.region_wh_id = #{bo.regionWhId}
          and oi.supplier_sku_id in
        <foreach collection="bo.supplierSkuId" item="supplierSkuId" open="(" close=")" separator=",">
            #{supplierSkuId}
        </foreach>
            and oi.logistics_id in
        <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
            #{logisticsId}
        </foreach>
          and oi.count - ifnull(b.delivery_quantity, 0) - ifnull(c.stockout_count, 0) > 0
        order by oi.id asc
    </select>


    <select id="getOrderInfoPage" resultType="cn.xianlink.order.domain.order.vo.OrderItemAggregateVO" >
        select
        oi.id as orderItemId,
        oi.order_id,
        oi.order_code,
        oi.sale_date,
        oi.supplier_id,
        oi.business_type,
        oi.status,
        oi.city_wh_id,
        oi.logistics_id,
        oi.region_wh_id,
        oi.place_id,
        oi.place_name,
        oi.customer_id,
        oi.supplier_sku_id,
        oi.buyer_code,
        oi.buyer_name,
        oi.total_amount,
        oi.spu_name,
        oi.spu_gross_weight,
        oi.price,
        oi.count as goodsCount,
        oi.settle_number as checkCount,
        oi.product_amount,
        oi.platform_service_amount,
        oi.platform_freight_amount,
        oi.financial_service_amount,
        oi.free_total_amount,
        oi.platform_service_free_amount,
        oi.freight_total_free_amount as regionFreightFreeAmount

        from
        order_item oi
        <include refid="orderInfoPageFilter"/>
    </select>
    <select id="queryRegionDeliver" resultType="cn.xianlink.order.domain.order.vo.QueryRegionDeliverVo" >
        select
        region_wh_id,
        supplier_id,
        supplier_dept_id,
        sum(count) as count,
        supplier_sku_id
        from
        order_item
        <where>
            del_flag = 0 and ( status = 'ALREADY' or status = 'FINISH' or ( status = 'CANCEL' and (cancel_type = 'OUT' or cancel_type = 'FEW') ) )
            <if test="bo.payTime != null">
                and pay_time &lt;= #{bo.payTime}
            </if>
            <if test="bo.regionWhId != null">
                and region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.supplierId != null">
                and supplier_id = #{bo.supplierId}
            </if>
            <if test="bo.saleDate != null">
                and sale_date = #{bo.saleDate}
            </if>
            <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
                and logistics_id in
                <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                    #{logisticsId}
                </foreach>
            </if>
            <if test="bo.buyerCodeList != null and bo.buyerCodeList.size() > 0">
                and buyer_code in
                <foreach collection="bo.buyerCodeList" item="buyerCode" open="(" close=")" separator=",">
                    #{buyerCode}
                </foreach>
            </if>
            <if test="bo.notBusinessTypes != null and bo.notBusinessTypes.size() > 0">
                and business_type not in
                <foreach collection="bo.notBusinessTypes" item="businessType" open="(" close=")" separator=",">
                    #{businessType}
                </foreach>
            </if>
        </where>
        group by supplier_id, supplier_dept_id
    </select>

    <select id="selectCheckCount" resultType="java.math.BigDecimal">
        select
        a.settle_number
        from
        order_item a
        <where>
            a.del_flag = 0
            <if test="orderIds != null and orderIds.size() > 0">
                and a.order_id in
                <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                    #{orderId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="queryDeliverLogistics" resultType="cn.xianlink.order.domain.order.vo.QueryDeliverLogisticsVo">
        select
            region_wh_id,
            city_wh_id,
            logistics_id,
            count(supplier_id) as supplierCount,
            sum(count) as count
        from
            order_item
        <where>
            del_flag = 0 and ( status = 'ALREADY' or status = 'FINISH' or ( status = 'CANCEL' and (cancel_type = 'OUT' or cancel_type = 'FEW') ) )
            <if test="bo.payTime != null">
                and pay_time &lt;= #{bo.payTime}
            </if>
            <if test="bo.saleDate != null">
                and sale_date = #{bo.saleDate}
            </if>
            <if test="bo.regionWhId != null">
                and region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
                and logistics_id in
                <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                    #{logisticsId}
                </foreach>
            </if>
            <if test="bo.buyerCodeList != null and bo.buyerCodeList.size() > 0">
                and buyer_code in
                <foreach collection="bo.buyerCodeList" item="buyerCode" open="(" close=")" separator=",">
                    #{buyerCode}
                </foreach>
            </if>
            <if test="bo.notBusinessTypes != null and bo.notBusinessTypes.size() > 0">
                and business_type not in
                <foreach collection="bo.notBusinessTypes" item="businessType" open="(" close=")" separator=",">
                    #{businessType}
                </foreach>
            </if>
        </where>
        group by logistics_id
    </select>

    <select id="queryDeliverLogisticsItem" resultType="cn.xianlink.order.domain.order.vo.QueryDeliverLogisticsItemVo">
        select
            region_wh_id,
            city_wh_id,
            logistics_id,
            supplier_id,
            supplier_dept_id,
            count(DISTINCT supplier_sku_id) as supplierSkuCount,
            sum(count) as count
        from
            order_item
        <where>
            del_flag = 0 and ( status = 'ALREADY' or status = 'FINISH' or ( status = 'CANCEL' and (cancel_type = 'OUT' or cancel_type = 'FEW') ) )
            <if test="bo.payTime != null">
                and pay_time &lt;= #{bo.payTime}
            </if>
            <if test="bo.saleDate != null">
                and sale_date = #{bo.saleDate}
            </if>
            <if test="bo.regionWhId != null">
                and region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
                and logistics_id in
                <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                    #{logisticsId}
                </foreach>
            </if>
            <if test="bo.notBusinessTypes != null and bo.notBusinessTypes.size() > 0">
                and business_type not in
                <foreach collection="bo.notBusinessTypes" item="businessType" open="(" close=")" separator=",">
                    #{businessType}
                </foreach>
            </if>
        </where>
        group by logistics_id, supplier_id, supplier_dept_id
    </select>

    <select id="queryItemList" resultType="cn.xianlink.order.domain.order.vo.QueryItemListVo" >
        select
            id,
            order_id,
            order_code,
            spu_name,
            supplier_sku_id,
            sale_date,
            logistics_id,
            count,
            spu_gross_weight,
            spu_net_weight,
            spu_gross_weight * count as totalGrossWeight,
            spu_net_weight * count as totalNetWeight,
            price,
            final_price,
            ROUND(price/spu_gross_weight, 2) as weightPrice,
            status,
            cancel_type,
            work_status,
            img_url,
            (count-refund_out_count-refund_few_count) as receiveCount,
            subsidy_free_amount,
            product_amount,
            supplier_id,
            supplier_dept_id,
            settle_time,
            business_type,
            region_wh_id,
            city_wh_id,
            buyer_code,
            buyer_name,
            prod_type,
            sku_id,
            product_free_amount
        from
            order_item
        where
            del_flag = 0 and order_id in
        <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>

    <select id="queryItemByIdList" resultType="cn.xianlink.order.domain.order.vo.QueryItemListVo" >
        select
        id,
        order_id,
        order_code,
        spu_name,
        supplier_sku_id,
        sale_date,
        logistics_id,
        count,
        spu_gross_weight,
        spu_net_weight,
        spu_gross_weight * count as totalGrossWeight,
        spu_net_weight * count as totalNetWeight,
        price,
        final_price,
        ROUND(price/spu_gross_weight, 2) as weightPrice,
        status,
        cancel_type,
        work_status,
        img_url,
        (count-refund_out_count-refund_few_count) as receiveCount,
        subsidy_free_amount,
        product_amount,
        supplier_id,
        supplier_dept_id,
        settle_time,
        business_type,
        region_wh_id,
        city_wh_id,
        buyer_code,
        buyer_name
        from
        order_item
        where
        del_flag = 0 and id in
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="queryItemBySkuId" resultType="cn.xianlink.order.domain.OrderItem">
        select * from order_item
        where order_id = #{orderId,jdbcType=BIGINT} and supplier_sku_id = #{skuId,jdbcType=BIGINT} limit 1
    </select>


    <select id="queryBySupplierId" resultType="cn.xianlink.order.domain.OrderItem">
        select  o.code, o.create_time,item.order_id, item.price, item.count, item.product_amount
        from order_item item
                 inner join `order` o on item.order_id = o.id
        where item.supplier_id = #{supplierId,jdbcType=BIGINT}
          and item.supplier_sku_id = #{skuId,jdbcType=BIGINT}
          and o.status != 'WAIT'
          and o.del_flag = 0
    </select>

    <select id="queryItemByStatistics" resultType="cn.xianlink.order.domain.OrderItem">
        select i.id, i.order_id, i.spu_id, i.product_amount, i.order_id,i.customer_id,i.final_price,i.sku_id,i.count
        from order_item as i
        where  i.customer_id in
        <foreach collection="customerIds" item="customerId" open="(" close=")" separator=",">
            #{customerId,jdbcType=BIGINT}
        </foreach>
        and i.status in ('ALREADY','FINISH')
        and i.create_time >= #{timeStart,jdbcType=TIMESTAMP}
        <if test="timeEnd != null">
            and i.create_time &lt;= #{timeEnd,jdbcType=TIMESTAMP}
        </if>
        and i.del_flag = 0
    </select>
    <update id="replaceSupplier">
        update
            order_item
        set
            del_flag = #{bo.id},
            status = #{bo.status},
            cancel_type = #{bo.cancelType},
            original_order_item_id = #{bo.originalOrderItemId},
            original_supplier_id = #{bo.originalSupplierId}
        where
            id = #{bo.id}
    </update>

    <select id="queryProductOrder" resultType="cn.xianlink.order.api.vo.RemoteSupTransProductOrderPriceVo">
        select
            supplier_sku_id as id,
            sale_date as saleDate,
            price,
            count(id) as orderCount,
            sum(count) as saleCount,
            sum(product_amount-product_free_amount) as saleAmount,
            sum(product_free_amount) as productFreeAmount,
            group_concat(distinct order_id separator ';') as orderIdList
        from
            order_item
        <where>
            <if test="bo.supplierSkuId != null">
                and supplier_sku_id = #{bo.supplierSkuId}
            </if>
            <if test="bo.orderIdList != null and bo.orderIdList.size() > 0">
                and order_id in
                <foreach collection="bo.orderIdList" item="orderId" open="(" close=")" separator=",">
                    #{orderId}
                </foreach>
            </if>
        </where>
        group by supplier_sku_id, price
    </select>

    <select id="queryProductOrderRecord" resultType="cn.xianlink.order.api.vo.RemoteSupTransProductOrderRecordVo">
        select
            order_id,
            order_code,
            supplier_sku_id,
            sale_date,
            price,
            count,
            product_amount - product_free_amount as productAmount,
            create_time,
            product_free_amount
        from
            order_item
        <where>
            <if test="bo.trans != null and bo.trans.size() > 0">
                (order_id, supplier_sku_id) in
                <foreach collection="bo.trans" item="item" open="(" separator="," close=")">
                    ( #{item.transId}, #{item.skuId})</foreach>
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="querySaleOrder" resultType="cn.xianlink.order.domain.dto.SupSaleOrderDto">
        select item.after_sale_day, item.business_type
        from order_item item
                 inner join `order` o on item.order_id = o.id
        where item.sale_date = '${salesDate}'
          and item.supplier_id = #{supId,jdbcType=BIGINT}
          and (item.cancel_type is null or item.cancel_type in ('OUT','FEW'))
          and (o.cancel_type is null or o.cancel_type in ('OUT', 'FEW'))
          and item.del_flag = 0
          and o.del_flag = 0
    </select>

    <select id="querySupNonSettle" resultType="java.lang.Long">
        select item.id from order_item item
                inner join `order` o on item.order_id = o.id
        where item.sale_date = '${salesDate}'
        and item.supplier_id = #{supId,jdbcType=BIGINT}
        and item.business_type = #{businessType,jdbcType=INTEGER}
        and item.settle_time is null
        and (item.cancel_type is null or item.cancel_type in ('OUT','FEW'))
        and (o.cancel_type is null or o.cancel_type in ('OUT', 'FEW'))
        and item.del_flag = 0
        and o.del_flag = 0
    </select>

    <sql id="supplierFilter">
        item.supplier_id in
        <foreach collection="suppliers" item="supplierId" open="(" close=")" separator=",">
            #{supplierId}
        </foreach>
    </sql>
    <sql id="deptFilter">
        item.supplier_dept_id in
        <foreach collection="supplierDepts" item="supplierDeptId" open="(" close=")" separator=",">
            #{supplierDeptId}
        </foreach>
    </sql>
    <sql id="supplierAndDeptFilter">
        <if test="supplierDepts.size() == 0 and suppliers.size() > 0">
            and <include refid="supplierFilter"/>
        </if>
        <if test="supplierDepts.size() > 0">
            and <include refid="deptFilter"/>
        </if>
    </sql>
    <sql id="buyerFilter">
        <if test="buyers != null and buyers.size() > 0">
            and item.buyer_code in
            <foreach collection="buyers" item="buyer" open="(" close=")" separator=",">
                #{buyer}
            </foreach>
        </if>
    </sql>

    <sql id="skuFilter">
        <if test="supplierSkuIds != null and supplierSkuIds.size() > 0">
            and item.supplier_sku_id in
            <foreach collection="supplierSkuIds" item="supplierSkuId" open="(" close=")" separator=",">
                #{supplierSkuId}
            </foreach>
        </if>
    </sql>
    <select id="sumRegionWhOrderAmount" resultType="cn.xianlink.order.domain.vo.report.OrderAmountVo">
        select
            coalesce(sum(item.`count`),0) as orderItemCount,
            coalesce(count(DISTINCT item.supplier_sku_id),0) as skuCount
        from order_item item inner join `order` o on item.order_id = o.id
        <where>
            item.del_flag = 0 and o.status in ('ALREADY', 'FINISH', 'CANCEL')
            and (o.cancel_type is null or o.cancel_type in ('OUT','FEW'))
            and item.pay_time &lt;= #{payTime}
            and item.sale_date=#{saleDate} and item.region_wh_id = #{regionWhId}
            <include refid="skuFilter"/>
            <include refid="buyerFilter"/>
            <include refid="supplierAndDeptFilter"/>
        </where>
    </select>

    <select id="supplierRankTotal" resultType="long">
        select count(distinct supplier_id) + count(distinct NULLIF(supplier_dept_id,0))
        from (
            select
                rpd.supplier_id,
                rpd.supplier_dept_id
                from refund_product_detail rpd join refund_record r  on r.id = rpd.refund_record_id and rpd.del_flag = 0
                join order_item item on rpd.order_item_id = item.id
            <where>
                rpd.del_flag = 0  and  rpd.refund_type != 1 and rpd.refund_status=1
                and r.region_wh_id = #{bo.regionWhId} and rpd.sale_date=#{bo.saleDate}
                <include refid="buyerFilter"/>
                <include refid="supplierAndDeptFilter"/>
            </where>
          union all
            select
                item.supplier_id,
                item.supplier_dept_id
                from order_item item inner join `order` o on item.order_id = o.id
            <where>
                item.del_flag = 0 and o.status in ('ALREADY', 'FINISH', 'CANCEL') and (o.cancel_type is null or o.cancel_type in ('OUT','FEW'))
                and item.region_wh_id = #{bo.regionWhId}  and item.sale_date=#{bo.saleDate}
                <include refid="buyerFilter"/>
                <include refid="supplierAndDeptFilter"/>
            </where>
        ) stat_records
    </select>

    <sql id="supplier_all">
        select
            0 as pay_amount,
            0 as item_count,
            (rpd.refund_amount - rpd.refund_financial_service_price) as refund_amount,
            rpd.supplier_id,
            rpd.supplier_dept_id
        from refund_product_detail rpd join refund_record r  on r.id = rpd.refund_record_id and rpd.del_flag = 0
        join order_item item on rpd.order_item_id = item.id
        <where>
            rpd.del_flag = 0  and  rpd.refund_type != 1 and rpd.refund_status=1
            and r.region_wh_id = #{bo.regionWhId} and rpd.sale_date=#{bo.saleDate}
            <include refid="buyerFilter"/>
            <if test="suppliers.size() > 0">
                and <include refid="supplierFilter"/>
            </if>
        </where>
        union all
        select
            item.total_amount as pay_amount,
            item.count as item_count,
            0 as refund_amount,
            item.supplier_id,
            item.supplier_dept_id
        from order_item item inner join `order` o on item.order_id = o.id
        <where>
            item.del_flag = 0 and o.status in ('ALREADY', 'FINISH', 'CANCEL') and (o.cancel_type is null or o.cancel_type in ('OUT','FEW'))
            and item.region_wh_id = #{bo.regionWhId}  and item.sale_date=#{bo.saleDate}
            <include refid="buyerFilter"/>
            <if test="suppliers.size() > 0">
                and <include refid="supplierFilter"/>
            </if>
        </where>
    </sql>
    <select id="supplierRank" resultType="cn.xianlink.order.domain.vo.report.SupplierRankVo">
        select
            coalesce(sum(payAmount),0) as pay_amount,
            coalesce(sum(itemCount),0) as order_item_count,
            coalesce(sum(refundAmount),0) as refund_amount,
            supplier_id,
            supplier_dept_id
        from (
            select
                sum(pay_amount) as payAmount,
                sum(item_count) as itemCount,
                sum(refund_amount) as refundAmount,
                supplier_id,
                supplier_dept_id
            from ( <include refid="supplier_all"/> ) item
            <where>
                item.supplier_dept_id != 0
                <if test="supplierDepts.size() > 0">
                    and <include refid="deptFilter"/>
                </if>
            </where>
            group by supplier_id, supplier_dept_id
          union all
            select
                sum(pay_amount) as payAmount,
                sum(item_count) as itemCount,
                sum(refund_amount) as refundAmount,
                supplier_id,
                0 as supplier_dept_id
            from ( <include refid="supplier_all"/> ) stat_rows
            group by supplier_id
        ) stat_all
        group by supplier_id, supplier_dept_id
    </select>

    <select id="commodityStatistics" resultType="cn.xianlink.order.domain.vo.report.StatBoardVo">
        SELECT coalesce(sum(`count`),0) as orderItemCount
             , coalesce(sum(settle_number), 0) AS settleItemCount
        , coalesce(count(DISTINCT supplier_sku_id),0) as skuCount, coalesce(sum(coalesce(spu_gross_weight,0) * `count`),0) as orderGrossWeight
        FROM order_item
        where status in ("FINISH","ALREADY", "CANCEL") and (cancel_type is null or cancel_type in ('OUT','FEW'))
          and pay_time &lt;= #{payTime}
        and sale_date=#{saleDate} and city_wh_id = #{cityWhId}
        <if test="customers != null and customers.size() > 0">
            and customer_id in
            <foreach collection="customers" item="customerId" open="(" close=")" separator=",">
                #{customerId}
            </foreach>
        </if>
        <if test="placePaths != null and placePaths.size() > 0">
            and place_path in
            <foreach collection="placePaths" item="placePath" open="(" close=")" separator=",">
                #{placePath}
            </foreach>
        </if>
    </select>

    <select id="selectLogisticsIds" resultType="Long">
        SELECT logistics_id
        FROM order_item
        where status in ("FINISH","ALREADY", "CANCEL" ) and (cancel_type is null or cancel_type in ('OUT','FEW'))
        and pay_time &lt;= #{payTime}
        and sale_date=#{saleDate} and city_wh_id = #{cityWhId}
        <if test="customers != null and customers.size() > 0">
            and customer_id in
            <foreach collection="customers" item="customerId" open="(" close=")" separator=",">
                #{customerId}
            </foreach>
        </if>
        GROUP BY logistics_id
    </select>

    <sql id="filterSupplierSkuIds">
        <if test="bo.supplierSkuIds != null and bo.supplierSkuIds.size() > 0">
            and oi.supplier_sku_id in
            <foreach collection="bo.supplierSkuIds" item="supplierSkuId" open="(" close=")" separator=",">
                #{supplierSkuId}
            </foreach>
        </if>
    </sql>
    <sql id="filterSupplierSkuIdsByEntruck">
        <if test="bo.supplierSkuIds != null and bo.supplierSkuIds.size() > 0">
            and eg.supplier_sku_id in
            <foreach collection="bo.supplierSkuIds" item="supplierSkuId" open="(" close=")" separator=",">
                #{supplierSkuId}
            </foreach>
        </if>
    </sql>

    <select id="citySaleNotSelectSaleman" resultType="cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo">
        SELECT
        od.`count`,
        od.total_amount as total_amount,
        od.customer_count as customer_count,
        od.refund_amount as refund_amount,
        od.supplier_sku_id,
        od.buyer_name,
        a.planDeliveryCount as plan_delivery_count,
        a.loadingCount as loading_count
        FROM
        (
            SELECT coalesce(sum(a.count), 0) as `count`
            , coalesce(sum(a.total_amount), 0) as total_amount
            , coalesce(count(DISTINCT a.customer_id), 0) as customer_count
            , coalesce(sum(a.refundAmount), 0) as refund_amount
            ,a.supplier_sku_id
            ,a.buyer_name
            FROM (
            SELECT
            oi.count as `count`
            ,oi.buyer_name
            , oi.total_amount as total_amount
            ,  oi.customer_id as customer_id
            , coalesce(sum(pd.refund_amount - pd.refund_financial_service_price), 0) as refundAmount
            ,oi.supplier_sku_id
            FROM
            order_item oi
            LEFT JOIN refund_product_detail pd ON pd.order_item_id = oi.id  AND pd.refund_status = 1 and pd.refund_type != 1
            WHERE
            oi.`STATUS` IN ( "FINISH", "ALREADY", "CANCEL" ) and (oi.cancel_type is null or oi.cancel_type in ('OUT','FEW'))
            and oi.pay_time &lt;= #{bo.payTime}
            and oi.sale_date=#{bo.saleDate} and oi.city_wh_id = #{bo.cityWhId}
            <include refid="filterSupplierSkuIds" />
            <if test="bo.salemanCustomers != null and bo.salemanCustomers.size() > 0">
                and oi.customer_id in
                <foreach collection="bo.salemanCustomers" item="customerId" open="(" close=")" separator=",">
                    #{customerId}
                </foreach>
            </if>
            <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
                AND (oi.logistics_id IN
                <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                    #{logisticsId}
                </foreach>
                or
                oi.logistics_id_level2 IN
                <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                    #{logisticsId}
                </foreach>
                )
            </if>
            <if test="bo.placePaths != null and bo.placePaths.size() > 0">
                and oi.place_path in
                <foreach collection="bo.placePaths" item="placePath" open="(" close=")" separator=",">
                    #{placePath}
                </foreach>
            </if>
            GROUP BY
            oi.id
            ) a
            GROUP BY a.supplier_sku_id
        ) od
        LEFT JOIN (
            SELECT
                eg.supplier_sku_id AS supplier_sku_id,
                coalesce(SUM( eg.entruck_quantity ),0) AS planDeliveryCount,
                coalesce(sum( eg.delivery_quantity ),0) AS loadingCount
            FROM
            rw_entruck_goods eg
            LEFT JOIN rw_entruck_record er ON er.id = eg.record_id
            WHERE
                er.sale_date=#{bo.saleDate} and er.city_wh_id = #{bo.cityWhId}
                <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
                    AND er.logistics_id IN
                    <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                        #{logisticsId}
                    </foreach>
                </if>
                <include refid="filterSupplierSkuIdsByEntruck" />
            GROUP BY
            eg.supplier_sku_id
        ) a ON od.supplier_sku_id = a.supplier_sku_id
        where od.supplier_sku_id is not null
    </select>


    <select id="commodityStatisticsCitySaleTotal" resultType="cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo">
        SELECT coalesce(sum(a.count), 0) as `count`
        , coalesce(sum(a.total_amount), 0) as total_amount
        , coalesce(count(DISTINCT a.customer_id), 0) as customer_count
        , coalesce(sum(a.refundAmount), 0) as refund_amount
        FROM (
            SELECT
                oi.count as `count`
                , oi.total_amount as total_amount
                ,  oi.customer_id as customer_id
                , coalesce(sum(pd.refund_amount - pd.refund_financial_service_price), 0) as refundAmount
            FROM
                order_item oi
                LEFT JOIN refund_product_detail pd ON pd.order_item_id = oi.id  AND pd.refund_status = 1 and pd.refund_type != 1
            WHERE
                oi.`STATUS` IN ( "FINISH", "ALREADY", "CANCEL" ) and (cancel_type is null or cancel_type in ('OUT','FEW'))
                and oi.sale_date=#{bo.saleDate} and oi.city_wh_id = #{bo.cityWhId} and oi.pay_time &lt;= #{bo.payTime}
                <include refid="filterSupplierSkuIds"/>
                <if test="bo.salemanCustomers != null and bo.salemanCustomers.size() > 0">
                    and oi.customer_id in
                    <foreach collection="bo.salemanCustomers" item="customerId" open="(" close=")" separator=",">
                        #{customerId}
                    </foreach>
                </if>
                <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
                    AND (oi.logistics_id IN
                    <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                        #{logisticsId}
                    </foreach>
                    or
                    oi.logistics_id_level2 IN
                    <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                        #{logisticsId}
                    </foreach>
                    )
                </if>
                <if test="bo.placePaths != null and bo.placePaths.size() > 0">
                    and oi.place_path in
                    <foreach collection="bo.placePaths" item="placePath" open="(" close=")" separator=",">
                        #{placePath}
                    </foreach>
                </if>
            GROUP BY oi.id
        ) a
    </select>

    <select id="commodityStatisticsCitySale" resultType="cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo">
        SELECT coalesce(sum(a.count), 0) as `count`
        , coalesce(sum(a.total_amount), 0) as total_amount
        , coalesce(count(DISTINCT a.customer_id), 0) as customer_count
        , coalesce(sum(a.refundAmount), 0) as refund_amount
        ,a.supplier_sku_id
        ,a.buyer_name
        FROM (
        SELECT
        oi.count as `count`
        , oi.total_amount as total_amount
        ,  oi.customer_id as customer_id
        , coalesce(sum(pd.refund_amount - pd.refund_financial_service_price), 0) as refundAmount
        ,oi.supplier_sku_id
        ,oi.buyer_name
        FROM
        order_item oi
        LEFT JOIN refund_product_detail pd ON pd.order_item_id = oi.id  AND pd.refund_status = 1 and pd.refund_type != 1
        WHERE
        oi.`STATUS` IN ( "FINISH", "ALREADY", "CANCEL" ) and (oi.cancel_type is null or oi.cancel_type in ('OUT','FEW'))
          and oi.pay_time &lt;= #{bo.payTime}
        and oi.sale_date=#{bo.saleDate} and oi.city_wh_id = #{bo.cityWhId}
        <include refid="filterSupplierSkuIds" />
        <if test="bo.salemanCustomers != null and bo.salemanCustomers.size() > 0">
            and oi.customer_id in
            <foreach collection="bo.salemanCustomers" item="customerId" open="(" close=")" separator=",">
                #{customerId}
            </foreach>
        </if>
        <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
            AND (oi.logistics_id IN
            <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                #{logisticsId}
            </foreach>
            or
            oi.logistics_id_level2 IN
            <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                #{logisticsId}
            </foreach>
            )
        </if>
        <if test="bo.placePaths != null and bo.placePaths.size() > 0">
            and oi.place_path in
            <foreach collection="bo.placePaths" item="placePath" open="(" close=")" separator=",">
                #{placePath}
            </foreach>
        </if>

        GROUP BY
        oi.id
        ) a
        GROUP BY a.supplier_sku_id
    </select>

    <select id="commodityStatisticsRegionSale" resultType="cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo">
        SELECT coalesce(sum(totalAmount),0) as total_amount,
        coalesce(sum(`count`),0) as `count`,
        coalesce(sum(refundAmount),0) as refund_amount,
        supplierSkuId, supplierId, buyerName, businessType
        FROM (
        SELECT
            item.total_amount AS totalAmount,
            item.`count` AS `count`,
            COALESCE ( sum( rod.refund_amount - rod.refund_financial_service_price ), 0 ) AS refundAmount,
            item.supplier_sku_id AS supplierSkuId,
            item.supplier_id AS supplierId,
            item.buyer_name AS buyerName,
            item.business_type AS businessType
        from order_item item
        LEFT JOIN refund_product_detail rod on rod.order_item_id = item.id AND rod.refund_status = 1 and rod.refund_type != 1

        where
            item.del_flag = 0 and item.status in ("ALREADY", "FINISH", "CANCEL" ) and (item.cancel_type is null or item.cancel_type in ('OUT','FEW'))
          and item.sale_date=#{saleDate} and item.region_wh_id = #{regionWhId} and item.pay_time &lt;= #{payTime}
            <include refid="skuFilter"/>
            <include refid="buyerFilter"/>
            <include refid="supplierAndDeptFilter"/>
        group by item.id ) a group by supplierSkuId
    </select>

    <select id="commodityStatisticsRegionSaleTotal" resultType="cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo">

        SELECT coalesce(sum(totalAmount),0) as totalAmount,
        coalesce(sum(`count`),0) as `count`,
        coalesce(sum(refundAmount),0) as refundAmount
        FROM (
        SELECT
        item.total_amount AS totalAmount,
        item.`count` AS `count`,
        COALESCE ( sum( rod.refund_amount - rod.refund_financial_service_price ), 0 ) AS refundAmount,
        item.supplier_sku_id AS supplierSkuId
        from order_item item
        LEFT JOIN refund_product_detail rod on rod.order_item_id = item.id AND rod.refund_status = 1 and rod.refund_type != 1
        where
        item.del_flag = 0 and item.status in ("ALREADY", "FINISH", "CANCEL" ) and (item.cancel_type is null or item.cancel_type in ('OUT','FEW'))
        and item.sale_date=#{saleDate} and item.region_wh_id = #{regionWhId} and item.pay_time &lt;= #{payTime}
        <include refid="skuFilter"/>
        <include refid="buyerFilter"/>
        <include refid="supplierAndDeptFilter"/>
        group by item.id ) a
    </select>

    <select id="citySaleNotSelectSalemanTotal" resultType="cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo">
        SELECT
        coalesce(SUM( planDeliveryCount ),0) AS planDeliveryCount,
        coalesce(sum( loadingCount ),0) AS loadingCount,
        coalesce(sum( count ), 0 ) `count`,
        coalesce(sum(total_amount ), 0 ) totalAmount,
        COALESCE ( sum( customer_count ), 0 ) AS customerCount,
        coalesce(sum( refund_amount ) , 0 )AS refundAmount
        FROM
        (
        SELECT coalesce(sum(a.count), 0) as `count`
        , coalesce(sum(a.total_amount), 0) as total_amount
        , coalesce(count(DISTINCT a.customer_id), 0) as customer_count
        , coalesce(sum(a.refundAmount), 0) as refund_amount
        ,a.supplier_sku_id
        FROM (
        SELECT
        oi.count as `count`
        , oi.total_amount as total_amount
        ,  oi.customer_id as customer_id
        , coalesce(sum(pd.refund_amount - pd.refund_financial_service_price), 0) as refundAmount
        , oi.supplier_sku_id AS supplier_sku_id
        FROM
        order_item oi
        LEFT JOIN refund_product_detail pd ON pd.order_item_id = oi.id  AND pd.refund_status = 1 and pd.refund_type != 1
        WHERE
        oi.`STATUS` IN ( "FINISH", "ALREADY", "CANCEL" ) and (cancel_type is null or cancel_type in ('OUT','FEW'))
        and oi.sale_date=#{bo.saleDate} and oi.city_wh_id = #{bo.cityWhId} and oi.pay_time &lt;= #{bo.payTime}
        <include refid="filterSupplierSkuIds"/>
        <if test="bo.salemanCustomers != null and bo.salemanCustomers.size() > 0">
            and oi.customer_id in
            <foreach collection="bo.salemanCustomers" item="customerId" open="(" close=")" separator=",">
                #{customerId}
            </foreach>
        </if>
        <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
            AND (oi.logistics_id IN
            <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                #{logisticsId}
            </foreach>
            or
            oi.logistics_id_level2 IN
            <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                #{logisticsId}
            </foreach>
            )
        </if>
        <if test="bo.placePaths != null and bo.placePaths.size() > 0">
            and oi.place_path in
            <foreach collection="bo.placePaths" item="placePath" open="(" close=")" separator=",">
                #{placePath}
            </foreach>
        </if>
        GROUP BY oi.id
        ) a
        ) od
        LEFT JOIN (
        SELECT
        eg.supplier_sku_id AS supplier_sku_id,
        coalesce(SUM( eg.entruck_quantity ),0) AS planDeliveryCount,
        coalesce(sum( eg.delivery_quantity ),0) AS loadingCount
        FROM
        rw_entruck_goods eg
        LEFT JOIN rw_entruck_record er ON er.id = eg.record_id
        WHERE
        er.sale_date=#{bo.saleDate} and er.city_wh_id = #{bo.cityWhId}
        <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
            AND er.logistics_id IN
            <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                #{logisticsId}
            </foreach>
        </if>
        <include refid="filterSupplierSkuIdsByEntruck" />
        GROUP BY
        eg.supplier_sku_id
        ) a ON od.supplier_sku_id = a.supplier_sku_id
        where od.supplier_sku_id is not null
    </select>

    <select id="statSupSale" resultType="cn.xianlink.order.domain.vo.report.StatBoardVo">
        SELECT  coalesce(sum(count),0) as orderItemCount,
                count(DISTINCT item.supplier_sku_id) as skuCount ,
                coalesce(sum(item.spu_gross_weight),0)  as orderGrossWeight ,
                coalesce(sum(item.price),0) as  productAmount
        FROM order_item item
        WHERE item.sale_date = #{bo.saleDate} and item.`STATUS` IN ( "FINISH", "ALREADY", "CANCEL" ) and (item.cancel_type is null or item.cancel_type in ('OUT','FEW'))
          and item.supplier_id = #{bo.supplierId} and item.pay_time &lt;= #{bo.payTime}
        <if test="bo.supplierDeptList != null and bo.supplierDeptList.size() > 0">
            and item.supplier_dept_id in
            <foreach collection="bo.supplierDeptList" item="supplierDeptId" open="(" close=")" separator=",">
                #{supplierDeptId}
            </foreach>
        </if>
    </select>
    <select id="commodityStatisticsSupSale" resultType="cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo">
        SELECT
        od.`count`,
        od.totalAmount,
        od.refundAmount,
        (od.count - a.planDeliveryCount) as deliveryCount,
        od.supplier_sku_id as supplierSkuId
        FROM
            (
                SELECT
                    coalesce(sum( oi.count ), 0 ) `count`,
                    coalesce(sum(oi.total_amount) ,0) as totalAmount,
                    coalesce(sum( pd.refund_amount - pd.refund_financial_service_price ) , 0 )AS refundAmount,
                    oi.supplier_sku_id AS supplier_sku_id
                FROM
                    order_item oi
                LEFT JOIN refund_product_detail pd ON pd.order_item_id = oi.id AND pd.refund_status = 1 and pd.refund_type != 1
                WHERE
                    oi.`STATUS` IN ( "FINISH", "ALREADY", "CANCEL" ) and (oi.cancel_type is null or oi.cancel_type in ('OUT','FEW'))
                    and oi.sale_date=#{bo.saleDate} and oi.supplier_id = #{bo.supplierId} and oi.pay_time &lt;= #{bo.payTime}
                    <if test="bo.supplierDeptList != null and bo.supplierDeptList.size() > 0">
                        and oi.supplier_dept_id in
                        <foreach collection="bo.supplierDeptList" item="supplierDeptId" open="(" close=")" separator=",">
                            #{supplierDeptId}
                        </foreach>
                    </if>

                GROUP BY
                    oi.supplier_sku_id
            ) od
                LEFT JOIN (
                SELECT
                    eg.supplier_sku_id AS supplier_sku_id,
                    coalesce(SUM( eg.entruck_quantity ),0) AS planDeliveryCount
                FROM
                    rw_entruck_goods eg
                        LEFT JOIN rw_entruck_record er ON er.id = eg.record_id
                WHERE
                    er.sale_date=#{bo.saleDate}
                    and er.supplier_id = #{bo.supplierId}
                    <if test="bo.supplierDeptList != null and bo.supplierDeptList.size() > 0">
                        and er.supplier_dept_id in
                        <foreach collection="bo.supplierDeptList" item="supplierDeptId" open="(" close=")" separator=",">
                            #{supplierDeptId}
                        </foreach>
                    </if>
                GROUP BY
                    eg.supplier_sku_id
            ) a ON od.supplier_sku_id = a.supplier_sku_id
        where od.supplier_sku_id is not null
    </select>

    <select id="commodityStatisticsSupTotal" resultType="cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo">
        SELECT
        coalesce(sum(od.`count`), 0 ) as `count`,
        coalesce(sum(od.totalAmount), 0 ) as totalAmount,
        coalesce(sum(od.refundAmount), 0 ) as refundAmount,
        coalesce(sum((od.count - a.planDeliveryCount)), 0 ) as deliveryCount
        FROM
            (
                SELECT
                    coalesce(sum( oi.count ), 0 ) `count`,
                    coalesce(sum(oi.total_amount),0) as totalAmount,
                    coalesce(sum( pd.refund_amount - pd.refund_financial_service_price) , 0 )AS refundAmount,
                    oi.supplier_sku_id AS supplier_sku_id
                FROM
                    order_item oi
                LEFT JOIN refund_product_detail pd ON pd.order_item_id = oi.id AND pd.refund_status = 1 and pd.refund_type != 1
                WHERE
                    oi.`STATUS` IN ( "FINISH", "ALREADY", "CANCEL" ) and (oi.cancel_type is null or oi.cancel_type in ('OUT','FEW'))
                    and oi.sale_date=#{bo.saleDate} and oi.supplier_id = #{bo.supplierId} and oi.pay_time &lt;= #{bo.payTime}
                    <if test="bo.supplierDeptList != null and bo.supplierDeptList.size() > 0">
                        and oi.supplier_dept_id in
                        <foreach collection="bo.supplierDeptList" item="supplierDeptId" open="(" close=")" separator=",">
                            #{supplierDeptId}
                        </foreach>
                    </if>

                GROUP BY
                    oi.supplier_sku_id
            ) od
                LEFT JOIN (
                SELECT
                    eg.supplier_sku_id AS supplier_sku_id,
                    coalesce(SUM( eg.entruck_quantity ),0) AS planDeliveryCount
                FROM
                    rw_entruck_goods eg
                        LEFT JOIN rw_entruck_record er ON er.id = eg.record_id
                WHERE
                    er.sale_date=#{bo.saleDate}
                    and er.supplier_id = #{bo.supplierId}
                    <if test="bo.supplierDeptList != null and bo.supplierDeptList.size() > 0">
                        and er.supplier_dept_id in
                        <foreach collection="bo.supplierDeptList" item="supplierDeptId" open="(" close=")" separator=",">
                            #{supplierDeptId}
                        </foreach>
                    </if>
                GROUP BY
                    eg.supplier_sku_id
            ) a ON od.supplier_sku_id = a.supplier_sku_id
        where od.supplier_sku_id is not null
    </select>

    <select id="commodityStatisticsRegionSaleSkuIds" resultType="Long">
        SELECT
        item.supplier_sku_id AS supplierSkuId
        from order_item item
        where
        item.del_flag = 0 and item.status in ("ALREADY", "FINISH", "CANCEL" ) and (item.cancel_type is null or item.cancel_type in ('OUT','FEW'))
        and item.sale_date=#{saleDate} and item.region_wh_id = #{regionWhId} and item.pay_time &lt;= #{payTime}
        <include refid="buyerFilter"/>
        <include refid="supplierAndDeptFilter"/>
        <include refid="skuFilter"/>
        group by item.id
    </select>


    <select id="stockoutCount" resultType="cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo">
        select COALESCE(sum(sgd.received), 0) as dispatchCount, item.supplier_sku_id as supplierSkuId
        from order_item item
        left join sort_goods_detail sgd
        on item.id = sgd.order_item_id
        where
        item.del_flag = 0 and item.status in ("ALREADY", "FINISH", "CANCEL" ) and (item.cancel_type is null or item.cancel_type in ('OUT','FEW'))
        and item.sale_date=#{saleDate} and item.region_wh_id = #{regionWhId} and item.pay_time &lt;= #{payTime}
        <include refid="buyerFilter"/>
        <include refid="supplierAndDeptFilter"/>
        <include refid="skuFilter"/>
        group by supplierSkuId
    </select>

    <select id="dispatchCount" resultType="cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo">

        SELECT
        item.supplier_sku_id as supplierSkuId
        , coalesce(sum(if(srd.type = 1 and audit_status = 2, srd.stockout_count, 0)),0) as fewCount
        , coalesce(sum(if(srd.type = 2 and audit_status = 2, srd.stockout_count, 0)),0) as lossCount
        , coalesce(sum(if(srd.type = 1 , srd.stockout_count, 0)),0) as allFewCount
        from order_item item
        left join stockout_record_detail srd on item.id = srd.order_item_id
        left join stockout_record sr on srd.stockout_record_id = sr.id
        where
        sr.audit_status != 3 and
        item.del_flag = 0 and item.status in ("ALREADY", "FINISH", "CANCEL" ) and (item.cancel_type is null or item.cancel_type in ('OUT','FEW'))
        and item.sale_date=#{saleDate} and item.region_wh_id = #{regionWhId} and item.pay_time &lt;= #{payTime}
        <include refid="buyerFilter"/>
        <include refid="supplierAndDeptFilter"/>
        <include refid="skuFilter"/>
        group by supplierSkuId
    </select>


    <select id="stockoutCountTotal" resultType="cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo">
        select COALESCE(sum(sgd.received), 0) as dispatchCount, item.supplier_sku_id as supplierSkuId
        from order_item item
        left join sort_goods_detail sgd
        on item.id = sgd.order_item_id
        where
        item.del_flag = 0 and item.status in ("ALREADY", "FINISH", "CANCEL" ) and (item.cancel_type is null or item.cancel_type in ('OUT','FEW'))
        and item.sale_date=#{saleDate} and item.region_wh_id = #{regionWhId} and item.pay_time &lt;= #{payTime}
        <include refid="buyerFilter"/>
        <include refid="supplierAndDeptFilter"/>
    </select>

    <select id="dispatchCountTotal" resultType="cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo">

        SELECT
        item.supplier_sku_id as supplierSkuId
        ,  coalesce(sum(if(srd.type = 1 and audit_status = 2 , srd.stockout_count, 0)),0) as fewCount
        , coalesce(sum(if(srd.type = 2 and audit_status = 2 , srd.stockout_count, 0)),0) as lossCount
        , coalesce(sum(if(srd.type = 1 , srd.stockout_count, 0)),0) as allFewCount
        from order_item item
        left join stockout_record_detail srd on item.id = srd.order_item_id
        left join stockout_record sr on srd.stockout_record_id = sr.id
        where
        sr.audit_status != 3 and
        item.del_flag = 0 and item.status in ("ALREADY", "FINISH", "CANCEL" ) and (item.cancel_type is null or item.cancel_type in ('OUT','FEW'))
        and item.sale_date=#{saleDate} and item.region_wh_id = #{regionWhId} and item.pay_time &lt;= #{payTime}
        <include refid="buyerFilter"/>
        <include refid="supplierAndDeptFilter"/>
    </select>

    <select id="dispatchCountCity" resultType="cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo">
        SELECT
        coalesce(sum(gd.received), 0) as dispatchCount
        ,oi.supplier_sku_id
        FROM
        order_item oi
        LEFT JOIN sort_goods_detail gd ON gd.order_item_id = oi.id
        WHERE
        oi.`STATUS` IN ( "FINISH", "ALREADY", "CANCEL" ) and (oi.cancel_type is null or oi.cancel_type in ('OUT','FEW'))
        and oi.pay_time &lt;= #{bo.payTime}
        and oi.sale_date=#{bo.saleDate} and oi.city_wh_id = #{bo.cityWhId}
        <if test="bo.salemanCustomers != null and bo.salemanCustomers.size() > 0">
            and oi.customer_id in
            <foreach collection="bo.salemanCustomers" item="customerId" open="(" close=")" separator=",">
                #{customerId}
            </foreach>
        </if>
        <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
            AND (oi.logistics_id IN
            <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                #{logisticsId}
            </foreach>
            or
            oi.logistics_id_level2 IN
            <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                #{logisticsId}
            </foreach>
            )
        </if>
        <if test="supplierSkuIds != null and supplierSkuIds.size() > 0">
            and oi.supplier_sku_id in
            <foreach collection="supplierSkuIds" item="supplierSkuId" open="(" close=")" separator=",">
                #{supplierSkuId}
            </foreach>
        </if>
        <if test="bo.placePaths != null and bo.placePaths.size() > 0">
            and oi.place_path in
            <foreach collection="bo.placePaths" item="placePath" open="(" close=")" separator=",">
                #{placePath}
            </foreach>
        </if>
        GROUP BY
        oi.supplier_sku_id

    </select>

    <select id="dispatchCountCityTotal" resultType="cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo">
        SELECT
        coalesce(sum(gd.received), 0) as dispatchCount
        FROM
        order_item oi
        LEFT JOIN sort_goods_detail gd ON gd.order_item_id = oi.id
        WHERE
        oi.`STATUS` IN ( "FINISH", "ALREADY", "CANCEL" ) and (oi.cancel_type is null or oi.cancel_type in ('OUT','FEW'))
        and oi.pay_time &lt;= #{bo.payTime}
        and oi.sale_date=#{bo.saleDate} and oi.city_wh_id = #{bo.cityWhId}
        <if test="bo.salemanCustomers != null and bo.salemanCustomers.size() > 0">
            and oi.customer_id in
            <foreach collection="bo.salemanCustomers" item="customerId" open="(" close=")" separator=",">
                #{customerId}
            </foreach>
        </if>
        <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
            AND (oi.logistics_id IN
            <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                #{logisticsId}
            </foreach>
            or
            oi.logistics_id_level2 IN
            <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                #{logisticsId}
            </foreach>
            )
        </if>
        <if test="bo.placePaths != null and bo.placePaths.size() > 0">
            and oi.place_path in
            <foreach collection="bo.placePaths" item="placePath" open="(" close=")" separator=",">
                #{placePath}
            </foreach>
        </if>
    </select>

    <select id="getSettleCount" resultType="Integer">
        SELECT oi.settle_number as  settleCount
        FROM order_item oi
        where oi.order_code = #{code}
    </select>

    <select id="selectLogisticsOrderForExport" resultType="cn.xianlink.order.domain.excel.LogisticsOrderExportDto">
        select  o.city_wh_id
        , oi.logistics_id
        , oi.`place_name`
        , oi.spu_name
        ,oi.place_name_level2 as placeNameLevel2
        , oi.supplier_sku_id
        , o.`create_name` as customerName
        , o.create_time
        , o.customer_id as customerId
        , oi.count
        , oi.platform_freight_amount as platformFreightAmount
        , oi.base_freight_amount as baseFreightAmount
        , oi.platform_freight_amount_level2 as platformFreightAmountLevel2
        , oi.freight_total_amount as freightAmount
        , oi.freight_total_free_amount as freightFreeAmount
        , oi.platform_service_amount
        , oi.platform_service_free_amount
        , oi.product_free_amount
        , oi.product_amount - oi.product_free_amount as productTotalAmount
        from order_item oi
                 join `order` o on o.id = oi.order_id and o.`del_flag` =0
        where
            o.`sale_date` = #{saleDate}
            and (oi.logistics_id in
                <foreach collection="logisticsIds" item="logisticsId" open="(" close=")" separator=",">
                    #{logisticsId}
                </foreach>
                or oi.logistics_id_level2 in
                <foreach collection="logisticsIds" item="logisticsId" open="(" close=")" separator=",">
                    #{logisticsId}
                </foreach>
                )
            and oi.cancel_type is null
        <if test="placePaths != null and placePaths.size() > 0">
            and oi.place_path in
            <foreach collection="placePaths" item="placePath" open="(" close=")" separator=",">
                #{placePath}
            </foreach>
        </if>
          and oi.`del_flag` =0
        order by oi.`place_id`,o.`create_name`, oi.spu_name, oi.count desc;
    </select>

    <select id="getSkuCountByCustomerId" resultType="cn.xianlink.order.domain.vo.order.OrderSkuCustomerVO">
        select supplier_sku_id as supplierSkuId,sku_id as skuId, coalesce(sum(count),0) as `count`
        from order_item oi
        where
         sale_date = #{saleDate}
        and `status` != 'CANCEL'
        and customer_id = #{customerId}
        <if test="supplierSkuIdList != null and supplierSkuIdList.size() > 0">
            and supplier_sku_id in
            <foreach collection="supplierSkuIdList" item="skuId" open="(" close=")" separator=",">
                #{skuId}
            </foreach>
        </if>
        <if test="skuIdList != null and skuIdList.size() > 0">
            and sku_id in
            <foreach collection="skuIdList" item="skuId" open="(" close=")" separator=",">
                #{skuId}
            </foreach>
        </if>
        group by supplier_sku_id
    </select>

    <update id="reopenAfterSales">
        update banguo_order.order_item
        set after_sale_day    = 2,
            after_sale_status = 1,
            status            = 'ALREADY',
            settle_time       = null
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="saleCustomerCount" resultType="cn.xianlink.order.domain.vo.report.SaleCustomerCountVo">

        SELECT
        oi.customer_id as customer_id,
        coalesce(sum(oi.`count`),0) as `order_count`,
        coalesce(COUNT( distinct oi.supplier_sku_id),0) as sku_count,
        coalesce(sum(oi.spu_gross_weight * oi.`count`),0) as spu_gross_weight,
        coalesce(sum(oi.refund_out_count),0) as out_count,
        coalesce(sum(oi.refund_few_count),0)as few_count,
        ifnull(gr.delivery_number, max(oi.delivery_number)) as delivery_number,
        oi.supplier_sku_id as supplier_sku_id
        FROM order_item oi
        left join receive_goods_record_detail sd on oi.id = sd.order_item_id
        left join receive_goods_record gr on sd.receive_goods_record_id = gr.id
        where oi.status in ("FINISH","ALREADY", "CANCEL") and (oi.cancel_type is null or oi.cancel_type in ('OUT','FEW'))
        and oi.sale_date=#{saleDate} and oi.city_wh_id = #{cityWhId}
        <if test="customers != null and customers.size() > 0">
            and oi.customer_id in
            <foreach collection="customers" item="customerId" open="(" close=")" separator=",">
                #{customerId}
            </foreach>
        </if>
        <if test="placePaths != null and placePaths.size() > 0">
            and oi.place_path in
            <foreach collection="placePaths" item="placePath" open="(" close=")" separator=",">
                #{placePath}
            </foreach>
        </if>
        GROUP BY
        oi.customer_id
    </select>

    <select id="selectOrderInfoExportPage" resultType="cn.xianlink.order.domain.dto.order.OrderItemExportSelectDTO">
        select o.id as orderId,
        oi.id as orderItemId,
        oi.sale_date,
        oi.city_wh_id,
        oi.region_wh_id,
        oi.logistics_id,
        oi.buyer_name,
        oi.customer_id,
        oi.spu_name,
        oi.supplier_sku_id,
        oi.supplier_id,
        oi.price,
        oi.spu_gross_weight,
        oi.spu_net_weight,
        oi.count,
        oi.product_amount,
        oi.platform_service_amount,
        oi.platform_freight_amount,
        oi.base_freight_amount,
        oi.region_freight_amount,
        oi.platform_service_free_amount,
        oi.freight_total_free_amount,
        o.financial_service_amount,
        o.product_amount as totalProductAmount,
        oi.pay_time,
        o.code as orderCode,
        oi.settle_time,
        oi.product_free_amount
        from banguo_order.order_item oi
        inner join banguo_order.`order` o on oi.order_id = o.id
        <include refid="orderInfoPageFilter"/>
    </select>
    <select id="getOrderSalesByCity" resultType="cn.xianlink.order.api.vo.RemoteOrderSalesVO">
        select
        sku_id, supplier_id, city_wh_id, category_id, category_id_level2, category_path_name,
        spu_name, sum(count) as thiSalesCount, cancel_type, region_wh_id
        from order_item
        where city_wh_id = #{cityWhId}
        and sale_date between #{saleDateStart} and #{saleDateEnd}
        and status in ('FINISH','ALREADY','CANCEL')
        and business_type != 40
        group by sku_id, cancel_type having sum(count) >=3
        order by cancel_type desc
    </select>
    <select id="getOrderSalesCustomer" resultType="cn.xianlink.order.api.vo.RemoteOrderSalesVO">
        select
        category_id_level2, cancel_type, group_concat(distinct customer_id) as customerIds
        from order_item
        where city_wh_id = #{cityWhId}
        and sale_date between #{saleDateStart} and #{saleDateEnd}
        and status in ('FINISH','ALREADY','CANCEL')
        and business_type != 40
        and category_id_level2 in
        <foreach collection="categoryIds" item="categoryId" open="(" close=")" separator=",">
            #{categoryId}
        </foreach>
        group by sku_id, cancel_type
        order by cancel_type desc
    </select>



    <select id="refundOrderDifference" resultType="cn.xianlink.order.domain.order.vo.QueryItemListVo">
        select
            *
        from
            order_item
        <where>
            del_flag = 0 and status in
            <foreach collection="bo.statusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
            <if test="bo.saleDate != null">
                and sale_date = #{bo.saleDate}
            </if>
            <if test="bo.supplierSkuIdList != null and bo.supplierSkuIdList.size() > 0">
                and supplier_sku_id in
                <foreach collection="bo.supplierSkuIdList" item="supplierSkuId" open="(" close=")" separator=",">
                    #{supplierSkuId}
                </foreach>
            </if>
            <if test="bo.orderItemIdList != null and bo.orderItemIdList.size() > 0">
                and id in
                <foreach collection="bo.orderItemIdList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        limit #{bo.offset}, #{bo.pageSize}
    </select>
    <select id="saleCustomerCountTotal" resultType="cn.xianlink.order.domain.vo.report.SaleCustomerCountVo">
        SELECT
        coalesce(sum(`count`),0) as `orderCount`,
        coalesce(COUNT( distinct supplier_sku_id),0) as skuCount,
        coalesce(sum(spu_gross_weight * `count`),0) as spuGrossWeight,
        coalesce(sum( refund_out_count),0) as outCount,
        coalesce(sum( refund_few_count),0)as fewCount
        FROM order_item
        where status in ("FINISH","ALREADY", "CANCEL") and (cancel_type is null or cancel_type in ('OUT','FEW'))
        and sale_date=#{saleDate} and city_wh_id = #{cityWhId}
        <if test="customers != null and customers.size() > 0">
            and customer_id in
            <foreach collection="customers" item="customerId" open="(" close=")" separator=",">
                #{customerId}
            </foreach>
        </if>
        <if test="placePaths != null and placePaths.size() > 0">
            and place_path in
            <foreach collection="placePaths" item="placePath" open="(" close=")" separator=",">
                #{placePath}
            </foreach>
        </if>
    </select>

    <update id="updateDistribution">
        update order_item
        <set>
            distribution_amount = CASE id
                <foreach collection="list" item="item">
                    WHEN #{item.id} THEN #{item.distributionAmount}
                </foreach>
            ELSE 0 END,
            distribution_tax_amount = CASE id
                <foreach collection="list" item="item">
                    WHEN #{item.id} THEN #{item.distributionTaxAmount}
                </foreach>
            ELSE 0 END
        </set>
        where id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="queryPlatformSubsidyByDate" resultType="cn.xianlink.order.domain.OrderItem">
        select
        *
        from order_item
        <where>
            status in ("FINISH","ALREADY")
            and del_flag = 0
            and (region_subsidy_amount > 0 or sku_subsidy_amount >0)
            and count - refund_out_count - refund_few_count > 0
            <if test="bo.supplierId != null">
                and supplier_id = #{bo.supplierId}
            </if>
            <if test="bo.supplierDeptId != null">
                and supplier_dept_id = #{bo.supplierDeptId}
            </if>
            <if test="bo.trans != null and bo.trans.size() > 0">
                and sale_date in
                <foreach collection="bo.trans" item="tran" open="(" close=")" separator=",">
                    #{tran.transDate}
                </foreach>
            </if>
        </where>
        order by id desc
        <if test="pageSize!= null and pageNum != null">
            limit #{pageNum}, #{pageSize}
        </if>
    </select>

    <select id="queryPlatformSubsidyTotal" resultType="cn.xianlink.order.api.vo.RemoteSupBillVo">
        select
        count(oi.id) as count,
        sum(oi.region_subsidy_amount + oi.sku_subsidy_amount - ifnull(rpd_total.refund_subsidy_free_amount_sum, 0)) as subsidyAmt
        from order_item oi
        left join (
        select order_item_id, sum(refund_subsidy_free_amount) as refund_subsidy_free_amount_sum
        from refund_product_detail
        where del_flag = 0 and refund_status in (0,1)
        group by order_item_id
        ) rpd_total on oi.id = rpd_total.order_item_id
        where oi.status in ("FINISH", "ALREADY")
        and oi.del_flag = 0
        and oi.region_subsidy_amount > 0
        <if test="bo.supplierId != null">
            and oi.supplier_id = #{bo.supplierId}
        </if>
        <if test="bo.supplierDeptId != null">
            and oi.supplier_dept_id = #{bo.supplierDeptId}
        </if>
        <if test="bo.transDate != null">
            and oi.sale_date = #{bo.transDate}
        </if>
    </select>

    <select id="selectMinPriceBySkuIds" resultType="cn.xianlink.order.api.vo.RemoteRegionSubMaxPriceVo">
        SELECT
        oi.supplier_sku_id as supplierSkuId,
        oi.sale_date as saleDate,
        MIN(
        COALESCE(
        CASE
        WHEN count > 0 THEN oi.price - (oi.region_subsidy_amount / CAST(oi.count AS DECIMAL(10,2)))
        ELSE oi.price
        END,
        0.00
                )
        ) as regionSubMinPrice FROM order_item oi
        WHERE supplier_sku_id IN
        <foreach item="item" collection="supSkuIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND region_subsidy_amount IS NOT NULL AND del_flag = 0
        group by supplier_sku_id
    </select>

    <select id="findFirstCustomer" resultType="java.lang.Long">
        select oi.customer_id as customerId
        from order_item oi
        where status in ("FINISH", "ALREADY")
          and del_flag = 0
          and sale_date = #{saleDate}
          and city_wh_id = #{cityWhId} and pay_time is not null
        order by pay_time asc limit 1
    </select>
</mapper>
