<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.OrderPayRecordMapper">

    <update id="deleteByOrderCode">
        update order_pay_record
        set del_flag = 1
        where order_code = #{orderCode}
          and del_flag = 0
    </update>

    <select id="selectUnPaidCount" resultType="int">
        select count(1) from order_pay_record where order_code = #{orderCode} and del_flag = 0 and status = 0
    </select>

    <select id="selectPaidCount" resultType="int">
        select count(1) from order_pay_record where order_code = #{orderCode} and del_flag = 0 and status = 1
    </select>

    <select id="selectUnPayByOrderCode" resultType="cn.xianlink.order.domain.OrderPayRecord">
        select * from order_pay_record where order_code = #{orderCode} and del_flag = 0 and status = 0
    </select>

    <update id="updatePaySign">
        update order_pay_record
        set pay_sign = #{paySign,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="refund">
        update order_pay_record
        set status = 2
        where order_code = #{orderCode,jdbcType=VARCHAR}
          and status = 1
    </update>

    <update id="completePay">
        update order_pay_record
        set status = 1
        where order_code = #{orderCode,jdbcType=VARCHAR}
          and status = 0
    </update>
</mapper>
