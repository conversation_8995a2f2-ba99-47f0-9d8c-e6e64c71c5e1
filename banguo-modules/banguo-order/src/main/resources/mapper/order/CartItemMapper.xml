<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.CartItemMapper">

    <select id="deleteCartItemById">
        delete from cart_item where id = #{id}
    </select>

    <select id="deleteByUserIdAndSupplierSkuIds">
        delete from cart_item where user_id = #{userId}
        and sku_id in (
            <foreach collection="supplierSkuIds" item="item" separator=",">
                #{item}
            </foreach>
        )
    </select>

    <select id="deleteByUserIdAndRegionWhId">
        delete from cart_item where user_id = #{userId}
        and region_wh_id = #{regionWhId}
    </select>

    <select id="deleteBySupplierSkuIds">
        delete from cart_item where
        supplier_sku_id in (
        <foreach collection="supplierSkuIds" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>

</mapper>
