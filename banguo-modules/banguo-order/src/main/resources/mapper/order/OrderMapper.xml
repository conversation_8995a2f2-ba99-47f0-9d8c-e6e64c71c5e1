<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.OrderMapper">

    <select id="queryPage" resultType="cn.xianlink.order.domain.order.vo.QueryPageVo">
        select
            o.id,
            o.customer_id as customerId,
            o.count as `count`,
            o.region_wh_id,
            o.subsidy_free_amount as subsidyFreeAmount,
            o.free_total_amount as freeTotalAmount,
            o.total_amount as totalAmount,
            o.business_type as businessType,
            o.status,
            o.pay_channel,
            o.pay_time as payTime,
            o.create_time as createTime,
            o.total_gross_weight as totalGrossWeight,
            o.total_net_weight as totalNetWeight,
            o.remark,
            o.code
        from
            `order` o
        <if test="(bo.spuName != null and bo.spuName != '') || (bo.skuName != null and bo.skuName != '') || (bo.workStatus != null and bo.workStatus != '') || (bo.logisticsIds != null and bo.logisticsIds.size() > 0) || (bo.supplierIdList != null and bo.supplierIdList.size() != 0)">
            left join order_item oi on o.id = oi.order_id and oi.del_flag = 0
        </if>
        <where>
            <if test="bo.supplierIdList != null and bo.supplierIdList.size() != 0">
                and oi.supplier_id in
                <foreach collection="bo.supplierIdList" item="item" open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
            <if test="bo.cityWhIdList != null and bo.cityWhIdList.size() != 0">
                and o.city_wh_id in
                <foreach collection="bo.cityWhIdList" item="item" open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
            <if test="bo.cancelTypeList != null and bo.cancelTypeList.size() != 0">
                and (o.cancel_type in
                <foreach collection="bo.cancelTypeList" item="item" open="(" close=")" separator="," >
                    #{item}
                </foreach>
                or o.cancel_type is null)
            </if>
            <if test="bo.businessTypeList != null and bo.businessTypeList.size() != 0">
                and o.business_type in
                <foreach collection="bo.businessTypeList" item="item" open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
            <if test="bo.customerIdList != null and bo.customerIdList.size() > 0">
                and o.customer_id in
                <foreach collection="bo.customerIdList" item="customerId" open="(" close=")" separator=",">
                    #{customerId}
                </foreach>
            </if>
            <if test="bo.skuName != null and bo.skuName != ''">
                and oi.spu_name like concat('%', #{bo.skuName}, '%')
            </if>
            <if test="bo.spuName != null and bo.spuName != ''">
                and oi.spu_name like concat('%', #{bo.spuName}, '%')
            </if>
            <if test="bo.cityWhId != null">
                and o.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.regionWhId != null">
                and o.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.businessType != null">
                and o.business_type = #{bo.businessType}
            </if>
            <if test="bo.workStatus != null">
                and oi.work_status = #{bo.workStatus}
            </if>
            <if test="bo.createTimeStart != null">
                and o.create_time &gt;= #{bo.createTimeStart}
            </if>
            <if test="bo.createTimeEnd != null">
                and o.create_time &lt;= #{bo.createTimeEnd}
            </if>
            <if test="bo.saleDateStart != null">
                and o.sale_date &gt;= #{bo.saleDateStart}
            </if>
            <if test="bo.saleDateEnd != null">
                and o.sale_date &lt;= #{bo.saleDateEnd}
            </if>
            <if test="bo.status != null and bo.status != ''">
                and o.status = #{bo.status}
            </if>
            <if test="bo.saleDate != null">
                and o.sale_date = #{bo.saleDate}
            </if>
            <if test="bo.logisticsIds != null and bo.logisticsIds.size() > 0">
                and (oi.logistics_id in
                <foreach collection="bo.logisticsIds" item="logisticsId" open="(" close=")" separator=",">
                    #{logisticsId}
                </foreach>
                 or oi.logistics_id_level2 in
                <foreach collection="bo.logisticsIds" item="logisticsId" open="(" close=")" separator=",">
                    #{logisticsId}
                </foreach>
                )
            </if>
            <if test="bo.placePaths != null and bo.placePaths.size() > 0">
                and o.place_path in
                <foreach collection="bo.placePaths" item="placePath" open="(" close=")" separator=",">
                    #{placePath}
                </foreach>
            </if>
            and o.del_flag = 0
        </where>
        <if test="(bo.spuName != null and bo.spuName != '') || (bo.skuName != null and bo.skuName != '') || (bo.workStatus != null and bo.workStatus != '') || (bo.logisticsIds != null and bo.logisticsIds.size() > 0)">
            group by o.id
        </if>
        order by o.create_time desc
    </select>


    <select id="queryPageDelivery" resultType="cn.xianlink.order.domain.order.vo.QueryDeliveryVo">
        select o.id, o.code, o.pay_time, o.region_wh_id, o.sale_date, o.business_type, o.remark, o.customer_id,
            o.logistics_id, o.city_wh_id,
            if(sd.id is null, if(ssr.id is null,10,90) ,30) as delivery_status,
            sd.create_time as delivery_time, sd.id as delivery_id
        from
        `order` o
            left join sup_delivery sd on o.code = sd.delivery_no and sd.del_flag = 0
            left join stockout_sku_record ssr on o.id = ssr.order_id and ssr.del_flag = 0 and ssr.type = 1 and stockout_count > 0
        <where>
            o.del_flag = 0 and (o.status = 'ALREADY' or o.status = 'FINISH' or (o.status = 'CANCEL' and (o.cancel_type = 'OUT' or o.cancel_type = 'FEW')))
            <if test="bo.code != null and bo.code != ''">
                and o.code = #{bo.code}
            </if>
            <if test="bo.customerId != null">
                and o.customer_id = #{bo.customerId}
            </if>
            <if test="bo.customerIdList != null and bo.customerIdList.size() > 0">
                and o.customer_id in
                <foreach collection="bo.customerIdList" item="customerId" open="(" close=")" separator=",">
                    #{customerId}
                </foreach>
            </if>
            <if test="bo.businessType != null">
                and o.business_type = #{bo.businessType}
            </if>
            <if test="bo.regionWhId != null">
                and o.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.saleDate != null">
                and o.sale_date = #{bo.saleDate}
            </if>
            <if test="bo.payTime != null">
                and o.pay_time &lt;= #{bo.payTime}
            </if>
            <if test="bo.deliveryStatus != null">
                and if(sd.id is null, if(ssr.id is null, 10, 90), 30) = #{bo.deliveryStatus}
            </if>
        </where>
        order by o.id desc
    </select>


    <resultMap id="queryDistributionMap" type="cn.xianlink.order.domain.order.vo.QueryDistributionVo">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="customerId" property="customerId"/>
        <result column="count" property="count"/>
        <result column="orderTotalAmount" property="totalAmount"/>
        <result column="business_type" property="businessType"/>
        <result column="createTime" property="createTime"/>
        <result column="createName" property="createName"/>
        <result column="place_name" property="placeName"/>
        <result column="address" property="address"/>
        <result column="lng" property="lng"/>
        <result column="lat" property="lat"/>
        <result column="place_id" property="placeId"/>
        <result column="place_name_level2" property="placeNameLevel2"/>
        <result column="place_id_level2" property="placeIdLevel2"/>
        <result column="logistics_id_level2" property="logisticsIdLevel2"/>
        <result column="address_level2" property="addressLevel2"/>
        <result column="financial_service_amount" property="financialServiceAmount"/>
        <result column="remark" property="remark"/>
        <result column="place_path" property="placePath"/>
        <collection property="orderItemList" ofType="cn.xianlink.order.domain.order.vo.QueryDistributionItemVo">
            <result column="id" property="orderId"/>
            <result column="itemId" property="id"/>
            <result column="orderId" property="orderId"/>
            <result column="itemCode" property="orderCode"/>
            <result column="customerId" property="customerId"/>
            <result column="spuName" property="spuName"/>
            <result column="supplierSkuId" property="supplierSkuId"/>
            <result column="itemCount" property="count"/>
            <result column="spuGrossWeight" property="spuGrossWeight"/>
            <result column="spuNetWeight" property="spuNetWeight"/>
            <result column="refundCount" property="refundCount"/>
            <result column="imgUrl" property="imgUrl"/>
            <result column="after_sale_day" property="afterSaleDay"/>
            <result column="supplier_id" property="supplierId"/>
            <result column="supplier_dept_id" property="supplierDeptId"/>
            <result column="city_wh_id" property="cityWhId"/>
            <result column="region_wh_id" property="regionWhId"/>
            <result column="logistics_id" property="logisticsId"/>
            <result column="buyer_code" property="buyerCode"/>
            <result column="sale_date" property="saleDate"/>
            <result column="price" property="price"/>
            <result column="final_price" property="finalPrice"/>
            <result column="coin_amount" property="coinAmount"/>
            <result column="platform_service_amount" property="platformServiceAmount"/>
            <result column="platform_service_free_amount" property="platformServiceFreeAmount"/>
            <result column="base_freight_amount" property="baseFreightAmount"/>
            <result column="base_freight_free_amount" property="baseFreightFreeAmount"/>
            <result column="region_freight_amount" property="regionFreightAmount"/>
            <result column="region_freight_free_amount" property="regionFreightFreeAmount"/>
            <result column="freight_total_amount" property="freightTotalAmount"/>
            <result column="freight_total_free_amount" property="freightTotalFreeAmount"/>
            <result column="coin_count" property="coinCount"/>
            <result column="other_total_amount" property="otherTotalAmount"/>
            <result column="product_amount" property="productAmount"/>
            <result column="total_amount" property="totalAmount"/>
            <result column="platform_freight_amount" property="platformFreightAmount"/>
            <result column="platform_freight_free_amount" property="platformFreightFreeAmount"/>
            <result column="free_total_amount" property="freeTotalAmount"/>
            <result column="pay_time" property="payTime"/>
            <result column="original_supplier_id" property="originalSupplierId"/>
            <result column="refund_few_count" property="refundFewCount"/>
            <result column="refund_out_count" property="refundOutCount"/>
            <result column="subsidy_free_amount" property="subsidyFreeAmount"/>
            <result column="work_status" property="workStatus"/>
            <result column="itemBusinessType" property="businessType"/>
            <result column="platformFreightAmountLevel2" property="platformFreightAmountLevel2"/>
            <result column="product_free_amount" property="productFreeAmount"/>
            <result column="platform_freight_free_amount_level2" property="platformFreightFreeAmountLevel2"/>
            <result column="prod_type" property="prodType"/>
            <result column="distribution_amount" property="distributionAmount"/>
            <result column="settle_count" property="settleCount"/>
            <result column="distribution_tax_amount" property="distributionTaxAmount"/>
            <result column="delivery_number" property="deliveryNumber"/>
            <result column="region_subsidy_amount" property="regionSubsidyAmount"/>
            <result column="place_id_level2" property="placeIdLevel2"/>
            <result column="oi_financial_service_amount" property="financialServiceAmount"/>
            <result column="sku_subsidy_amount" property="skuSubsidyAmount"/>
        </collection>
    </resultMap>
    <select id="queryDistribution" resultMap="queryDistributionMap" >
        select
            o.id,
            o.code,
            o.customer_id as customerId,
            o.count,
            o.total_amount as orderTotalAmount,
            o.business_type,
            o.create_time as createTime,
            o.create_name as createName,
            o.place_id,
            o.place_name,
            o.address,
            o.logistics_id_level2,
            o.place_id_level2,
            o.place_name_level2,
            o.address_level2,
            o.financial_service_amount,
            o.remark,
            o.place_path,
            i.id as itemId,
            i.order_id as orderId,
            i.customer_id as itemCustomerId,
            i.spu_name as spuName,
            i.supplier_sku_id as supplierSkuId,
            i.count as itemCount,
            i.spu_gross_weight as spuGrossWeight,
            i.spu_net_weight as spuNetWeight,
            (i.refund_out_count + i.refund_few_count) as refundCount,
            i.img_url as imgUrl,
            i.after_sale_day,
            i.supplier_id,
            i.supplier_dept_id,
            i.region_wh_id,
            i.city_wh_id,
            i.logistics_id,
            i.buyer_code,
            i.sale_date,
            i.price,
            i.final_price,
            i.coin_amount,
            i.platform_service_amount,
            i.platform_service_free_amount,
            i.base_freight_amount,
            i.base_freight_free_amount,
            i.region_freight_amount,
            i.region_freight_free_amount,
            i.freight_total_amount,
            i.freight_total_free_amount,
            i.coin_count,
            i.other_total_amount,
            i.product_amount,
            i.total_amount,
            i.platform_freight_amount,
            i.platform_freight_free_amount,
            i.free_total_amount,
            i.pay_time,
            i.original_supplier_id,
            i.refund_few_count,
            i.refund_out_count,
            i.subsidy_free_amount,
            i.work_status,
            o.code as itemCode,
            i.business_type as itemBusinessType,
            i.platform_freight_amount_level2 as platformFreightAmountLevel2,
            i.product_free_amount,
            i.platform_freight_free_amount_level2,
            i.prod_type,
            i.distribution_amount,
            i.settle_count,
            i.distribution_tax_amount,
            i.delivery_number,
            i.region_subsidy_amount,
            i.place_id_level2,
            i.financial_service_amount as oi_financial_service_amount,
            i.sku_subsidy_amount
        from `order` o
        left join order_item i on o.id = i.order_id and i.del_flag = 0
        <where>
            o.del_flag = 0
            <if test="bo.status != null and bo.status != ''">
                and o.status = #{bo.status}
            </if>
            <if test="bo.orderItemStatus != null and bo.orderItemStatus != ''">
                and i.status = #{bo.orderItemStatus}
            </if>
            <if test="bo.payTime != null">
                and o.pay_time &lt;= #{bo.payTime}
            </if>
            <if test="bo.orderId != null">
                and o.id = #{bo.orderId}
            </if>
            <if test="bo.cityWhId != null">
                and o.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.workStatusList != null and bo.workStatusList.size() > 0">
                and i.work_status in
                <foreach collection="bo.workStatusList" item="workStatus" open="(" close=")" separator=",">
                    #{workStatus}
                </foreach>
            </if>
            <if test="bo.regionsWhIdList != null and bo.regionsWhIdList.size() > 0">
                and o.region_wh_id in
                <foreach collection="bo.regionsWhIdList" item="regionsWhId" open="(" close=")" separator=",">
                    #{regionsWhId}
                </foreach>
            </if>
            <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
                and o.logistics_id in
                <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                    #{logisticsId}
                </foreach>
            </if>
            <if test="bo.supplierSkuIdList != null and bo.supplierSkuIdList.size() > 0">
                and i.supplier_sku_id in
                <foreach collection="bo.supplierSkuIdList" item="supplierSkuId" open="(" close=")" separator=",">
                    #{supplierSkuId}
                </foreach>
            </if>
            <if test="bo.orderItemIdList != null and bo.orderItemIdList.size() > 0">
                and i.id in
                <foreach collection="bo.orderItemIdList" item="orderItemId" open="(" close=")" separator=",">
                    #{orderItemId}
                </foreach>
            </if>
            <if test="bo.statusList != null and bo.statusList.size() > 0">
                and o.status in
                <foreach collection="bo.statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getUnpaidCount" resultType="cn.xianlink.order.domain.order.vo.GetUnpaidCountVo">
        select
            business_type as businessType,
            count(id) as count
        from
            `order`
        where del_flag = 0 and status = #{orderStatus} and customer_id = #{customerId} group by business_type
    </select>

    <select id="getSettlementOrderInfo" resultType="cn.xianlink.order.domain.vo.SettlementRelationOrderInfoVo">
        select
        o.sale_date,
        o.city_wh_id,
        sum(o.count) as goodsCount,
        sum(o.product_amount) as goodsAmount,
        sum(o.platform_service_amount) as platformServiceAmount,
        sum(o.platform_freight_amount) as platformFreightAmount,
        sum(o.financial_service_amount) as financialServiceAmount,
        sum(o.free_total_amount) as freeTotalAmount,
        sum(o.platform_service_free_amount) as platformServiceFreeAmount,
        sum(o.platform_freight_free_amount) as regionFreightFreeAmount,
        group_concat(distinct o.id separator ';') as orderIdList,
        sr.status
        from
        `order` o
        left join banguo_order.settlement_relation sr on
        o.sale_date = sr.sale_date
        and o.city_wh_id = sr.city_wh_id
        where o.del_flag = 0
        and o.status = 'ALREADY'
        <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
           and o.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
        </if>
        <if test="bo.createTimeStart != null and bo.createTimeEnd != null">
            and sr.create_time between #{bo.createTimeStart} and #{bo.createTimeStart}
        </if>
        <if test="bo.status == 0">
            and sr.id is null
        </if>
        <if test="bo.status == 1">
            and sr.id is not null
        </if>
        <if test="bo.cityWhId != null">
           and o.city_wh_id = #{bo.cityWhId}
        </if>
        group by o.sale_date,o.city_wh_id
        order by o.sale_date desc, sum(o.count) desc, o.update_time desc
    </select>


    <select id="listByOrderCode" resultType="java.lang.String">
        select distinct(o.code) from `order` o left join
        `order_item` oi on o.id = oi.order_id
        <where>
            <if test="orderCode != null and orderCode != ''">
                o.code like concat('%', #{orderCode}, '%')
            </if>
            <if test="supplierId != null">
                and oi.supplier_id = #{supplierId}
            </if>
            <if test="cityWhId != null">
                and o.city_wh_id = #{cityWhId}
            </if>
            <if test="saleDate != null">
                and o.sale_date = #{saleDate}
            </if>
            <if test="regionWhId != null">
                and o.region_wh_id = #{regionWhId}
            </if>
        </where>
        limit 20
    </select>
    <select id="queryAdminPage" resultType="cn.xianlink.order.domain.order.vo.QueryAdminPageVo">
        select * from `order` o
        <if test="bo.spuName != null and bo.spuName != ''">
            left join order_item oi on o.id = oi.order_id and oi.del_flag = 0
        </if>
        <where>
            o.del_flag = 0
            <if test="bo.spuName != null and bo.spuName != ''">
                and oi.spu_name like concat('%', #{bo.spuName}, '%')
            </if>
            <if test="bo.orderCode != null and bo.orderCode != ''">
                and o.code like concat('%', #{bo.orderCode}, '%')
            </if>
            <if test="bo.status != null and bo.status != ''">
                and o.status = #{bo.status}
            </if>
            <if test="bo.customerId != null">
                and o.customer_id = #{bo.customerId}
            </if>
            <if test="bo.cityWhId != null">
                and o.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.regionWhId != null">
                and o.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.createName != null and bo.createName != ''">
                and o.create_name = #{bo.createName}
            </if>
            <if test="bo.saleDateStart != null">
                and o.sale_date &gt;= #{bo.saleDateStart}
            </if>
            <if test="bo.saleDateEnd != null">
                and o.sale_date &lt;= #{bo.saleDateEnd}
            </if>
            <if test="bo.createTimeStart != null">
                and o.create_time &gt;= #{bo.createTimeStart}
            </if>
            <if test="bo.createTimeEnd != null">
                and o.create_time &lt;= #{bo.createTimeEnd}
            </if>
            <if test="bo.payTimeStart != null">
                and o.pay_time &gt;= #{bo.payTimeStart}
            </if>
            <if test="bo.payTimeEnd != null">
                and o.pay_time &lt;= #{bo.payTimeEnd}
            </if>
        </where>
        order by o.create_time desc
    </select>

    <select id="countOrderCustomer" resultType="long">
        select count(distinct customer_id) from `order` o
        where del_flag = 0 and status in ('ALREADY', 'FINISH', 'CANCEL') and (o.cancel_type is null or o.cancel_type in ('OUT','FEW'))
              and sale_date=#{saleDate} and city_wh_id = #{cityWhId}
    </select>


    <select id="selectOrderCustomer" resultType="long">
        select distinct customer_id from `order` o
        where del_flag = 0 and status in ('ALREADY', 'FINISH', 'CANCEL') and (o.cancel_type is null or o.cancel_type in ('OUT','FEW'))
              and sale_date=#{saleDate}  and city_wh_id = #{cityWhId} and o.pay_time &lt;= #{payTime}
        <if test="placePaths != null and placePaths.size() > 0">
            and o.place_path in
            <foreach collection="placePaths" item="placePath" open="(" close=")" separator=",">
                #{placePath}
            </foreach>
        </if>
    </select>

    <select id="sumOrderAmount" resultType="cn.xianlink.order.domain.vo.report.OrderAmountVo">
        select
            coalesce(sum(coalesce(total_amount,0) - coalesce(financial_service_amount,0)),0) as payAmount,
            coalesce(sum(product_amount - product_free_amount),0) as productAmount,
            coalesce(sum(freight_total_amount - freight_total_free_amount),0) as freightTotalAmount,
            coalesce(sum(financial_service_amount),0) as financialServiceAmount,
            coalesce(sum(platform_service_free_amount),0) as platformServiceFreeAmount,
            coalesce(sum(platform_freight_free_amount),0) as platformFreightFreeAmount,
            coalesce(sum(free_total_amount),0) as freeTotalAmount,
            coalesce(sum(platform_service_amount - platform_service_free_amount),0) as platformServiceAmount
        from `order` o
        <where>
            del_flag = 0 and o.status in ('ALREADY', 'FINISH', 'CANCEL') and (o.cancel_type is null or o.cancel_type in ('OUT','FEW'))
            and sale_date=#{saleDate} and city_wh_id = #{cityWhId} and pay_time &lt;= #{payTime}
            <if test="regionWhId != null">
                and region_wh_id = #{regionWhId}
            </if>
            <if test="customers != null and customers.size() > 0">
                and customer_id in
                <foreach collection="customers" item="customerId" open="(" close=")" separator=",">
                    #{customerId}
                </foreach>
            </if>
            <if test="placePaths != null and placePaths.size() > 0">
                and o.place_path in
                <foreach collection="placePaths" item="placePath" open="(" close=")" separator=",">
                    #{placePath}
                </foreach>
            </if>

        </where>
    </select>
    <resultMap id="customerOrderPageMap" type="cn.xianlink.order.domain.order.vo.CustomerOrderVO">
        <result column="id" property="orderId"/>
        <result column="code" property="orderCode"/>
        <result column="customer_id" property="customerId"/>
        <result column="status" property="status"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="count" property="count"/>
        <result column="create_time" property="createTime"/>
        <result column="sale_date" property="saleDate"/>
        <result column="region_wh_id" property="regionWhId"/>
        <result column="city_wh_id" property="cityWhId"/>
        <result column="logistics_id" property="logisticsId"/>
        <result column="place_id" property="placeId"/>
        <result column="cancel_time" property="cancelTime"/>
        <result column="pay_time" property="payTime"/>
        <result column="business_type" property="businessType"/>
        <result column="total_gross_weight" property="totalGrossWeight"/>
        <result column="product_amount" property="productAmount"/>
        <result column="freight_total_amount" property="freightTotalAmount"/>
        <result column="platform_service_amount" property="platformServiceAmount"/>
        <result column="financial_service_amount" property="financialServiceAmount"/>
        <result column="free_total_amount" property="freeTotalAmount"/>
        <result column="product_free_amount" property="productFreeAmount"/>
        <collection property="customerOrderItemVOList" ofType="cn.xianlink.order.domain.order.vo.CustomerOrderItemVO" columnPrefix="item_">
            <result column="id" property="id"/>
            <result column="order_id" property="orderId"/>
            <result column="order_code" property="orderCode"/>
            <result column="supplier_spu_id" property="supplierSpuId"/>
            <result column="supplier_sku_id" property="supplierSkuId"/>
            <result column="spu_name" property="supplierSpuName"/>
            <result column="business_type" property="businessType"/>
            <result column="count" property="count"/>
            <result column="img_url" property="imgUrl"/>
            <result column="product_amount" property="productAmount"/>
            <result column="spu_gross_weight" property="spuGrossWeight"/>
            <result column="spu_net_weight" property="spuNetWeight"/>
            <result column="supplier_id" property="supplierId"/>
            <result column="supplier_dept_id" property="supplierDeptId"/>
            <result column="final_price" property="finalPrice"/>
            <result column="work_status" property="workStatus"/>
            <result column="product_free_amount" property="productFreeAmount"/>
            <result column="platform_freight_free_amount_level2" property="platformFreightFreeAmountLevel2"/>
            <result column="prod_type" property="prodType"/>
        </collection>
    </resultMap>
    <select id="customerOrderPage" resultMap="customerOrderPageMap">
        SELECT
            o.id,
            o.`code`,
            o.customer_id,
            o.`status`,
            o.pay_channel,
            o.total_amount,
            o.count,
            o.create_time,
            o.sale_date,
            o.region_wh_id,
            o.city_wh_id,
            o.logistics_id,
            o.place_id,
            o.cancel_time,
            o.pay_time,
            o.business_type,
            o.total_gross_weight,
            o.product_amount,
            o.freight_total_amount,
            o.platform_service_amount,
            o.financial_service_amount,
            o.free_total_amount,
            oi.id AS item_id,
            oi.order_id AS item_order_id,
            oi.order_code AS item_order_code,
            oi.supplier_spu_id AS item_supplier_spu_id,
            oi.supplier_sku_id AS item_supplier_sku_id,
            oi.spu_name AS item_supplierSpuName,
            oi.business_type AS item_business_type,
            oi.count AS item_count,
            oi.img_url AS item_img_url,
            oi.product_amount AS item_product_amount,
            oi.buyer_code as item_buyer_code,
            oi.buyer_name as item_buyer_name,
            oi.spu_gross_weight AS item_spu_gross_weight,
            oi.spu_net_weight AS item_spu_net_weight,
            coalesce(oi.spu_gross_weight * oi.count,0) AS item_total_gross_weight,
            coalesce(oi.spu_net_weight  * oi.count,0) AS item_total_net_weight,
            oi.supplier_id AS item_supplier_id,
            oi.supplier_dept_id AS item_supplier_dept_id,
            oi.work_status as item_work_status
        FROM
            `order` o
                LEFT JOIN order_item oi ON o.id = oi.order_id
        <where>
            <if test="bo.regionWhId != null">
               and o.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.customerIdList != null and bo.customerIdList.size() > 0">
                and o.customer_id in
                <foreach collection="bo.customerIdList" item="customerId" open="(" close=")" separator=",">
                    #{customerId}
                </foreach>
            </if>
            <if test="bo.status != null and bo.status !=''">
                and o.`status` = #{bo.status}
            </if>
           <if test="bo.cityWhId != null  ">
                and o.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.logisticsId != null">
                and o.logistics_id = #{bo.logisticsId}
            </if>

            <if test="bo.placeId != null  ">
                and o.place_id = #{bo.placeId}
            </if>
            <if test="bo.orderCode != null and bo.orderCode != '' ">
                and o.code like concat('%', #{bo.orderCode}, '%')
            </if>
            <if test="bo.supplierSpuName != null and bo.supplierSpuName != '' ">
                and oi.spu_name like concat('%', #{bo.supplierSpuName}, '%')
            </if>
            <if test="bo.businessType != null  ">
                and o.business_type = #{bo.businessType}
            </if>
            <if test="bo.saleDate != null  ">
                and o.sale_date = #{bo.saleDate}
            </if>
        </where>
        group by o.id
        order by o.create_time desc
    </select>

    <select id="customerOrderInfo" resultMap="customerOrderPageMap">
        SELECT
            o.id,
            o.`code`,
            o.customer_id,
            o.`status`,
            o.total_amount,
            o.count,
            o.create_time,
            o.sale_date,
            o.region_wh_id,
            o.city_wh_id,
            o.logistics_id,
            o.place_id,
            o.place_id_level2,
            o.cancel_time,
            o.pay_time,
            o.business_type,
            o.total_gross_weight,
            o.product_amount,
            o.freight_total_amount,
            o.platform_service_amount,
            o.financial_service_amount,
            o.free_total_amount,
            o.product_free_amount,
            oi.id AS item_id,
            oi.order_id AS item_order_id,
            oi.order_code AS item_order_code,
            oi.supplier_spu_id AS item_supplier_spu_id,
            oi.supplier_sku_id AS item_supplier_sku_id,
            oi.spu_name AS item_supplierSpuName,
            oi.business_type AS item_business_type,
            oi.count AS item_count,
            oi.img_url AS item_img_url,
            oi.product_amount AS item_product_amount,
            oi.spu_gross_weight AS item_spu_gross_weight,
            oi.spu_net_weight AS item_spu_net_weight,
            oi.buyer_code as item_buyer_code,
            oi.buyer_name as item_buyer_name,
            coalesce(oi.spu_gross_weight * oi.count,0) AS item_total_gross_weight,
            coalesce(oi.spu_net_weight  * oi.count,0) AS item_total_net_weight,
            oi.supplier_id AS item_supplier_id,
            oi.supplier_dept_id AS item_supplier_dept_id,
            oi.final_price as item_final_price,
            oi.work_status as item_work_status,
            oi.product_free_amount as item_product_free_amount,
            oi.platform_freight_free_amount_level2 as item_platform_freight_free_amount_level2,
            oi.prod_type as item_prod_type
        FROM
            `order` o
                LEFT JOIN order_item oi ON o.id = oi.order_id
        where o.code = #{orderCode}
        <if test="orderItemId != null and orderItemId != ''">
            and oi.id = #{orderItemId}
        </if>
    </select>

    <select id="cityWeight" resultType="cn.xianlink.order.domain.vo.order.CityOrderWeightVO">
        select
        coalesce(sum(total_gross_weight),0) as spuGrossWeight,
        coalesce(sum(count),0) as `count`,
        city_wh_id as cityWhId,
        logistics_id as logisticsId,
        sale_date as saleDate
        from `order`
        <where>
            del_flag = 0 and status in ('ALREADY', 'FINISH', 'CANCEL') and (cancel_type is null or cancel_type in ('OUT','FEW'))
            and region_wh_id = #{regionWhId}
            <if test="cityWhId != null">
                and city_wh_id = #{cityWhId}
            </if>
            <if test="saleDate != null">
                and sale_date = #{saleDate}
            </if>
        </where>
        group by city_wh_id ,logistics_id
    </select>

    <select id="citySaleData" resultType="cn.xianlink.order.domain.vo.order.CityOrderWeightVO">
        select
        coalesce(sum(item.`count`),0) as `count`,
        coalesce(sum(coalesce(item.spu_gross_weight,0) * item.`count`),0) as spuGrossWeight,
        o.city_wh_id as cityWhId,
        count(DISTINCT o.customer_id ) as customer_count
        from `order` o join  order_item item on item.order_id = o.id
        <where>
            o.del_flag = 0 and o.status in ('ALREADY', 'FINISH', 'CANCEL') and (o.cancel_type is null or o.cancel_type in ('OUT','FEW'))
            and o.region_wh_id = #{regionWhId}
            <include refid="skuFilter"/>
            <if test="cityWhId != null">
                and o.city_wh_id = #{cityWhId}
            </if>
            <if test="saleDate != null">
                and o.sale_date = #{saleDate}
            </if>
        </where>
        group by o.city_wh_id
        order by `count` desc
    </select>

    <sql id="skuFilter">
        <if test="skuIds != null and skuIds.size() > 0">
            and item.sku_id in
            <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
                #{skuId}
            </foreach>
        </if>
    </sql>

    <select id="citySaleDataTotal" resultType="cn.xianlink.order.domain.vo.order.CityOrderWeightVO">
        select
        coalesce(sum(item.`count`),0) as `count`,
        coalesce(sum(coalesce(item.spu_gross_weight,0) * item.`count`),0) as spuGrossWeight,
        count(DISTINCT o.customer_id ) as customer_count,
        count(DISTINCT o.city_wh_id ) as saleCityCount
        from `order` o join  order_item item on item.order_id = o.id
        <where>
            o.del_flag = 0 and o.status in ('ALREADY', 'FINISH', 'CANCEL') and (o.cancel_type is null or o.cancel_type in ('OUT','FEW'))
            and item.del_flag = 0
            and o.region_wh_id = #{regionWhId}
            <include refid="skuFilter"/>
            <if test="cityWhId != null">
                and o.city_wh_id = #{cityWhId}
            </if>
            <if test="saleDate != null">
                and o.sale_date = #{saleDate}
            </if>
        </where>
    </select>

    <select id="queryOrderDeliveryNumber" resultType="cn.xianlink.order.domain.order.vo.QueryDeliveryNumberVo">
        select
        o.customer_id as customerId,
        o.logistics_id as logisticsId,
        o.logistics_id_level2 as logisticsIdLevel2,
        o.place_id_level2 as placeIdLevel2,
        o.sale_date as saleDate,
        oi.delivery_number as deliveryNumber
        from
        `order` o
        left join order_item oi on o.id = oi.order_id
        <where>
            o.del_flag = 0
            and oi.del_flag = 0
            and oi.delivery_number > 0
            <if test="bo.saleDate != null">
                and o.sale_date = #{bo.saleDate}
            </if>
            <if test="bo.statusList != null and bo.statusList.size()>0">
                and o.status in
                <foreach collection="bo.statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="bo.regionsWhIdList != null and bo.regionsWhIdList.size() > 0">
                and o.region_wh_id in
                <foreach collection="bo.regionsWhIdList" item="regionsWhId" open="(" close=")" separator=",">
                    #{regionsWhId}
                </foreach>
            </if>
            <if test="bo.logisticsIdList != null and bo.logisticsIdList.size() > 0">
                and o.logistics_id in
                <foreach collection="bo.logisticsIdList" item="logisticsId" open="(" close=")" separator=",">
                    #{logisticsId}
                </foreach>
            </if>
        </where>
        group by o.customer_id,o.sale_date,o.place_id_level2
    </select>

    <update id="updateDeliveryNumber">
        update order_item set delivery_number = #{deliveryNumber} where order_id = #{orderId}
    </update>
</mapper>