<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.ReportLossOrderFlowMapper">

    <update id="deleteByReportLossId">
        update report_loss_order_flow set del_flag = 1 where report_loss_id = #{reportLossId}
    </update>

    <select id="selectByReportLossId" resultType="cn.xianlink.order.domain.ReportLossOrderFlow">
        select * from report_loss_order_flow where report_loss_id = #{reportLossId} and del_flag = 0 order by id desc;
    </select>

    <update id="deleteById">
        update report_loss_order_flow set del_flag = 1 where id = #{id}
    </update>
</mapper>
