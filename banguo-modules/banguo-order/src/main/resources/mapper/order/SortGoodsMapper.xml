<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.SortGoodsMapper">

    <select id="sortPage" resultType="cn.xianlink.order.domain.vo.sortGoods.SortGoodsPageVO">
        select
        s.id, s.city_wh_id, s.logistics_id, s.rw_depart_id, s.rw_entruck_id, s.rw_entruck_no, s.region_wh_id, s.supplier_sku_id,
        s.spu_name, s.spu_gross_weight, s.spu_net_weight, s.order_received, s.all_received, s.received, s.unreceived, s.actual_received,
        s.sort_count, s.img_url, s.sale_date, s.update_code, s.update_name, s.update_time, s.place_id, s.after_sale_type,
        MIN(d.status) OVER(PARTITION BY d.sort_goods_id) AS status
        FROM sort_goods s
        INNER JOIN sort_goods_detail d on d.sort_goods_id = s.id
        where
        s.del_flag = 0 and d.del_flag = 0
        <if test="bo.saleDate != null ">
            and s.sale_date = #{bo.saleDate}
        </if>
        <if test="bo.cityWhId != null and bo.cityWhId != 0">
            and s.city_wh_id = #{bo.cityWhId}
        </if>
        <if test="bo.spuName != null and bo.spuName != ''">
            and s.spu_search_name like concat('%', #{bo.spuName},'%')
        </if>
        <if test="bo.createTimeStart != null and bo.createTimeEnd != null">
            and s.create_time between #{bo.createTimeStart} and #{bo.createTimeEnd}
        </if>
        <if test="bo.supplierSkuIds != null and bo.supplierSkuIds.size() > 0">
            and s.supplier_sku_id in
            <foreach collection="bo.supplierSkuIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bo.rwDepartId != null">
            and s.rw_depart_id = #{bo.rwDepartId}
        </if>
        <if test="bo.rwEntruckId != null">
            and s.rw_entruck_id = #{bo.rwEntruckId}
        </if>
        <if test="bo.logisticsIds != null and bo.logisticsIds.size() != 0">
            and s.logistics_id in
            <foreach collection="bo.logisticsIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            and
            <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
                (
            </if>
            d.place_id_level2 = 0
        </if>
        <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
            <if test="bo.logisticsIds != null and bo.logisticsIds.size() > 0">
                or
            </if>
            <if test="bo.logisticsIds == null  or bo.logisticsIds.size() == 0">
                and
            </if>
            d.place_id_level2 in
            <foreach collection="bo.placeIdLevel2List" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            <if test="bo.logisticsIds != null and bo.logisticsIds.size() > 0">
                )
            </if>
        </if>

        group by s.id
        <if test="bo.status != null">
            HAVING MIN(d.status) = #{bo.status}
        </if>
        order by d.status,
        <if test="bo.receivedSort != null and bo.receivedSort == 1">
            s.received desc,
        </if>
         s.create_time desc, s.id
    </select>
    <select id="getSortById" resultType="cn.xianlink.order.domain.vo.sortGoods.SortGoodsPageVO">
        select * from sort_goods
        where id = #{id}
        and del_flag = 0
    </select>
    <select id="getDeliveryNumber" resultType="cn.xianlink.order.domain.excel.SortGoodsExportVo">
        select d.customer_id, d.delivery_number, d.customer_name, d.place_level2_code,
        sum(d.order_receivable) as orderTotalCount, s.region_wh_id
        from sort_goods_detail d
        left join sort_goods s on d.sort_goods_id = s.id and s.del_flag = 0
        where
        d.del_flag = 0
        <if test="bo.placeId != null and bo.placeId != 0">
            and s.place_id = #{bo.placeId} and place_id_level2 = 0
        </if>
        <if test="bo.placeIdLevel2 != null and bo.placeIdLevel2 != 0">
            and d.place_id_level2 = #{bo.placeIdLevel2}
        </if>
        and d.create_time between #{bo.createTimeStart} and #{bo.createTimeEnd}
        group by d.customer_id
        order by d.delivery_number
    </select>
    <select id="getSortGoodsByStatus" resultType="cn.xianlink.order.domain.SortGoods">
        select
        s.id, s.city_wh_id, s.logistics_id, s.place_id, s.region_wh_id, s.supplier_sku_id, s.spu_name,
        s.spu_search_name, s.img_url, s.spu_gross_weight, s.spu_net_weight, s.order_received,
        s.received, s.sort_count, s.status, s.sale_date, s.after_sale_type,s.rw_entruck_id,
        s.rw_depart_id, s.rw_entruck_no, s.all_received,
        sum(d.received) as actualReceived, sum(s.unreceived) as unreceived
        from sort_goods_detail d left join sort_goods s on d.sort_goods_id = s.id
        where
        s.del_flag = 0
        <if test="notStatusList != null and notStatusList.size() > 0">
            and d.status not in
            <foreach collection="notStatusList" item="status" separator="," open="(" close=")">
                #{status}
            </foreach>
        </if>
        <if test="saleDate != null ">
            and s.sale_date = #{saleDate}
        </if>
        <if test="logisticsIds != null and logisticsIds.size() > 0">
            and s.logistics_id in
            <foreach collection="logisticsIds" item="logisticsId" separator="," open="(" close=")">
                #{logisticsId}
            </foreach>
            and
            <if test="placeIdLevel2List != null and placeIdLevel2List.size() > 0">
                (
            </if>
            d.place_id_level2 = 0
        </if>
        <if test="placeIdLevel2List != null and placeIdLevel2List.size()>0">
            <if test="logisticsIds != null and logisticsIds.size() > 0">
                or
            </if>
            <if test="logisticsIds == null  or logisticsIds.size() == 0">
                and
            </if>
            d.place_id_level2 in
            <foreach collection="placeIdLevel2List" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            <if test="logisticsIds != null and logisticsIds.size() > 0">
                )
            </if>
        </if>
        group by s.id
        order by d.status, s.received desc, s.create_time desc, s.id
    </select>
    <select id="waitConfirmSortList" resultType="cn.xianlink.order.domain.SortGoods">
        select
        d.status, s.supplier_sku_id, s.spu_name, s.sale_date,
        s.received, sum(d.received) as actualReceived, sum(d.all_received) as allReceived, sum(d.unreceived) as unreceived,
        MIN(d.status) OVER(PARTITION BY d.sort_goods_id) AS status
        FROM sort_goods s
        INNER JOIN sort_goods_detail d on d.sort_goods_id = s.id
        where
        s.city_wh_id = #{bo.cityWhId}
        and s.del_flag = 0
        <if test="bo.logisticsIds != null and bo.logisticsIds.size() != 0">
            and s.logistics_id in
            <foreach collection="bo.logisticsIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            and
            <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
                (
            </if>
            d.place_id_level2 = 0
        </if>
        <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
            <if test="bo.logisticsIds != null and bo.logisticsIds.size() > 0">
                or
            </if>
            <if test="bo.logisticsIds == null  or bo.logisticsIds.size() == 0">
                and
            </if>
            d.place_id_level2 in
            <foreach collection="bo.placeIdLevel2List" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            <if test="bo.logisticsIds != null and bo.logisticsIds.size() > 0">
                )
            </if>
        </if>
        group by s.id
        <if test="status != null">
            HAVING MIN(d.status) = #{status}
        </if>
        order by s.supplier_sku_id desc
    </select>

</mapper>
