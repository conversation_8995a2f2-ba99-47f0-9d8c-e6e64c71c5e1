<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.OrderPayAccountTransMapper">

    <update id="deleteByOrderCode">
        update order_pay_account_trans
        set del_flag = 1
        where order_code = #{orderCode}
          and del_flag = 0
    </update>


    <select id="orderItermPage" resultType="cn.xianlink.order.domain.OrderPayAccountTrans">
        select id,
        trans_type,
        pay_time,
        order_code,
        order_id,
        item_id,
        customer_id,
        city_wh_name,
        region_wh_name,
        order_remark,
        order_amount,
        order_count,
        supplier_id,
        sale_date,
        spu_name,
        order_price,
        statement_no,
        trade_trans_no,
        update_time
        FROM order_pay_account_trans
        <where>
            <if test="bo.orderCode != null and bo.orderCode != ''">
                and order_code like concat('%', #{bo.orderCode,jdbcType=VARCHAR}, '%')
            </if>
            <if test="bo.customerId != null">
                and customer_id = #{bo.customerId,jdbcType=BIGINT}
            </if>
            <if test="bo.supplierId != null">
                and supplier_id = #{bo.supplierId,jdbcType=BIGINT}
            </if>
            <if test="bo.cityWhId != null">
                and city_wh_id = #{bo.cityWhId,jdbcType=BIGINT}
            </if>
            <if test="bo.regionWhId != null">
                and region_wh_id = #{bo.regionWhId,jdbcType=BIGINT}
            </if>
            <if test="bo.transType != null">
                and trans_type = #{bo.transType,jdbcType=INTEGER}
            </if>
            <if test="bo.saleTimeStart != null">
                and sale_date >= #{bo.saleTimeStart,jdbcType=TIMESTAMP}
            </if>
            <if test="bo.saleTimeEnd != null">
                and sale_date &lt;= #{bo.saleTimeEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="bo.spuCode != null and bo.spuCode != ''">
                and spu_code = #{bo.spuCode,jdbcType=VARCHAR}
            </if>
            <if test="bo.spuName != null and bo.spuName != ''">
                and spu_name like concat('%', #{bo.spuName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="bo.payTimeStart != null">
                and pay_time >= #{bo.payTimeStart,jdbcType=TIMESTAMP}
            </if>
            <if test="bo.payTimeEnd != null">
                and pay_time &lt;= #{bo.payTimeEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="bo.updateTimeStart != null">
                and update_time >= #{bo.updateTimeStart,jdbcType=TIMESTAMP}
            </if>
            <if test="bo.updateTimeEnd != null">
                and update_time &lt;= #{bo.updateTimeEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="bo.statementNo != null and bo.statementNo != ''">
                and statement_no like concat('%', #{bo.statementNo,jdbcType=VARCHAR}, '%')
            </if>
            <if test="bo.statementStatus != null and bo.statementStatus == 1">
                and statement_no = ''
            </if>
            <if test="bo.statementStatus != null and bo.statementStatus == 2">
                and statement_no != ''
            </if>
            <if test="bo.tradeTransNo != null and bo.tradeTransNo != ''">
                and trade_trans_no like concat('%', #{bo.tradeTransNo,jdbcType=VARCHAR}, '%')
            </if>
            and del_flag = 0
        </where>
        order by id desc
    </select>

    <update id="completePay">
        update order_pay_account_trans
        set pay_time = now()
        where order_code = #{orderCode,jdbcType=VARCHAR}
          and pay_time is null
    </update>

    <update id="updateStatement">
        update order_pay_account_trans set statement_no = #{statementNo,jdbcType=VARCHAR}, trade_trans_no = #{splitBillNo,jdbcType=VARCHAR}
        where order_id = #{orderId,jdbcType=BIGINT} and supplier_id = #{supplierId,jdbcType=BIGINT} and sku_id = #{skuId,jdbcType=BIGINT} and statement_no = ''
    </update>

    <select id="skuCollectionPage" resultType="cn.xianlink.order.domain.vo.pay.SupPaymentMiniListVo">
        select supplier_id as supplierId, sku_id as skuId, spu_name as spuName, sale_date as saleDate, count(1) as orderNumber, sum(order_count) as skuNumber, sum(order_amount) as skuAmount
        from order_pay_account_trans
        where supplier_id = #{bo.supplierId,jdbcType=BIGINT}
          and statement_no = ''
          and pay_time is not null
        <if test="bo.spuName != null and bo.spuName != ''">
            and spu_name LIKE CONCAT('%', #{bo.spuName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="bo.timeStart != null">
            and create_time >= #{bo.timeStart,jdbcType=TIMESTAMP}
        </if>
        <if test="bo.timeEnd != null">
            and create_time &lt;= #{bo.timeStart,jdbcType=TIMESTAMP}
        </if>
        and del_flag = 0
        group by sku_id
        order by sale_date desc
    </select>
</mapper>
