<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.ReportLossOrderMapper">
    <select id="miniPage" resultType="cn.xianlink.order.domain.ReportLossOrder">
        select o.id,
               o.order_item_id,
               o.flow_id,
               o.supplier_dept_id,
               o.loss_type,
               o.order_no,
               o.spu_name,
               o.sku_id,
               o.supplier_sku_id,
               o.sku_img_url,
               o.spu_net_weight,
               o.sku_price,
               o.loss_amount,
               o.loss_subsidy_free_amount,
               o.loss_weight,
               o.report_loss_no,
               o.customer_id,
               o.loss_status,
               o.spu_gross_weight,
               o.refund_goods_amount,
               o.refund_subsidy_free_amount,
               o.region_wh_id,
               o.region_wh_name
        from report_loss_order o
        <where>
            <if test="bo.customerId != null">
                and o.customer_id = #{bo.customerId,jdbcType=BIGINT}
            </if>
            <if test="bo.supplierId != null">
                and o.supplier_id = #{bo.supplierId,jdbcType=BIGINT}
            </if>
            <if test="bo.supplierDeptId != null">
                and o.supplier_dept_id = #{bo.supplierDeptId,jdbcType=BIGINT}
            </if>
            <if test="bo.cityWhId != null">
                and o.city_wh_id = #{bo.cityWhId,jdbcType=BIGINT}
            </if>
            <if test="bo.regionWhId != null">
                and o.region_wh_id = #{bo.regionWhId,jdbcType=BIGINT}
            </if>
            <if test="bo.lossStatus != null">
                and o.loss_status = #{bo.lossStatus,jdbcType=VARCHAR}
            </if>
            <if test="bo.orderCode != null and bo.orderCode != ''">
                and o.order_no like concat('%', #{bo.orderCode,jdbcType=VARCHAR}, '%')
            </if>
            <if test="bo.spuName != null and bo.spuName != ''">
                and o.spu_name like concat('%', #{bo.spuName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="bo.createTimeStart != null">
                and o.create_time &gt;= #{bo.createTimeStart,jdbcType=TIMESTAMP}
            </if>
            <if test="bo.createTimeEnd != null">
                and o.create_time &lt;= #{bo.createTimeEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="bo.updateTimeStart != null">
                and o.update_time &gt;= #{bo.updateTimeStart,jdbcType=TIMESTAMP}
            </if>
            <if test="bo.updateTimeEnd != null">
                and o.update_time &lt;= #{bo.updateTimeEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
                and o.sale_date between '${bo.saleDateStart}' and '${bo.saleDateEnd}'
            </if>
            <if test="bo.saleTimeStart != null">
                and o.sale_date &gt;= #{bo.saleTimeStart}
            </if>
            <if test="bo.placeIdList != null and bo.placeIdList.size() > 0">
                and o.place_id in
                <foreach collection="bo.placeIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and
                <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
                    (
                </if>
                o.place_id_level2 = 0
            </if>
            <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
                <if test="bo.placeIdList != null and bo.placeIdList.size() > 0">
                    or
                </if>
                <if test="bo.placeIdList == null">
                    and
                </if>
                o.place_id_level2 in
                <foreach collection="bo.placeIdLevel2List" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <if test="bo.placeIdList != null and bo.placeIdList.size() > 0">
                    )
                </if>
            </if>
            <if test="bo.customerIdList != null and bo.customerIdList.size() != 0">
                and o.customer_id in
                <foreach collection="bo.customerIdList" item="item" open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
            <if test="bo.supplierIdList != null and bo.supplierIdList.size() != 0">
                and o.supplier_id in
                <foreach collection="bo.supplierIdList" item="item" open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
            <if test="bo.cityWhIdList != null and bo.cityWhIdList.size() != 0">
                and o.city_wh_id in
                <foreach collection="bo.cityWhIdList" item="item" open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
            and o.del_flag = 0
        </where>
        order by id desc
    </select>

    <select id="numStatistics" resultType="cn.xianlink.order.domain.vo.report.ReportNumStatisticsVo">
        select o.loss_status as statusCode,
               count(o.id)   as nums
        from report_loss_order o
        <where>
            <if test="customerId != null">
                o.customer_id = #{customerId,jdbcType=BIGINT}
            </if>
            <if test="supplierId != null">
                o.supplier_id = #{supplierId,jdbcType=BIGINT}
            </if>
            <if test="cityWhId != null">
                o.city_wh_id = #{cityWhId,jdbcType=BIGINT}
            </if>
            <if test="supplierDeptId != null">
                and o.supplier_dept_id = #{supplierDeptId,jdbcType=BIGINT}
            </if>
            <if test="regionWhId != null">
                o.region_wh_id = #{regionWhId,jdbcType=BIGINT}
            </if>
            and o.loss_status in (0
              , 3
              , 4)
            and o.del_flag = 0
            group by loss_status
        </where>
    </select>


    <select id="refundPage" resultType="cn.xianlink.order.domain.vo.report.ReportLossPcListVo">
        select id,
               order_item_id,
               report_loss_no,
               supplier_dept_id,
               order_no as orderCode,
               loss_status,
               region_wh_name,
               city_wh_name,
               customer_id,
               supplier_id,
               sale_date,
               spu_name,
               refund_goods_actual_amount,
               refund_subsidy_free_amount,
               refund_time,
               statement_status,
               statement_no,
               split_bill_no,
               update_time,
               remark
        from report_loss_order
        where
        loss_status in
        <foreach collection="bo.lossStatus" item="item" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        and refund_time is not null
        <if test="bo.reportLossNo != null and bo.reportLossNo != ''">
            and report_loss_no like concat('%', #{bo.reportLossNo,jdbcType=VARCHAR}, '%')
        </if>
        <if test="bo.supplierDeptId != null">
            and supplier_dept_id = #{bo.supplierDeptId,jdbcType=BIGINT}
        </if>
        <if test="bo.orderCode != null and bo.orderCode != ''">
            and order_no like concat('%', #{bo.orderCode,jdbcType=VARCHAR}, '%')
        </if>
        <if test="bo.customerId != null">
            and customer_id = #{bo.customerId,jdbcType=BIGINT}
        </if>
        <if test="bo.supplierId != null">
            and supplier_id = #{bo.supplierId,jdbcType=BIGINT}
        </if>
        <if test="bo.cityWhId != null">
            and city_wh_id = #{bo.cityWhId,jdbcType=BIGINT}
        </if>
        <if test="bo.regionWhId != null">
            and region_wh_id = #{bo.regionWhId,jdbcType=BIGINT}
        </if>
        <if test="bo.saleTimeStart != null">
            and sale_date >= #{bo.saleTimeStart,jdbcType=TIMESTAMP}
        </if>
        <if test="bo.saleTimeEnd != null">
            and sale_date &lt;= #{bo.saleTimeEnd,jdbcType=TIMESTAMP}
        </if>
        <if test="bo.spuName != null and bo.spuName != ''">
            and spu_name like concat('%', #{bo.spuName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="bo.refundTimeStart != null">
            and refund_time >= #{bo.refundTimeStart,jdbcType=TIMESTAMP}
        </if>
        <if test="bo.refundTimeEnd != null">
            and refund_time &lt;= #{bo.refundTimeEnd,jdbcType=TIMESTAMP}
        </if>
        <if test="bo.updateTimeStart != null">
            and update_time >= #{bo.updateTimeStart,jdbcType=TIMESTAMP}
        </if>
        <if test="bo.updateTimeEnd != null">
            and update_time &lt;= #{bo.updateTimeEnd,jdbcType=TIMESTAMP}
        </if>
        <if test="bo.statementStatus != null">
            and statement_status = #{bo.statementStatus,jdbcType=INTEGER}
        </if>
        <if test="bo.statementNo != null and bo.statementNo != ''">
            and statement_no like concat('%', #{bo.statementNo,jdbcType=VARCHAR}, '%')
        </if>
        <if test="bo.splitBillNo != null and bo.splitBillNo != ''">
            and split_bill_no like concat('%', #{bo.splitBillNo,jdbcType=VARCHAR}, '%')
        </if>
        and del_flag = 0
        order by id desc
    </select>

    <select id="selectByItemId" resultType="cn.xianlink.order.domain.ReportLossOrder">
        select *
        from report_loss_order
        where order_item_id = #{itemId,jdbcType=BIGINT}
          and del_flag = 0
    </select>

    <update id="updateStatement">
        update report_loss_order
        set statement_no  = #{statementNo,jdbcType=VARCHAR},
            split_bill_no = #{splitBillNo,jdbcType=VARCHAR},
            statement_status = 2
        where order_item_id = #{itemId,jdbcType=BIGINT}
          and (loss_status = 1 or loss_status = 5)
          and del_flag = 0
    </update>

    <update id="deleteById">
        update report_loss_order
        set del_flag    = 1,
            loss_status = -1
        where id = #{id,jdbcType=BIGINT}
          and del_flag = 0
    </update>

    <select id="queryLossAmount" resultType="cn.xianlink.order.domain.ReportLossOrder">
        select loss_amount, loss_subsidy_free_amount, spu_id, refund_goods_amount, refund_goods_actual_amount,refund_subsidy_free_amount
        from report_loss_order
        where customer_id = #{customerId,jdbcType=BIGINT}
          and order_id in
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId,jdbcType=BIGINT}
        </foreach>
        and del_flag = 0
    </select>

    <select id="getByOrderItemId" resultType="cn.xianlink.order.domain.vo.report.ReportLossItemInfoVo">
        select a.order_item_id, a.loss_amount, a.refund_goods_amount,
        a.refund_goods_actual_amount, b.responsibility_type, b.refund_goods_amount as lossFlowRefundAmount
        from report_loss_order a left join report_loss_order_flow b on
        a.id = b.report_loss_id
        where a.order_item_id = #{orderItemId,jdbcType=BIGINT}
        and a.del_flag = 0
    </select>


    <select id="selectByAccTrans" resultType="cn.xianlink.order.domain.ReportLossOrder">
        select o.id,
               o.order_item_id,
               o.supplier_dept_id,
               o.order_id,
               o.customer_id,
               o.flow_id,
               o.loss_type,
               o.order_no,
               o.spu_name,
               o.sku_id,
               o.supplier_sku_id,
               o.sku_img_url,
               o.spu_net_weight,
               o.sku_price,
               o.loss_amount,
               o.loss_subsidy_free_amount,
               o.loss_weight,
               o.report_loss_no,
               o.customer_id,
               o.loss_status,
               o.spu_gross_weight,
               o.refund_goods_amount,
               o.refund_goods_actual_amount,
               o.refund_subsidy_free_amount,
               o.sale_date,
               o.create_time,
               f.refund_goods_amount as sku_amount
        from report_loss_order o
        left join report_loss_order_flow f on o.flow_id = f.id and f.loss_status = 5
        <where>
            <if test="nos != null and nos.size > 0">
                report_loss_no in
                <foreach collection="nos" item="item" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="supplierDeptId != null">
                and o.supplier_dept_id = #{supplierDeptId,jdbcType=BIGINT}
            </if>
            and supplier_id = #{supplierId,jdbcType=BIGINT}
        </where>
        order by id desc
    </select>

    <select id="selectByNo" resultType="cn.xianlink.order.domain.ReportLossOrder">
        select * from report_loss_order where report_loss_no = #{reportLossNo,jdbcType=VARCHAR}
    </select>
    <select id="getLossInfoByOrderCode" resultType="cn.xianlink.order.domain.vo.order.AdminOrderInfoVo">
        SELECT
            coalesce(sum(IF(rlof.loss_status = 5 and rlof.responsibility_type = 2 ,rlof.refund_goods_amount,0)) ,0)as cityLossAmount,
            coalesce(sum(IF(rlof.loss_status = 5 and rlof.responsibility_type = 3 ,rlof.refund_goods_amount,0)),0) as regionLossAmount,
            coalesce(sum(IF(rlof.loss_status != 5 or (rlof.loss_status = 5 and rlof.responsibility_type = 1 ) ,rlof.refund_goods_amount,0)) ,0)as supLossAmount
        FROM  order_item oi
                  LEFT JOIN report_loss_order rlo on rlo.order_item_id = oi.id
                  LEFT JOIN report_loss_order_flow rlof on rlof.report_loss_id = rlo.id
        WHERE oi.order_code = #{orderCode} and rlof.loss_status in (1,2,3,4,5,6)
    </select>
    <select id="applyLossAmount" resultType="java.math.BigDecimal" >
        SELECT
            coalesce(SUM(rlo.loss_amount),0) as applyLossAmount
        FROM  order_item oi
        LEFT JOIN report_loss_order rlo on rlo.order_item_id = oi.id
        WHERE oi.order_code = #{orderCode} and rlo.loss_status in (1,2,3,4,5,6)
    </select>
</mapper>
