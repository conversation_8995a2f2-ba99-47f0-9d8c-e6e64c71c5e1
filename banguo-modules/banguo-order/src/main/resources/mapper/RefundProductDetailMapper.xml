<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.RefundProductDetailMapper">

    <select id="supRefundPage" resultType="cn.xianlink.order.domain.vo.refundRecord.SupRefundPageVO">
        SELECT
            r.supplier_sku_id,
            r.spu_name,
            r.supplier_dept_id,
            r.spu_gross_weight,
            r.settle_status,
            r.spu_id,
            sum(r.stockout_count) as allRefundCount,
            sum(r.refund_product_amount)+sum(r.refund_subsidy_free_amount) as refundAmount,
            count(r.order_item_id) as refundOrderCount,
            r.spu_net_weight,
            r.refund_time,
            r.sale_date
        FROM
            refund_product_detail r
        WHERE
            r.del_flag = 0
        and refund_type in (1,2,3)
        <if test="bo.supplierId != null">
            and r.supplier_id = #{bo.supplierId}
        </if>
        <if test="bo.supplierDeptId != null">
            and r.supplier_dept_id = #{bo.supplierDeptId}
        </if>
        <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
            and r.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
        </if>
        <if test="bo.settleStatus != null">
            and r.settle_status = #{bo.settleStatus}
        </if>
        GROUP BY r.supplier_sku_id,r.sale_date
        ORDER BY r.refund_time DESC, r.id
    </select>

    <update id="updateStatement">
        update refund_product_detail set supplier_settle_code = #{statementNo,jdbcType=VARCHAR}, sub_refund_code = #{splitBillNo,jdbcType=VARCHAR}, settle_status = 1
        where
        order_item_id = #{itemId,jdbcType=BIGINT}
        and supplier_settle_code = ''
        and refund_status = 1
        and del_flag = 0
    </update>

    <select id="skuRefundPage" resultType="cn.xianlink.order.domain.vo.pay.SupPaymentMiniListVo">
        select supplier_id      as supplierId,
               supplier_sku_id  as skuId,
               spu_name         as spuName,
               count(1)         as orderNumber,
               supplier_dept_id as supplierDeptId,
               sum(stockout_count) as skuNumber,
               sum(refund_product_amount) as skuAmount,
               sale_date        as saleDate
        from refund_product_detail
        where supplier_id = #{bo.supplierId,jdbcType=BIGINT}
          and refund_type in (1,2,3)
          and supplier_settle_code = ''
        <if test="bo.spuName != null and bo.spuName != ''">
            and spu_name LIKE CONCAT('%', #{bo.spuName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="bo.supplierDeptId != null">
            and supplier_dept_id = #{bo.supplierDeptId,jdbcType=BIGINT}
        </if>
        <if test="bo.timeStart != null">
            and create_time >= #{bo.timeStart,jdbcType=TIMESTAMP}
        </if>
        <if test="bo.timeEnd != null">
            and create_time &lt;= #{bo.timeStart,jdbcType=TIMESTAMP}
        </if>
        group by supplier_sku_id,sale_date
        ORDER BY create_time DESC, id;
    </select>

    <select id="skuRefundList" resultType="cn.xianlink.order.domain.vo.order.SupPaymentOrderMiniListVo">
        select record.create_time as orderCreateTime,
        record.order_code as orderCode,
        record.order_id as orderId,
        detail.supplier_dept_id,
        detail.order_price as skuPrice,sum(detail.stockout_count) as skuNumber,sum(detail.refund_product_amount) as skuAmount
        from refund_product_detail as detail
                 inner join refund_record as record on detail.refund_record_id = record.id
        where detail.supplier_id = #{bo.supplierId,jdbcType=BIGINT}
          and detail.supplier_sku_id = #{bo.skuId,jdbcType=BIGINT}
        <if test="bo.supplierDeptId != null">
            and detail.supplier_dept_id = #{bo.supplierDeptId,jdbcType=BIGINT}
        </if>
          and detail.refund_type in (1,2,3)
        group by record.id
    </select>

    <select id="getSettlementRefundInfo" resultType="cn.xianlink.order.domain.RefundProductDetail">
       select a.* from
        refund_product_detail a left join refund_record b on a.refund_record_id = b.id
        <where>
            a.del_flag = 0
            <if test="saleDate != null">
                and a.sale_date = #{saleDate}
            </if>
            <if test="cityWhId != null">
                and b.city_wh_id = #{cityWhId}
            </if>
        </where>
    </select>
    <select id="queryRefund" resultType="cn.xianlink.order.api.vo.RemoteSupTransRefundPriceVo">
        select
        a.supplier_sku_id  as id,
        a.supplier_dept_id,
        a.spu_net_weight,
        count(1)         as orderCount,
        sum(a.stockout_count) as saleCount,
        sum(a.refund_product_amount)+sum(a.refund_subsidy_free_amount) as saleAmount, a.img_url, a.sale_date, a.order_price,
        group_concat(distinct b.order_id separator ';') as orderIdList
        from refund_product_detail a left join refund_record b on a.refund_record_id = b.id
        <where>
            <if test="bo.supplierSkuId != null">
                a.supplier_sku_id = #{bo.supplierSkuId}
            </if>
            <if test="bo.refundRecordIdList != null and bo.refundRecordIdList.size() > 0">
                and a.refund_record_id in
                <foreach collection="bo.refundRecordIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by a.supplier_sku_id,a.order_price
    </select>
    <select id="queryProductRefundRecord" resultType="cn.xianlink.order.api.vo.RemoteSupTransRefundRecordVo">
        select
        rr.order_code,
        rr.code as refundNo,
        rr.order_id,
        rpd.settle_price as price,
        rpd.order_price,
        rpd.supplier_sku_id,
        rpd.refund_record_id,
        rpd.stockout_count as count,
        rpd.supplier_dept_id,
        rpd.refund_product_amount + rpd.refund_subsidy_free_amount as productAmount,
        rpd.create_time
        from refund_product_detail rpd left join refund_record rr on rpd.refund_record_id = rr.id
        <where>
            <if test="bo.trans != null and bo.trans.size() > 0">
                    (rpd.refund_record_id, rpd.supplier_sku_id) in
                    <foreach collection="bo.trans" item="item" open="(" separator="," close=")">
                        ( #{item.transId}, #{item.skuId})</foreach>
            </if>
        </where>
    </select>

    <select id="selectByRefundId" resultType="cn.xianlink.order.domain.RefundProductDetail">
        select * from refund_product_detail where refund_record_id = #{refundId} and del_flag = 0
    </select>

    <select id="getIdBySourceCodeAndItem" resultType="java.lang.Long">
        select rpd.id
        from banguo_order.refund_record rr inner join banguo_order.refund_product_detail rpd on rr.id = rpd.refund_record_id
        where rr.source_code = #{sourceCode,jdbcType=VARCHAR} and rpd.order_item_id = #{itemId,jdbcType=BIGINT} order by id desc limit 1
    </select>

    <select id="selectListByDifRefundType" resultType="cn.xianlink.order.domain.vo.refundRecord.RefundRecordDetailVO">
        select rpd.order_item_id ,rr.dif_refund_type
        from refund_product_detail rpd
        join  refund_record rr   on rr.id = rpd.refund_record_id
        where
        rpd.order_item_id in
        <foreach collection="itemIds"  item="itemId" separator="," close=")" open="(">
                #{itemId}
            </foreach>
        and rpd.refund_type = #{refundType}
    </select>
    <select id="queryRefundBillList" resultType="cn.xianlink.order.domain.dto.trans.TransRefundItemDTO">
        select rpd.refund_product_amount as refundProductAmount,
        rpd.order_item_id         as orderItemId,
        rpd.stockout_count        as stockoutCount,
        rpd.refund_record_id      as refundId,
        oi.sku_id                 as skuId,
        oi.supplier_sku_id        as supplierSkuId,
        oi.price,
        oi.sale_date,
        oi.buyer_name,
        oi.pay_time,
        rr.source_code            as sourceCode
        from refund_product_detail rpd
        inner join refund_record rr on rpd.refund_record_id = rr.id
        inner join order_item oi on rpd.order_item_id = oi.id
        where rpd.refund_record_id IN
             <foreach collection="refundIds" item="i" open="(" separator="," close=")">
                 #{i,jdbcType=BIGINT}
             </foreach>
          and oi.supplier_id = #{supplierId,jdbcType=BIGINT}
          and oi.supplier_sku_id = #{supplierSkuId,jdbcType=BIGINT}
         <if test="supplierDeptId != null">
             and oi.supplier_dept_id = #{supplierDeptId,jdbcType=BIGINT}
         </if>
    </select>
</mapper>
