<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.RefundRecordMapper">

    <resultMap id="difPageMap" type="cn.xianlink.order.domain.vo.refundRecord.DifferenceRefundVO">
        <id property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="customerId" column="customer_id"/>
        <result property="orderCode" column="order_code"/>
        <result property="refundStatus" column="orderRefundStatus"/>
    </resultMap>

    <resultMap id="refundPageMap" type="cn.xianlink.order.domain.vo.refundRecord.RefundRecordVO">
        <id property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="orderId" column="order_id"/>
        <result property="orderCode" column="order_code"/>
        <result property="refundStatus" column="orderRefundStatus"/>
        <collection property="amountDetails" ofType="cn.xianlink.order.domain.vo.refundRecord.RefundAmountVO">
            <id property="detailId" column="detailId"/>
            <result property="supplierSkuId" column="supplier_sku_id"/>
            <result property="spuName" column="spu_name"/>
            <result property="refundType" column="refund_type"/>
            <result property="refundCount" column="stockout_count"/>
            <result property="refundAmount" column="refund_amount"/>
            <result property="refundStatus" column="refund_status"/>
        </collection>
    </resultMap>

    <select id="difPage" resultMap="difPageMap">
        select
        r.id, r.order_id, r.order_code, r.refund_status as orderRefundStatus, r.customer_id, r.dif_refund_type
        from
        refund_record r
        left join refund_product_detail rpd on r.id = rpd.refund_record_id and rpd.del_flag = 0
        <where>
            r.del_flag = 0
            <if test="bo.refundType != null">
                and r.refund_type = #{bo.refundType}
            </if>
            <if test="bo.refundStatus != null">
                and r.refund_status = #{bo.refundStatus}
            </if>
            <if test="bo.code != null and bo.code != ''">
                and r.code = #{bo.code}
            </if>
            <if test="bo.supplierId != null">
                and rpd.supplier_id = #{bo.supplierId}
            </if>
            <if test="bo.supplierDeptId != null">
                and rpd.supplier_dept_id = #{bo.supplierDeptId}
            </if>
            <if test="bo.spuName != null">
                and rpd.spu_name like concat('%', #{bo.spuName},'%')
            </if>
            <if test="bo.customerId != null">
                and r.customer_id = #{bo.customerId}
            </if>
            <if test="bo.regionWhId != null">
                and r.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.cityWhId != null">
                and r.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.orderCode != null and bo.orderCode != ''">
                and r.order_code = #{bo.orderCode}
            </if>
            <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
                and rpd.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
            </if>
            <if test="bo.createTimeStart != null and bo.createTimeEnd != null">
                and r.create_time between #{bo.createTimeStart} and #{bo.createTimeEnd}
            </if>
            <if test="bo.updateTimeStart != null and bo.updateTimeEnd != null">
                and r.update_time between #{bo.updateTimeStart} and #{bo.updateTimeEnd}
            </if>
            <if test="bo.payTimeStart != null and bo.payTimeEnd != null">
                and r.pay_time between #{bo.payTimeStart} and #{bo.payTimeEnd}
            </if>
            <if test="bo.refundTimeStart != null and bo.refundTimeEnd != null">
                and rpd.refund_time between #{bo.refundTimeStart} and #{bo.refundTimeEnd}
            </if>
            <if test="bo.settleStatus != null">
                and rpd.settle_status = #{bo.settleStatus}
            </if>
            <if test="bo.subRefundCode != null and bo.subRefundCode != ''">
                and rpd.sub_refund_code = #{bo.subRefundCode}
            </if>
            <if test="bo.supplierSettleCode != null and bo.supplierSettleCode != ''">
                and rpd.supplier_settle_code = #{bo.supplierSettleCode}
            </if>
            <if test="bo.difRefundType != null">
                and r.dif_refund_type = #{bo.difRefundType}
            </if>
            <if test="bo.placeIdList != null and bo.placeIdList.size() > 0">
                and r.place_id in
                <foreach collection="bo.placeIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and
                <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
                    (
                </if>
                r.place_id_level2 = 0
            </if>
            <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
                <if test="bo.placeIdList != null and bo.placeIdList.size() > 0">
                    or
                </if>
                <if test="bo.placeIdList == null">
                    and
                </if>
                r.place_id_level2 in
                <foreach collection="bo.placeIdLevel2List" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <if test="bo.placeIdList != null and bo.placeIdList.size() > 0">
                    )
                </if>
            </if>
        </where>
        order by rpd.refund_time desc, r.update_time desc, rpd.id
    </select>

    <select id="refundPage" resultMap="refundPageMap">
        select
        r.id, r.code, r.order_id, r.order_code, r.refund_status as orderRefundStatus, rpd.id as detailId, rpd.supplier_sku_id, rpd.spu_name, rpd.refund_type,
        rpd.stockout_count, rpd.refund_amount, rpd.refund_status
        from
        refund_record r
        left join refund_product_detail rpd on r.id = rpd.refund_record_id and rpd.del_flag = 0
        <where>
            r.del_flag = 0
            and r.refund_type in (2,3)
            and r.refund_amount > 0
            <if test="bo.refundType != null">
                and r.refund_type = #{bo.refundType}
            </if>
            <if test="bo.refundStatus != null">
                and r.refund_status = #{bo.refundStatus}
            </if>
            <if test="bo.code != null and bo.code != ''">
                and r.code = #{bo.code}
            </if>
            <if test="bo.supplierId != null">
                and rpd.supplier_id = #{bo.supplierId}
            </if>
            <if test="bo.customerId != null">
                and r.customer_id = #{bo.customerId}
            </if>
            <if test="bo.regionWhId != null">
                and r.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.cityWhId != null">
                and r.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.orderCode != null and bo.orderCode != ''">
                and r.order_code = #{bo.orderCode}
            </if>
            <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
                and rpd.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
            </if>
            <if test="bo.createTimeStart != null and bo.createTimeEnd != null">
                and r.create_time between #{bo.createTimeStart} and #{bo.createTimeEnd}
            </if>
            <if test="bo.updateTimeStart != null and bo.updateTimeEnd != null">
                and r.update_time between #{bo.updateTimeStart} and #{bo.updateTimeEnd}
            </if>
            <if test="bo.payTimeStart != null and bo.payTimeEnd != null">
                and r.pay_time between #{bo.payTimeStart} and #{bo.payTimeEnd}
            </if>
            <if test="bo.refundTimeStart != null and bo.refundTimeEnd != null">
                and rpd.refund_time between #{bo.refundTimeStart} and #{bo.refundTimeEnd}
            </if>
            <if test="bo.settleStatus != null">
                and rpd.settle_status = #{bo.settleStatus}
            </if>
            <if test="bo.subRefundCode != null and bo.subRefundCode != ''">
                and rpd.sub_refund_code = #{bo.subRefundCode}
            </if>
            <if test="bo.supplierSettleCode != null and bo.supplierSettleCode != ''">
                and rpd.supplier_settle_code = #{bo.supplierSettleCode}
            </if>
            <if test="bo.spuName != null and bo.spuName != ''">
                and rpd.spu_name = #{bo.spuName}
            </if>
        </where>
        order by r.create_time desc, r.id
    </select>

    <select id="adminRefundPage" resultType="cn.xianlink.order.domain.vo.refundRecord.RefundRecordPageVO">
        select
        r.id as refundRecordId, r.code, r.region_wh_id, r.city_wh_id, r.customer_id, rpd.sale_date as saleDay, r.order_code, rpd.order_item_id,
        rpd.spu_id, rpd.spu_name, rpd.stockout_count, rpd.spu_gross_weight, rpd.spu_net_weight, rpd.order_count, rpd.order_price,
        rpd.settle_price, rpd.refund_amount, rpd.refund_subsidy_free_amount, rpd.refund_price_type, rpd.refund_type, rpd.refund_remark, rpd.create_time, rpd.update_time,
        rpd.settle_status, rpd.sub_refund_code, rpd.supplier_settle_code as settleNo, rpd.refund_time, rpd.supplier_id
        from refund_product_detail rpd
        left join refund_record r on rpd.refund_record_id = r.id and r.del_flag = 0
        <where>
            rpd.del_flag = 0
            and r.refund_type in (1,2,3)
            <if test="bo.code != null and bo.code != ''">
                and r.code like concat('%', #{bo.code},'%')
            </if>
            <if test="bo.spuName != null and bo.spuName != ''">
                and rpd.spu_name like concat('%', #{bo.spuName},'%')
            </if>
            <if test="bo.supplierId != null">
                and rpd.supplier_id = #{bo.supplierId}
            </if>
            <if test="bo.regionWhId != null">
                and r.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.cityWhId != null">
                and r.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.logisticsId != null">
                and rpd.logistics_id = #{bo.logisticsId}
            </if>
            <if test="bo.orderCode != null and bo.orderCode != ''">
                and r.order_code = #{bo.orderCode}
            </if>
            <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
                and rpd.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
            </if>
            <if test="bo.createTimeStart != null and bo.createTimeEnd != null">
                and rpd.create_time between #{bo.createTimeStart} and #{bo.createTimeEnd}
            </if>
            <if test="bo.updateTimeStart != null and bo.updateTimeEnd != null">
                and rpd.update_time between #{bo.updateTimeStart} and #{bo.updateTimeEnd}
            </if>
            <if test="bo.payTimeStart != null and bo.payTimeEnd != null">
                and r.pay_time between #{bo.payTimeStart} and #{bo.payTimeEnd}
            </if>
            <if test="bo.refundTimeStart != null and bo.refundTimeEnd != null">
                and rpd.refund_time between #{bo.refundTimeStart} and #{bo.refundTimeEnd}
            </if>
            <if test="bo.settleStatus != null">
                and rpd.settle_status = #{bo.settleStatus}
            </if>
            <if test="bo.refundStatus != null">
                and rpd.refund_status = #{bo.refundStatus}
            </if>
            <if test="bo.subRefundCode != null and bo.subRefundCode != ''">
                and rpd.sub_refund_code like concat('%', #{bo.subRefundCode},'%')
            </if>
            <if test="bo.supplierSettleCode != null and bo.supplierSettleCode != ''">
                and rpd.supplier_settle_code like concat('%', #{bo.supplierSettleCode},'%')
            </if>
            <if test="bo.refundType != null">
                and r.refund_type = #{bo.refundType}
            </if>
        </where>
        order by r.update_time desc, r.id
    </select>
    <select id="difPageAdmin" resultType="cn.xianlink.order.domain.vo.refundRecord.DifferenceRefundDetailVO">
        select
        r.id as refundRecordId, r.code, r.order_code, rpd.order_item_id, rpd.spu_id, rpd.spu_name, rpd.spu_gross_weight, rpd.spu_net_weight,
        rpd.stockout_count as actualCount, rpd.order_price, rpd.settle_price, rpd.refund_product_amount, rpd.refund_service_amount, rpd.refund_amount,
        r.pay_time, rpd.refund_status, rpd.img_url, rpd.logistics_id, rpd.logistics_name, r.region_wh_id, r.city_wh_id, r.customer_id,
        rpd.create_time, rpd.update_time, rpd.refund_time, rpd.order_count, rpd.supplier_id, r.dif_refund_type
        from refund_product_detail rpd
        left join refund_record r on rpd.refund_record_id = r.id and r.del_flag = 0
        <where>
            rpd.del_flag = 0
            <if test="bo.code != null and bo.code != ''">
                and r.code like concat('%', #{bo.code},'%')
            </if>
            <if test="bo.spuName != null and bo.spuName != ''">
                and rpd.spu_name like concat('%', #{bo.spuName},'%')
            </if>
            <if test="bo.supplierId != null">
                and rpd.supplier_id = #{bo.supplierId}
            </if>
            <if test="bo.regionWhId != null">
                and r.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.cityWhId != null">
                and r.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.logisticsId != null">
                and rpd.logistics_id = #{bo.logisticsId}
            </if>
            <if test="bo.orderCode != null and bo.orderCode != ''">
                and r.order_code = #{bo.orderCode}
            </if>
            <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
                and rpd.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
            </if>
            <if test="bo.createTimeStart != null and bo.createTimeEnd != null">
                and rpd.create_time between #{bo.createTimeStart} and #{bo.createTimeEnd}
            </if>
            <if test="bo.updateTimeStart != null and bo.updateTimeEnd != null">
                and rpd.update_time between #{bo.updateTimeStart} and #{bo.updateTimeEnd}
            </if>
            <if test="bo.payTimeStart != null and bo.payTimeEnd != null">
                and r.pay_time between #{bo.payTimeStart} and #{bo.payTimeEnd}
            </if>
            <if test="bo.refundTimeStart != null and bo.refundTimeEnd != null">
                and rpd.refund_time between #{bo.refundTimeStart} and #{bo.refundTimeEnd}
            </if>
            <if test="bo.settleStatus != null">
                and rpd.settle_status = #{bo.settleStatus}
            </if>
            <if test="bo.refundStatus != null">
                and r.refund_status = #{bo.refundStatus}
            </if>
            <if test="bo.subRefundCode != null and bo.subRefundCode != ''">
                and rpd.sub_refund_code like concat('%', #{bo.subRefundCode},'%')
            </if>
            <if test="bo.supplierSettleCode != null and bo.supplierSettleCode != ''">
                and rpd.supplier_settle_code like concat('%', #{bo.supplierSettleCode},'%')
            </if>
            <if test="bo.refundType != null">
                and r.refund_type = #{bo.refundType}
            </if>
            <if test="bo.difRefundType != null">
                and r.dif_refund_type = #{bo.difRefundType}
            </if>
        </where>
        order by r.update_time desc
    </select>
    <select id="adminDifPage" resultType="cn.xianlink.order.domain.vo.refundRecord.DifferenceRefundPageVO">
        select
        r.id as refundRecordId, r.code, r.region_wh_id, r.city_wh_id, r.customer_id, rpd.sale_date as saleDay, r.order_code, rpd.order_item_id,
        rpd.spu_id, rpd.spu_name, rpd.stockout_count, rpd.spu_gross_weight, rpd.spu_net_weight, rpd.order_count, rpd.order_price,
        rpd.settle_price, rpd.refund_amount, rpd.refund_price_type, rpd.refund_type, rpd.refund_remark, rpd.create_time, rpd.update_time,
        rpd.settle_status, rpd.sub_refund_code, rpd.supplier_settle_code as settleNo, rpd.refund_time, rpd.supplier_id
        from refund_product_detail rpd
        left join refund_record r on rpd.refund_record_id = r.id and r.del_flag = 0
        <where>
            rpd.del_flag = 0
            <if test="bo.code != null and bo.code != ''">
                and r.code like concat('%', #{bo.code},'%')
            </if>
            <if test="bo.spuName != null and bo.spuName != ''">
                and rpd.spu_name like concat('%', #{bo.spuName},'%')
            </if>
            <if test="bo.supplierId != null">
                and rpd.supplier_id = #{bo.supplierId}
            </if>
            <if test="bo.regionWhId != null">
                and r.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.cityWhId != null">
                and r.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.logisticsId != null">
                and rpd.logistics_id = #{bo.logisticsId}
            </if>
            <if test="bo.orderCode != null and bo.orderCode != ''">
                and r.order_code = #{bo.orderCode}
            </if>
            <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
                and rpd.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
            </if>
            <if test="bo.createTimeStart != null and bo.createTimeEnd != null">
                and rpd.create_time between #{bo.createTimeStart} and #{bo.createTimeEnd}
            </if>
            <if test="bo.updateTimeStart != null and bo.updateTimeEnd != null">
                and rpd.update_time between #{bo.updateTimeStart} and #{bo.updateTimeEnd}
            </if>
            <if test="bo.payTimeStart != null and bo.payTimeEnd != null">
                and r.pay_time between #{bo.payTimeStart} and #{bo.payTimeEnd}
            </if>
            <if test="bo.refundTimeStart != null and bo.refundTimeEnd != null">
                and rpd.refund_time between #{bo.refundTimeStart} and #{bo.refundTimeEnd}
            </if>
            <if test="bo.settleStatus != null">
                and rpd.settle_status = #{bo.settleStatus}
            </if>
            <if test="bo.refundStatus != null">
                and rpd.refund_status = #{bo.refundStatus}
            </if>
            <if test="bo.subRefundCode != null and bo.subRefundCode != ''">
                and rpd.sub_refund_code like concat('%', #{bo.subRefundCode},'%')
            </if>
            <if test="bo.supplierSettleCode != null and bo.supplierSettleCode != ''">
                and rpd.supplier_settle_code like concat('%', #{bo.supplierSettleCode},'%')
            </if>
            <if test="bo.refundType != null">
                and r.refund_type = #{bo.refundType}
            </if>
        </where>
        order by r.update_time desc, r.id
    </select>
    <select id="getRefundBySourceCode" resultType="cn.xianlink.order.domain.vo.refundRecord.RefundBySourceVO">
        select
        r.id, r.source_code, rpd.refund_time
        from
        refund_record r left join refund_product_detail rpd on r.id = rpd.refund_record_id and rpd.del_flag = 0
        where
        r.del_flag = 0
        and r.source_code = #{code}
        limit 1
    </select>
    <select id="auditPage" resultType="cn.xianlink.order.domain.vo.refundRecord.RefundPageVO">
        select
        r.id, r.code, r.order_code, r.source_code, rpd.sale_date, r.region_wh_id, r.city_wh_id, r.customer_id, r.customer_name,
        r.refund_type, r.refund_status, r.refund_product_amount, r.refund_other_amount, r.refund_amount,r.refund_subsidy_free_amount,
        sum(rpd.refund_service_amount) as refundServiceAmount,
        sum(rpd.refund_platform_freight) as refundPlatformFreight,
        sum(rpd.refund_base_freight) as refundBaseFreight,
        sum(rpd.refund_region_freight) as refundRegionFreight,
        sum(rpd.refund_freight_amount) as refundFreightAmount,
        sum(rpd.refund_financial_service_price) as refundFinancialServicePrice,
        r.pay_time, r.update_time, r.create_time, r.audit_status, rpd.refund_time, r.refund_product_free_amount
        from
        refund_record r left join refund_product_detail rpd on r.id = rpd.refund_record_id and rpd.del_flag = 0
        <where>
            r.del_flag = 0
            <if test="bo.code != null and bo.code != ''">
                and r.code like concat('%', #{bo.code},'%')
            </if>
            <if test="bo.customerId != null">
                and r.customer_id = #{bo.customerId}
            </if>
            <if test="bo.regionWhId != null">
                and r.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.cityWhId != null">
                and r.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.orderCode != null and bo.orderCode != ''">
                and r.order_code = #{bo.orderCode}
            </if>
            <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
                and rpd.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
            </if>
            <if test="bo.createTimeStart != null and bo.createTimeEnd != null">
                and r.create_time between #{bo.createTimeStart} and #{bo.createTimeEnd}
            </if>
            <if test="bo.updateTimeStart != null and bo.updateTimeEnd != null">
                and r.update_time between #{bo.updateTimeStart} and #{bo.updateTimeEnd}
            </if>
            <if test="bo.payTimeStart != null and bo.payTimeEnd != null">
                and r.pay_time between #{bo.payTimeStart} and #{bo.payTimeEnd}
            </if>
            <if test="bo.refundTimeStart != null and bo.refundTimeEnd != null">
                and rpd.refund_time between #{bo.refundTimeStart} and #{bo.refundTimeEnd}
            </if>
            <if test="bo.refundStatus != null">
                and r.refund_status = #{bo.refundStatus}
            </if>
            <if test="bo.refundType != null">
                and r.refund_type = #{bo.refundType}
            </if>
            <if test="bo.auditStatus != null">
                and r.audit_status = #{bo.auditStatus}
            </if>
            <if test="bo.spuName != null and bo.spuName != ''">
                and (rpd.spuName like concat('%', #{bo.spuName},'%') or rpd.spu_standards like concat('%', #{bo.spuName},'%') )
            </if>
        </where>
        group by r.id
        order by r.audit_status, r.update_time desc, r.id
    </select>
    <select id="queryRefundDiff" resultType="cn.xianlink.order.api.vo.RemoteSupTransRefundDiffPriceVo">
        select
        a.supplier_sku_id  as id,
        a.order_item_id,
        a.spu_net_weight,
        a.supplier_dept_id,
        a.order_price as currentPrice,
        count(1)         as orderCount,
        sum(a.stockout_count) as saleCount,
        sum(a.refund_product_amount)+sum(a.refund_subsidy_free_amount) as saleAmount, a.img_url, a.sale_date, a.order_price,
        group_concat(distinct b.order_id separator ';') as orderIdList
        from refund_product_detail a left join refund_record b on a.refund_record_id = b.id
        <where>
            <if test="bo.supplierSkuId != null">
                a.supplier_sku_id = #{bo.supplierSkuId}
            </if>
            <if test="bo.refundRecordIdList != null and bo.refundRecordIdList.size() > 0">
                and a.refund_record_id in
                <foreach collection="bo.refundRecordIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by a.supplier_sku_id,a.order_price
    </select>

    <select id="selectByExpand" resultType="cn.xianlink.order.domain.RefundRecord">
        select * from refund_record where source_code = #{sourceCode,jdbcType=VARCHAR} and expand = #{expand,jdbcType=VARCHAR} and del_flag = 0
        and refund_amount > 0 and occupy_status = 1 and refund_status = 0 limit 1
    </select>

    <select id="queryByItemId" resultType="cn.xianlink.order.domain.vo.refundRecord.RefundRecordVO">
        select r.id,r.code,r.order_id,r.order_code,r.refund_status,d.refund_product_amount,r.refund_type,
        d.refund_distribution_amount, d.refund_subsidy_free_amount,r.dif_refund_type
        from refund_record r inner join refund_product_detail d on r.id = d.refund_record_id
        where d.order_item_id = #{itemId,jdbcType=BIGINT} and d.del_flag = 0 and r.del_flag = 0;
    </select>

    <select id="getRefundInfoByOrderItemId" resultType="cn.xianlink.order.domain.order.vo.OrderItemAggregateVO" >
        select
        coalesce(sum(rpd.refund_service_amount),0) as refund_service_amount,
        coalesce(sum(rpd.refund_freight_amount),0) as refund_freight_amount,
        coalesce(sum(rpd.refund_financial_service_price),0) as refund_financial_service_price,
        (case when  rpd.refund_type = 2 then  rpd.settle_price else 0 end) as lackPrice,
        coalesce(sum(case when  rpd.refund_type = 2 then  rpd.stockout_count else 0 end),0) as lackCount,
        coalesce(sum(case when  rpd.refund_type = 2 then  rpd.refund_amount  - rpd.refund_financial_service_price else 0 end),0) as lackRefundAmount,
        (case when  rpd.refund_type = 3 then  rpd.settle_price else 0 end) as lessPrice,
        coalesce(sum(case when  rpd.refund_type = 3 then  rpd.stockout_count else 0 end),0) as lessCount,
        coalesce(sum(case when  rpd.refund_type = 3 then  rpd.refund_amount  - rpd.refund_financial_service_price else 0 end),0) as lessRefundAmount,
        (case when  rpd.refund_type = 4 then  rpd.settle_price else 0 end) as diffPrice,
        coalesce(sum(case when  rpd.refund_type = 4 then  rpd.stockout_count else 0 end),0) as diffCount,
        coalesce(sum(case when  rpd.refund_type = 4 then  rpd.refund_amount  - rpd.refund_financial_service_price else 0 end),0) as diffRefundAmount,
        coalesce(sum(case when  rpd.refund_type = 5 then  rpd.refund_amount  - rpd.refund_financial_service_price else 0 end),0) as lossRefundAmount
        from
        refund_product_detail rpd
        where rpd.del_flag = 0
        and rpd.order_item_id = #{orderItemId}
    </select>

    <select id="getBatchCodeRefund" resultType="cn.xianlink.order.api.bo.RemoteStrockBatchRefundBo" >

        select
        sum(rpd.refund_product_amount) as refundAmount,rpd.order_item_id
        from
        refund_product_detail rpd
        where rpd.del_flag = 0
          <if test="orderItemIds != null and orderItemIds.size() > 0">
            and rpd.order_item_id in
            <foreach collection="orderItemIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
          </if>
        and rpd.refund_status = 1
        and rpd.refund_type = 5
        group by rpd.order_item_id
    </select>

    <select id="lessRefundAmount" resultType="cn.xianlink.order.api.bo.RemoteStrockBatchRefundBo" >

        SELECT
            sum(rd.blame_count) as lessCount ,sum(rd.amount) as lessAmount,srd.order_item_id
        FROM
            stockout_record_detail srd
                JOIN stockout_record sr ON srd.stockout_record_id = sr.id
                JOIN blame_record_detail rd on rd.source_code = sr.`code`
        WHERE

            srd.order_item_id in
            <foreach collection="orderItemIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>

            and srd.status = 2 and srd.type = 2 and rd.blame_status = 1 and rd.responsibility_type = 1
        group by srd.order_item_id
    </select>

    <select id="sumCityWhRefundAmount" resultType="cn.xianlink.order.domain.vo.report.OrderAmountVo">
        select
            coalesce(sum(rpd.refund_amount - rpd.refund_financial_service_price),0) as refundAmount,
            coalesce(sum(rpd.refund_financial_service_price),0) as refundFinancialAmount,
            coalesce(sum(case when  rpd.refund_type = 3 or rpd.refund_type = 2 then  rpd.refund_service_amount else 0 end),0) as refundServiceAmount,
            coalesce(sum(case when  rpd.refund_type = 3 or rpd.refund_type = 2 then rpd.refund_freight_amount  else 0 end),0) as refundFreightAmount,
            coalesce(sum(case when  rpd.refund_type = 5 then  rpd.refund_amount  - rpd.refund_financial_service_price else 0 end),0) as lossAmount,
            coalesce(sum(case when  rpd.refund_type = 4 then  rpd.refund_amount  - rpd.refund_financial_service_price else 0 end),0) as refundDifferenceAmount,
            coalesce(sum(case when  rpd.refund_type = 3 or rpd.refund_type = 2 then  rpd.refund_product_amount else 0 end),0) as fewAmount
        from refund_record r join refund_product_detail rpd on r.id = rpd.refund_record_id and rpd.del_flag = 0
        <where>
            rpd.refund_type != 1 and rpd.refund_status=1
            and r.city_wh_id = #{cityWhId}
            <if test="regionWhId != null">
                and r.region_wh_id = #{regionWhId}
            </if>
            and rpd.sale_date = #{saleDate}
            <if test="customers != null and customers.size()>0">
                and r.customer_id in
                <foreach collection="customers" item="customerId" open="(" close=")" separator=",">
                    #{customerId}
                </foreach>
            </if>
            <if test="placePaths != null and placePaths.size() > 0">
                and r.place_path in
                <foreach collection="placePaths" item="placePath" open="(" close=")" separator=",">
                    #{placePath}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getRefundCustomers" resultType="long">
        select
            r.customer_id
        from refund_record r join refund_product_detail rpd on r.id = rpd.refund_record_id and rpd.del_flag = 0
        <where>
            rpd.refund_type != 1 and rpd.refund_status=1
            and r.pay_time &lt;= #{payTime}
            and r.city_wh_id = #{cityWhId}
            and rpd.sale_date = #{saleDate}
            <if test="placePaths != null and placePaths.size() > 0">
                and r.place_path in
                <foreach collection="placePaths" item="placePath" open="(" close=")" separator=",">
                    #{placePath}
                </foreach>
            </if>
        </where>
    </select>

    <sql id="supplierFilter">
        rpd.supplier_id in
        <foreach collection="suppliers" item="supplierId" open="(" close=")" separator=",">
            #{supplierId}
        </foreach>
    </sql>
    <sql id="deptFilter">
        rpd.supplier_dept_id in
        <foreach collection="supplierDepts" item="supplierDeptId" open="(" close=")" separator=",">
            #{supplierDeptId}
        </foreach>
    </sql>
    <select id="sumRegionWhRefundAmount" resultType="cn.xianlink.order.domain.vo.report.OrderAmountVo">
        select
            coalesce(sum(rpd.refund_amount - rpd.refund_financial_service_price),0) as refundAmount,
            coalesce(sum(case when  rpd.refund_type = 3 or rpd.refund_type = 2 then  rpd.refund_service_amount else 0 end),0) as refundServiceAmount,
            coalesce(sum(case when  rpd.refund_type = 3 or rpd.refund_type = 2 then rpd.refund_freight_amount  else 0 end),0) as refundFreightAmount,
            coalesce(sum(case when  rpd.refund_type = 3 or rpd.refund_type = 2 then  rpd.refund_product_amount else 0 end),0) as fewAmount,
            coalesce(sum(case when  rpd.refund_type = 5 then  rpd.refund_amount - rpd.refund_financial_service_price else 0 end),0) as lossAmount,
            coalesce(sum(case when  rpd.refund_type = 4 then  rpd.refund_amount - rpd.refund_financial_service_price else 0 end),0) as refundDifferenceAmount
        from refund_product_detail rpd join refund_record r  on r.id = rpd.refund_record_id and rpd.del_flag = 0
            <if test="buyers != null and buyers.size()>0">
                join order_item item on rpd.order_item_id = item.id
            </if>
        <where>
            rpd.refund_type != 1 and rpd.refund_status=1
            and r.region_wh_id = #{regionWhId}
            and rpd.sale_date = #{saleDate}
            <if test="buyers != null and buyers.size()>0">
                and item.buyer_code in
                <foreach collection="buyers" item="buyer" open="(" close=")" separator=",">
                    #{buyer}
                </foreach>
            </if>
            <if test="suppliers.size() > 0">
                and (
                <include refid="supplierFilter"/>
                <if test="supplierDepts.size()>0">
                    or <include refid="deptFilter"/>
                </if>
                )
            </if>
            <if test="suppliers.size() == 0 and supplierDepts.size() > 0">
                <include refid="deptFilter"/>
            </if>
        </where>
    </select>

    <select id="statSupSale" resultType="cn.xianlink.order.domain.vo.report.StatBoardVo">
        SELECT coalesce(sum(rpd.refund_amount),0) as refundAmount ,
        coalesce(sum(if(rpd.refund_type = 2,rpd.refund_amount - rpd.refund_financial_service_price,0)),0) as stockOutRefund,
        coalesce(sum(if(rpd.refund_type = 3,rpd.refund_amount - rpd.refund_financial_service_price,0)) ,0)as lessGoodsRefund ,
        coalesce(sum(if(rpd.refund_type = 4,rpd.refund_amount - rpd.refund_financial_service_price,0) ),0) as differenceRefund,
        coalesce(sum(if(rpd.refund_type = 5,rpd.refund_amount - rpd.refund_financial_service_price,0) ),0) as breakageRefund
        FROM refund_product_detail rpd
        WHERE rpd.refund_status = 1 and rpd.refund_type != 1
         and rpd.sale_date = #{bo.saleDate}
            and rpd.supplier_id = #{bo.supplierId}
            <if test="bo.supplierDeptList != null and bo.supplierDeptList.size() > 0">
               and rpd.supplier_dept_id in
                <foreach collection="bo.supplierDeptList" item="supplierDeptId" open="(" close=")" separator=",">
                    #{supplierDeptId}
                </foreach>
            </if>
    </select>
    <select id="adminOrderRefund" resultType="cn.xianlink.order.domain.vo.order.AdminOrderInfoVo">
        SELECT
            coalesce(sum(rpd.refund_amount),0) as refundAmount,
            coalesce(sum(if(rpd.refund_type = 2,rpd.refund_amount - rpd.refund_financial_service_price,0)),0) as lackAmount,
            coalesce(sum(if(rpd.refund_type = 2,rpd.stockout_count,0)),0) as lackCount,
            coalesce(sum(if(rpd.refund_type = 3,rpd.refund_amount - rpd.refund_financial_service_price,0)) ,0)as shortAmount ,
            coalesce(sum(if(rpd.refund_type = 3,rpd.stockout_count,0)) ,0)as shortCount ,
            coalesce(sum(if(rpd.refund_type = 4,rpd.refund_amount - rpd.refund_financial_service_price,0) ),0) as difAmount,
            coalesce(sum(if(rpd.refund_type = 4,rpd.stockout_count,0) ),0) as difCount,
            coalesce(sum(if(rpd.refund_type = 5,rpd.refund_amount - rpd.refund_financial_service_price,0) ),0) as lossAmount,
            coalesce(sum(rpd.refund_service_amount ),0) as refundServiceAmount,
            coalesce(sum(rpd.refund_freight_amount ),0) as refundFreightAmount,
            coalesce(sum(rpd.refund_financial_service_price ),0) as refundFinancialServicePrice
        FROM  refund_record rr
                  LEFT JOIN refund_product_detail rpd on rr.id = rpd.refund_record_id
        WHERE rr.order_code = #{orderCode}
    </select>

    <select id="selectRefundTypeToDayAmount" resultType="java.math.BigDecimal">
        select sum(refund_amount) from refund_record
        <where>
            region_wh_id = #{regionWhId}
            and refund_type = #{refundType}
            and create_time between #{startTime} and #{endTime}
            and refund_status = 1
            and del_flag = 0
        </where>
    </select>

    <select id="selectBySourceAndItemId" resultType="cn.xianlink.order.domain.RefundRecord">
        select r.* from banguo_order.refund_record r inner join banguo_order.refund_product_detail p on r.id = p.refund_record_id
        where r.source_code = #{sourceCode,jdbcType=VARCHAR} and p.order_item_id = #{itemId,jdbcType=BIGINT} and r.del_flag = 0 and p.del_flag = 0
    </select>

    <select id="selectRefundExportPage" resultType="cn.xianlink.order.domain.vo.refundRecord.RefundExportSelectDTO">
        select
        r.id, r.code, r.order_code, r.source_code, rpd.sale_date, r.region_wh_id, r.city_wh_id, r.customer_id, r.customer_name,
        r.refund_type, r.refund_status, r.refund_product_amount, r.refund_other_amount, r.refund_amount,r.refund_subsidy_free_amount,
        sum(rpd.refund_service_amount) as refundServiceAmount,
        sum(rpd.refund_platform_freight) as refundPlatformFreight,
        sum(rpd.refund_base_freight) as refundBaseFreight,
        sum(rpd.refund_region_freight) as refundRegionFreight,
        sum(rpd.refund_freight_amount) as refundFreightAmount,
        sum(rpd.refund_financial_service_price) as refundFinancialServicePrice,
        r.pay_time, r.update_time, r.create_time, r.audit_status, rpd.refund_time, rpd.supplier_id, rpd.logistics_id, rpd.logistics_name,
        rpd.order_item_id, rpd.supplier_sku_id, r.occupy_status, rpd.stockout_count
        from
        refund_record r left join refund_product_detail rpd on r.id = rpd.refund_record_id and rpd.del_flag = 0
        <where>
            r.del_flag = 0
            <if test="bo.code != null and bo.code != ''">
                and r.code like concat('%', #{bo.code},'%')
            </if>
            <if test="bo.customerId != null">
                and r.customer_id = #{bo.customerId}
            </if>
            <if test="bo.regionWhId != null">
                and r.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.cityWhId != null">
                and r.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.orderCode != null and bo.orderCode != ''">
                and r.order_code = #{bo.orderCode}
            </if>
            <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
                and rpd.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
            </if>
            <if test="bo.createTimeStart != null and bo.createTimeEnd != null">
                and r.create_time between #{bo.createTimeStart} and #{bo.createTimeEnd}
            </if>
            <if test="bo.updateTimeStart != null and bo.updateTimeEnd != null">
                and r.update_time between #{bo.updateTimeStart} and #{bo.updateTimeEnd}
            </if>
            <if test="bo.payTimeStart != null and bo.payTimeEnd != null">
                and r.pay_time between #{bo.payTimeStart} and #{bo.payTimeEnd}
            </if>
            <if test="bo.refundTimeStart != null and bo.refundTimeEnd != null">
                and rpd.refund_time between #{bo.refundTimeStart} and #{bo.refundTimeEnd}
            </if>
            <if test="bo.refundStatus != null">
                and r.refund_status = #{bo.refundStatus}
            </if>
            <if test="bo.refundType != null">
                and r.refund_type = #{bo.refundType}
            </if>
            <if test="bo.auditStatus != null">
                and r.audit_status = #{bo.auditStatus}
            </if>
        </where>
        group by r.id
        order by r.audit_status, r.update_time desc, r.id
    </select>
</mapper>