<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.StockoutRecordMapper">

    <select id="stockoutPage" resultType="cn.xianlink.order.domain.vo.stockOut.StockoutPageVO">
        select sr.*, sum(srd.refund_product_amount) as stockoutAmount
        from stockout_record sr left join stockout_record_detail srd on sr.id = srd.stockout_record_id
        <where>
            sr.del_flag = 0
            <if test="bo.cityWhId != null">
                and sr.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.regionWhId != null">
                and sr.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.supplierId != null">
                and sr.supplier_id = #{bo.supplierId}
            </if>
            <if test="bo.businessTypeList != null and bo.businessTypeList.size() > 0">
                and sr.business_type in
                <foreach collection="bo.businessTypeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="bo.logisticsId != null">
                and sr.logistics_id = #{bo.logisticsId}
            </if>
            <if test="bo.supplierSkuId != null">
                and sr.supplier_sku_id = #{bo.supplierSkuId}
            </if>
            <if test="bo.spuName != null and bo.spuName != ''">
                and sr.spu_name like concat('%', #{bo.spuName},'%')
            </if>
            <if test="bo.code != null and bo.code != ''">
                and sr.code = #{bo.code}
            </if>
            <if test="bo.type != null">
                and sr.type = #{bo.type}
            </if>
            <if test="bo.orderType != null">
                and sr.order_type = #{bo.orderType}
            </if>
            <if test="bo.blameStatus != null">
                and sr.blame_status = #{bo.blameStatus}
            </if>
            <if test="bo.refundStatus != null">
                and sr.refund_status = #{bo.refundStatus}
            </if>
            <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
                and sr.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
            </if>
            <if test="bo.saleDate != null">
                and sr.sale_date = #{bo.saleDate}
            </if>
            <if test="bo.createTimeStart != null">
                and sr.create_time >= #{bo.createTimeStart,jdbcType=TIMESTAMP}
            </if>
            <if test="bo.createTimeEnd != null">
                and #{bo.createTimeEnd,jdbcType=TIMESTAMP} >= sr.create_time
            </if>
            <if test="bo.auditStatus != null">
                and sr.audit_status = #{bo.auditStatus}
            </if>
            <if test="bo.buyerId != null">
                and sr.buyer_id = #{bo.buyerId}
            </if>
            <if test="bo.logisticsIds != null and bo.logisticsIds.size() > 0">
                and sr.logistics_id in
                <foreach collection="bo.logisticsIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and
                <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
                    (
                </if>
                sr.place_id_level2 = 0
            </if>
            <if test="bo.placeIdLevel2List != null and bo.placeIdLevel2List.size() > 0">
                <if test="bo.logisticsIds != null and bo.logisticsIds.size() > 0">
                    or
                </if>
                <if test="bo.logisticsIds == null or bo.logisticsIds.size() == 0">
                    and
                </if>
                sr.place_id_level2 in
                <foreach collection="bo.placeIdLevel2List" item="placeId" open="(" separator="," close=")">
                    #{placeId}
                </foreach>
                <if test="bo.logisticsIds != null and bo.logisticsIds.size() > 0">
                    )
                </if>
            </if>
        </where>
        group by sr.id
        order by sr.sale_date desc, sr.id
    </select>
    <select id="selectByCode" resultType="cn.xianlink.order.domain.StockoutRecord">
        select
        *
        from stockout_record
        where
        del_flag = 0
        and code = #{code}
    </select>


    <select id="getStockoutOrderCodeByCode" resultType="cn.xianlink.order.domain.vo.stockOut.StockoutDetailVO">
        select srd.order_code, sr.id, sr.city_wh_id from stockout_record sr
        left join stockout_record_detail srd on sr.id = srd.stockout_record_id
        where sr.code = #{billCode} limit 1
    </select>
    <select id="getInfoById" resultType="cn.xianlink.order.domain.vo.stockOut.StockoutDetailVO">
        select sr.*, sum(srd.refund_product_amount) as stockoutAmount,sr.place_id_level2
        from stockout_record sr left join stockout_record_detail srd on sr.id = srd.stockout_record_id and srd.del_flag = 0
        where sr.id = #{id}
    </select>
    <select id="getInfoByCode" resultType="cn.xianlink.order.domain.vo.stockOut.StockoutDetailVO">
        select sr.*, sum(srd.refund_product_amount) as stockoutAmount
        from stockout_record sr left join stockout_record_detail srd on sr.id = srd.stockout_record_id and srd.del_flag = 0
        where sr.code = #{code}
    </select>

</mapper>
