<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.StockoutSkuRecordMapper">
    <select id="getSpuSaleTime" resultType="cn.xianlink.order.domain.ReceiveGoodsRecordDetail">
        SELECT rd.supplier_sku_id , rd.after_sale_time
        FROM sort_goods_detail gd
                 LEFT JOIN receive_goods_record_detail rd on gd.order_item_id = rd.order_item_id
        WHERE gd.sort_goods_id in
        <if test="sortGoodsIds != null and sortGoodsIds.size() > 0">
            <foreach collection="sortGoodsIds" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="sortGoodsDetailIds != null and sortGoodsDetailIds.size() > 0">
            AND gd.id in
            <foreach collection="sortGoodsDetailIds" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
        GROUP BY rd.supplier_sku_id
    </select>

    <update id="updateStatus">
        UPDATE stockout_sku_record
        SET status = #{status}
        WHERE id = #{id}
    </update>
</mapper>
