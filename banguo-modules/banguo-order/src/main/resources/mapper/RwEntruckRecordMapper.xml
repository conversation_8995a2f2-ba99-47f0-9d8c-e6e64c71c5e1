<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.RwEntruckRecordMapper">
    <select id="customWaitEntruckSumList" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckRecordSumVo">
        select rer.parking_no,
             rer.logistics_id,
             rer.region_wh_id,
             rer.city_wh_id,
             rer.sale_date,
             sum(reg.entruck_quantity) as entruck_quantity,
             sum(reg.entruck_quantity * reg.spu_gross_weight) as entruck_gross_weight,
             sum(if(reg.diff_status = 31, reg.delivery_quantity-reg.entruck_quantity, 0)) as wait_entruck_quantity,
             sum(if(reg.diff_status = 31, (reg.delivery_quantity-reg.entruck_quantity)*reg.spu_gross_weight, 0)) as wait_entruck_gross_weight
        from rw_entruck_record rer
                inner join rw_entruck_goods reg on rer.id = reg.record_id and reg.del_flag = 0
        where rer.del_flag = 0
            and rer.sale_date = #{bo.saleDate}
            and rer.region_wh_id = #{bo.regionWhId}
            and rer.status in (10, 20, 30)
            and rer.is_affiliated = 0
            and rer.entruck_no = ''
        <if test="bo.logisticsId != null">
            and rer.logistics_id = #{bo.logisticsId}
        </if>
        group by rer.parking_no, rer.logistics_id, rer.region_wh_id, rer.city_wh_id
        order by rer.parking_no asc
    </select>

    <select id="completeEntruckLogisticsCount" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckRecordSumVo">
        select rer.sale_date, rer.region_wh_id, rer.logistics_id
        from rw_entruck_record rer
            inner join rw_entruck_goods reg on rer.id = reg.record_id and reg.del_flag = 0
        where rer.del_flag = 0
          and rer.sale_date = #{bo.saleDate}
          and rer.status = 30
          and rer.is_affiliated = 0
          and rer.entruck_no = ''
        <if test="bo.regionWhId != null">
            and rer.region_wh_id = #{bo.regionWhId}
        </if>
        and reg.entruck_quantity > 0
        group by rer.sale_date, rer.region_wh_id, rer.logistics_id

    </select>

    <select id="customWaitEntruckSumListByDeliveryId"
            resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckRecordSumVo">
        select rer.id,
               rer.parking_no,
               rer.logistics_id,
               rer.region_wh_id,
               rer.city_wh_id,
               rer.total_delivery_quantity,
               rer.status,
               rer.entruck_no,
               count(reg.supplier_sku_id) as total_sku_count,
               sum(reg.delivery_quantity-reg.entruck_quantity) as wait_entruck_quantity
        from rw_entruck_record rer
                 inner join rw_entruck_goods reg on rer.id = reg.record_id and reg.del_flag = 0
        where rer.del_flag = 0
          and rer.delivery_id = #{deliveryId}
          and rer.status in (20, 30)
        group by rer.id
        order by rer.parking_no asc
    </select>

    <select id="customEntruckRecordGoodsQuantityListByEntruckNo"
            resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckRecordSumVo">
        select rer.id, reg.entruck_quantity
        from rw_entruck_record rer
                 inner join rw_entruck_goods reg on rer.id = reg.record_id and reg.del_flag = 0
        where rer.del_flag = 0
          and rer.entruck_id = #{entruckId}
          and rer.entruck_no = #{entruckNo}
          and reg.supplier_sku_id = #{supplierSkuId}
          and reg.entruck_quantity > 0
        order by rer.id asc
    </select>

    <select id="customRecordIdListByBuyerCode" resultType="java.lang.Long">
        select distinct rer.id
        from rw_entruck_record rer
            inner join rw_entruck_goods reg on rer.id = reg.record_id and reg.del_flag = 0
        where rer.del_flag = 0
            and rer.sale_date = #{bo.saleDate}
            and rer.region_wh_id = #{bo.regionWhId}
        <if test="bo.buyerCode != null and bo.buyerCode != '' ">
            and reg.buyer_code = #{bo.buyerCode}
        </if>
        <if test="bo.spuName != null and bo.spuName != '' ">
            and reg.spu_name like concat('%', #{bo.spuName}, '%')
        </if>
    </select>

    <select id="waitCreateEntruckNoList" resultType="cn.xianlink.order.domain.entruck.vo.RwEntruckRecordSumVo">
        select rer.sale_date, rer.logistics_id
        from rw_entruck_record rer
                 inner join rw_entruck_goods reg on rer.id = reg.record_id and reg.del_flag = 0
        where rer.del_flag = 0
          and rer.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
          and rer.region_wh_id = #{bo.regionWhId}
          and rer.status = 30
          and rer.is_affiliated = 0
          and rer.entruck_no = ''
          and reg.entruck_quantity > 0
        group by rer.sale_date, rer.logistics_id
    </select>
</mapper>
