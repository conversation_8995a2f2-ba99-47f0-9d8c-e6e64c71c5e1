<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.SupDeliveryGoodsMapper">
    <select id="customSumListBySupplierId" resultType="cn.xianlink.order.domain.delivery.vo.SupDeliveryGoodsSumVo">
        select sdg.supplier_sku_id, sum(sdg.delivery_quantity) as delivery_quantity
        from sup_delivery sd
                 inner join sup_delivery_goods sdg on sdg.delivery_id = sd.id and sdg.del_flag = 0
        where sd.del_flag = 0
          and sd.status in (10, 20, 30)
          and sd.sale_date = #{bo.saleDate}
          and sd.region_wh_id = #{bo.regionWhId}
          and sd.supplier_id = #{bo.supplierId}
        group by sdg.supplier_sku_id
    </select>

    <select id="customSumListByDeliveryId" resultType="cn.xianlink.order.domain.delivery.vo.SupDeliveryGoodsSumVo">
        SELECT sdg.supplier_sku_id,
               sdg.actual_quantity - sum(ifnull(reg.entruck_quantity, 0)) surplus_quantity
        FROM sup_delivery_goods sdg
                 left join rw_entruck_goods reg
                           on sdg.delivery_id = reg.delivery_id and sdg.supplier_sku_id = reg.supplier_sku_id and reg.del_flag = 0
        where sdg.del_flag = 0
          and sdg.delivery_id = #{deliveryId}
          and sdg.actual_quantity > 0
        group by sdg.supplier_sku_id
    </select>

    <select id="customSumListByPickingNo" resultType="cn.xianlink.order.domain.delivery.vo.SupDeliveryGoodsSumVo">
        SELECT sdg.supplier_sku_id,
               sum(sdg.actual_quantity) - sum(reg.entruck_quantity) surplus_quantity
        FROM sup_delivery sd
                 inner join sup_delivery_goods sdg on sd.id = sdg.delivery_id and sdg.del_flag = 0
                 left join rw_entruck_goods reg
                           on sdg.delivery_id = reg.delivery_id and sdg.supplier_sku_id = reg.supplier_sku_id and reg.del_flag = 0
        where sd.del_flag = 0
          and sd.picking_no = #{pickingNo}
        group by sdg.supplier_sku_id
    </select>

    <select id="customQuantitySumBySupplierSkuId" resultType="cn.xianlink.order.domain.delivery.vo.SupDeliveryGoodsSumVo">
        select sdg.supplier_sku_id, sum(sdg.delivery_quantity) as delivery_quantity
        from sup_delivery_goods sdg
                 inner join sup_delivery sd on sdg.delivery_id = sd.id and sd.del_flag = 0
        where sdg.supplier_sku_id = #{bo.supplierSkuId}
          and sdg.del_flag = 0
          and sd.status &lt;> 90
        group by sdg.supplier_sku_id
    </select>

    <select id="deliveryIdListBySpuName" resultType="java.lang.Long">
        select distinct sd.id
        from sup_delivery sd
                 inner join sup_delivery_goods sdg on sdg.delivery_id = sd.id and sdg.del_flag = 0
        where sd.del_flag = 0
          and sd.sale_date = #{bo.saleDate}
          and sd.region_wh_id = #{bo.regionWhId}
        <if test="bo.buyerCode != null and bo.buyerCode != '' ">
            and sdg.buyer_code = #{bo.buyerCode}
        </if>
        <if test="bo.spuName != null and bo.spuName != '' ">
            and sdg.spu_name like concat('%', #{bo.spuName}, '%')
        </if>
    </select>

    <select id="waitInspectGoodsList" resultType="cn.xianlink.order.domain.todo.vo.TodoGoodsVo">
        select sd.business_type, sd.supplier_id, sd.sale_date,
            sdg.supplier_sku_id, sdg.spu_name, sdg.buyer_name, sdg.spu_gross_weight, sdg.spu_net_weight, sum(sdg.delivery_quantity) as sku_quantity
        from sup_delivery sd
            inner join sup_delivery_goods sdg on sdg.delivery_id = sd.id and sdg.del_flag = 0
        where sd.del_flag = 0
            and sd.status = 10
            and sdg.status = 10
            and sd.region_wh_id = #{bo.regionWhId}
        <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
            and sd.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
        </if>
        <if test="bo.buyerCode != null and bo.buyerCode != '' ">
            and sdg.buyer_code = #{bo.buyerCode}
        </if>
        <if test="bo.supplierId != null ">
            and sd.supplier_id = #{bo.supplierId}
        </if>
        group by sdg.supplier_sku_id
    </select>

    <update id="autoCompleteInspect" >
        update sup_delivery_goods
        set actual_quantity = delivery_quantity,
            status = 21,
            status_time = now(),
            inspect_remark = '送货结束自动质检'
        where del_flag = 0
          and delivery_id in
        <foreach collection="deliveryIds" item="deliveryId" open="(" separator="," close=")">
            #{deliveryId}
        </foreach>
        and status = 10
    </update>
</mapper>
