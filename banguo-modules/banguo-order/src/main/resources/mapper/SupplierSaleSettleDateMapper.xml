<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.SupplierSaleSettleDateMapper">

    <select id="select" resultType="cn.xianlink.order.domain.SupplierSaleSettleDate">
        select * from supplier_sale_settle_date
        where sale_date = '${saleDate}'
            and sup_id = #{supplierId,jdbcType=BIGINT}
            and business_type = #{businessType,jdbcType=INTEGER}
    </select>

    <select id="selectListBySup" resultType="cn.xianlink.order.domain.SupplierSaleSettleDate">
        select * from supplier_sale_settle_date
        where sale_date = '${saleDate}'
        and sup_id = #{supplierId,jdbcType=BIGINT}
    </select>

    <select id="selectNonSettledPage" resultType="cn.xianlink.order.domain.SupplierSaleSettleDate">
        select * from supplier_sale_settle_date
        where completed_settle = 0 and estimated_settle_date <![CDATA[ < ]]> '${currentDate}'
    </select>

    <update id="reopenSettle">
        update supplier_sale_settle_date
        set completed_settle = 0
        where sup_id = #{supplierId,jdbcType=BIGINT} and sale_date = '${saleDate}' and business_type = #{businessType,jdbcType=INTEGER}
    </update>
</mapper>
