<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.xianlink</groupId>
        <artifactId>banguo-modules</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>banguo-trade</artifactId>

    <description>
        banguo-trade 交易模块
    </description>

    <dependencies>
        <!-- yzh 佣金提现 -->
        <dependency>
            <groupId>com.yunzhanghu.openapi</groupId>
            <artifactId>sdk</artifactId>
            <version>1.4.24-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-sentinel</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <!-- xianlink Common Log -->
        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-log</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-dict</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-doc</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-web</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-mybatis</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-dubbo</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-seata</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-idempotent</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-tenant</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-security</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-translation</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-sensitive</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-encrypt</artifactId>
            <version>${xianjian.vision}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15to18</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-rocketmq</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <!-- xianlink Api System -->
        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-api-system</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-api-resource</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>banguo-api-ai</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>banguo-api-basic</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>banguo-api-product</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>banguo-api-trade</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>banguo-api-order</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>banguo-api-bi</artifactId>
        </dependency>

        <!-- 微信小程序对接， 含 b2b 支付 -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
            <version>4.6.0</version>
        </dependency>

        <!-- 拉卡拉支付  要求  bcprov-jdk15to18 是 1.68 的 -->
        <dependency>
            <groupId>com.lkl.laop.sdk</groupId>
            <artifactId>lkl-laop-java-sdk</artifactId>
            <version>1.0.7</version>
            <systemPath>${pom.basedir}/libs/lkl-java-sdk-1.0.7.jar</systemPath>
            <scope>system</scope>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>4.0.1</version>
        </dependency>

        <!-- Hutool FTP 需要 -->
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.9.0</version>
        </dependency>

        <!-- 平安openapi，云支付，电商见证宝  -->
        <dependency>
            <groupId>com.pingan.openbank</groupId>
            <artifactId>api-sdk</artifactId>
            <scope>system</scope>
            <version>1.9.133</version>
            <systemPath>${pom.basedir}/libs/api-sdk-1.9.133.jar</systemPath>
        </dependency>

        <!-- 平安openapi 需要1.64版，  xianjian-common-encrypt中集成 1.76版， 不能通用 -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15to18</artifactId>
            <version>1.70</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <filtering>true</filtering>
                <includes><include>*.yml</include></includes>
            </resource>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <excludes><exclude>*.yml</exclude></excludes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>