<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.trade.mapper.TradeAccTransferMapper">

    <!--  1 划转使用 min(status) , 为了可以判断 TA 类型是否可执行转账接口
          2 划转单没有 6 状态， 6值是换供应商调出状态，忽略掉 -->
    <select id="queryStatusByTrans" resultType="cn.xianlink.trade.domain.TradeAccTransfer">
        select max(case when status=6 then 0 else status end) as status, min(status) as inf_status,
        max(case when trans_type = 'TI' then avail_no else '' end) as avail_no,
        max(case when trans_type = 'TO' then avail_no else '' end) as source_avail_no
        from trade_acc_trans where trans_no = #{transNo} and del_flag = 0
        and trans_type in <foreach collection="transTypes" item="transType" open="(" separator="," close=")">#{transType}</foreach>
    </select>

</mapper>
