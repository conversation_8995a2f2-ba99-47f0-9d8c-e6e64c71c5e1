<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.trade.mapper.TradePayMapper">

    <select id="queryRefundAmt" resultType="cn.xianlink.trade.domain.TradePay">
        select max(refund_seq) as refund_seq,sum(refund_amt) as refund_amt,sum(refund_split_amt) as refund_split_amt,
        sum(case when busi_field = 'refund_amt1' then refund_amt else 0 end) as refund_amt1,
        sum(case when busi_field = 'refund_amt1' then refund_split_amt else 0 end) as refund_split_amt1,
        sum(case when busi_field = 'refund_amt2' then refund_amt else 0 end) as refund_amt2,
        sum(case when busi_field = 'refund_amt2' then refund_split_amt else 0 end) as refund_split_amt2,
        sum(case when busi_field = 'refund_amt3' then refund_amt else 0 end) as refund_amt3,
        sum(case when busi_field = 'refund_amt3' then refund_split_amt else 0 end) as refund_split_amt3,
        sum(case when busi_field = 'refund_amt4' then refund_amt else 0 end) as refund_amt4,
        sum(case when busi_field = 'refund_amt4' then refund_split_amt else 0 end) as refund_split_amt4
        from trade_pay_refund where order_no = #{orderNo} and del_flag = 0 and inf_status = 1
    </select>

    <update id="updateRefundAmt">
        update trade_pay a set a.refund_amt = #{vo.refundAmt},a.refund_split_amt = #{vo.refundSplitAmt},
        a.refund_amt1 = #{vo.refundAmt1},a.refund_split_amt1 = #{vo.refundSplitAmt1},
        a.refund_amt2 = #{vo.refundAmt2},a.refund_split_amt2 = #{vo.refundSplitAmt2},
        a.refund_amt3 = #{vo.refundAmt3},a.refund_split_amt3 = #{vo.refundSplitAmt3},
        a.refund_amt4 = #{vo.refundAmt4},a.refund_split_amt4 = #{vo.refundSplitAmt4},a.refund_seq = #{vo.refundSeq}
        where a.id = #{payId, jdbcType=BIGINT}
    </update>

    <!-- 全部为 6 时， 订单才是 6 状态 -->
    <select id="queryStatusByTrans" resultType="cn.xianlink.trade.domain.TradePay">
        select max(status) as status, max(case when status=6 then 0 else status end) as inf_status
        from trade_acc_trans where trans_no = #{transNo} and del_flag = 0
        and trans_type in  <foreach collection="transTypes" item="transType" open="(" separator="," close=")">#{transType}</foreach>
    </select>

    <update id="updateStatusByTrans">
        update trade_pay a set a.status = case when a.pay_amt=a.refund_amt and #{vo.status}=6 then #{vo.status} else #{vo.infStatus} end
        where a.order_no = #{vo.orderNo} and a.del_flag = 0
    </update>

    <update id="updateAcctStatus" parameterType="java.util.List">
        <foreach collection="bos" item="bo" separator=";">
            update trade_pay set acct_status=1,acct_date=#{acctDate},out_fee_amt=#{bo.outFeeAmt} where trade_no=#{bo.tradeNo}
        </foreach>
    </update>

</mapper>
