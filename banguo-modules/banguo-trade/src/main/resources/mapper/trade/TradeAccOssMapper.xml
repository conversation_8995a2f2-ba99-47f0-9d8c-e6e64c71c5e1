<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.trade.mapper.TradeAccOssMapper">

    <select id="queryAcctStatus" resultType="cn.xianlink.trade.domain.vo.TradeAccOssStatusVo">
        select group_concat(case when del_flag = 0 and inf_status = 1 and acct_status = 0 then split_no else null end separator ';') as incomplete_nos,
        sum(case when del_flag = 0 and inf_status = 1 and acct_status = 0 then 1 else 0 end) as incomplete_count,
        sum(case when acct_status = 1 then 1 else 0 end) as complete_count,
        group_concat(case when (del_flag != 0 or inf_status != 1) and acct_status = 1 then split_no else null end separator ';') as del_complete_nos,
        sum(case when (del_flag != 0 or inf_status != 1) and acct_status = 1 then 1 else 0 end) as del_complete_count
        from trade_acc_split where acct_date = #{acctDate} and trans_amt > 0
        <choose><when test="fileType == 'CZ'">
            and ((channel = 'weixinB2b' and trans_type in ('OP', 'OR')) or (channel = 'pinganCloudWX' and trans_type = 'OP'))
        </when><when test="fileType == 'TX'">
            and (trans_type = 'CH' or (channel = 'pinganCloudWX' and trans_type='OR'))
        </when><when test="fileType == 'JY'">
            and ((channel != 'weixinB2b' and trans_type in ('OP', 'OR'))  or trans_type in ('TI', 'TO', 'DI', 'DO', 'CI', 'CO'))
        </when><otherwise>
            and 1=2
        </otherwise></choose>
    </select>

</mapper>
