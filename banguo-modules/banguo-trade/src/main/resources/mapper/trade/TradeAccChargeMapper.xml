<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.trade.mapper.TradeAccChargeMapper">

    <select id="queryChargeByRefund" resultType="cn.xianlink.trade.domain.TradeAccCharge">
        select coalesce(sum(refund_amt),0) as refund_amt,count(*) as refund_count
        from trade_acc_charge where relate_no = #{chargeNo} and charge_type = 'CO' and inf_status = 1 and del_flag = 0
    </select>

</mapper>
