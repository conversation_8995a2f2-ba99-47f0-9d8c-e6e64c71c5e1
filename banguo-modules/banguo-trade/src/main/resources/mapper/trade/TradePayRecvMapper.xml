<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.trade.mapper.TradePayRecvMapper">

    <update id="updateAcctStatus" parameterType="java.util.List">
        <foreach collection="bos" item="bo" separator=";">
            update trade_pay_recv set acct_status=1,acct_date=#{acctDate} where recv_no=#{bo.recvNo} and del_flag=0
        </foreach>
    </update>

</mapper>
