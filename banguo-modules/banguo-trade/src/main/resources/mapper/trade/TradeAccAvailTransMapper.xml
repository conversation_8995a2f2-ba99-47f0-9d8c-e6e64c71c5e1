<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.trade.mapper.TradeAccAvailTransMapper">

    <!-- 销售未结算 记 0  （availId is null， 或 availDate 超了）  记账日期 = 销售日期  -->
    <update id="insertSupUndonePay">
        insert into trade_acc_avail_trans (trans_id,trans_date,org_id,org_code,trans_amt,commission_amt,avail_id,avail_no,
        avail_date,account_date,status,tenant_id)
        select a.id,a.trans_date,a.org_id,a.org_code,a.trans_amt,a.commission_amt,a.avail_id,a.avail_no,
        c.avail_date,#{transDate},0,a.tenant_id
        from trade_acc_trans a left join trade_acc_avail c on a.avail_id=c.id and c.del_flag = 0
        where a.del_flag = 0 and a.trans_type = 'OP' and a.trans_date=#{transDate} and a.org_code=#{orgCode}
        and a.org_type = 2 and a.status in (1,2,3,4) and (c.avail_date is null or c.avail_date &gt; #{availDate})
    </update>

    <!-- 退款占用未结算 记 0  按正值插入！！！！！ （availId is null， 或 availDate 超了）  记账日期 = 销售日期  -->
    <update id="insertSupUndoneOccupy">
        insert into trade_acc_avail_trans (trans_id,trans_date,org_id,org_code,trans_amt,commission_amt,avail_id,avail_no,
        avail_date,account_date,status,tenant_id)
        select a.id,a.trans_date,a.org_id,a.org_code,-a.trans_amt,-a.commission_amt,a.avail_id,a.avail_no,
        c.avail_date,#{transDate},0,a.tenant_id
        from trade_acc_trans a inner join trade_pay_refund b on a.trans_no = b.refund_no and b.del_flag = 0 and b.is_occupy != 0
            left join trade_acc_avail c on a.avail_id=c.id and c.del_flag = 0
        where a.del_flag = 0 and a.trans_type = 'OR' and a.trans_date=#{transDate} and a.org_code=#{orgCode} and a.busi_field!='refund_amt2'
        and a.org_type = 2 and a.status in (1,2,3,4) and (c.avail_date is null or c.avail_date &gt; #{availDate})
    </update>

    <!-- 正常确认 1 和 超期确认 2，  超期确认仅考虑 销售 + 退款释放， 结算日期超了的     记账日期 = 销售日期 （结算日期 - 3 ）  -->
    <update id="insertSupOverdue">
        insert into trade_acc_avail_trans (trans_id,trans_date,org_id,org_code,trans_amt,commission_amt,avail_id,avail_no,
        avail_date,account_date,status,tenant_id)
        select id,trans_date,org_id,org_code,trans_amt,commission_amt,avail_id,avail_no,#{availDate},#{transDate},
        case when trans_type in ('OS','OP') and trans_date &lt; #{transDate} then 2 else 1 end,tenant_id
        from trade_acc_trans
        where del_flag = 0 and avail_id in <foreach collection="availIds" item="availId" open="(" separator="," close=")">#{availId}</foreach>
    </update>

    <update id="updateSupUndoneBatch" parameterType="java.util.List">
        <foreach collection="transVos" item="vo" separator=";">
            update trade_acc_avail_trans set avail_id=#{vo.availId},avail_no=#{vo.availNo},avail_date=#{vo.availDate}
            where trans_id=#{vo.transId} and status=0
        </foreach>
    </update>

    <!--  退款合计中  要减去 undone 退款，  undone 退款按负值显示，  这样 total_amt 中就不含 undone 部分了（正负抵消了）  -->
    <select id="customSupUndonePageList" resultType="cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransVo">
        SELECT b.trans_date,0 as status,null as avail_date,a.in_amt+a.out_amt
        +a.commission_amt+a.pay_amt+a.refund_amt2+a.refund_amt3+a.refund_lack_amt1+a.refund_short_amt1
        +a.undone_refund_amt3+a.undone_refund_lack_amt1+a.undone_refund_short_amt1 as total_amt,
        a.in_amt+a.out_amt as in_out_amt,a.in_amt,a.out_amt,a.commission_amt,
        a.pay_amt,a.refund_amt2+a.refund_amt3+a.refund_lack_amt1+a.refund_short_amt1 as refund_amt,
        a.refund_amt2,a.refund_amt3,a.refund_lack_amt1,a.refund_short_amt1,
        a.undone_refund_amt3+a.undone_refund_lack_amt1+a.undone_refund_short_amt1 as undone_refund_amt,
        0 as undone_pay_amt,a.undone_refund_amt3,a.undone_refund_lack_amt1,a.undone_refund_short_amt1,
        0 as undone_pay_amt,0 as overdue_pay_amt,0 as overdue_refund_amt,
        0 as overdue_refund_amt3,0 as overdue_refund_lack_amt1,0 as overdue_refund_short_amt1
        FROM (SELECT trans_date,sum(-commission_amt) as commission_amt,
        sum(case when busi_field='pay_amt' then trans_amt else 0 end) as pay_amt,
        sum(case when busi_field='in_amt' then trans_amt else 0 end) as in_amt,
        sum(case when busi_field='out_amt' then trans_amt else 0 end) as out_amt,
        sum(case when busi_field='refund_amt2' then trans_amt else 0 end) as refund_amt2,
        sum(case when busi_type= 'RL' then case when inf_status=10 then -trans_amt else trans_amt end else 0 end) as refund_lack_amt1,
        sum(case when busi_type= 'RE' then case when inf_status=10 then -trans_amt else trans_amt end else 0 end) as refund_short_amt1,
        sum(case when busi_field='refund_amt3' then case when inf_status=10 then -trans_amt else trans_amt end else 0 end) as refund_amt3,
        -sum(case when busi_type= 'RL' and inf_status=10 then -trans_amt else 0 end) as undone_refund_lack_amt1,
        -sum(case when busi_type= 'RE' and inf_status=10 then -trans_amt else 0 end) as undone_refund_short_amt1,
        -sum(case when busi_field='refund_amt3' and inf_status=10 then -trans_amt else 0 end) as undone_refund_amt3
        FROM trade_acc_trans
        WHERE del_flag = 0 and org_type = 2 and org_code = #{bo.supplierCode} and status in (1,2,3,4)
        <if test="bo.deptId != null and bo.deptId!=0">
            and dept_id = #{bo.deptId}
        </if>
        <if test="bo.transDateStart != null">
            and trans_date &gt;= #{bo.transDateStart}
        </if>
        <if test="bo.transDateEnd != null">
            and trans_date &lt;= #{bo.transDateEnd}
        </if>
        GROUP BY trans_date) a right join
        (SELECT <foreach collection="dates" item="date" separator=" union SELECT ">#{date} as trans_date</foreach>) b
        on a.trans_date = b.trans_date order by b.trans_date desc
    </select>

    <sql id="commonUndoneWhere">
        WHERE del_flag = 0 and org_type = 2 and org_code = #{bo.supplierCode} and trans_date = #{bo.transDate} and status in (1,2,3,4)
        <if test="bo.deptId != null and bo.deptId!=0">
            and dept_id = #{bo.deptId}
        </if>
        <choose><when test="bo.type == 'transfer'">
            and busi_field in ('in_amt','out_amt')
        </when><when test="bo.type == 'pay'">
            and busi_field='pay_amt'
        </when><when test="bo.type == 'refund'">
            and busi_field in ('refund_amt1','refund_amt2','refund_amt3')
        </when><when test="bo.type == 'refundUndone'">
            and inf_status=10 and busi_field in ('refund_amt1','refund_amt2','refund_amt3')
        </when><when test="bo.type == 'commission'">
            and commission_amt != 0 and trans_type in ('OP','OR','OS')
        </when><otherwise>
            and 1=2
        </otherwise>
        </choose>
    </sql>

    <select id="customSupUndoneSkuTransIds" resultType="cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransSkuVo">
        SELECT sku_id as supplier_sku_id,trans_id<if test="bo.type == 'commission'">,trans_type</if>
        FROM trade_acc_trans
        <include refid="commonUndoneWhere"/>
        and sku_id in <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">#{skuId}</foreach>
        order by sku_id desc
    </select>

    <select id="customSupUndoneSkuPageList" resultType="cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransSkuVo">
        SELECT sku_id as supplier_sku_id,${symbol}sum(case when inf_status=10 then -trans_amt else trans_amt end) as trans_amt,
        ${symbol}sum(case when inf_status=10 then commission_amt else -commission_amt end) as commission_amt
        <if test="bo.type == 'commission'">,trans_type</if>
        FROM trade_acc_trans
        <include refid="commonUndoneWhere"/>
        <if test="bo.skuName != null and bo.skuName!=''">
            and sku_name like concat('%', #{bo.skuName}, '%')
        </if>
        GROUP BY sku_id<if test="bo.type == 'commission'">,trans_type</if>
        order by sku_id desc
    </select>

    <!-- 条件和上面 customSupUndoneSkuPageList 的一致 -->
    <select id="customSupUndoneSkuPageTotal" resultType="cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransSkuVo">
        SELECT ${symbol}coalesce(sum(case when inf_status=10 then -trans_amt else trans_amt end),0) as trans_amt,count(*) as trans_count,
        ${symbol}coalesce(sum(case when inf_status=10 then commission_amt else -commission_amt end),0) as commission_amt
        FROM trade_acc_trans
        <include refid="commonUndoneWhere"/>
        <if test="bo.skuName != null and bo.skuName!=''">
            and sku_name like concat('%', #{bo.skuName}, '%')
        </if>
    </select>

    <select id="customSupUndoneOrderPageList" resultType="cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransOrderVo">
        SELECT sku_id as supplier_sku_id,trans_id,trans_no,relate_no,trans_date,busi_type,
        ${symbol}case when inf_status=10 then -trans_amt else trans_amt end trans_amt,
        ${symbol}case when inf_status=10 then commission_amt else -commission_amt end commission_amt
        <if test="bo.type == 'commission'">,trans_type</if>
        FROM trade_acc_trans
        <include refid="commonUndoneWhere"/>
        <if test="bo.transNo != null and bo.transNo!=''">
            and trans_no = #{bo.transNo}
        </if>
        and sku_id = #{bo.skuId}
        order by trans_id desc
    </select>

    <!-- 条件和上面 customSupUndoneOrderPageList 的一致 -->
    <select id="customSupUndoneOrderPageTotal" resultType="cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransOrderVo">
        SELECT ${symbol}coalesce(sum(case when inf_status=10 then -trans_amt else trans_amt end),0) trans_amt,count(*) as trans_qty,
        ${symbol}coalesce(sum(case when inf_status=10 then commission_amt else -commission_amt end),0) commission_amt
        FROM trade_acc_trans
        <include refid="commonUndoneWhere"/>
        <if test="bo.transNo != null and bo.transNo!=''">
            and trans_no = #{bo.transNo}
        </if>
        and sku_id = #{bo.skuId}
    </select>

    <!--  以下是日报表查询   -->
    <select id="customSupAvailPageList" resultType="cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransVo">
        SELECT b.trans_date,case when undone_count=0 then 2 else 1 end as status,a.avail_date,a.in_amt+a.out_amt
        +a.commission_amt+a.pay_amt+a.refund_amt2+a.refund_amt3+a.refund_lack_amt1+a.refund_short_amt1
        +a.undone_pay_amt+a.undone_refund_amt3+a.undone_refund_lack_amt1+a.undone_refund_short_amt1
        +a.overdue_pay_amt+a.overdue_refund_amt3+a.overdue_refund_lack_amt1+a.overdue_refund_short_amt1 as total_amt,
        a.in_amt+a.out_amt as in_out_amt,a.in_amt,a.out_amt,a.commission_amt,
        a.refund_amt2+a.refund_amt3+a.refund_lack_amt1+a.refund_short_amt1 as refund_amt,
        a.pay_amt,a.refund_amt2,a.refund_amt3,a.refund_lack_amt1,a.refund_short_amt1,
        a.undone_refund_amt3+a.undone_refund_lack_amt1+a.undone_refund_short_amt1 as undone_refund_amt,
        a.undone_pay_amt,a.undone_refund_amt3,a.undone_refund_lack_amt1,a.undone_refund_short_amt1,
        a.overdue_refund_amt3+a.overdue_refund_lack_amt1+a.overdue_refund_short_amt1 as overdue_refund_amt,
        a.overdue_pay_amt,a.overdue_refund_amt3,a.overdue_refund_lack_amt1,a.overdue_refund_short_amt1
        FROM (SELECT m.account_date as trans_date,min(m.avail_date) as avail_date,
        sum(case when m.status = 0 and m.avail_date is null then 1 else 0 end) as undone_count,
        sum(case when n.busi_field='in_amt' then m.trans_amt else 0 end) as in_amt,
        sum(case when n.busi_field='out_amt' then m.trans_amt else 0 end) as out_amt,
        sum(case when n.busi_field='refund_amt2' then m.trans_amt else 0 end) as refund_amt2,
        sum(case when m.status = 1 then -m.commission_amt else 0 end) as commission_amt,
        sum(case when m.status != 2 and n.busi_field='pay_amt' then m.trans_amt else 0 end) as pay_amt,
        sum(case when m.status != 2 and n.busi_type= 'RL' then m.trans_amt else 0 end) as refund_lack_amt1,
        sum(case when m.status != 2 and n.busi_type= 'RE' then m.trans_amt else 0 end) as refund_short_amt1,
        sum(case when m.status != 2 and n.busi_field='refund_amt3' then m.trans_amt else 0 end) as refund_amt3,
        -sum(case when m.status = 0 and n.busi_field='pay_amt' then m.trans_amt else 0 end) as undone_pay_amt,
        -sum(case when m.status = 0 and n.busi_type= 'RL' then m.trans_amt else 0 end) as undone_refund_lack_amt1,
        -sum(case when m.status = 0 and n.busi_type= 'RE' then m.trans_amt else 0 end) as undone_refund_short_amt1,
        -sum(case when m.status = 0 and n.busi_field='refund_amt3' then m.trans_amt else 0 end) as undone_refund_amt3,
        sum(case when m.status = 2 and n.busi_field='pay_amt' then m.trans_amt else 0 end) as overdue_pay_amt,
        sum(case when m.status = 2 and n.busi_type= 'RL' then m.trans_amt else 0 end) as overdue_refund_lack_amt1,
        sum(case when m.status = 2 and n.busi_type= 'RE' then m.trans_amt else 0 end) as overdue_refund_short_amt1,
        sum(case when m.status = 2 and n.busi_field='refund_amt3' then m.trans_amt else 0 end) as overdue_refund_amt3
        FROM trade_acc_avail_trans m, trade_acc_trans n
        WHERE m.del_flag = 0 and m.org_code = #{bo.supplierCode} and m.trans_id=n.id
        <if test="bo.deptId != null and bo.deptId!=0">
            and n.dept_id = #{bo.deptId}
        </if>
        <if test="bo.transDateStart != null">
            and m.account_date &gt;= #{bo.transDateStart}
        </if>
        <if test="bo.transDateEnd != null">
            and m.account_date &lt;= #{bo.transDateEnd}
        </if>
        GROUP BY m.account_date
        <if test="bo.settleStatus != null">
            <if test="bo.settleStatus == 1">
                HAVING undone_count != 0
            </if>
            <if test="bo.settleStatus == 2">
                HAVING undone_count = 0
            </if>
        </if>
        ) a right join
        (SELECT <foreach collection="dates" item="date" separator=" union SELECT ">#{date} as trans_date</foreach>) b
        on a.trans_date = b.trans_date order by b.trans_date desc
    </select>

    <sql id="commonAvailWhere">
        WHERE m.del_flag = 0 and m.org_code = #{bo.supplierCode} and m.account_date = #{bo.transDate} and m.trans_id=n.id
        <if test="bo.deptId != null and bo.deptId!=0">
            and n.dept_id = #{bo.deptId}
        </if>
        <choose><when test="bo.type == 'transfer'">
            and n.busi_field in ('in_amt','out_amt')
        </when><when test="bo.type == 'pay'">
            and m.status != 2 and n.busi_field='pay_amt'
        </when><when test="bo.type == 'refund'">
            <!-- refund_amt2 只有 status=1 情况才有 -->
            and m.status != 2 and n.busi_field in ('refund_amt1', 'refund_amt3','refund_amt2')
        </when><when test="bo.type == 'payUndone'">
            and m.status = 0 and n.busi_field='pay_amt'
        </when><when test="bo.type == 'refundUndone'">
            and m.status = 0 and n.busi_field in ('refund_amt1', 'refund_amt3')
        </when><when test="bo.type == 'payOverdue'">
            and m.status = 2 and n.busi_field='pay_amt'
        </when><when test="bo.type == 'refundOverdue'">
            and m.status = 2 and n.busi_field in ('refund_amt1', 'refund_amt3')
        </when><when test="bo.type == 'commission'">
            and m.status != 2 and m.commission_amt!=0 and n.trans_type in ('OP','OR','OS')
        </when><otherwise>
            and 1=2
        </otherwise>
        </choose>
    </sql>

    <select id="customSupAvailSkuTransIds" resultType="cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransSkuVo">
        SELECT n.sku_id as supplier_sku_id,n.trans_id<if test="bo.type == 'commission'">,n.trans_type</if>
        FROM trade_acc_avail_trans m, trade_acc_trans n
        <include refid="commonAvailWhere"/>
        and n.sku_id in <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">#{skuId}</foreach>
        order by n.sku_id desc
    </select>

    <select id="customSupAvailSkuPageList" resultType="cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransSkuVo">
        SELECT n.sku_id as supplier_sku_id,${symbol}sum(m.trans_amt) as trans_amt,${symbol}sum(-m.commission_amt) as commission_amt
        <if test="bo.type == 'commission'">,n.trans_type</if>
        FROM trade_acc_avail_trans m, trade_acc_trans n
        <include refid="commonAvailWhere"/>
        <if test="bo.skuName != null and bo.skuName!=''">
            and n.sku_name like concat('%', #{bo.skuName}, '%')
        </if>
        GROUP BY n.sku_id<if test="bo.type == 'commission'">,n.trans_type</if>
        order by n.sku_id desc
    </select>

    <!-- 条件和上面 customSupAvailSkuPageList 的一致 -->
    <select id="customSupAvailSkuPageTotal" resultType="cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransSkuVo">
        SELECT ${symbol}coalesce(sum(m.trans_amt),0) as trans_amt,count(*) as trans_count,
        ${symbol}coalesce(sum(-m.commission_amt),0) as commission_amt
        FROM trade_acc_avail_trans m, trade_acc_trans n
        <include refid="commonAvailWhere"/>
        <if test="bo.skuName != null and bo.skuName!=''">
            and n.sku_name like concat('%', #{bo.skuName}, '%')
        </if>
    </select>

    <select id="customSupAvailOrderPageList" resultType="cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransOrderVo">
        SELECT n.sku_id as supplier_sku_id,n.trans_id,n.trans_no,n.relate_no,n.trans_date,n.busi_type,
        ${symbol}m.trans_amt as trans_amt,${symbol}(-m.commission_amt) as commission_amt
        <if test="bo.type == 'commission'">,n.trans_type</if>
        FROM trade_acc_avail_trans m, trade_acc_trans n
        <include refid="commonAvailWhere"/>
        <if test="bo.transNo != null and bo.transNo!=''">
            and n.trans_no = #{bo.transNo}
        </if>
        and n.sku_id = #{bo.skuId}
        order by m.trans_id desc
    </select>

    <!-- 条件和上面 customSupAvailOrderPageList 的一致 -->
    <select id="customSupAvailOrderPageTotal" resultType="cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransOrderVo">
        SELECT ${symbol}coalesce(sum(m.trans_amt),0) as trans_amt,count(*) as trans_qty,
        ${symbol}coalesce(sum(-m.commission_amt),0) as commission_amt
        FROM trade_acc_avail_trans m, trade_acc_trans n
        <include refid="commonAvailWhere"/>
        <if test="bo.transNo != null and bo.transNo!=''">
            and n.trans_no = #{bo.transNo}
        </if>
        and n.sku_id = #{bo.skuId}
    </select>

    <select id="customSupAvailTransExportList" resultType="cn.xianlink.trade.domain.vo.sup.excel.SupTradeAccSupDeptAcctRefundExcelVo">
        SELECT n.trans_date,n.trans_type,n.trans_id,n.trans_no,n.trans_org_id,n.org_id,n.dept_id,n.sku_id,n.sku_id as supplier_sku_id,
        n.sku_name,n.freeze_time,n.relate_no,n.busi_type,n.cash_no,n.status as cash_status,m.avail_id,m.avail_no,m.avail_date,m.status,
        case when m.status = 0 then -m.trans_amt else m.trans_amt end trans_amt,
        case when m.status = 0 then m.commission_amt else -m.commission_amt end commission_amt
        FROM trade_acc_avail_trans m, trade_acc_trans n
        WHERE m.del_flag = 0 and m.org_code = #{bo.supplierCode} and m.account_date = #{bo.transDate} and m.trans_id=n.id
        ORDER BY m.status desc, m.trans_id asc
    </select>

</mapper>
