<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.trade.mapper.TradeCommTransMapper">

    <select id="queryCommCashTransById" resultType="cn.xianlink.trade.domain.vo.comm.TradeCommTransVo">
        select a.id,a.trans_type,a.trans_id,a.trans_no,a.trans_date,a.salary_amt,a.cash_amt,a.status,a.tenant_id,
        b.commission_amt,b.salary_amt as expire_amt,a.avail_date,a.customer_id
        from trade_comm_trans a,trade_comm_cash_detail b
        where b.cash_id = #{cashId} and b.del_flag = 0 and b.comm_trans_id = a.id
        order by a.id asc
    </select>

    <update id="insertCommTransByTrans">
        insert into trade_comm_trans (acc_trans_id,trans_type,trans_id,trans_no,trans_date,sku_id,sku_name,
        acct_org_id,trans_org_id,dept_id,customer_id,busi_type,relate_no,relate_id,commission_amt,salary_amt,tenant_id)
        select a.id,a.trans_type,a.trans_id,a.trans_no,a.trans_date,a.sku_id,a.sku_name,a.acct_org_id,a.trans_org_id,
        a.dept_id,a.comm_customer_id,a.busi_type,a.relate_no,a.relate_id,a.commission_amt,a.comm_salary_amt,a.tenant_id
        from trade_acc_trans a
        where a.del_flag = 0 and a.id in (<foreach collection="ids" item="id" separator=",">#{id}</foreach>)
    </update>

</mapper>
