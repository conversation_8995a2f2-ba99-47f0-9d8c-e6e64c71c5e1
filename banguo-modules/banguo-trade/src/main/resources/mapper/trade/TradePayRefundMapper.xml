<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.trade.mapper.TradePayRefundMapper">

    <select id="queryStatusByTrans" resultType="cn.xianlink.trade.domain.TradePayRefund">
        select max(status) as status
        from trade_acc_trans where trans_no = #{transNo} and del_flag = 0
        and trans_type in <foreach collection="transTypes" item="transType" open="(" separator="," close=")">#{transType}</foreach>
    </select>

    <update id="updateAcctStatus" parameterType="java.util.List">
        <foreach collection="bos" item="bo" separator=";">
            update trade_pay_refund set acct_status=1,acct_date=#{acctDate},out_fee_amt=#{bo.outFeeAmt} where trade_refund_no=#{bo.tradeRefundNo}
        </foreach>
    </update>

</mapper>
