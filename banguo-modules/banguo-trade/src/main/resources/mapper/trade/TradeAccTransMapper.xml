<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.trade.mapper.TradeAccTransMapper">

    <select id="customSupList" resultType="cn.xianlink.trade.domain.vo.TradeAccAccountVo">
        select 2 as org_type,org_id,
        sum(case when status=1 then trans_amt-commission_amt else 0 end) as freeze_amt,
        sum(case when status=1 then commission_amt else 0 end) as freeze_comm_amt,
        sum(case when status=1 and busi_field='in_amt' then trans_amt else 0 end) as freeze_in_amt,
        -sum(case when status=1 and busi_field='out_amt' then trans_amt else 0 end) as freeze_out_amt,
        sum(case when status=1 and busi_field='pay_amt' then trans_amt else 0 end) as freeze_pay_amt,
        -sum(case when status=1 and busi_field='refund_amt1' then trans_amt else 0 end) as freeze_refund_amt1,
        -sum(case when status=1 and busi_field='refund_amt2' then trans_amt else 0 end) as freeze_refund_amt2,
        -sum(case when status=1 and busi_field='refund_amt3' then trans_amt else 0 end) as freeze_refund_amt3,
        sum(case when status=2 then trans_amt-commission_amt else 0 end) as avail_amt,
        sum(case when status=2 then commission_amt else 0 end) as avail_comm_amt,
        sum(case when status=2 and busi_field='in_amt' then trans_amt else 0 end) as avail_in_amt,
        -sum(case when status=2 and busi_field='out_amt' then trans_amt else 0 end) as avail_out_amt,
        sum(case when status=2 and busi_field='pay_amt' then trans_amt else 0 end) as avail_pay_amt,
        -sum(case when status=2 and busi_field='refund_amt1' then trans_amt else 0 end) as avail_refund_amt1,
        -sum(case when status=2 and busi_field='refund_amt2' then trans_amt else 0 end) as avail_refund_amt2,
        -sum(case when status=2 and busi_field='refund_amt3' then trans_amt else 0 end) as avail_refund_amt3,
        sum(case when status=3 then trans_amt-commission_amt else 0 end) as cash_amt,
        sum(case when status=3 then commission_amt else 0 end) as cash_comm_amt,
        sum(case when status=3 and busi_field='in_amt' then trans_amt else 0 end) as cash_in_amt,
        -sum(case when status=3 and busi_field='out_amt' then trans_amt else 0 end) as cash_out_amt,
        sum(case when status=3 and busi_field='pay_amt' then trans_amt else 0 end) as cash_pay_amt,
        -sum(case when status=3 and busi_field='refund_amt1' then trans_amt else 0 end) as cash_refund_amt1,
        -sum(case when status=3 and busi_field='refund_amt2' then trans_amt else 0 end) as cash_refund_amt2,
        -sum(case when status=3 and busi_field='refund_amt3' then trans_amt else 0 end) as cash_refund_amt3,
        sum(case when status=4 then trans_amt-commission_amt else 0 end) as check_amt,
        sum(case when status=4 then commission_amt else 0 end) as check_comm_amt,
        sum(case when status=4 and busi_field='in_amt' then trans_amt else 0 end) as check_in_amt,
        -sum(case when status=4 and busi_field='out_amt' then trans_amt else 0 end) as check_out_amt,
        sum(case when status=4 and busi_field='pay_amt' then trans_amt else 0 end) as check_pay_amt,
        -sum(case when status=4 and busi_field='refund_amt1' then trans_amt else 0 end) as check_refund_amt1,
        -sum(case when status=4 and busi_field='refund_amt2' then trans_amt else 0 end) as check_refund_amt2,
        -sum(case when status=4 and busi_field='refund_amt3' then trans_amt else 0 end) as check_refund_amt3
        from trade_acc_trans ${ew.customSqlSegment}
        group by org_id order by org_id asc
    </select>

    <select id="customSupDeptAcctDatePageList" resultType="cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAcctDateVo">
        SELECT b.trans_date,coalesce(a.pay_amt-a.commission_amt-a.refund_lack_amt1-a.refund_short_amt1-a.refund_freeze_lack_amt1
            -a.refund_freeze_short_amt1-a.refund_amt2-a.refund_amt3-a.refund_freeze_amt3+a.in_amt-a.out_amt,0) as total_amt,
        coalesce(a.pay_amt,0) as pay_amt,coalesce(a.commission_amt,0) as commission_amt,
        coalesce(a.in_amt-a.out_amt,0) as in_out_amt,coalesce(a.in_amt,0) as in_amt,coalesce(a.out_amt,0) as out_amt,
        coalesce(a.refund_lack_amt1+a.refund_short_amt1+a.refund_amt2+a.refund_amt3,0) as refund_amt,
        coalesce(a.refund_amt2,0) as refund_amt2,coalesce(a.refund_amt3,0) as refund_amt3,
        coalesce(a.refund_lack_amt1,0) as refund_lack_amt1,coalesce(a.refund_short_amt1,0) as refund_short_amt1,
        coalesce(a.refund_freeze_lack_amt1+refund_freeze_short_amt1+refund_freeze_amt3,0) as refund_freeze_amt,
        coalesce(a.refund_freeze_short_amt1,0) as refund_freeze_short_amt1,coalesce(a.refund_freeze_lack_amt1,0) as refund_freeze_lack_amt1,
        coalesce(a.refund_freeze_amt3,0) as refund_freeze_amt3
        FROM (SELECT trans_date,
            sum(commission_amt) as commission_amt,
            sum(case when busi_field='in_amt' then trans_amt else 0 end) as in_amt,
            -sum(case when busi_field='out_amt' then trans_amt else 0 end) as out_amt,
            sum(case when busi_field='pay_amt' then trans_amt else 0 end) as pay_amt,
            -sum(case when busi_field='refund_amt2' then trans_amt else 0 end) as refund_amt2,
            -sum(case when busi_field='refund_amt1' and busi_type= 'RL' and inf_status = 1 then trans_amt else 0 end) as refund_lack_amt1,
            -sum(case when busi_field='refund_amt1' and busi_type= 'RE' and inf_status = 1 then trans_amt else 0 end) as refund_short_amt1,
            -sum(case when busi_field='refund_amt3' and inf_status = 1 then trans_amt else 0 end) as refund_amt3,
            -sum(case when busi_field='refund_amt1' and busi_type= 'RL' and inf_status != 1 then trans_amt else 0 end) as refund_freeze_lack_amt1,
            -sum(case when busi_field='refund_amt1' and busi_type= 'RE' and inf_status != 1 then trans_amt else 0 end) as refund_freeze_short_amt1,
            -sum(case when busi_field='refund_amt3' and inf_status != 1 then trans_amt else 0 end) as refund_freeze_amt3
            FROM trade_acc_trans ${ew.customSqlSegment}
            GROUP BY trans_date) a right join
            (SELECT <foreach collection="dates" item="date" separator=" union SELECT ">#{date} as trans_date</foreach>) b
        on a.trans_date = b.trans_date order by b.trans_date desc
    </select>

    <select id="customSupDeptTransPageList" resultType="cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptTransVo">
        SELECT trans_date
        FROM trade_acc_trans ${ew.customSqlSegment}
        GROUP BY trans_date order by trans_date desc
    </select>
    <select id="getByOrderItemId" resultType="cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptTransVo">
        SELECT trans_date
        FROM trade_acc_trans ${ew.customSqlSegment}
        GROUP BY trans_date order by trans_date desc
    </select>


    <!--
        sum(case when busi_field='pay_amt' then trans_amt else 0 end) as pay_amt,
        sum(case when busi_field='refund_amt1' then -trans_amt else 0 end) as refund_amt,
        sum(case when busi_field='refund_amt2' then -trans_amt else 0 end) as diff_refund_amt,
        sum(case when busi_field='refund_amt3' then -trans_amt else 0 end) as loss_refund_amt,
        group_concat(distinct case when busi_field = 'in_amt' then trans_id else '' end separator ';') as in_ids,
        group_concat(distinct case when busi_field = 'out_amt' then trans_id else '' end separator ';') as out_ids
    -->
    <select id="customSupAccountTransPageList" resultType="cn.xianlink.trade.domain.vo.TradeAccTransVo">
        SELECT trans_date,sku_id,sum(trans_amt) as trans_amt,sum(commission_amt) as commission_amt
        FROM trade_acc_trans ${ew.customSqlSegment}
        group by trans_date,sku_id having sum(trans_amt) != 0
        order by trans_date desc,sku_id desc
    </select>

    <select id="customSupAccountTransIdsList" resultType="cn.xianlink.trade.domain.vo.TradeAccTransVo">
        SELECT trans_date,sku_id,case when busi_type = 'TA' then relate_id else trans_id end as trans_id
        FROM trade_acc_trans ${ew.customSqlSegment}
    </select>

    <select id="customSupAccountTransSumList" resultType="cn.xianlink.trade.domain.vo.TradeAccTransVo">
        SELECT trans_date,sum(trans_amt) as trans_amt,sum(commission_amt) as commission_amt
        FROM trade_acc_trans ${ew.customSqlSegment}
        group by trans_date order by trans_date desc
    </select>

    <select id="queryOrderRelateList" resultType="cn.xianlink.trade.domain.vo.TradeAccTransVo">
        select id,trans_type,trans_id,trans_no,trans_date,trans_org_id,acct_org_id,org_type,org_id,org_code,dept_id,
            sku_id,relate_amt,split_no,split_rel_no,split_rel_amt,trans_amt,fee_amt,split_amt,
            commission_amt,comm_salary_amt,comm_customer_id,trans_min_amt,trans_max_amt,split_org_code,split_acct_code,
            out_org_code,out_acct_code,status,is_occupy,avail_id
        from trade_acc_trans
        where relate_type = 'OP' and relate_no = #{relateNo} and del_flag = 0
        <if test="orgs != null and orgs.size()>0">
            and (org_code, org_type, sku_id) in (<foreach collection="orgs" item="org" separator=",">
            (#{org.orgCode}, #{org.orgType}, #{org.skuId})</foreach>)
        </if>
        <if test="transTypes != null and transTypes.size()>0">
            and trans_type in (<foreach collection="transTypes" item="id" separator=",">#{id}</foreach>)
        </if>
        order by id asc
    </select>

    <select id="queryStatusListByRelate" resultType="cn.xianlink.trade.domain.vo.TradeAccTransVo">
        select id,trans_type,trans_id,trans_no,trans_date,trans_org_id,acct_org_id,org_type,org_id,org_code,dept_id,
        sku_id,busi_type,relate_type,relate_no,relate_amt,split_no,trans_amt,fee_amt,split_amt,
        commission_amt,comm_salary_amt,comm_customer_id,trans_min_amt,trans_max_amt,
        status,status_time,avail_id,avail_no,cash_id,cash_no,freeze_time,avail_time,cash_time,remark,is_occupy
        from trade_acc_trans
        where relate_type = #{relateType} and relate_no = #{relateNo} and del_flag = 0
        <if test="orgs != null and orgs.size()>0">
            and (org_code, org_type, sku_id) in (<foreach collection="orgs" item="org" separator=",">
                (#{org.orgCode}, #{org.orgType}, #{org.skuId})</foreach>)
        </if>
        <!-- （仅订单调用）划转单优先供应商，优先负值 -->
        order by org_type desc, trans_amt asc
    </select>

    <select id="querySupplierFreezeTrans" resultType="cn.xianlink.trade.domain.vo.TradeAccTransVo">
        SELECT org_type,org_id,org_code,sku_id,max(dept_id) as dept_id,max(comm_customer_id) as comm_customer_id,
        max(sku_name) as sku_name,sum(trans_amt) as trans_amt,sum(split_amt) as split_amt,sum(fee_amt) as fee_amt,
        sum(commission_amt) as commission_amt,sum(comm_salary_amt) as comm_salary_amt
        FROM trade_acc_trans
        WHERE relate_type = #{relateType} AND relate_no = #{relateNo}
          AND del_flag = 0 AND org_type = 2 AND status = 1 AND trans_type in ('OP', 'TO')
        GROUP BY org_type,org_id,org_code,sku_id
    </select>

    <!-- 订单取消明细数据查询, 验证退款暂时时，包含本次的退款数据 -->
    <select id="queryPayCancelTrans" resultType="cn.xianlink.trade.domain.vo.TradeAccTransVo">
        SELECT org_type,org_id,org_code,sku_id,max(dept_id) as dept_id,max(comm_customer_id) as comm_customer_id,
        max(sku_name) as sku_name,sum(trans_amt) as trans_amt,sum(split_amt) as split_amt,sum(fee_amt) as fee_amt,
        sum(commission_amt) as commission_amt,sum(comm_salary_amt) as comm_salary_amt
        FROM trade_acc_trans
        WHERE relate_type = 'OP' AND relate_no = #{orderNo} AND del_flag=0
        <choose><when test="refundNos != null and refundNos.size()>0">
            AND ((trans_no in <foreach collection="refundNos" item="refundNo" open="(" separator="," close=")">#{refundNo}</foreach>
            AND trans_type in ('OR','OS')) or is_occupy=0)
        </when><otherwise>
            AND is_occupy=0
        </otherwise></choose>
        GROUP BY org_type,org_id,org_code,sku_id
        <if test="!isZero">
            HAVING sum(trans_amt) != 0 or sum(split_amt) != 0
        </if>
    </select>

    <update id="updateInfStatus">
        update trade_acc_trans set inf_status = case when trans_amt != split_amt then 3 else 1 end,
            inf_time = case when trans_amt != split_amt then now() else null end, update_time = now()
        where trans_no = #{transNo} and del_flag = 0 and inf_status not in (1,3)
        and trans_type in <foreach collection="transTypes" item="transType" open="(" separator="," close=")">#{transType}</foreach>
        <if test="splitNos != null and splitNos.size()>0">
            and split_no in <foreach collection="splitNos" item="splitNo" open="(" separator="," close=")">#{splitNo}</foreach>
        </if>
    </update>

    <!-- 使用 split_rel_no ，因为如果全部取消时（OR OS 对冲掉了）， split_no 值可能为空 -->
    <update id="updateRefundInfStatus">
        update trade_acc_trans a, (
            select split_rel_no,sum(trans_amt) as trans_amt,sum(split_amt) as split_amt from trade_acc_trans
            where trans_no = #{transNo} and trans_type in ('OR','OS') and del_flag = 0 and inf_status not in (1,3)
        <if test="splitNos != null and splitNos.size()>0">
            and split_no in (''<foreach collection="splitNos" item="splitNo" open="," separator="," close=")">#{splitNo}</foreach>
        </if>
            group by split_rel_no
        ) b
        set a.inf_status = case when b.trans_amt != b.split_amt then 3 else 1 end, a.is_occupy = 0,
        a.inf_time = case when b.trans_amt != b.split_amt then now() else null end, update_time = now()
        where a.split_rel_no = b.split_rel_no and a.trans_no = #{transNo} and a.trans_type in ('OR','OS')
        and a.del_flag = 0 and a.inf_status not in (1,3)
    </update>

    <!-- 佣金营销计算
        营销账户    供应商    transAmt - transMaxAmt
                    佣金账户  commissionAmt - transMinAmt
    -->
    <select id="queryDiscountChecking" resultType="cn.xianlink.trade.domain.vo.TradeAccTransVo">
        SELECT trans_no,trans_date,relate_no,trans_org_id,acct_org_id,org_id,org_code,org_type,split_rel_no,
        min(trans_type) as trans_type,max(split_org_code) as split_org_code,max(split_acct_code) as split_acct_code,
        max(total_amt) as total_amt,max(inf_retries) as inf_retries,sum(trans_amt-trans_max_amt) as split_amt,
        group_concat(id separator ';') as remark,min(create_time) as create_time,max(busi_type) as busi_type
        FROM trade_acc_trans
        WHERE relate_type = 'OP' and del_flag = 0 and inf_status = 3
          and inf_retries &lt; #{infMaxRetries} and inf_time between #{bo.infTimeStart} and #{bo.infTimeEnd}
        GROUP BY trans_no,trans_date,relate_no,trans_org_id,acct_org_id,org_id,org_code,org_type,split_rel_no
        HAVING sum(trans_amt-trans_max_amt) != 0
        ORDER BY split_amt DESC
    </select>

    <!-- 测试使用， 和上面的sql返回保持一致.
         为了退款释放记录的 split_org_code， 使用按split_rel_no 分组最大 split_org_code -->
    <select id="queryDiscountTrans" resultType="cn.xianlink.trade.domain.vo.TradeAccTransVo">
        SELECT trans_no,trans_date,relate_no,trans_org_id,acct_org_id,org_id,org_code,org_type,split_rel_no,
        min(trans_type) as trans_type,max(split_org_code) as split_org_code,max(split_acct_code) as split_acct_code,
        max(total_amt) as total_amt,max(inf_retries) as inf_retries,sum(trans_amt-trans_max_amt) as split_amt,
        group_concat(id separator ';') as remark,min(create_time) as create_time,max(busi_type) as busi_type
        FROM trade_acc_trans
        WHERE trans_no = #{bo.transNo} and relate_type = 'OP' and del_flag = 0 and inf_status = 3
        <if test="bo.transType != null and bo.transType != ''">
            <choose><when test="bo.transType == 'OR'">
                AND trans_type in ('OR','OS')
            </when><otherwise>
                AND trans_type = #{bo.transType}
            </otherwise></choose>
        </if>
        <if test="bo.splitOrgCode != null and bo.splitOrgCode != ''">
            and split_org_code = #{bo.splitOrgCode}
        </if>
        GROUP BY trans_no,trans_date,relate_no,trans_org_id,acct_org_id,org_id,org_code,org_type,split_rel_no
        HAVING sum(trans_amt-trans_max_amt) != 0
    </select>

    <select id="queryOrderItemIds" resultType="cn.xianlink.trade.domain.vo.TradeAccTransVo">
        select *
        from trade_acc_trans
        where (trans_no,sku_id) in
        <foreach collection="bos" item="bo" open="(" separator="," close=")">
            (#{bo.orderCode,jdbcType=VARCHAR},#{bo.supplierSkuId,jdbcType=BIGINT})
        </foreach>
          and trans_type = 'OP'
          and del_flag = 0
    </select>
</mapper>
