<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.trade.mapper.TradeCustAccountMapper">

    <select id="queryAccountSum" resultType="cn.xianlink.trade.domain.vo.TradeCustAccountVo">
        select yda_avail_amt,freeze_amt + avail_amt as avail_amt,
        comm_freeze_amt,comm_avail_amt,comm_cash_amt,comm_check_amt,comm_expire_amt,
        comm_freeze_amt+comm_avail_amt+comm_cash_amt+comm_check_amt+comm_expire_amt as comm_total_amt,
        sala_freeze_amt,sala_avail_amt,sala_cash_amt,sala_check_amt,sala_expire_amt,sala_expected_amt,
        sala_freeze_amt+sala_avail_amt+sala_cash_amt+sala_check_amt+sala_expire_amt as sala_total_amt
        from trade_cust_account where customer_id = #{customerId} and del_flag = 0
    </select>

    <select id="queryExistAccount" resultType="cn.xianlink.trade.domain.vo.TradeCustAccountVo">
        select customer_id,freeze_amt,avail_amt
        from trade_cust_account
        where del_flag=0 and customer_id in (<foreach collection="ids" item="id" separator=",">#{id}</foreach>)
    </select>

    <update id="insertNotExistAccount">
        insert ignore into trade_cust_account (customer_id) values
        <foreach collection="vos" item="vo" separator=",">(#{vo.customerId})</foreach>
    </update>

    <select id="queryAccountAmt" resultType="cn.xianlink.trade.domain.vo.TradeCustAccountVo">
        select a.customer_id,sum(a.freeze_amt) as freeze_amt, sum(a.avail_amt) as avail_amt,max(a.comm_avail_date) as comm_avail_date,
        sum(a.comm_freeze_amt) as comm_freeze_amt,sum(a.comm_avail_amt) as comm_avail_amt,sum(a.comm_cash_amt) as comm_cash_amt,
        sum(a.comm_expire_amt) as comm_expire_amt,sum(a.comm_check_amt) as comm_check_amt,
        sum(a.sala_freeze_amt) as sala_freeze_amt,sum(a.sala_avail_amt) as sala_avail_amt,sum(a.sala_cash_amt) as sala_cash_amt,
        sum(a.sala_expire_amt) as sala_expire_amt,sum(a.sala_check_amt) as sala_check_amt,sum(a.sala_expected_amt) as sala_expected_amt
        from trade_cust_trans_acct a
        where a.id in <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
        group by a.customer_id
    </select>

    <update id="updateAccountAmt" parameterType="java.util.List">
        <foreach collection="vos" item="vo" separator=";">
        update trade_cust_account a set
            a.freeze_amt = a.freeze_amt + #{vo.freezeAmt},a.avail_amt = a.avail_amt + #{vo.availAmt},
            a.comm_freeze_amt = a.comm_freeze_amt + #{vo.commFreezeAmt},a.comm_avail_amt = a.comm_avail_amt + #{vo.commAvailAmt},
            a.comm_cash_amt = a.comm_cash_amt + #{vo.commCashAmt},a.comm_expire_amt = a.comm_expire_amt + #{vo.commExpireAmt},
            a.comm_check_amt = a.comm_check_amt + #{vo.commCheckAmt},
            a.sala_freeze_amt = a.sala_freeze_amt + #{vo.salaFreezeAmt},a.sala_avail_amt = a.sala_avail_amt + #{vo.salaAvailAmt},
            a.sala_cash_amt = a.sala_cash_amt + #{vo.salaCashAmt},a.sala_expire_amt = a.sala_expire_amt + #{vo.salaExpireAmt},
            a.sala_check_amt = a.sala_check_amt + #{vo.salaCheckAmt}, a.sala_expected_amt = a.sala_expected_amt + #{vo.salaExpectedAmt}
            <if test="vo.commAvailDate != null">,a.comm_avail_date = #{vo.commAvailDate}</if>
        where a.customer_id=#{vo.customerId} and a.del_flag=0
        </foreach>
    </update>

    <update id="insertTransAcctByCommTrans">
        insert into trade_cust_trans_acct (customer_id,trans_type,trans_id,trans_no,trans_date,
        source_type,cust_trans_id<foreach collection="updates" item="symbol" index="key" separator="">,comm_${key}_amt,sala_${key}_amt</foreach>)
        select customer_id,trans_type,trans_id,trans_no,trans_date,1,id
        <foreach collection="updates" item="symbol" index="key" separator="">,${symbol}commission_amt,${symbol}salary_amt</foreach>
        from trade_comm_trans
        where del_flag = 0 and acc_trans_id in <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
    </update>

</mapper>
