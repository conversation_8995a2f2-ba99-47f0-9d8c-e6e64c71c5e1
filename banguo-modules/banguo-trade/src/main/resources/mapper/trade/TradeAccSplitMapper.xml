<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.trade.mapper.TradeAccSplitMapper">

    <update id="updateSplitBatch" parameterType="java.util.List">
        <foreach collection="bos" item="bo" separator=";">
            update trade_acc_split set inf_time=#{bo.infTime},inf_status=#{bo.infStatus},acct_date=#{bo.acctDate},
            out_trade_no=#{bo.outTradeNo},out_success_time=#{bo.outSuccessTime} where split_no=#{bo.splitNo}
        </foreach>
    </update>

    <select id="queryPaySplitError" resultType="cn.xianlink.trade.domain.vo.TradePayVo">
        select a.order_no,a.pay_split_amt,sum(b.trans_amt) as out_cash_amt
        from trade_pay a,trade_acc_split b
        where b.trans_type = 'OP' and a.order_no = b.trans_no
        and a.del_flag = 0 and b.del_flag = 0 and a.acct_date = #{acctDate}
        group by a.order_no,a.pay_split_amt having a.pay_split_amt!=sum(b.trans_amt)
    </select>

    <select id="queryPaySplitDeleteError" resultType="cn.xianlink.trade.domain.vo.TradePayVo">
        select a.order_no,a.pay_split_amt,sum(b.trans_amt) as out_cash_amt
        from trade_pay a,trade_acc_split b
        where b.trans_type = 'OP' and a.order_no = b.trans_no and a.trade_no = b.trade_no
        and a.del_flag != 0 and b.del_flag = 0 and a.acct_date = #{acctDate}
        group by a.order_no,a.pay_split_amt having 0!=sum(b.trans_amt)
    </select>

    <select id="queryRefundSplitError" resultType="cn.xianlink.trade.domain.vo.TradePayRefundVo">
        select a.refund_no,a.refund_split_amt,sum(b.trans_amt) as out_cash_amt
        from trade_pay_refund a,trade_acc_split b
        where b.trans_type = 'OR' and a.refund_no = b.trans_no
        and a.del_flag = 0 and b.del_flag = 0 and a.acct_date = #{acctDate}
        group by a.refund_no,a.refund_split_amt having a.refund_split_amt!=sum(b.trans_amt)
    </select>

    <select id="queryRefundSplitDeleteError" resultType="cn.xianlink.trade.domain.vo.TradePayRefundVo">
        select a.refund_no,a.refund_split_amt,sum(b.trans_amt) as out_cash_amt
        from trade_pay_refund a,trade_acc_split b
        where b.trans_type = 'OR' and a.refund_no = b.trans_no
        and a.del_flag != 0 and b.del_flag = 0 and a.acct_date = #{acctDate}
        group by a.refund_no,a.refund_split_amt having 0!=sum(b.trans_amt)
    </select>


</mapper>
