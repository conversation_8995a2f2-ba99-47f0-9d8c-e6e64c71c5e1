<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.trade.mapper.TradeCustTransMapper">

    <update id="updateCustBalAmt" parameterType="java.util.List">
        <foreach collection="bos" item="bo" separator=";">
            update trade_cust_trans set avail_bal_amt=#{bo.availBalAmt} where id=#{bo.id}
        </foreach>
    </update>

</mapper>
