<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.trade.mapper.TradeAccAccountMapper">

    <select id="queryOrgAccountSum" resultType="cn.xianlink.trade.domain.vo.TradeAccAccountVo">
        select coalesce(sum(yda_freeze_amt),0) as yda_freeze_amt,coalesce(sum(yda_avail_amt),0) as yda_avail_amt,
        coalesce(sum(freeze_amt),0) as freeze_amt,coalesce(sum(freeze_comm_amt),0) as freeze_comm_amt,
        coalesce(sum(freeze_in_amt),0) as freeze_in_amt,coalesce(sum(freeze_out_amt),0) as freeze_out_amt,
        coalesce(sum(freeze_pay_amt),0) as freeze_pay_amt,coalesce(sum(freeze_refund_amt),0) as freeze_refund_amt,
        coalesce(sum(freeze_refund_amt1),0) as freeze_refund_amt1,coalesce(sum(freeze_refund_amt2),0) as freeze_refund_amt2,
        coalesce(sum(freeze_refund_amt3),0) as freeze_refund_amt3,coalesce(sum(freeze_refund_amt4),0) as freeze_refund_amt4,
        coalesce(sum(avail_amt),0) as avail_amt,coalesce(sum(avail_comm_amt),0) as avail_comm_amt,
        coalesce(sum(avail_in_amt),0) as avail_in_amt,coalesce(sum(avail_out_amt),0) as avail_out_amt,
        coalesce(sum(avail_pay_amt),0) as avail_pay_amt,coalesce(sum(avail_refund_amt),0) as avail_refund_amt,
        coalesce(sum(avail_refund_amt1),0) as avail_refund_amt1,coalesce(sum(avail_refund_amt2),0) as avail_refund_amt2,
        coalesce(sum(avail_refund_amt3),0) as avail_refund_amt3,coalesce(sum(avail_refund_amt4),0) as avail_refund_amt4,
        coalesce(sum(cash_amt),0) as cash_amt,coalesce(sum(cash_comm_amt),0) as cash_comm_amt,
        coalesce(sum(cash_in_amt),0) as cash_in_amt,coalesce(sum(cash_out_amt),0) as cash_out_amt,
        coalesce(sum(cash_pay_amt),0) as cash_pay_amt,coalesce(sum(cash_refund_amt),0) as cash_refund_amt,
        coalesce(sum(cash_refund_amt1),0) as cash_refund_amt1,coalesce(sum(cash_refund_amt2),0) as cash_refund_amt2,
        coalesce(sum(cash_refund_amt3),0) as cash_refund_amt3,coalesce(sum(cash_refund_amt4),0) as cash_refund_amt4,
        coalesce(sum(check_amt),0) as check_amt,coalesce(sum(check_comm_amt),0) as check_comm_amt,
        coalesce(sum(check_in_amt),0) as check_in_amt,coalesce(sum(check_out_amt),0) as check_out_amt,
        coalesce(sum(check_pay_amt),0) as check_pay_amt,coalesce(sum(check_refund_amt),0) as check_refund_amt,
        coalesce(sum(check_refund_amt1),0) as check_refund_amt1,coalesce(sum(check_refund_amt2),0) as check_refund_amt2,
        coalesce(sum(check_refund_amt3),0) as check_refund_amt3,coalesce(sum(check_refund_amt4),0) as check_refund_amt4
        from trade_acc_account
        where org_code = #{orgCode} and org_type = #{orgType} and del_flag = 0
        <if test="deptId != null and deptId != 0">
            and dept_id = #{deptId}
        </if>
    </select>

    <select id="customSupAvailList" resultType="cn.xianlink.trade.domain.vo.TradeAccAccountVo">
        select acct_org_id,org_type,org_id,org_code,dept_id,yda_freeze_amt,yda_avail_amt,
        freeze_amt,freeze_comm_amt,freeze_in_amt,freeze_out_amt,freeze_pay_amt,freeze_refund_amt,
        freeze_refund_amt1,freeze_refund_amt2,freeze_refund_amt3,freeze_refund_amt4,
        avail_amt,avail_comm_amt,avail_in_amt,avail_out_amt,avail_pay_amt,avail_refund_amt,
        avail_refund_amt1,avail_refund_amt2,avail_refund_amt3,avail_refund_amt4
        from trade_acc_account ${ew.customSqlSegment}
    </select>

    <select id="customSupList" resultType="cn.xianlink.trade.domain.vo.TradeAccAccountVo">
        select 2 as org_type,org_id,sum(freeze_amt) as freeze_amt,sum(freeze_comm_amt) as freeze_comm_amt,
        sum(freeze_in_amt) as freeze_in_amt,-sum(freeze_out_amt) as freeze_out_amt,
        sum(freeze_pay_amt) as freeze_pay_amt,-sum(freeze_refund_amt) as freeze_refund_amt,
        -sum(freeze_refund_amt1) as freeze_refund_amt1,-sum(freeze_refund_amt2) as freeze_refund_amt2,
        -sum(freeze_refund_amt3) as freeze_refund_amt3,-sum(freeze_refund_amt4) as freeze_refund_amt4,
        sum(avail_amt) as avail_amt,sum(avail_comm_amt) as avail_comm_amt,
        sum(avail_in_amt) as avail_in_amt,-sum(avail_out_amt) as avail_out_amt,
        sum(avail_pay_amt) as avail_pay_amt,-sum(avail_refund_amt) as avail_refund_amt,
        -sum(avail_refund_amt1) as avail_refund_amt1,-sum(avail_refund_amt2) as avail_refund_amt2,
        -sum(avail_refund_amt3) as avail_refund_amt3,-sum(avail_refund_amt4) as avail_refund_amt4,
        sum(cash_amt) as cash_amt,sum(cash_comm_amt) as cash_comm_amt,
        sum(cash_in_amt) as cash_in_amt,-sum(cash_out_amt) as cash_out_amt,
        sum(cash_pay_amt) as cash_pay_amt,-sum(cash_refund_amt) as cash_refund_amt,
        -sum(cash_refund_amt1) as cash_refund_amt1,-sum(cash_refund_amt2) as cash_refund_amt2,
        -sum(cash_refund_amt3) as cash_refund_amt3,-sum(cash_refund_amt4) as cash_refund_amt4,
        sum(check_amt) as check_amt,sum(check_comm_amt) as check_comm_amt,
        sum(check_in_amt) as check_in_amt,-sum(check_out_amt) as check_out_amt,
        sum(check_pay_amt) as check_pay_amt,-sum(check_refund_amt) as check_refund_amt,
        -sum(check_refund_amt1) as check_refund_amt1,-sum(check_refund_amt2) as check_refund_amt2,
        -sum(check_refund_amt3) as check_refund_amt3,-sum(check_refund_amt4) as check_refund_amt4
        from trade_acc_account
        ${ew.customSqlSegment}
        group by org_id order by org_id asc
    </select>

    <!-- 插入不存在的
    <update id="insertAccount">
        insert ignore into trade_acc_account (acct_org_id,org_code,org_type,dept_id,org_id,out_org_code,out_acct_code)
        select acct_org_id,org_code,org_type,dept_id,max(org_id),max(out_org_code),max(out_acct_code)
        from trade_acc_trans
        where id in <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
        group by acct_org_id,org_code,org_type,dept_id
    </update>-->

    <!-- 状态汇总
    <update id="updateAccountAmt">
        update trade_acc_account a, (select out_org_code,org_code,org_type,dept_id,acct_org_id,sum(trans_amt) as trans_amt,
            sum(case when trans_type in ('OR','OS') then trans_amt else 0 end) as refund_amt,
            <foreach collection="fields" item="field" index="index" separator=",">
                sum(case when busi_field = #{field} then trans_amt else 0 end) as trans_${field}
            </foreach>
            from trade_acc_trans
            where id in <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
            group by out_org_code,org_code,org_type,dept_id,acct_org_id) b
        set
        <foreach collection="updates" item="symbol" index="key" separator=",">
            a.${key}_amt = a.${key}_amt ${symbol} b.trans_amt, a.${key}_refund_amt = a.${key}_refund_amt ${symbol} b.refund_amt,
            <foreach collection="fields" item="field" index="index" separator=",">
                a.${key}_${field} = a.${key}_${field} ${symbol} b.trans_${field}
            </foreach>
        </foreach>
        where a.out_org_code=b.out_org_code and a.org_code=b.org_code and a.org_type=b.org_type
          and a.dept_id=b.dept_id and a.acct_org_id=b.acct_org_id
    </update>-->

    <update id="insertTransAcctByAvail">
        insert into trade_acc_trans_acct (acct_org_id,dept_id,org_id,org_code,org_type,out_org_code,out_acct_code,busi_field,
        trans_date,trans_no,trans_type,trans_id<foreach collection="updates" item="symbol" index="key" separator="">,${key}_amt,${key}_comm_amt</foreach>)
        select acct_org_id,dept_id,org_id,org_code,org_type,out_org_code,out_acct_code,busi_field,trans_date,trans_no,trans_type,trans_id
            <foreach collection="updates" item="symbol" index="key" separator="">,${symbol} trans_amt,${symbol} commission_amt</foreach>
        from trade_acc_trans
        where del_flag = 0 and avail_id in <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
    </update>

    <update id="insertTransAcctByTrans">
        insert into trade_acc_trans_acct (acct_org_id,dept_id,org_id,org_code,org_type,out_org_code,out_acct_code,busi_field,
        trans_date,trans_no,trans_type,trans_id<foreach collection="updates" item="symbol" index="key" separator="">,${key}_amt,${key}_comm_amt</foreach>)
        select acct_org_id,dept_id,org_id,org_code,org_type,out_org_code,out_acct_code,busi_field,trans_date,trans_no,trans_type,trans_id
            <foreach collection="updates" item="symbol" index="key" separator="">,${symbol} trans_amt,${symbol} commission_amt</foreach>
        from trade_acc_trans
        where del_flag = 0 and id in <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
    </update>

    <select id="queryExistAccount" resultType="cn.xianlink.trade.domain.vo.TradeAccAccountVo">
        select acct_org_id,dept_id,org_code,org_type,out_org_code,org_id,out_acct_code,freeze_amt,avail_amt,check_amt
         from trade_acc_account
        where (acct_org_id,dept_id,org_code,org_type,out_org_code) in <foreach collection="vos" item="vo" open="(" separator="," close=")">
            (#{vo.acctOrgId}, #{vo.deptId}, #{vo.orgCode}, #{vo.orgType}, #{vo.outOrgCode})</foreach>
    </select>

    <update id="insertNotExistAccount">
        insert ignore into trade_acc_account (acct_org_id,dept_id,org_code,org_type,out_org_code,org_id,out_acct_code) values
        <foreach collection="vos" item="vo" separator=",">
        (#{vo.acctOrgId}, #{vo.deptId}, #{vo.orgCode}, #{vo.orgType}, #{vo.outOrgCode}, #{vo.orgId}, #{vo.outAcctCode})
        </foreach>
    </update>

    <select id="_queryAccountAmt" resultType="cn.xianlink.trade.domain.vo.TradeAccAccountVo">
        select acct_org_id, dept_id, org_code, org_type, out_org_code,
        sum(freeze_amt-freeze_comm_amt) as freeze_amt, sum(avail_amt-avail_comm_amt) as avail_amt,
        sum(cash_amt-cash_comm_amt) as cash_amt, sum(check_amt-check_comm_amt) as check_amt,
        sum(-freeze_comm_amt) as freeze_comm_amt, sum(-avail_comm_amt) as avail_comm_amt, sum(-cash_comm_amt) as cash_comm_amt, sum(-check_comm_amt) as check_comm_amt,
        sum(case when busi_field in ('refund_amt1', 'refund_amt2', 'refund_amt3', 'refund_amt4') then freeze_amt else 0 end) as freeze_refund_amt,
        sum(case when busi_field in ('refund_amt1', 'refund_amt2', 'refund_amt3', 'refund_amt4') then avail_amt else 0 end) as avail_refund_amt,
        sum(case when busi_field in ('refund_amt1', 'refund_amt2', 'refund_amt3', 'refund_amt4') then cash_amt else 0 end) as cash_refund_amt,
        sum(case when busi_field in ('refund_amt1', 'refund_amt2', 'refund_amt3', 'refund_amt4') then check_amt else 0 end) as check_refund_amt,
        sum(case when busi_field = 'pay_amt' then freeze_amt else 0 end) as freeze_pay_amt,
        sum(case when busi_field = 'pay_amt' then avail_amt else 0 end) as avail_pay_amt,
        sum(case when busi_field = 'pay_amt' then cash_amt else 0 end) as cash_pay_amt,
        sum(case when busi_field = 'pay_amt' then check_amt else 0 end) as check_pay_amt,
        sum(case when busi_field = 'refund_amt1' then freeze_amt else 0 end) as freeze_refund_amt1,
        sum(case when busi_field = 'refund_amt1' then avail_amt else 0 end) as avail_refund_amt1,
        sum(case when busi_field = 'refund_amt1' then cash_amt else 0 end) as cash_refund_amt1,
        sum(case when busi_field = 'refund_amt1' then check_amt else 0 end) as check_refund_amt1,
        sum(case when busi_field = 'refund_amt2' then freeze_amt else 0 end) as freeze_refund_amt2,
        sum(case when busi_field = 'refund_amt2' then avail_amt else 0 end) as avail_refund_amt2,
        sum(case when busi_field = 'refund_amt2' then cash_amt else 0 end) as cash_refund_amt2,
        sum(case when busi_field = 'refund_amt2' then check_amt else 0 end) as check_refund_amt2,
        sum(case when busi_field = 'refund_amt3' then freeze_amt else 0 end) as freeze_refund_amt3,
        sum(case when busi_field = 'refund_amt3' then avail_amt else 0 end) as avail_refund_amt3,
        sum(case when busi_field = 'refund_amt3' then cash_amt else 0 end) as cash_refund_amt3,
        sum(case when busi_field = 'refund_amt3' then check_amt else 0 end) as check_refund_amt3,
        sum(case when busi_field = 'refund_amt4' then freeze_amt else 0 end) as freeze_refund_amt4,
        sum(case when busi_field = 'refund_amt4' then avail_amt else 0 end) as avail_refund_amt4,
        sum(case when busi_field = 'refund_amt4' then cash_amt else 0 end) as cash_refund_amt4,
        sum(case when busi_field = 'refund_amt4' then check_amt else 0 end) as check_refund_amt4,
        sum(case when busi_field = 'in_amt' then freeze_amt else 0 end) as freeze_in_amt,
        sum(case when busi_field = 'in_amt' then avail_amt else 0 end) as avail_in_amt,
        sum(case when busi_field = 'in_amt' then cash_amt else 0 end) as cash_in_amt,
        sum(case when busi_field = 'in_amt' then check_amt else 0 end) as check_in_amt,
        sum(case when busi_field = 'out_amt' then freeze_amt else 0 end) as freeze_out_amt,
        sum(case when busi_field = 'out_amt' then avail_amt else 0 end) as avail_out_amt,
        sum(case when busi_field = 'out_amt' then cash_amt else 0 end) as cash_out_amt,
        sum(case when busi_field = 'out_amt' then check_amt else 0 end) as check_out_amt
        from trade_acc_trans_acct
        where (org_type=2 or org_code in (<foreach collection="virtuals" item="id" separator=",">#{id}</foreach>))
        and id in (<foreach collection="ids" item="id" separator=",">#{id}</foreach>)
        group by acct_org_id, dept_id, org_code, org_type, out_org_code
    </select>

    <update id="_updateAccountAmt" parameterType="java.util.List">
        <foreach collection="vos" item="vo" separator=";">
        update trade_acc_account a set
        a.freeze_amt = a.freeze_amt + #{vo.freezeAmt},
        a.avail_amt = a.avail_amt + #{vo.availAmt},
        a.cash_amt = a.cash_amt + #{vo.cashAmt},
        a.check_amt = a.check_amt + #{vo.checkAmt},
        a.freeze_comm_amt = a.freeze_comm_amt + #{vo.freezeCommAmt},
        a.avail_comm_amt = a.avail_comm_amt + #{vo.availCommAmt},
        a.cash_comm_amt = a.cash_comm_amt + #{vo.cashCommAmt},
        a.check_comm_amt = a.check_comm_amt + #{vo.checkCommAmt},
        a.freeze_refund_amt = a.freeze_refund_amt + #{vo.freezeRefundAmt},
        a.avail_refund_amt = a.avail_refund_amt + #{vo.availRefundAmt},
        a.cash_refund_amt = a.cash_refund_amt + #{vo.cashRefundAmt},
        a.check_refund_amt = a.check_refund_amt + #{vo.checkRefundAmt},
        a.freeze_pay_amt = a.freeze_pay_amt + #{vo.freezePayAmt},
        a.avail_pay_amt = a.avail_pay_amt + #{vo.availPayAmt},
        a.cash_pay_amt = a.cash_pay_amt + #{vo.cashPayAmt},
        a.check_pay_amt = a.check_pay_amt + #{vo.checkPayAmt},
        a.freeze_refund_amt1 = a.freeze_refund_amt1 + #{vo.freezeRefundAmt1},
        a.avail_refund_amt1 = a.avail_refund_amt1 + #{vo.availRefundAmt1},
        a.cash_refund_amt1 = a.cash_refund_amt1 + #{vo.cashRefundAmt1},
        a.check_refund_amt1 = a.check_refund_amt1 + #{vo.checkRefundAmt1},
        a.freeze_refund_amt2 = a.freeze_refund_amt2 + #{vo.freezeRefundAmt2},
        a.avail_refund_amt2 = a.avail_refund_amt2 + #{vo.availRefundAmt2},
        a.cash_refund_amt2 = a.cash_refund_amt2 + #{vo.cashRefundAmt2},
        a.check_refund_amt2 = a.check_refund_amt2 + #{vo.checkRefundAmt2},
        a.freeze_refund_amt3 = a.freeze_refund_amt3 + #{vo.freezeRefundAmt3},
        a.avail_refund_amt3 = a.avail_refund_amt3 + #{vo.availRefundAmt3},
        a.cash_refund_amt3 = a.cash_refund_amt3 + #{vo.cashRefundAmt3},
        a.check_refund_amt3 = a.check_refund_amt3 + #{vo.checkRefundAmt3},
        a.freeze_refund_amt4 = a.freeze_refund_amt4 + #{vo.freezeRefundAmt4},
        a.avail_refund_amt4 = a.avail_refund_amt4 + #{vo.availRefundAmt4},
        a.cash_refund_amt4 = a.cash_refund_amt4 + #{vo.cashRefundAmt4},
        a.check_refund_amt4 = a.check_refund_amt4 + #{vo.checkRefundAmt4},
        a.freeze_in_amt = a.freeze_in_amt + #{vo.freezeInAmt},
        a.avail_in_amt = a.avail_in_amt + #{vo.availInAmt},
        a.cash_in_amt = a.cash_in_amt + #{vo.cashInAmt},
        a.check_in_amt = a.check_in_amt + #{vo.checkInAmt},
        a.freeze_out_amt = a.freeze_out_amt + #{vo.freezeOutAmt},
        a.avail_out_amt = a.avail_out_amt + #{vo.availOutAmt},
        a.cash_out_amt = a.cash_out_amt + #{vo.cashOutAmt},
        a.check_out_amt = a.check_out_amt + #{vo.checkOutAmt}
        where a.acct_org_id=#{vo.acctOrgId} and a.dept_id=#{vo.deptId} and a.org_code=#{vo.orgCode}
        and a.org_type=#{vo.orgType} and a.out_org_code=#{vo.outOrgCode}
        </foreach>
    </update>

    <update id="updateAccountAmt">
        update trade_acc_account a, (select acct_org_id,dept_id,org_code,org_type,out_org_code,
        sum(freeze_amt-freeze_comm_amt) as freeze_amt, sum(avail_amt-avail_comm_amt) as avail_amt,
        sum(cash_amt-cash_comm_amt) as cash_amt, sum(check_amt-check_comm_amt) as check_amt,
        sum(-freeze_comm_amt) as freeze_comm_amt, sum(-avail_comm_amt) as avail_comm_amt, sum(-cash_comm_amt) as cash_comm_amt, sum(-check_comm_amt) as check_comm_amt,
        sum(case when busi_field in ('refund_amt1', 'refund_amt2', 'refund_amt3', 'refund_amt4') then freeze_amt else 0 end) as freeze_refund_amt,
        sum(case when busi_field in ('refund_amt1', 'refund_amt2', 'refund_amt3', 'refund_amt4') then avail_amt else 0 end) as avail_refund_amt,
        sum(case when busi_field in ('refund_amt1', 'refund_amt2', 'refund_amt3', 'refund_amt4') then cash_amt else 0 end) as cash_refund_amt,
        sum(case when busi_field in ('refund_amt1', 'refund_amt2', 'refund_amt3', 'refund_amt4') then check_amt else 0 end) as check_refund_amt,
        sum(case when busi_field = 'pay_amt' then freeze_amt else 0 end) as freeze_pay_amt,
        sum(case when busi_field = 'pay_amt' then avail_amt else 0 end) as avail_pay_amt,
        sum(case when busi_field = 'pay_amt' then cash_amt else 0 end) as cash_pay_amt,
        sum(case when busi_field = 'pay_amt' then check_amt else 0 end) as check_pay_amt,
        sum(case when busi_field = 'refund_amt1' then freeze_amt else 0 end) as freeze_refund_amt1,
        sum(case when busi_field = 'refund_amt1' then avail_amt else 0 end) as avail_refund_amt1,
        sum(case when busi_field = 'refund_amt1' then cash_amt else 0 end) as cash_refund_amt1,
        sum(case when busi_field = 'refund_amt1' then check_amt else 0 end) as check_refund_amt1,
        sum(case when busi_field = 'refund_amt2' then freeze_amt else 0 end) as freeze_refund_amt2,
        sum(case when busi_field = 'refund_amt2' then avail_amt else 0 end) as avail_refund_amt2,
        sum(case when busi_field = 'refund_amt2' then cash_amt else 0 end) as cash_refund_amt2,
        sum(case when busi_field = 'refund_amt2' then check_amt else 0 end) as check_refund_amt2,
        sum(case when busi_field = 'refund_amt3' then freeze_amt else 0 end) as freeze_refund_amt3,
        sum(case when busi_field = 'refund_amt3' then avail_amt else 0 end) as avail_refund_amt3,
        sum(case when busi_field = 'refund_amt3' then cash_amt else 0 end) as cash_refund_amt3,
        sum(case when busi_field = 'refund_amt3' then check_amt else 0 end) as check_refund_amt3,
        sum(case when busi_field = 'refund_amt4' then freeze_amt else 0 end) as freeze_refund_amt4,
        sum(case when busi_field = 'refund_amt4' then avail_amt else 0 end) as avail_refund_amt4,
        sum(case when busi_field = 'refund_amt4' then cash_amt else 0 end) as cash_refund_amt4,
        sum(case when busi_field = 'refund_amt4' then check_amt else 0 end) as check_refund_amt4,
        sum(case when busi_field = 'in_amt' then freeze_amt else 0 end) as freeze_in_amt,
        sum(case when busi_field = 'in_amt' then avail_amt else 0 end) as avail_in_amt,
        sum(case when busi_field = 'in_amt' then cash_amt else 0 end) as cash_in_amt,
        sum(case when busi_field = 'in_amt' then check_amt else 0 end) as check_in_amt,
        sum(case when busi_field = 'out_amt' then freeze_amt else 0 end) as freeze_out_amt,
        sum(case when busi_field = 'out_amt' then avail_amt else 0 end) as avail_out_amt,
        sum(case when busi_field = 'out_amt' then cash_amt else 0 end) as cash_out_amt,
        sum(case when busi_field = 'out_amt' then check_amt else 0 end) as check_out_amt
        from trade_acc_trans_acct
        where (org_type=2 or org_code in (<foreach collection="virtuals" item="id" separator=",">#{id}</foreach>))
        and id in (<foreach collection="ids" item="id" separator=",">#{id}</foreach>)
        group by acct_org_id,dept_id,org_code,org_type,out_org_code) b set
        a.freeze_amt = a.freeze_amt + b.freeze_amt,
        a.avail_amt = a.avail_amt + b.avail_amt,
        a.cash_amt = a.cash_amt + b.cash_amt,
        a.check_amt = a.check_amt + b.check_amt,
        a.freeze_comm_amt = a.freeze_comm_amt + b.freeze_comm_amt,
        a.avail_comm_amt = a.avail_comm_amt + b.avail_comm_amt,
        a.cash_comm_amt = a.cash_comm_amt + b.cash_comm_amt,
        a.check_comm_amt = a.check_comm_amt + b.check_comm_amt,
        a.freeze_refund_amt = a.freeze_refund_amt + b.freeze_refund_amt,
        a.avail_refund_amt = a.avail_refund_amt + b.avail_refund_amt,
        a.cash_refund_amt = a.cash_refund_amt + b.cash_refund_amt,
        a.check_refund_amt = a.check_refund_amt + b.check_refund_amt,
        a.freeze_pay_amt = a.freeze_pay_amt + b.freeze_pay_amt,
        a.avail_pay_amt = a.avail_pay_amt + b.avail_pay_amt,
        a.cash_pay_amt = a.cash_pay_amt + b.cash_pay_amt,
        a.check_pay_amt = a.check_pay_amt + b.check_pay_amt,
        a.freeze_refund_amt1 = a.freeze_refund_amt1 + b.freeze_refund_amt1,
        a.avail_refund_amt1 = a.avail_refund_amt1 + b.avail_refund_amt1,
        a.cash_refund_amt1 = a.cash_refund_amt1 + b.cash_refund_amt1,
        a.check_refund_amt1 = a.check_refund_amt1 + b.check_refund_amt1,
        a.freeze_refund_amt2 = a.freeze_refund_amt2 + b.freeze_refund_amt2,
        a.avail_refund_amt2 = a.avail_refund_amt2 + b.avail_refund_amt2,
        a.cash_refund_amt2 = a.cash_refund_amt2 + b.cash_refund_amt2,
        a.check_refund_amt2 = a.check_refund_amt2 + b.check_refund_amt2,
        a.freeze_refund_amt3 = a.freeze_refund_amt3 + b.freeze_refund_amt3,
        a.avail_refund_amt3 = a.avail_refund_amt3 + b.avail_refund_amt3,
        a.cash_refund_amt3 = a.cash_refund_amt3 + b.cash_refund_amt3,
        a.check_refund_amt3 = a.check_refund_amt3 + b.check_refund_amt3,
        a.freeze_refund_amt4 = a.freeze_refund_amt4 + b.freeze_refund_amt4,
        a.avail_refund_amt4 = a.avail_refund_amt4 + b.avail_refund_amt4,
        a.cash_refund_amt4 = a.cash_refund_amt4 + b.cash_refund_amt4,
        a.check_refund_amt4 = a.check_refund_amt4 + b.check_refund_amt4,
        a.freeze_in_amt = a.freeze_in_amt + b.freeze_in_amt,
        a.avail_in_amt = a.avail_in_amt + b.avail_in_amt,
        a.cash_in_amt = a.cash_in_amt + b.cash_in_amt,
        a.check_in_amt = a.check_in_amt + b.check_in_amt,
        a.freeze_out_amt = a.freeze_out_amt + b.freeze_out_amt,
        a.avail_out_amt = a.avail_out_amt + b.avail_out_amt,
        a.cash_out_amt = a.cash_out_amt + b.cash_out_amt,
        a.check_out_amt = a.check_out_amt + b.check_out_amt
        where a.acct_org_id=b.acct_org_id and a.dept_id=b.dept_id and a.org_code=b.org_code
        and a.org_type=b.org_type and a.out_org_code=b.out_org_code
    </update>

</mapper>
