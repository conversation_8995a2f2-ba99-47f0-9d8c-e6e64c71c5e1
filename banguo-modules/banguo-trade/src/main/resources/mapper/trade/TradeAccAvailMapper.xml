<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.trade.mapper.TradeAccAvailMapper">

    <select id="queryCashAmt" resultType="cn.xianlink.trade.domain.vo.TradeAccAvailVo">
        select coalesce(sum(avail_amt),0) as avail_amt,coalesce(sum(commission_amt),0) as commission_amt,
            count(id) as id,concat(';',group_concat(distinct acct_org_id separator ';'), ';') as avail_no,
            concat(';',group_concat(distinct dept_id separator ';'), ';') as cash_no
        from trade_acc_avail
        where cash_id = #{cashId} and del_flag = 0
    </select>

    <update id="updateAvailAmt">
        update trade_acc_avail a, (select
            max(trans_no) as trans_no,sum(trans_amt-commission_amt) as trans_amt,sum(-commission_amt) as commission_amt,
            sum(case when trans_type in ('OR','OS') then trans_amt else 0 end) as refund_amt,
            <foreach collection="fields" item="field" index="index" separator=",">
                sum(case when busi_field = #{field} then trans_amt else 0 end) as ${field}
            </foreach>
            from trade_acc_trans where avail_id = #{availId} and del_flag = 0) b
        set a.priority=case when coalesce(b.trans_amt,0)&lt;0 then 1 else 0 end,
            a.relate_no=case when a.is_auto=0 then coalesce(b.trans_no,'') else '' end,
            a.avail_amt = coalesce(b.trans_amt,0), a.commission_amt = coalesce(b.commission_amt,0),
            a.refund_amt = coalesce(b.refund_amt,0), a.update_time = now(),
        <foreach collection="fields" item="field" index="index" separator=",">
            a.${field} = coalesce(b.${field},0)
        </foreach>
        where a.id = #{availId}
    </update>

</mapper>
