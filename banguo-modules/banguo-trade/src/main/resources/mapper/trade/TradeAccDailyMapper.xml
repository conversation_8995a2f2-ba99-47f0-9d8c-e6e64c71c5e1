<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.trade.mapper.TradeAccDailyMapper">

    <select id="queryDailyAmt" resultType="cn.xianlink.trade.domain.vo.org.TradeAccDailyAmtVo">
        select out_org_code,acct_date,
        sum(case when trans_type='DI' then trans_amt else 0 end) market_pay_amt,
        sum(case when trans_type='DO' then -trans_amt else 0 end) market_refund_amt,
        sum(case when trans_type='OP' then trans_amt else 0 end) pay_amt,
        sum(case when trans_type='OR' then -trans_amt else 0 end) refund_amt,
        sum(case when trans_type='OP' then commission_amt else 0 end) comm_pay_amt,
        sum(case when trans_type='OR' then -commission_amt else 0 end) comm_refund_amt,
        sum(case when trans_type='OR' and busi_type in ('RC','RP') then -trans_amt else 0 end) refund_amt3,
        sum(case when trans_type='OR' and busi_type = 'RB' then -trans_amt else 0 end) refund_amt2,
        sum(case when trans_type='TO' then -trans_amt else 0 end) as out_amt,
        sum(case when trans_type='TI' then trans_amt else 0 end) as in_amt,
        sum(case when trans_type='CH' then -trans_amt else 0 end) as cash_amt,
        sum(case when trans_type='CH' then fee_amt else 0 end) as cash_fee_amt,
        sum(case when trans_type='CI' then trans_amt else 0 end) + sum(case when trans_type='CO' then -trans_amt else 0 end) as market_amt
        from trade_acc_split
        where del_flag=0 and inf_status=1 and acct_date = #{acctDate} group by out_org_code,acct_date
    </select>

    <update id="insertBatch" >
        insert into trade_acc_daily (bank_id,acct_date,source_org_id,source_org_code,out_org_type,out_org_code,out_org_name,out_acct_code)
        select id,#{acctDate},source_org_id,source_org_code,out_org_type,out_org_code,out_org_name,out_acct_code
        from trade_org_bank a
        where del_flag=0 and out_org_type!=7 and out_org_code in (<foreach collection="outOrgCodes" item="code" separator=",">#{code}</foreach>)
    </update>

</mapper>
