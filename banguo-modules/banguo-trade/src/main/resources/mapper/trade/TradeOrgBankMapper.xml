<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.trade.mapper.TradeOrgBankMapper">

    <!-- 平安银行端余额（刷新后的 bank_avail_amt） = freeze_amt + avail_amt + check_amt - freeze_transfer_amt -->
    <select id="customList" resultType="cn.xianlink.trade.domain.vo.org.TradeOrgBankAmtVo">
        select a.id,a.out_org_type,a.out_org_code,a.out_org_name,a.out_acct_code,a.bank_mobile,a.bank_account,
            a.yda_bank_avail_amt,a.bank_avail_amt,a.bank_cash_amt,a.bank_amt_time,a.status,
            sum(coalesce(b.yda_freeze_amt,0)) as yda_freeze_amt,sum(coalesce(b.yda_avail_amt,0)) as yda_avail_amt,
            sum(coalesce(b.yda_acct_avail_amt,0)) as yda_acct_avail_amt,
            sum(coalesce(b.freeze_amt,0)) as freeze_amt,sum(coalesce(b.avail_amt,0)) as avail_amt,
            sum(coalesce(b.freeze_amt+b.avail_amt+b.check_amt-b.freeze_in_amt-b.freeze_out_amt,0)) as acct_avail_amt
        from trade_org_bank a left join trade_acc_account b on a.out_org_code = b.out_org_code
        where a.del_flag = 0
        <if test="qbo.outOrgType != null">
            AND a.out_org_type = #{qbo.outOrgType}
        </if>
        <if test="qbo.outOrgCodes != null and qbo.outOrgCodes.size()>0">
            AND a.out_org_code in <foreach collection="qbo.outOrgCodes" item="code" open="(" separator="," close=")">#{code,jdbcType=VARCHAR}</foreach>
        </if>
        <if test="qbo.outAcctCode != null and qbo.outAcctCode != ''">
            AND a.out_acct_code = #{qbo.outAcctCode,jdbcType=VARCHAR}
        </if>
        <if test="qbo.status != null">
            AND a.status = #{qbo.status}
        </if>
        group by a.id,a.out_org_type,a.out_org_code,a.out_org_name,a.out_acct_code,a.bank_mobile,a.bank_account,
        a.yda_bank_avail_amt,a.bank_avail_amt,a.bank_cash_amt,a.bank_amt_time,a.status
        order by a.id desc
    </select>

    <select id="customCustList" resultType="cn.xianlink.trade.domain.vo.org.TradeOrgBankAmtVo">
        select a.id,a.out_org_type,a.out_org_code,a.out_org_name,a.out_acct_code,
        a.yda_bank_avail_amt,a.bank_avail_amt,a.bank_cash_amt,a.bank_amt_time,a.status,
        sum(coalesce(b.yda_avail_amt,0)) as yda_acct_avail_amt,sum(coalesce(b.freeze_amt+b.avail_amt,0)) as acct_avail_amt
        from trade_org_bank a left join trade_cust_account b on a.source_org_id = b.customer_id
        where a.del_flag = 0 and a.out_org_type = 7
        <if test="qbo.outOrgCodes != null and qbo.outOrgCodes.size()>0">
            AND a.out_org_code in <foreach collection="qbo.outOrgCodes" item="code" open="(" separator="," close=")">#{code,jdbcType=VARCHAR}</foreach>
        </if>
        <if test="qbo.outAcctCode != null and qbo.outAcctCode != ''">
            AND a.out_acct_code = #{qbo.outAcctCode,jdbcType=VARCHAR}
        </if>
        <if test="qbo.status != null">
            AND a.status = #{qbo.status}
        </if>
        group by a.id,a.out_org_type,a.out_org_code,a.out_org_name,a.out_acct_code,
        a.yda_bank_avail_amt,a.bank_avail_amt,a.bank_cash_amt,a.bank_amt_time,a.status
        order by a.id desc
    </select>

    <update id="updateBankAmtBatch" parameterType="java.util.List">
        <foreach collection="bos" item="bo" separator=";">
            update trade_org_bank set yda_bank_avail_amt = #{bo.balanceAmt} where out_org_code = #{bo.outOrgCode}
        </foreach>
    </update>

</mapper>
