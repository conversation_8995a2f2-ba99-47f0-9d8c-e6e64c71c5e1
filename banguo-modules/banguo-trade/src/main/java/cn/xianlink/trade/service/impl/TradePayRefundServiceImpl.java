package cn.xianlink.trade.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.api.util.BaseEntityAutoFill;
import cn.xianlink.trade.api.domain.bo.OrderPayJobQueryBo;
import cn.xianlink.trade.api.domain.bo.OrderRefundBo;
import cn.xianlink.trade.comm.CommServiceHelper;
import cn.xianlink.trade.config.properties.ChannelsProperties;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.TradeAccTrans;
import cn.xianlink.trade.domain.TradePay;
import cn.xianlink.trade.domain.TradePayRefund;
import cn.xianlink.trade.domain.bo.*;
import cn.xianlink.trade.domain.vo.TradeAccTransVo;
import cn.xianlink.trade.domain.vo.TradePayRefundPageVo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;
import cn.xianlink.trade.mapper.TradeAccTransMapper;
import cn.xianlink.trade.mapper.TradePayMapper;
import cn.xianlink.trade.mapper.TradePayRefundMapper;
import cn.xianlink.trade.service.*;
import cn.xianlink.trade.service.biz.TradeBaseDataBizService;
import cn.xianlink.trade.service.biz.TradeBaseUtilBizService;
import cn.xianlink.trade.utils.MapstructPageUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 支付退款Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class TradePayRefundServiceImpl implements ITradePayRefundService {

    private final transient ChannelsProperties channelsProperties;
    private final transient TradePayRefundMapper baseMapper;
    private final transient TradePayMapper tradePayMapper;
    private final transient TradeAccTransMapper tradeAccTransMapper;
    private final transient ITradeAccSplitService tradeAccSplitService;
    private final transient ITradeAccTransService tradeAccTransService;
    private final transient ITradeCustTransService tradeCustTransService;
    private final transient ITradeOrgRelationService tradeOrgRelationService;
    private final transient TradeBaseUtilBizService tradeBaseUtilBizService;
    private final transient TradeBaseDataBizService tradeBaseDataBizService;
    private final transient CommServiceHelper commServiceHelper;
    private transient final List<Integer> RefundCheckStatusList = Arrays.asList(RefundInfStatusEnum.CHECK.getCode(), RefundInfStatusEnum.PROCESSING.getCode());

    @Override
    public List<TradePayRefundVo> queryRelByNo(String orderNo, String refundNo) {
        return baseMapper.selectVoList(Wrappers.lambdaQuery(TradePayRefund.class)
                // .and(q -> q.eq(TradePayRefund::getRefundRelNo, refundNo).or().eq(TradePayRefund::getRefundNo, refundNo))
                .eq(TradePayRefund::getRefundRelNo, refundNo)
                .eq(TradePayRefund::getOrderNo, orderNo).eq(TradePayRefund::getDelFlag, 0)
                .orderByAsc(TradePayRefund::getRefundNo));
    }

    @Override
    public TradePayRefundVo queryByTradeNo(String tradeRefundNo) {
        return baseMapper.selectVoOne(Wrappers.lambdaQuery(TradePayRefund.class)
                .eq(TradePayRefund::getTradeRefundNo, tradeRefundNo));
    }
    /**
     * 查询退款
     */
    @Override
    public List<TradePayRefundVo> queryByTradeNos(List<String> tradeNos) {
        if (CollectionUtil.isEmpty(tradeNos)) {
            return new ArrayList<>();
        }
        return baseMapper.selectVoList(Wrappers.lambdaQuery(TradePayRefund.class)
                .select(TradePayRefund::getOrderNo, TradePayRefund::getRefundNo, TradePayRefund::getOrderDate, TradePayRefund::getRefundDate,
                        TradePayRefund::getTradeNo, TradePayRefund::getTradeRefundNo, TradePayRefund::getOutSuccessTime, TradePayRefund::getOutChannelNo,
                        TradePayRefund::getRefundAmt, TradePayRefund::getRefundSplitAmt, TradePayRefund::getOutFeeAmt, TradePayRefund::getBusiType,
                        TradePayRefund::getRefundInfStatus, TradePayRefund::getInfStatus, TradePayRefund::getStatus, TradePayRefund::getDelFlag)
                .in(TradePayRefund::getTradeRefundNo, tradeNos));
    }

    /**
     * 查询退款未完成
     */
    @Override
    public List<TradePayRefundVo> queryRefundProcessing(OrderPayJobQueryBo bo) {
        LambdaQueryWrapper<TradePayRefund> lqw = Wrappers.lambdaQuery();
        lqw.between(TradePayRefund::getInfTime, bo.getInfTimeStart(), bo.getInfTimeEnd());
        lqw.eq(TradePayRefund::getRefundInfStatus, RefundInfStatusEnum.PROCESSING.getCode());
        lqw.le(TradePayRefund::getRefundInfTime, DateUtil.offsetSecond(new Date(), -10));
        lqw.eq(TradePayRefund::getDelFlag, 0);
        lqw.orderByAsc(TradePayRefund::getId);
        return baseMapper.selectVoList(lqw);
    }
    /**
     * 查询退款未审核
     */
    @Override
    public List<TradePayRefundVo> queryRefundChecking(OrderPayJobQueryBo bo) {
        LambdaQueryWrapper<TradePayRefund> lqw = Wrappers.lambdaQuery(TradePayRefund.class)
                .select(TradePayRefund::getId, TradePayRefund::getChannel, TradePayRefund::getAppId,
                        TradePayRefund::getRefundNo, TradePayRefund::getOrderNo, TradePayRefund::getInfAuto,
                        TradePayRefund::getRefundInfStatus, TradePayRefund::getInfStatus, TradePayRefund::getStatus);
        lqw.between(TradePayRefund::getInfTime, bo.getInfTimeStart(), bo.getInfTimeEnd());
        Integer infRetries = Math.max(bo.getInfRetries(),
                bo.getLongTime() ? channelsProperties.getInfMaxRetries():channelsProperties.getInfMinRetries());
        // 1 未超过执行次数， 或 执行中的
        lqw.and(l ->
            l.or(ll -> ll.lt(TradePayRefund::getInfRetries, infRetries).lt(TradePayRefund::getRefundInfRetries, infRetries))
            .or(ll -> ll.eq(TradePayRefund::getRefundInfStatus, RefundInfStatusEnum.PROCESSING.getCode()))
        );
        // 2 是自动 或 分帐已经退完成的
        lqw.and(l -> l.or(ll -> ll.eq(TradePayRefund::getInfAuto, bo.getInfAuto()))
                .or(ll -> ll.eq(TradePayRefund::getInfStatus, RefundInfStatusEnum.SUCCESS.getCode()))
        );
        // 3 是自动 或 分帐已经退完成的
        lqw.and(l ->
                l.or(ll -> ll.in(TradePayRefund::getInfStatus, RefundCheckStatusList))
                .or(ll -> ll.in(TradePayRefund::getRefundInfStatus, RefundCheckStatusList))
                .or(ll -> ll.eq(TradePayRefund::getInfStatus, RefundInfStatusEnum.SUCCESS.getCode())
                        .eq(TradePayRefund::getRefundInfStatus, RefundInfStatusEnum.FAIL.getCode()))
        );
        lqw.eq(TradePayRefund::getDelFlag, 0);
        lqw.orderByAsc(TradePayRefund::getId);
        return baseMapper.selectVoList(lqw);
    }
    /**
     * 查询退款未审核
     */
    @Override
    public List<TradePayRefundVo> queryRefundFailed(LocalDate transDate) {
        LambdaQueryWrapper<TradePayRefund> lqw = getRefundFailedWrapper(transDate);
        lqw.ne(TradePayRefund::getChannel, PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode());
        List<TradePayRefundVo> list = baseMapper.selectVoList(lqw);
        // 大额支付的退款是 T + 1， 所以第二日进行验证
        LambdaQueryWrapper<TradePayRefund> lqwLarge = getRefundFailedWrapper(transDate.plusDays(-1));
        lqwLarge.eq(TradePayRefund::getChannel, PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode());
        list.addAll(baseMapper.selectVoList(lqwLarge));
        return list;
    }

    private LambdaQueryWrapper<TradePayRefund> getRefundFailedWrapper(LocalDate transDate) {
        LambdaQueryWrapper<TradePayRefund> lqw = Wrappers.lambdaQuery();
        lqw.between(TradePayRefund::getRefundDate, transDate.plusDays(-1), transDate);
        lqw.ne(TradePayRefund::getIsOccupy, YNStatusEnum.ENABLE.getCode()); // 占用状态的不比较
        lqw.eq(TradePayRefund::getInfAuto, YNStatusEnum.ENABLE.getCode());
        lqw.and(l -> l.ne(TradePayRefund::getInfStatus, RefundInfStatusEnum.SUCCESS.getCode())
                .or(ll -> ll.ne(TradePayRefund::getRefundInfStatus, RefundInfStatusEnum.SUCCESS.getCode())));
        lqw.eq(TradePayRefund::getDelFlag, 0);
        return lqw;
    }

    @Override
    @BaseEntityAutoFill
    public TableDataInfo<TradePayRefundPageVo> queryPageList(TradePayRefundQueryBo bo) {
        LambdaQueryWrapper<TradePayRefund> lqw = buildRefundQueryWrapper(bo);
        Page<TradePayRefundVo> result = baseMapper.selectVoPage(bo.build(), lqw);
        return TableDataInfo.build(MapstructPageUtils.convert(result, TradePayRefundPageVo.class));
    }

    @Override
    @BaseEntityAutoFill
    public List<TradePayRefundPageVo> queryList(TradePayRefundQueryBo bo) {
        LambdaQueryWrapper<TradePayRefund> lqw = buildRefundQueryWrapper(bo);
        return MapstructUtils.convert(baseMapper.selectVoList(lqw), TradePayRefundPageVo.class);
    }

    private LambdaQueryWrapper<TradePayRefund> buildRefundQueryWrapper(TradePayRefundQueryBo bo) {
        boolean isOrderDate = bo.getOrderDateStart() != null && bo.getOrderDateEnd() != null;
        boolean isAcctDate = bo.getAcctDateStart() != null && bo.getAcctDateEnd() != null;
        if (!isOrderDate && !isAcctDate) {
            throw new ServiceException("销售日期或记账日期必须输入一组");
        }
        LambdaQueryWrapper<TradePayRefund> lqw = Wrappers.lambdaQuery();
        lqw.between(isOrderDate, TradePayRefund::getOrderDate, bo.getOrderDateStart(), bo.getOrderDateEnd());
        lqw.between(isAcctDate, TradePayRefund::getAcctDate, bo.getAcctDateStart(), bo.getAcctDateEnd());
        lqw.eq(StringUtils.isNotBlank(bo.getChannel()), TradePayRefund::getChannel, bo.getChannel());
        lqw.eq(StringUtils.isNotBlank(bo.getBusiType()), TradePayRefund::getBusiType, bo.getBusiType());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), TradePayRefund::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getRefundNo()), TradePayRefund::getRefundNo, bo.getRefundNo());
        lqw.eq(StringUtils.isNotBlank(bo.getTradeNo()), TradePayRefund::getTradeNo, bo.getTradeNo());
        lqw.eq(StringUtils.isNotBlank(bo.getTradeRefundNo()), TradePayRefund::getTradeRefundNo, bo.getTradeRefundNo());
        lqw.eq(StringUtils.isNotBlank(bo.getBusiType()), TradePayRefund::getBusiType, bo.getBusiType());
        lqw.eq(bo.getIsOccupy() != null, TradePayRefund::getIsOccupy, bo.getIsOccupy());
        // lqw.and(StringUtils.isNotBlank(bo.getRefundNo()), q -> q.eq(TradePayRefund::getRefundRelNo, bo.getRefundNo())
        //                .or().eq(TradePayRefund::getRefundNo, bo.getRefundNo()));
        lqw.eq(bo.getStatus() != null, TradePayRefund::getStatus, bo.getStatus());
        // lqw.eq(bo.getInfStatus() != null, TradePayRefund::getInfStatus, bo.getInfStatus());
        lqw.eq(bo.getRefundInfStatus() != null, TradePayRefund::getRefundInfStatus, bo.getRefundInfStatus());
        if (CollectionUtil.isNotEmpty(bo.getAcctOrgCodes())) {
            lqw.in(TradePayRefund::getAcctOrgId, tradeBaseUtilBizService.queryIdsByCodes(bo.getAcctOrgCodes(), BaseTypeEnum.REGION_WH.getCode()));
        }
        if (CollectionUtil.isNotEmpty(bo.getTransOrgCodes())) {
            lqw.in(TradePayRefund::getOrderOrgId, tradeBaseUtilBizService.queryIdsByCodes(bo.getTransOrgCodes(), BaseTypeEnum.CITY_WH.getCode()));
        }
        if (CollectionUtil.isNotEmpty(bo.getCustomerCodes())) {
            lqw.in(TradePayRefund::getCustomerId, tradeBaseUtilBizService.queryIdsByCodes(bo.getCustomerCodes(), BaseTypeEnum.CUSTOMER.getCode()));
        }
        lqw.and(ll -> ll.eq(TradePayRefund::getDelFlag, 0).or(l -> l.eq(TradePayRefund::getBusiType, RefundBusiTypeEnum.REFUND_EXCEPTION.getCode())));
        lqw.orderByDesc(TradePayRefund::getId);
        return lqw;
    }

    /**
     * 基于已删除的支付单，创建退款单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertException(TradePayBo infTbo, TradePayRefundBo tbo,TradePayVo pvo) {
        TradePay pay = MapstructUtils.convert(infTbo, TradePay.class);
        pay.setPayInfStatus(null);
        pay.setInfTime(null);
        pay.setInfStatus(null);
        pay.setInfReason("关闭后收到支付回调，自动退款！");
        pay.setRefundSplitAmt(pvo.getPaySplitAmt());
        pay.setRefundAmt(pvo.getPayAmt());
        pay.setRefundSeq(10);
        if (pay.getOutSuccessTime() != null) {
            pay.setAcctDate(LocalDateTimeUtil.of(pay.getOutSuccessTime()).toLocalDate());
        }
        if (tradePayMapper.update(pay, Wrappers.lambdaUpdate(TradePay.class)
                .eq(TradePay::getId, pay.getId()).eq(TradePay::getRefundSeq, 0)) == 0) {
            log.keyword(pvo.getOrderNo(), "payCallback").warn("支付回调，异常退款重复，不处理");
            return;
        }
        // 仅退款插入
        tbo.setBusiType(RefundBusiTypeEnum.REFUND_EXCEPTION.getCode());
        tbo.setIsOccupy(0);
        tbo.setInfAuto(YNStatusEnum.ENABLE.getCode());
        tbo.setRefundNo(tradeBaseUtilBizService.getNextTradeRefundNo(LocalDate.now()));
        tbo.setRefundId(pvo.getOrderId());
        tbo.setChannel(pvo.getChannel()); // 异常插入对应订单渠道
        TradePayRefund add =  createPayRefundBo(tbo, pvo);
        add.setInfStatus(RefundInfStatusEnum.SUCCESS.getCode());
        add.setRefundInfStatus(RefundInfStatusEnum.CHECK.getCode());
        add.setTradeRefundNo(add.getRefundNo());
        add.setRefundSplitAmt(pvo.getPaySplitAmt());
        add.setRefundAmt(pvo.getPayAmt());
        baseMapper.insert(add);
    }

    private TradePayRefund createPayRefundBo(TradePayRefundBo tbo, TradePayVo pvo) {
        TradePayRefund add = MapstructUtils.convert(tbo, TradePayRefund.class);
        /////// add.setChannel(pvo.getChannel());
        add.setAppId(pvo.getAppId());
        add.setPayId(pvo.getId());
        add.setOrderId(pvo.getOrderId());
        add.setOrderNo(pvo.getOrderNo());
        add.setOrderInfTime(pvo.getInfTime());
        add.setOrderDate(pvo.getOrderDate());
        add.setPayerIp(pvo.getPayerIp());
        add.setCustomerId(pvo.getCustomerId());
        add.setCustomerCode(pvo.getCustomerCode());
        add.setCustomerOutCode(pvo.getCustomerOutCode());
        add.setCustomerAcctCode(pvo.getCustomerAcctCode());
        add.setPayAmt(pvo.getPayAmt());
        add.setPaySplitAmt(pvo.getPaySplitAmt());
        add.setOrderOrgId(pvo.getOrderOrgId());
        add.setAcctOrgId(pvo.getAcctOrgId());
        add.setTradeNo(pvo.getTradeNo());
        add.setRefundSeq(pvo.getRefundSeq() < 10 ? 10 : (pvo.getRefundSeq() + 1));
        add.setBusiField(RefundBusiTypeEnum.getFieldByCode(add.getBusiType()));
        add.setRefundRelNo(add.getRefundNo());
        // 如果插入数据后不直接调用平安接口， 那么调用接口时要重新更新两个时间字段！！！
        add.setRefundDate(LocalDate.now());
        add.setInfStatus(add.getIsOccupy() == 1 ? RefundInfStatusEnum.INF_INIT.getCode(): RefundInfStatusEnum.CHECK.getCode());
        add.setInfTime(Convert.toDate(DateUtil.now()));
        add.setInfRetries(0);
        add.setRefundInfStatus(add.getInfStatus());
        add.setRefundInfTime(add.getInfTime());
        add.setRefundInfRetries(add.getInfRetries());
        return add;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradePayRefundVo insertByBo(TradePayRefundBo tbo, List<TradeAccTransBo> transBos,
                                       TradePayVo pvo, int remarkMaxSplit) {
        TradePayRefund add = createPayRefundBo(tbo, pvo);
        List<TradeAccTrans> addBos = MapstructUtils.convert(transBos, TradeAccTrans.class);
        // 1 处理数据
        Map<Long, RemoteBaseDataVo> skuMap = tradeBaseDataBizService.querySupplierSkuList(
                addBos.stream().map(TradeAccTrans::getSkuId).filter(skuId -> skuId != 0L).toList()
                ).stream().collect(Collectors.toMap(RemoteBaseDataVo::getId, Function.identity()));
        Map<Long, RemoteBaseDataVo> commCustMap = tradeBaseUtilBizService.queryBaseListByIds(
                addBos.stream().map(TradeAccTrans::getCommCustomerId).filter(customerId -> customerId != 0L).collect(Collectors.toSet()).stream().toList(),
                AccountOrgTypeEnum.CUSTOMER.getCode()).stream().collect(Collectors.toMap(RemoteBaseDataVo::getId, Function.identity()));
        // 2 退款单的关联分账单号
        List<TradeAccTransVo> relateTransVos = tradeAccTransService.queryOrderRelateList(add.getOrderNo(), null, addBos)
                .stream().filter(vo -> vo.getIsOccupy() != 1).toList();
        Map<String, List<TradeAccTransVo>> relateTransMap = relateTransVos.stream().collect(
                Collectors.groupingBy(vo -> String.format("%s_%s_%s", vo.getOrgCode(), vo.getOrgType(), vo.getSkuId()), Collectors.toList()));
        // Map<String, TradeAccTransVo> transferNos = tradeAccTransService.getOrderTransferNos(tbo.getOrderNo());
        List<List<TradeAccTrans>> allBoss = new ArrayList<>();
        List<TradeAccTrans> oneBos = new ArrayList<>();
        Set<String> oneRelNos = new HashSet<>();
        BigDecimal totalAmt = BigDecimal.ZERO;
        // 3 循环
        for (int i = 0; i < addBos.size(); i++) {
            TradeAccTrans trans = addBos.get(i);
            // 3.1 商品和基础信息
            if (AccountOrgTypeEnum.SUPPLIER.getCode().equals(trans.getOrgType())) {
                RemoteBaseDataVo dataVo = skuMap.get(trans.getSkuId());
                if (dataVo == null) {
                    throw new ServiceException(String.format("批次商品id %s不存在",
                            trans.getSkuId() == 0L ? "" : trans.getSkuId()));
                }
                trans.setSkuName(dataVo.getName());
            }
            if (trans.getCommCustomerId() != 0) {
                RemoteBaseDataVo dataVo = commCustMap.get(trans.getCommCustomerId());
                if (dataVo == null) {
                    throw new ServiceException(String.format("分销客户id %s不存在",
                            trans.getCommCustomerId() == 0L ? "" : trans.getCommCustomerId()));
                }
            }
            totalAmt = totalAmt.add(trans.getTransAmt()); // transAmt 含 feeAmt
            setInsertTransRow(trans, add, tbo.getRemark());
            if (trans.getTransAmt().compareTo(BigDecimal.ZERO) > 0) {
                throw new ServiceException("退款金额不能小于零");
            } else if (trans.getTransAmt().compareTo(BigDecimal.ZERO) == 0) {
                trans.setInfStatus(RefundInfStatusEnum.SUCCESS.getCode());
                trans.setIsOccupy(0); // 默认不占用
            } else {
                // 金额小于 0， 且是供应商的情况， 设置程  1
                trans.setIsOccupy(AccountOrgTypeEnum.SUPPLIER.getCode().equals(trans.getOrgType()) ? add.getIsOccupy() : 0); // 只有 0 和 1 的情况
            }
            if (trans.getSplitAmt().compareTo(trans.getTransAmt()) < 0) {
                throw new ServiceException("实退金额不能大于退款金额");
            }
            if (trans.getCommissionAmt().compareTo(trans.getTransAmt()) < 0) {
                throw new ServiceException("佣金不能大于退款金额");
            }
            if (trans.getCommSalaryAmt().compareTo(trans.getCommissionAmt()) < 0) {
                throw new ServiceException("发薪金额不能大于佣金");
            }
            List<TradeAccTrans> addupBos = setTransSplitRel(trans, relateTransMap);
            Set<String> addupRelNos = addupBos.stream().map(TradeAccTrans::getSplitRelNo)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            oneRelNos.addAll(addupRelNos);
            if (remarkMaxSplit > 0 && oneRelNos.size() > remarkMaxSplit) {
                allBoss.add(oneBos);
                oneRelNos = new HashSet<>(addupRelNos);
                oneBos = new ArrayList<>();
            }
            oneBos.addAll(addupBos);
        }
        allBoss.add(oneBos);
        if (totalAmt.compareTo(add.getRefundAmt()) != 0) {
            throw new ServiceException("分账金额与总金额不一致");
        }
        // 创建佣金行数据
        TradeAccTrans commTransRow = getInsertCommTransRow(addBos);
        if (commTransRow != null) {
            setInsertTransRow(commTransRow, add, tbo.getRemark());
            oneBos.addAll(setTransSplitRel(commTransRow, relateTransMap));
        }
        // 只有订单分账成功后，才做这个判断， 后续订单分账单号在 refundCheck 时重新获取
        if (PayInfStatusEnum.SUCCESS.getCode().equals(pvo.getInfStatus()) &&
                addBos.stream().anyMatch(bo -> StringUtils.isEmpty(bo.getSplitRelNo()) &&
                        bo.getSplitAmt().subtract(bo.getTransMinAmt()).compareTo(BigDecimal.ZERO) != 0)) {
            throw new ServiceException("银行子账户关联变化，关联单号未找到", R.FAIL);
        }
        // 4 按 trans 顺序截取成新的 list
        List<String> refundNos = new ArrayList<>();
        TradePayRefundVo refundVo = insertPayRefund(add, refundNos, allBoss.get(0));
        for (int i = 1; i < allBoss.size(); i++) {
            TradePayRefund refund = new TradePayRefund();
            BeanUtil.copyProperties(add, refund, Constants.BeanCopyIgnoreNullValue);
            refund.setRefundNo(String.format("%s_%s", add.getRefundNo(), i));
            refund.setInfAuto(YNStatusEnum.DISABLE.getCode()); // 依赖主退款自动类型
            refund.setId(null); // 清空id， 保证新插入一条
            insertPayRefund(refund, refundNos, allBoss.get(i));
        }
        return refundVo;
    }

    private TradePayRefundVo insertPayRefund(TradePayRefund add, List<String> refundNos, List<TradeAccTrans> addBos) {
        add.setTradeRefundNo(add.getRefundNo());
        add.setRefundSplitAmt(BigDecimal.ZERO);
        add.setRefundAmt(BigDecimal.ZERO);
        addBos.forEach(bo -> {
            bo.setTransNo(add.getRefundNo());
            add.setRefundSplitAmt(add.getRefundSplitAmt().subtract(bo.getSplitAmt().subtract(bo.getTransMinAmt())));
            add.setRefundAmt(add.getRefundAmt().subtract(bo.getTransAmt().subtract(bo.getCommissionAmt())));
        });
        baseMapper.insert(add);
        refundNos.add(add.getRefundNo()); // 用于判断退款额度是否足够
        TradePayRefundVo refundVo = MapstructUtils.convert(add, TradePayRefundVo.class);
        refundVo.setSplits(tradeAccSplitService.batchInsertByBo(add.getOrderNo(), refundNos, false,
                new TradeAccSplitAddBo().setChannel(refundVo.getChannel())
                        .setTradeNo(refundVo.getTradeRefundNo()).setTotalAmt(refundVo.getRefundSplitAmt())
                        .setInfTime(refundVo.getInfTime()).setInitTime(refundVo.getCreateTime()), addBos));
        return refundVo;
    }

    private void setInsertTransRow(TradeAccTrans trans, TradePayRefund add, String remark) {
        trans.setTransType(AccountTransTypeEnum.OR.getCode());
        trans.setTransId(add.getRefundId());
        trans.setTransNo(add.getRefundNo());
        trans.setTransDate(add.getOrderDate()); // 按销售周期
        trans.setTransOrgId(add.getOrderOrgId());
        trans.setAcctOrgId(add.getAcctOrgId());
        trans.setBusiType(add.getBusiType());
        trans.setBusiField(add.getBusiField());
        trans.setRelateType(AccountTransTypeEnum.OP.getCode());
        trans.setRelateNo(add.getOrderNo());
        trans.setRelateId(add.getOrderId());
        trans.setRelateAmt(add.getPayAmt());
        trans.setTotalAmt(add.getRefundAmt());
        trans.setStatusTime(Convert.toDate(DateUtil.now()));
        trans.setRemark(remark);
        trans.setTransAmt(trans.getTransAmt().negate());
        trans.setFeeAmt(trans.getFeeAmt().negate());
        trans.setSplitAmt(trans.getSplitAmt().negate());
        trans.setCommissionAmt(trans.getCommissionAmt().negate());
        trans.setCommSalaryAmt(trans.getCommSalaryAmt().negate());
        trans.setTransMinAmt(trans.getTransMinAmt().negate());
		trans.setTransMaxAmt(trans.getTransMaxAmt().negate());
    }

    private TradeAccTrans getInsertCommTransRow(List<TradeAccTrans> accTransList) {
        BigDecimal commissionAmt = BigDecimal.ZERO;
        BigDecimal transMinAmt = BigDecimal.ZERO;
        for (TradeAccTrans trans : accTransList) {
            commissionAmt = commissionAmt.add(trans.getCommissionAmt());
            transMinAmt = transMinAmt.add(trans.getTransMinAmt());
        }
        if (commissionAmt.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        TradeOrgBankRelaVo commOrgVo = tradeOrgRelationService.queryBankByCode(
                commServiceHelper.getCommAccOrgCode(), AccountOrgTypeEnum.REGION_WH.getCode());
        Map<String, List<TradeAccTrans>> transMap = accTransList.stream().collect(
                Collectors.groupingBy(vo -> String.format("%s_%s_%s", vo.getOrgCode(), vo.getOrgType(), vo.getSkuId()), Collectors.toList()));
        List<TradeAccTrans> transList = transMap.get(String.format("%s_%s_0", commOrgVo.getOrgCode(), commOrgVo.getOrgType()));
        TradeAccTrans trans = new TradeAccTrans();
        if (CollectionUtil.isNotEmpty(transList)) {
            // 使用传入的佣金金额， 如果设置也要设置成 负数 ！！！！！
            trans = transList.get(0);
            trans.setTransAmt(commissionAmt); // 直接使用负数， 后续无处理
            trans.setSplitAmt(transMinAmt);
        } else {
            trans.setTransAmt(commissionAmt.abs()); // 转成正数, 因后续有处理变成负数
            trans.setSplitAmt(transMinAmt.abs());
        }
        trans.setCommissionAmt(BigDecimal.ZERO);
        trans.setCommSalaryAmt(BigDecimal.ZERO);
        trans.setTransMinAmt(trans.getCommissionAmt());
        trans.setTransMaxAmt(trans.getSplitAmt());
        trans.setFeeAmt(BigDecimal.ZERO);
        trans.setDeptId(0L);
        trans.setSkuId(0L);
        trans.setCommCustomerId(0L);
        trans.setOrgType(commOrgVo.getOrgType());
        trans.setOrgId(commOrgVo.getOrgId());
        trans.setOrgCode(commOrgVo.getOrgCode());
        //  trans.setOutOrgCode(commOrgVo.getOutOrgCode());  使用订单的， 这里注释掉
        //  trans.setOutAcctCode(commOrgVo.getOutAcctCode());
        // 传入的分帐数据中含有佣金记录，则返回 null
        return CollectionUtil.isNotEmpty(transList) ? null : trans;
    }
    private List<TradeAccTrans> setTransSplitRel(TradeAccTrans trans, Map<String, List<TradeAccTransVo>> relateTransMap) {
        List<TradeAccTrans> addupBos = new ArrayList<>();
        String transKey = String.format("%s_%s_%s", trans.getOrgCode(), trans.getOrgType(), trans.getSkuId());
        List<TradeAccTransVo> relateSkuVos = relateTransMap.get(transKey);
        if (CollectionUtil.isEmpty(relateSkuVos)) {
            throw new ServiceException(getExceptionInfo(trans, "关联商品不存在"));
        }
        addupBos.add(trans);
        for (TradeAccTransVo transVo : relateSkuVos.stream().filter(vo -> Constants.PayPositiveTypes.contains(vo.getTransType())).toList()) {
            if (AccountTransTypeEnum.TI.getCode().equals(transVo.getTransType())) {
                throw new ServiceException("不支持更换供应商", R.FAIL);
            }
            trans.setSplitRelNo(transVo.getSplitNo());
            trans.setSplitRelAmt(transVo.getTransAmt());
            trans.setSplitOrgCode(transVo.getSplitOrgCode());
            trans.setSplitAcctCode(transVo.getSplitAcctCode());
            trans.setOutOrgCode(transVo.getOutOrgCode());
            trans.setOutAcctCode(transVo.getOutAcctCode());
            trans.setDeptId(transVo.getDeptId()); // 使用订单时的deptId 和 commCustomerId
            trans.setCommCustomerId(transVo.getCommCustomerId());
            BigDecimal residueTransAmt = transVo.getTransAmt().add(trans.getTransAmt()).add(relateSkuVos.stream()
                    .filter(vo -> Constants.RefundTypes.contains(vo.getTransType())).map(TradeAccTransVo::getTransAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
            BigDecimal residueSplitAmt = transVo.getSplitAmt().add(trans.getSplitAmt()).add(relateSkuVos.stream()
                    .filter(vo -> Constants.RefundTypes.contains(vo.getTransType())).map(TradeAccTransVo::getSplitAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
            BigDecimal residueCommAmt = transVo.getCommissionAmt().add(trans.getCommissionAmt()).add(relateSkuVos.stream()
                    .filter(vo -> Constants.RefundTypes.contains(vo.getTransType())).map(TradeAccTransVo::getCommissionAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
            if (residueCommAmt.compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException(getExceptionInfo(trans, "佣金余额不足"));
            }
            if (residueTransAmt.compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException(getExceptionInfo(trans, "退款余额不足"));
            }
            if (residueSplitAmt.compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException(getExceptionInfo(trans, "实际退款余额不足"));
            }
            if (residueTransAmt.compareTo(residueSplitAmt) < 0) {
                throw new ServiceException(getExceptionInfo(trans, "剩余实退金额不能大于剩余退款金额"));
            }
            if (residueTransAmt.compareTo(residueCommAmt) < 0) {
                throw new ServiceException(getExceptionInfo(trans, "剩余佣金不能大于剩余退款金额"));
            }
        }
        return addupBos;
    }

	private String getExceptionInfo(TradeAccTrans trans, String info) {
		return String.format("支付单 %s, %s(%s) %s", trans.getRelateNo(), trans.getSkuName() == null ? "" : trans.getSkuName(), trans.getSkuId(), info);
	}

    /*
    *   里面有更换供应商的代码，佣金需求后停止维护
    private List<TradeAccTrans> setTransSplitRel(TradeAccTrans trans, Map<String, List<TradeAccTransVo>> relateTransMap,
                                                 Map<String, TradeAccTransVo> transferNos) {
        List<TradeAccTrans> addupBos = new ArrayList<>();
        // 3.2 验证商品订单中是否存在
        String transKey = String.format("%s_%s_%s", trans.getOrgCode(), trans.getOrgType(), trans.getSkuId());
        List<TradeAccTransVo> relateSkuVos = relateTransMap.get(transKey);
        if (CollectionUtil.isEmpty(relateSkuVos)) {
            throw new ServiceException(String.format("退款单 %s, skuId %s 关联支付单中不存在", trans.getTransNo(), trans.getSkuId()), R.FAIL);
        }
        // 3.3 退款商品， 基于同 orgCode + skuId ，关联订单或换供应商
        //      同 orgCode + skuId 两种情况， 一条支付，可能多条换供应商。 退款关联时优先关联支付
        TradeAccTrans preAccTrans = trans;
        addupBos.add(preAccTrans);
        boolean isResidue = true; // 保证 splitAmt = 0 的数据也找到对应的订单数据
        for (TradeAccTransVo transVo : relateSkuVos.stream().filter(vo -> Constants.PayPositiveTypes.contains(vo.getTransType())).toList()) {
            /* 去掉该限制， 暂时先观察
            if (!AccountStatusEnum.FREEZE.getCode().equals(transVo.getStatus())) {
                throw new ServiceException(String.format("退款单 %s, skuId %s 关联支付单不是待结算状态", trans.getTransNo(), preAccTrans.getSkuId()));
            }*//**
            // 3.3.1 订单中，只有 OP 和 TI 为正数， 订单没有 SplitRelNo， TI 有 SplitRelNo
            TradeAccTransVo accTransVo;
            if (AccountTransTypeEnum.TI.getCode().equals(transVo.getTransType())) {
                // TODO 调入数据，使用 SplitRelNo 查找到原订单数据  调入时 relateSkuVos 的 skuId 都不同，当前逻辑有问题，但暂未处理
                accTransVo = transferNos.get(transVo.getSplitRelNo());
            } else {
                accTransVo = transVo;
            }
            // 调入的分账单号都使用原支付单的
            BigDecimal residueSplitAmt = accTransVo.getSplitAmt().add(relateSkuVos.stream()
                    .filter(vo -> Constants.RefundTypes.contains(vo.getTransType())).map(TradeAccTransVo::getSplitAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
            BigDecimal residueTransAmt = accTransVo.getTransAmt().add(relateSkuVos.stream()
                    .filter(vo -> Constants.RefundTypes.contains(vo.getTransType())).map(TradeAccTransVo::getTransAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
            if (residueTransAmt.add(preAccTrans.getTransAmt()).compareTo(BigDecimal.ZERO) >= 0) {
                // 3 剩余金额充足， 使用支付单的关联分账单号
                preAccTrans.setSplitRelNo(accTransVo.getSplitNo());
                preAccTrans.setSplitRelAmt(accTransVo.getTransAmt());
                preAccTrans.setSplitOrgCode(accTransVo.getSplitOrgCode());
                preAccTrans.setSplitAcctCode(accTransVo.getSplitAcctCode());
                preAccTrans.setOutOrgCode(accTransVo.getOutOrgCode());
                preAccTrans.setOutAcctCode(accTransVo.getOutAcctCode());
                isResidue = false;
                break;
            } else if (residueTransAmt.compareTo(BigDecimal.ZERO) > 0) {
                TradeAccTrans newAccTrans = new TradeAccTrans();
                BeanUtil.copyProperties(preAccTrans, newAccTrans, Constants.BeanCopyIgnoreNullValue);
                preAccTrans.setTransAmt(residueTransAmt.negate());
                preAccTrans.setSplitAmt(residueSplitAmt.negate());
                preAccTrans.setSplitRelNo(accTransVo.getSplitNo());
                preAccTrans.setSplitRelAmt(accTransVo.getTransAmt());
                preAccTrans.setSplitOrgCode(accTransVo.getSplitOrgCode());
                preAccTrans.setSplitAcctCode(accTransVo.getSplitAcctCode());
                preAccTrans.setOutOrgCode(accTransVo.getOutOrgCode());
                preAccTrans.setOutAcctCode(accTransVo.getOutAcctCode());
                newAccTrans.setTransAmt(newAccTrans.getTransAmt().subtract(preAccTrans.getTransAmt()));
                newAccTrans.setSplitAmt(newAccTrans.getSplitAmt().subtract(preAccTrans.getSplitAmt()));
                // 使用余额全部扣时，可能会造成 preAccTrans, newAccTrans 这行的 transAmt > splitAmt 的情况 ！！！！！
                if (preAccTrans.getTransAmt().compareTo(preAccTrans.getSplitAmt()) > 0) {
                    throw new ServiceException(String.format("退款单 %s, skuId %s 实际退款大于退款金额", trans.getTransNo(), preAccTrans.getSkuId()), R.FAIL);
                }
                if (newAccTrans.getTransAmt().compareTo(newAccTrans.getSplitAmt()) > 0) {
                    throw new ServiceException(String.format("退款单 %s, skuId %s 实际退款大于退款金额", trans.getTransNo(), preAccTrans.getSkuId()), R.FAIL);
                }
                preAccTrans = newAccTrans;
                addupBos.add(preAccTrans);
            }
        }
        if (isResidue) {
            throw new ServiceException(String.format("支付单 %s, skuId %s 可退款余额不足", trans.getRelateNo(), preAccTrans.getSkuId()));
        }
        return addupBos;
    }
    */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradePayRefundVo insertByRelease(List<TradePayRefundBo> refundBos, List<TradeAccTransBo> accTransBos) {
        List<TradeAccTrans> addBos = new ArrayList<>();
        List<TradeAccTransVo> accTransVos = tradeAccTransService.queryTransList(
                refundBos.stream().map(TradePayRefundBo::getRefundNo).toList(), Constants.RefundTypes);
        Map<String, List<TradeAccTransVo>> transVoMaps = accTransVos.stream().collect(
                Collectors.groupingBy(vo -> String.format("%s_%s_%s", vo.getOrgCode(), vo.getOrgType(), vo.getSkuId()), Collectors.toList()));
        Map<String, List<TradeAccTransBo>> transBoMaps = accTransBos.stream().collect(
                        Collectors.groupingBy(bo -> String.format("%s_%s_%s", bo.getOrgCode(), bo.getOrgType(), bo.getSkuId()), Collectors.toList()));
        if (transVoMaps.size() != transBoMaps.size()) {
            throw new ServiceException(String.format("退款单 %s 释放明细条数不一致", refundBos.get(0).getRefundNo()));
        }
        Map<Long, RemoteBaseDataVo> commCustMap = tradeBaseUtilBizService.queryBaseListByIds(
                accTransBos.stream().map(TradeAccTransBo::getCommCustomerId).filter(customerId -> customerId != 0L).collect(Collectors.toSet()).stream().toList(),
                AccountOrgTypeEnum.CUSTOMER.getCode()).stream().collect(Collectors.toMap(RemoteBaseDataVo::getId, Function.identity()));
        // 重新合计退款单头金额
        Map<String, TradePayRefundBo> refundBoMaps = refundBos.stream().collect(
                Collectors.toMap(TradePayRefundBo::getRefundNo, v -> v.setRefundSplitAmt(BigDecimal.ZERO).setRefundAmt(BigDecimal.ZERO)));
        for (Map.Entry<String, List<TradeAccTransVo>> entry: transVoMaps.entrySet()) {
            List<TradeAccTransBo> transBos = transBoMaps.get(entry.getKey());
            List<TradeAccTransVo> transVos = entry.getValue();
            if (transBos == null) {
                throw new ServiceException(String.format("退款释放批次商品id %s不存在",
                        transVos.get(0).getSkuId() == 0L ? "" : transVos.get(0).getSkuId()));
            }
            if (transBos.size() != 1) {
                throw new ServiceException(String.format("退款释放批次商品id %s数据重复",
                        transVos.get(0).getSkuId() == 0L ? "" : transVos.get(0).getSkuId()));
            }
            TradeAccTransBo transBo = transBos.get(0);
            if (transBo.getCommCustomerId() != 0) {
                RemoteBaseDataVo dataVo = commCustMap.get(transBo.getCommCustomerId());
                if (dataVo == null) {
                    throw new ServiceException(String.format("分销客户id %s不存在",
                            transBo.getCommCustomerId() == 0L ? "" : transBo.getCommCustomerId()));
                }
            }
            if (transVos.size() == 1) {
                TradeAccTrans releaseTrans = setTransRelease(transVos.get(0), transBo, refundBoMaps);
                if (releaseTrans.getTransAmt().compareTo(BigDecimal.ZERO) != 0
                    || releaseTrans.getSplitAmt().compareTo(BigDecimal.ZERO) != 0
                    || releaseTrans.getCommissionAmt().compareTo(BigDecimal.ZERO) != 0) {
                    addBos.add(releaseTrans);
                }
            } else {
                // TODO 暂不处理因换供应商造成拆行问题

            }
        }
        TradeAccTrans commTransRow = getInsertCommTransRow(addBos);
        if (commTransRow != null) {
            TradePayRefundBo refundBo = refundBos.get(refundBos.size() - 1);
            setInsertTransRow(commTransRow, MapstructUtils.convert(refundBo, TradePayRefund.class), "");
            List<TradeAccTransVo> relateTransVos = tradeAccTransService.queryOrderRelateList(refundBos.get(0).getOrderNo(),
                    null, new ArrayList<>()).stream().filter(vo -> vo.getIsOccupy() != 1).toList();
            Map<String, List<TradeAccTransVo>> relateTransMap = relateTransVos.stream().collect(
                    Collectors.groupingBy(vo -> String.format("%s_%s_%s", vo.getOrgCode(), vo.getOrgType(), vo.getSkuId()), Collectors.toList()));
            setTransSplitRel(commTransRow, relateTransMap);
            refundBo.setRefundAmt(refundBo.getRefundAmt().subtract(commTransRow.getTransAmt()));
            refundBo.setRefundSplitAmt(refundBo.getRefundSplitAmt().subtract(commTransRow.getSplitAmt()));
            commTransRow.setTransType(AccountTransTypeEnum.OS.getCode());
            addBos.add(commTransRow);
        }
        for (int i = 0; i < refundBos.size(); i++) {
            TradePayRefundBo refundBo = refundBos.get(i);
            TradePayRefund update = new TradePayRefund();
            update.setInfStatus(PayChannelEnum.INNER_ZERO.getCode().equals(refundBo.getChannel()) ?
                    PayInfStatusEnum.SUCCESS.getCode() : PayInfStatusEnum.CHECK.getCode());
            update.setInfTime(Convert.toDate(DateUtil.now()));
            update.setRefundInfStatus(update.getInfStatus());
            update.setRefundInfTime(update.getInfTime());
            update.setIsOccupy(2);
            update.setRefundSplitAmt(refundBo.getRefundSplitAmt());
            update.setRefundAmt(refundBo.getRefundAmt());
            update.setInfAuto(i == 0 ? refundBo.getInfAuto() : 0);
//            if (refundBo.getRefundAmt().compareTo(BigDecimal.ZERO) == 0) {
//                update.setAcctDate(LocalDate.now()); // 不退款的也填上记账日期
//            }
            if (baseMapper.update(update, Wrappers.lambdaUpdate(TradePayRefund.class)
                    .eq(TradePayRefund::getRefundNo, refundBo.getRefundNo()).eq(TradePayRefund::getIsOccupy, 1)
                    .eq(TradePayRefund::getDelFlag, YNStatusEnum.DISABLE.getCode())) == 0){
                throw new ServiceException(String.format("退款单 %s 占用已释放", refundBos.get(0).getRefundNo()));
            }
            BeanUtil.copyProperties(update, refundBo, Constants.BeanCopyIgnoreNullValue);
        }
        if (addBos.size() > 0) {
            tradeAccSplitService.batchInsertByBo(refundBos.get(0).getOrderNo(),
                    refundBos.stream().map(TradePayRefundBo::getRefundNo).toList(), false, null, addBos);
        }
        return MapstructUtils.convert(refundBos.get(0), TradePayRefundVo.class);
    }

    private TradeAccTrans setTransRelease(TradeAccTransVo transVo, TradeAccTransBo transBo, Map<String, TradePayRefundBo> refundBos) {
        TradeAccTrans releaseTrans = new TradeAccTrans();
        // 金额保留差额
        releaseTrans.setTransAmt(transVo.getTransAmt().add(transBo.getTransAmt()).negate());
        releaseTrans.setFeeAmt(transVo.getFeeAmt().add(transBo.getFeeAmt()).negate());
        releaseTrans.setSplitAmt(transVo.getSplitAmt().add(transBo.getSplitAmt()).negate());
        releaseTrans.setCommissionAmt(transVo.getCommissionAmt().add(transBo.getCommissionAmt()).negate());
        releaseTrans.setCommSalaryAmt(transVo.getCommSalaryAmt().add(transBo.getCommSalaryAmt()).negate());
        releaseTrans.setTransMinAmt(transVo.getTransMinAmt().add(transBo.getTransMinAmt()).negate());
		releaseTrans.setTransMaxAmt(transVo.getTransMaxAmt().add(transBo.getTransMaxAmt()).negate());
        TradePayRefundBo refundBo = refundBos.get(transVo.getTransNo());
        refundBo.setRefundAmt(refundBo.getRefundAmt().add(transBo.getTransAmt().subtract(transBo.getCommissionAmt())));
        refundBo.setRefundSplitAmt(refundBo.getRefundSplitAmt().add(transBo.getSplitAmt().subtract(transBo.getTransMinAmt())));
        if (releaseTrans.getTransAmt().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("退款金额不能小于零");
        }
        if (releaseTrans.getSplitAmt().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("实际退款不能小于零");
        }
        releaseTrans.setOrgCode(transVo.getOrgCode());
        releaseTrans.setOrgId(transVo.getOrgId());
        releaseTrans.setOrgType(transVo.getOrgType());
        releaseTrans.setSkuId(transVo.getSkuId());
        releaseTrans.setSkuName(transVo.getSkuName());
        releaseTrans.setIsOccupy(0);
        releaseTrans.setDeptId(transVo.getDeptId());  // 使用从订单拉过来
        releaseTrans.setCommCustomerId(transVo.getCommCustomerId());
        releaseTrans.setStatusTime(Convert.toDate(DateUtil.now()));
        releaseTrans.setSplitOrgCode(transVo.getSplitOrgCode());
        releaseTrans.setSplitAcctCode(transVo.getSplitAcctCode());
        releaseTrans.setSplitRelNo(transVo.getSplitRelNo());
        releaseTrans.setSplitRelAmt(transVo.getSplitRelAmt());
        releaseTrans.setOutOrgCode(transVo.getOutOrgCode());
        releaseTrans.setOutAcctCode(transVo.getOutAcctCode());
        releaseTrans.setTransType(AccountTransTypeEnum.OS.getCode());
        releaseTrans.setTransId(transVo.getTransId());
        releaseTrans.setTransNo(transVo.getTransNo());
        releaseTrans.setTransDate(transVo.getTransDate()); // 按销售周期
        releaseTrans.setTransOrgId(transVo.getTransOrgId());
        releaseTrans.setAcctOrgId(transVo.getAcctOrgId());
        releaseTrans.setBusiType(transVo.getBusiType());
        releaseTrans.setBusiField(transVo.getBusiField());
        releaseTrans.setRelateType(transVo.getRelateType());
        releaseTrans.setRelateNo(transVo.getRelateNo());
        releaseTrans.setRelateId(transVo.getRelateId());
        releaseTrans.setRelateAmt(transVo.getRelateAmt());
        releaseTrans.setTotalAmt(transVo.getTotalAmt());
        releaseTrans.setRemark(transVo.getRemark());
        return releaseTrans;
    }

    // 退款 + 分账
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradePayRefundVo updateCheckByBo(TradePayRefundBo tbo) {
        // 对接银行的单号都要重新生成, 记账日期， 接口调用时间 ！！！！！
        TradePayRefund update = new TradePayRefund();
        update.setId(tbo.getId());
        update.setInfRetries(tbo.getInfRetries() + 1);
        update.setInfTime(Convert.toDate(DateUtil.now()));
        update.setInfStatus(RefundInfStatusEnum.PROCESSING.getCode());
        update.setRefundInfTime(update.getInfTime());
        update.setRefundInfStatus(update.getInfStatus());
        int count = 0;
        try {
            count = updatePayRefund(update, true);
        } catch (DuplicateKeyException dke) {
            count = updatePayRefund(update, true); // 重试一次
        }
        if (count == 0) {
            throw new ServiceException("退款单接口状态已改变");
        }
        return updatePayRefundSplit(tbo, update, true);
    }

    private int updatePayRefund(TradePayRefund update, boolean isCreateNo) {
        if (isCreateNo) {
            update.setTradeRefundNo(tradeBaseUtilBizService.getNextTradeRefundNo(LocalDate.now()));
        }
        return baseMapper.update(update, Wrappers.lambdaUpdate(TradePayRefund.class)
                .eq(TradePayRefund::getId, update.getId())
                .in(TradePayRefund::getRefundInfStatus, Arrays.asList(RefundInfStatusEnum.CHECK.getCode(),
                        RefundInfStatusEnum.FAIL.getCode())));
    }

    private TradePayRefundVo updatePayRefundSplit(TradePayRefundBo tbo, TradePayRefund update, boolean isSplit) {
        // 因释放流水中 splitOrgCode 未null， 这里按
        TradePayRefundVo refundVo = MapstructUtils.convert(tbo, TradePayRefundVo.class);
        BeanUtil.copyProperties(update, refundVo, Constants.BeanCopyIgnoreNullValue);
        if (isSplit) {
            // 验证订单分账是否完成， 并生成关联到分账单号
            List<TradeAccTransVo> transVos = tradeAccTransService.queryTransList(tbo.getRefundNo(), Constants.RefundTypes)
                    .stream().filter(vo -> !PayInfStatusEnum.SUCCESS.getCode().equals(vo.getInfStatus())
                            && !PayInfStatusEnum.CHECK.getCode().equals(vo.getInfStatus())).toList();
            if (transVos.stream().anyMatch(vo -> StringUtils.isEmpty(vo.getSplitRelNo()) &&
                    vo.getSplitAmt().subtract(vo.getTransMinAmt()).compareTo(BigDecimal.ZERO) != 0)) {
                throw new ServiceException(String.format("退款单 %s 对应的支付分账未完成", refundVo.getRefundNo()));
            }
            refundVo.setSplits(tradeAccSplitService.batchUpdateByNo(new TradeAccSplitAddBo().setChannel(refundVo.getChannel())
                            .setTradeNo(refundVo.getTradeRefundNo()).setTotalAmt(refundVo.getRefundSplitAmt())
                            .setInfTime(refundVo.getInfTime()).setInitTime(refundVo.getCreateTime()),
                    MapstructUtils.convert(transVos, TradeAccTrans.class)));
        } else {
            refundVo.setSplits(new ArrayList<>());
        }
        return refundVo;
    }

    // 仅退款
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradePayRefundVo updateRefundCheckByBo(TradePayRefundBo tbo) {
        // 对接银行的单号都要重新生成, 记账日期， 接口调用时间 ！！！！！
        TradePayRefund update = new TradePayRefund();
        update.setId(tbo.getId());
        update.setRefundInfRetries(tbo.getRefundInfRetries() + 1);
        update.setRefundInfTime(Convert.toDate(DateUtil.now()));
        update.setRefundInfStatus(RefundInfStatusEnum.PROCESSING.getCode());
        // 微信B2b， 第一次 或 失败时 ， 要重新生成新的退款单号
        boolean isCreateNo = tbo.getRefundInfRetries() == 0
                || RefundInfStatusEnum.FAIL.getCode().equals(tbo.getRefundInfStatus());
        int count = 0;
        try {
            count = updatePayRefund(update, isCreateNo);
        } catch (DuplicateKeyException dke) {
            count = updatePayRefund(update, isCreateNo); // 重试一次
        }
        if (count == 0) {
            throw new ServiceException("退款单接口状态已改变");
        }
        return updatePayRefundSplit(tbo, update, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRefundInfData(boolean isOnlyPay, TradePayRefundBo infTbo, TradePayRefundVo tvo) {
        TradePayRefund update = MapstructUtils.convert(infTbo, TradePayRefund.class);
        boolean isFail = (RefundBusiTypeEnum.REFUND_EXCEPTION.getCode().equals(tvo.getBusiType())
                && RefundInfStatusEnum.SUCCESS.getCode().equals(update.getRefundInfStatus()))
                || RefundInfStatusEnum.FAIL.getCode().equals(update.getInfStatus());
        update.setDelFlag(isFail ? tvo.getId() : 0);
        if (update.getOutSuccessTime() != null) {
            update.setAcctDate(LocalDateTimeUtil.of(update.getOutSuccessTime()).toLocalDate());
        }
        boolean isSuccess = baseMapper.update(update, Wrappers.lambdaUpdate(TradePayRefund.class)
                .eq(TradePayRefund::getId, update.getId()).eq(TradePayRefund::getDelFlag, 0)
                .ne(TradePayRefund::getRefundInfStatus, RefundInfStatusEnum.SUCCESS.getCode())) > 0;
        if (isSuccess) {
            if (isFail) {
                tradeAccSplitService.deleteSplit(true, new TradeAccSplitUpdateBo()
                        .setTransNo(tvo.getRefundNo()).setTransTypes(Constants.RefundTypes).setInfReason(update.getInfReason()));
            } else if (RefundInfStatusEnum.SUCCESS.getCode().equals(update.getRefundInfStatus())) {
                if (!isOnlyPay) {
                    tradeAccSplitService.updateSplit(new TradeAccSplitUpdateBo()
                            .setTransNo(tvo.getRefundNo()).setTransTypes(Constants.RefundTypes)
                            .setInfStatus(RefundInfStatusEnum.SUCCESS.getCode()).setOutSuccessTime(update.getOutSuccessTime())
                            .setOutTradeNo(update.getOutTradeNo()));
                }
                // 退款，合计订单上退款累加金额
                TradePay pay = tradePayMapper.queryRefundAmt(tvo.getOrderNo());
                if (pay != null) {
                    tradePayMapper.updateRefundAmt(tvo.getPayId(), pay);
                }
                if (PayChannelEnum.PAY_PINGAN_CLOUD_BALANCE.getCode().equals(tvo.getChannel())) {
                    tradeCustTransService.insertTransByRefund(update.getOutSuccessTime(), tvo);
                }
            }
        }
        return isSuccess;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRefundInfFail(boolean isOnlyPay, Integer infStatus, TradePayRefundVo tvo, ServiceException se) {
        boolean isFail = RefundInfStatusEnum.INF_FAIL.getCode().equals(infStatus);
        String message = StrUtil.sub(se.getMessage(), 0, 128);
        TradePayRefund update = new TradePayRefund();
        update.setDelFlag(isFail ? tvo.getId() : 0);
        update.setId(tvo.getId());
        if (!isOnlyPay) {
            update.setInfStatus(infStatus);
        }
        update.setRefundInfStatus(infStatus);
        update.setInfReason(message);
        int count = baseMapper.updateById(update);
        if (count > 0 && !isOnlyPay) {
            // 再次重试， 仅删除 split，
            tradeAccSplitService.deleteSplit(isFail, new TradeAccSplitUpdateBo()
                    .setTransNo(tvo.getRefundNo()).setTransTypes(Constants.RefundTypes).setInfReason(message));
        }
    }

    // 仅分账
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradePayRefundVo updateSplitCheckByBo(TradePayRefundBo tbo) {
        TradePayRefund update = new TradePayRefund();
        update.setId(tbo.getId());
        update.setInfRetries(tbo.getInfRetries() + 1);
        update.setInfTime(Convert.toDate(DateUtil.now()));
        update.setInfStatus(RefundInfStatusEnum.PROCESSING.getCode());
        if (baseMapper.update(update, Wrappers.lambdaUpdate(TradePayRefund.class)
                .eq(TradePayRefund::getId, update.getId())
                .and(l -> l.eq(TradePayRefund::getInfStatus, RefundInfStatusEnum.CHECK.getCode())
                        .or(ll -> ll.eq(TradePayRefund::getInfStatus, RefundInfStatusEnum.PROCESSING.getCode())
                                .le(TradePayRefund::getInfTime, DateUtil.offsetSecond(new Date(), -10))))) == 0) {
            throw new ServiceException("退款单分账状态已改变");
        }
        return updatePayRefundSplit(tbo, update, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSplitInfData(boolean isEnd, TradePayRefundVo tvo) {
        if (isEnd) {
            TradePayRefund update = new TradePayRefund();
            update.setId(tvo.getId());
            update.setInfStatus(RefundInfStatusEnum.SUCCESS.getCode());
            update.setInfReason("");
            tvo.setInfStatus(update.getInfStatus()); // 同步返回，用于后续判断
            if (RefundInfStatusEnum.SUCCESS.getCode().equals(tvo.getRefundInfStatus())) {
                // 没有分账记录， 退款金额为零的情况
                update.setRefundInfStatus(update.getInfStatus());
                update.setOutSuccessTime(Convert.toDate(DateUtil.now()));
                update.setAcctDate(LocalDateTimeUtil.of(update.getOutSuccessTime()).toLocalDate());
                update.setInfReason("未调用退款接口");
                update.setOutTradeNo("");
            }
            baseMapper.updateById(update);
        }
        tradeAccSplitService.updateSplit(new TradeAccSplitUpdateBo()
                .setTransNo(tvo.getRefundNo()).setTransTypes(Constants.RefundTypes), tvo.getSplits());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSplitInfFail(TradePayRefundVo tvo, ServiceException se) {
        String message = StrUtil.sub(se.getMessage(), 0, 128);
        TradePayRefund update = new TradePayRefund();
        update.setId(tvo.getId());
        update.setInfStatus(RefundInfStatusEnum.CHECK.getCode());
        tvo.setInfStatus(update.getInfStatus()); // 同步返回，用于后续判断
        update.setInfReason(message);
        int count = baseMapper.updateById(update);
        if (count > 0) {
            tradeAccSplitService.deleteSplit(false, new TradeAccSplitUpdateBo()
                    .setTransNo(tvo.getRefundNo()).setTransTypes(Constants.RefundTypes).setInfReason(message));
        }
        if (se.getMessage().contains("[ERR180]")) {
            // 订单不存在[ERR180]   说明当前记录订单的分账单号错误 (订单分账之前可能失败)， 重新更新一次分账单号
            updateRefundSplitNo(tvo.getOrderNo(), tvo.getRefundNo());
        }
    }

    @Override
    // 重新获取退款单对应支付单的分账单号， 极端情况时可能出现， 不控制事务， 是其他事务调用
    public void updateRefundSplitNo(String orderNo, String refundNo) {
        List<TradeAccTransVo> relateTransVos = tradeAccTransMapper.queryOrderRelateList(orderNo, Constants.PayPositiveTypes, null)
                .stream().filter(vo -> !AccountStatusEnum.CANCEL.getCode().equals(vo.getStatus())).toList();
        Map<String, List<TradeAccTransVo>> relateTransMap = relateTransVos.stream().collect(
                Collectors.groupingBy(vo -> String.format("%s_%s_%s", vo.getOrgCode(), vo.getOrgType(), vo.getSkuId()), Collectors.toList()));
        for (TradeAccTransVo transVo: tradeAccTransService.queryTransList(refundNo, Constants.RefundTypes)) {
            String transKey = String.format("%s_%s_%s", transVo.getOrgCode(), transVo.getOrgType(), transVo.getSkuId());
            List<TradeAccTransVo> relateTrans = relateTransMap.get(transKey);
            if (relateTrans != null && relateTrans.size() == 1) {
                TradeAccTransVo relateVo = relateTrans.get(0);
                String splitRelNo = AccountTransTypeEnum.TI.getCode().equals(relateVo.getTransType()) ? relateVo.getSplitRelNo() : relateVo.getSplitNo();
                tradeAccTransMapper.update(Wrappers.lambdaUpdate(TradeAccTrans.class)  // 如果是划转入， 就获取到对应订单的
                        .set(TradeAccTrans::getSplitRelNo, splitRelNo).eq(TradeAccTrans::getId, transVo.getId()));
                log.keyword(orderNo, "updateRefundSplitNo").info(String.format("退款单 %s id=%s 更新 splitRelNo=%s", refundNo, transVo.getId(), splitRelNo));
            } else {
                log.keyword(orderNo, "updateRefundSplitNo").warn(String.format("退款单 %s id=%s 未找到对应的 splitRelNo ", refundNo, transVo.getId()));
            }
        }
    }
    @Override
    public boolean querySame(OrderRefundBo bo, TradePayRefundVo tvo) {
        return tvo.getRefundAmt().compareTo(bo.getRefundAmt()) == 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAcctStatus(LocalDate acctDate, List<TradePayRefundBo> bos) {
        if (bos.size() > 0) {
            baseMapper.updateAcctStatus(acctDate, bos);
        }
    }

}