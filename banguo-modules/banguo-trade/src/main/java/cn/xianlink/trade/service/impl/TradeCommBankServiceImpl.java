package cn.xianlink.trade.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.resource.api.RemoteCaptchaService;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.resource.api.domain.RemoteFile;
import cn.xianlink.resource.api.enums.CaptchaCodeType;
import cn.xianlink.system.api.RemoteClientService;
import cn.xianlink.system.api.RemoteMiniAppService;
import cn.xianlink.system.api.domain.bo.RemoteMiniAuthBo;
import cn.xianlink.system.api.domain.vo.RemoteClientVo;
import cn.xianlink.trade.comm.CommServiceHelper;
import cn.xianlink.trade.comm.SignChannelEnum;
import cn.xianlink.trade.comm.youwei.YouweiCommServiceImpl;
import cn.xianlink.trade.comm.youwei.constant.YouweiSignInfStatusEnum;
import cn.xianlink.trade.domain.TradeCommBank;
import cn.xianlink.trade.domain.bo.TradeCommBankBo;
import cn.xianlink.trade.domain.vo.comm.TradeCommBankVo;
import cn.xianlink.trade.mapper.TradeCommBankMapper;
import cn.xianlink.trade.service.ITradeCommBankService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import lombok.CustomLog;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@CustomLog
@RequiredArgsConstructor
@Service
public class TradeCommBankServiceImpl implements ITradeCommBankService {
    private final TradeCommBankMapper baseMapper;

//    private final YouweiCommServiceImpl youweiCommService;

    private final CommServiceHelper commServiceHelper;

	@DubboReference(timeout = 300000)
    private final transient RemoteFileService remoteFileService;

    @DubboReference
    private final RemoteCaptchaService remoteCaptchaService;

    @DubboReference
    private RemoteMiniAppService remoteMiniAppService;

    @DubboReference
    private RemoteClientService remoteClientService;
    /**
     * 查询客户有为签约
     */
    @Override
    public TradeCommBankVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询客户有为签约列表
     */
    @Override
    public TableDataInfo<TradeCommBankVo> queryPageList(TradeCommBankBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TradeCommBank> lqw = buildQueryWrapper(bo);
        Page<TradeCommBankVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询客户有为签约列表
     */
    @Override
    public List<TradeCommBankVo> queryList(TradeCommBankBo bo) {
        LambdaQueryWrapper<TradeCommBank> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TradeCommBank> buildQueryWrapper(TradeCommBankBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TradeCommBank> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCustomerId() != null, TradeCommBank::getCustomerId, bo.getCustomerId());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerCode()), TradeCommBank::getCustomerCode, bo.getCustomerCode());
        lqw.like(StringUtils.isNotBlank(bo.getPersonName()), TradeCommBank::getPersonName, bo.getPersonName());
        lqw.eq(StringUtils.isNotBlank(bo.getPersonIdCard()), TradeCommBank::getPersonIdCard, bo.getPersonIdCard());
        lqw.eq(StringUtils.isNotBlank(bo.getBankMobile()), TradeCommBank::getBankMobile, bo.getBankMobile());
        lqw.eq(StringUtils.isNotBlank(bo.getBankAccount()), TradeCommBank::getBankAccount, bo.getBankAccount());
        lqw.eq(StringUtils.isNotBlank(bo.getBankEicon()), TradeCommBank::getBankEicon, bo.getBankEicon());
        lqw.eq(StringUtils.isNotBlank(bo.getBankBranch()), TradeCommBank::getBankBranch, bo.getBankBranch());
        lqw.eq(StringUtils.isNotBlank(bo.getContractUrl()), TradeCommBank::getContractUrl, bo.getContractUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getCancelUrl()), TradeCommBank::getCancelUrl, bo.getCancelUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getOutFlowId()), TradeCommBank::getOutFlowId, bo.getOutFlowId());
        lqw.eq(bo.getInfTime() != null, TradeCommBank::getInfTime, bo.getInfTime());
        lqw.eq(bo.getInfStatus() != null, TradeCommBank::getInfStatus, bo.getInfStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getInfReason()), TradeCommBank::getInfReason, bo.getInfReason());
//        lqw.eq(StringUtils.isNotBlank(bo.getCreateCode()), TradeCommBank::getCreateCode, bo.getCreateCode());
//        lqw.like(StringUtils.isNotBlank(bo.getCreateName()), TradeCommBank::getCreateName, bo.getCreateName());
//        lqw.eq(StringUtils.isNotBlank(bo.getUpdateCode()), TradeCommBank::getUpdateCode, bo.getUpdateCode());
//        lqw.like(StringUtils.isNotBlank(bo.getUpdateName()), TradeCommBank::getUpdateName, bo.getUpdateName());
        return lqw;
    }

    /**
     * 新增客户有为签约
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(TradeCommBankBo bo) {
        if (SignChannelEnum.YZH.eq(bo.getSignChannel())) {
            // 绑定微信用户 openid
            if (StringUtils.isBlank(bo.getWxCode())) {
                throw new ServiceException("微信code不能为空");
            }
            if (StringUtils.isBlank(bo.getBankMobile())) {
                bo.setBankMobile(LoginHelper.getLoginUser().getPhoneNo());
            }

            RemoteClientVo clientVo = remoteClientService.getClientInfo(bo.getClientid());
            RemoteMiniAuthBo authBo = remoteMiniAppService.auth(bo.getWxCode(), clientVo.getDeviceId());
            if (StringUtils.isBlank(authBo.getOpenid())) {
                throw new ServiceException("微信openid不能为空");
            }
            bo.setOpenId(authBo.getOpenid());
            log.keyword("openid", bo.getOpenId(), bo.getCustomerId()).info("userId={}, customerId={}", LoginHelper.getLoginUser().getUserId(), bo.getCustomerId());
        } else {
            // 绑定银行卡
            if (StringUtils.isBlank(bo.getBankMobile())) {
                throw new ServiceException("银行卡绑定手机号不能为空");
            }
            if (StringUtils.isBlank(bo.getSmsCode())) {
                throw new ServiceException("请输入验证码");
            }
            if (!remoteCaptchaService.verify(bo.getSmsCode(), CaptchaCodeType.BindCard, bo.getBankMobile())) {
                throw new ServiceException("验证码错误");
            }
            if (StringUtils.isBlank(bo.getBankAccount())) {
                throw new ServiceException("银行卡号不能为空");
            }
        }

        TradeCommBank add = MapstructUtils.convert(bo, TradeCommBank.class);
        validEntityBeforeSave(add);
        add.setInfTime(new Date());
        add.setInfStatus(YouweiSignInfStatusEnum.PROCESSING.getInfStatus());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        signRequest(bo);
        return flag;
    }

    public boolean signRequest(TradeCommBankBo bo) {
        // 请求有为
        try {
            TradeCommBankBo res = commServiceHelper.getService(bo.getSignChannel()).sign(bo);
            String reason = YouweiSignInfStatusEnum.PROCESSING.eq(res.getInfStatus()) ? "" :  res.getInfReason();
            log.keyword("signRequest", bo.getCustomerId()).info("res={} 客户={} 签约返回={}", res, bo.getCustomerId(), res.getOutFlowId());
            baseMapper.update(Wrappers.lambdaUpdate(TradeCommBank.class)
                    .set(res.getOutFlowId() != null, TradeCommBank::getOutFlowId, res.getOutFlowId())
                    .set(TradeCommBank::getInfStatus, res.getInfStatus())
                    .set(TradeCommBank::getInfReason, reason ==  null ? "" : reason)
                    .eq(TradeCommBank::getId, bo.getId()));
            return true;
        } catch (Exception e) {
            log.keyword("signRequestErr", bo.getCustomerId()).error( bo.getCustomerId() + "签约请求失败", e);
            baseMapper.update(Wrappers.lambdaUpdate(TradeCommBank.class)
                    .set(TradeCommBank::getInfStatus, YouweiSignInfStatusEnum.FAIL.getInfStatus())
                    .set(TradeCommBank::getInfReason, StringUtils.abbreviate(e.getMessage(), 120))
                    .eq(TradeCommBank::getId, bo.getId()));
        }
        return false;
    }

    /**
     * 修改客户有为签约
     */
    @Override
    public Boolean updateByBo(TradeCommBankBo bo) {
        TradeCommBank bank = baseMapper.selectById(bo.getId());
        if (bank == null) {
            throw new ServiceException("查不到绑卡信息");
        }
        if (SignChannelEnum.YOUWEI.eq(bo.getSignChannel())) {
            if (StringUtils.isNotBlank(bo.getBankMobile())) {
                if (StringUtils.isBlank(bo.getSmsCode())) {
                    throw new ServiceException("请输入验证码");
                }
                if (!remoteCaptchaService.verify(bo.getSmsCode(), CaptchaCodeType.BindCard, bo.getBankMobile())) {
                    throw new ServiceException("验证码错误");
                }
            }
        }
        if (YouweiSignInfStatusEnum.matchAny(bank.getInfStatus(), YouweiSignInfStatusEnum.PROCESSING, YouweiSignInfStatusEnum.FAIL, YouweiSignInfStatusEnum.FINISH)) {
            TradeCommBank update = MapstructUtils.convert(bo, TradeCommBank.class);
            validEntityBeforeSave(update);
            if (baseMapper.updateById(update) > 0) {
                if (StringUtils.isEmpty(bank.getOutFlowId()) || YouweiSignInfStatusEnum.FAIL.eq(bank.getInfStatus())) {
                    bo.setCustomerId(bank.getCustomerId());
                    bo.setCustomerCode(bank.getCustomerCode());
                    signRequest(bo);
                }
                return true;
            }
        }
        return false;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TradeCommBank entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public TradeCommBankVo queryByCustomerId(Long customerId) {
        LambdaQueryWrapper<TradeCommBank> lqw = Wrappers.lambdaQuery();
        lqw.eq(TradeCommBank::getCustomerId,customerId);
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public void updateSignCallback(TradeCommBankBo updateBo) {
        if (updateBo == null || updateBo.getOutFlowId() == null || updateBo.getInfStatus() == null) {
            return ;
        }
        LambdaQueryWrapper<TradeCommBank> lqw = Wrappers.lambdaQuery();
        lqw.eq(TradeCommBank::getOutFlowId, updateBo.getOutFlowId());
        TradeCommBank bank = baseMapper.selectOne(lqw);
        if (bank == null) {
            return;
        }

        if (YouweiSignInfStatusEnum.PROCESSING.getInfStatus().equals(bank.getInfStatus()) || YouweiSignInfStatusEnum.FAIL.getInfStatus().equals(bank.getInfStatus())) {
            baseMapper.update(Wrappers.lambdaUpdate(TradeCommBank.class)
                    .set(TradeCommBank::getContractUrl, updateBo.getContractUrl())
                    .set(TradeCommBank::getInfStatus, updateBo.getInfStatus())
                    .set(TradeCommBank::getInfReason, YouweiSignInfStatusEnum.FINISH.eq(updateBo.getInfStatus()) ? "" :  updateBo.getInfReason())
                    .eq(TradeCommBank::getId, bank.getId()));

            bank.setContractUrl(updateBo.getContractUrl());
            downloadContract(bank);
        }
    }

    @Override
    public boolean resign(Long id, Boolean force) {
        TradeCommBank bank = baseMapper.selectById(id);
        if (StringUtils.isNotEmpty(bank.getOutFlowId())) {
            if (Boolean.FALSE.equals(force)) {
                return false;
            }
            // 先解约
            doSignClose(bank);
        }
        TradeCommBankBo bo = BeanUtil.copyProperties(bank, TradeCommBankBo.class);
        return signRequest(bo);
    }

    @Override
    public boolean signClose(Long id) {
        TradeCommBank bank = baseMapper.selectById(id);
        if (bank.getOutFlowId() != null) {
            return doSignClose(bank);
        }
        return true;
    }

    @Override
    public boolean checkSign(Long id) {
        TradeCommBank bank = baseMapper.selectById(id);
        if (StringUtils.isNotEmpty(bank.getOutFlowId())) {
            TradeCommBankBo res = commServiceHelper.getService(bank.getSignChannel()).signQuery(bank);
            if (!Objects.equals(bank.getInfStatus(), res.getInfStatus())) {
                baseMapper.update(Wrappers.lambdaUpdate(TradeCommBank.class)
                        .set(StringUtils.isEmpty(bank.getContractUrl()), TradeCommBank::getContractUrl, res.getContractUrl())
                        .set(TradeCommBank::getInfStatus, res.getInfStatus())
                        .set(TradeCommBank::getInfReason, YouweiSignInfStatusEnum.FINISH.eq(res.getInfStatus()) ? "" :  res.getInfReason())
                        .eq(TradeCommBank::getId, bank.getId()));

                bank.setContractUrl(res.getContractUrl());
                downloadContract(bank);
            }
            return true;
        }

        return false;
    }

    private boolean doSignClose(TradeCommBank bank) {
        TradeCommBankBo res = commServiceHelper.getService(bank.getSignChannel()).signClose(bank);
        if (YouweiSignInfStatusEnum.CLOSE.getInfStatus().equals(bank.getInfStatus())) {
            baseMapper.update(Wrappers.lambdaUpdate(TradeCommBank.class)
                    .set( res.getContractUrl() != null, TradeCommBank::getCancelUrl, res.getContractUrl())
                    .set(TradeCommBank::getOutFlowId, null)
                    .eq(TradeCommBank::getId, bank.getId()));
            return true;
        }
        return false;
    }

    @Async
    private void downloadContract(TradeCommBank bank) {
        if (StringUtils.isEmpty(bank.getContractUrl())) {
            return;
        }
        byte[] content = commServiceHelper.getService(bank.getSignChannel()).downloadContract(bank);
        if (content != null && content.length > 0) {
            // 下载合同文件，上传到般果的OSS
            RemoteFile upload = remoteFileService.upload(bank.getCustomerCode(), bank.getCustomerCode(), "application/pdf", content);
            baseMapper.update(Wrappers.lambdaUpdate(TradeCommBank.class)
                    .set(TradeCommBank::getContractUrl, upload.getUrl())
                    .eq(TradeCommBank::getId, bank.getId()));
        }
    }
}