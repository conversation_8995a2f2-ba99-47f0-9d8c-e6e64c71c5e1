package cn.xianlink.trade.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.api.enums.trade.AccountOrgPropertyEnum;
import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.api.enums.trade.AccountStatusEnum;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.bo.RemoteQueryInfoListBo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.common.api.util.BaseEntityAutoFill;
import cn.xianlink.trade.api.domain.vo.RemoteSupAvailDateVo;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.constant.TradeCacheNames;
import cn.xianlink.trade.domain.TradeAccAvail;
import cn.xianlink.trade.domain.TradeAccAvailTrans;
import cn.xianlink.trade.domain.TradeAccTrans;
import cn.xianlink.trade.domain.TradeOrgRelation;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupAvailTransExportBo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupAvailTransSkuOrderQueryBo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupAvailTransSkuQueryBo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupDeptAcctDetailQueryBo;
import cn.xianlink.trade.domain.vo.TradeAccAvailTransVo;
import cn.xianlink.trade.domain.vo.TradeAccAvailVo;
import cn.xianlink.trade.domain.vo.TradeAccTransVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgRelationVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransOrderVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransSkuVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupSkuVo;
import cn.xianlink.trade.domain.vo.sup.excel.SupTradeAccSupDeptAcctRefundExcelVo;
import cn.xianlink.trade.mapper.TradeAccAvailMapper;
import cn.xianlink.trade.mapper.TradeAccAvailTransMapper;
import cn.xianlink.trade.mapper.TradeAccTransMapper;
import cn.xianlink.trade.mapper.TradeOrgRelationMapper;
import cn.xianlink.trade.service.ITradeAccAvailTransService;
import cn.xianlink.trade.service.biz.TradeBaseUtilBizService;
import cn.xianlink.trade.utils.SheetTableDataInfo;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 分账变更流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class TradeAccAvailTransServiceImpl implements ITradeAccAvailTransService {

    private final transient TradeOrgRelationMapper tradeOrgRelationMapper;
    private final transient TradeAccAvailTransMapper baseMapper;
    private final transient TradeAccAvailMapper tradeAccAvailMapper;
    private final transient TradeAccTransMapper tradeAccTransMapper;
    private final transient TradeBaseUtilBizService tradeBaseUtilBizService;

    @DubboReference
    private final transient RemoteSupplierSkuService remoteSupplierSkuService;

    private void setSupplierSku(List<? extends SupTradeAccSupSkuVo> supSkuVos) {
        List<Long> supplierSkuIds = supSkuVos.stream().map(SupTradeAccSupSkuVo::getSupplierSkuId).filter(vo -> vo != null && vo != 0L).distinct().toList();
        Map<Long, RemoteSupplierSkuInfoVo> skuInfoVoMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(supplierSkuIds)) {
            List<RemoteSupplierSkuInfoVo> skuInfoVoList = remoteSupplierSkuService.queryInfoList(new RemoteQueryInfoListBo().setSupplierSkuIdList(supplierSkuIds));
            // List<RemoteSupplierSkuFundsInfoVo> skuInfoVoList = remoteSupplierSkuService.queryInfoListByFunds(supplierSkuIds);
            if (ObjectUtil.isNotEmpty(skuInfoVoList)) {
                skuInfoVoMap = skuInfoVoList.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, u -> u, (n1, n2) -> n1));
            }
        }
        Map<Long, RemoteSupplierSkuInfoVo> finalSkuInfoVoMap = skuInfoVoMap;
        supSkuVos.forEach(vo -> {
            RemoteSupplierSkuInfoVo skuInfoVo = finalSkuInfoVoMap.get(vo.getSupplierSkuId());
            if (ObjectUtil.isNotEmpty(skuInfoVo)) {
                vo.setSupplierSkuName(skuInfoVo.getSpuName());
                vo.setProducer(skuInfoVo.getProducer());
                vo.setSpuStandards(skuInfoVo.getSpuStandards());
                // 产地简称
                // vo.setAreaCode(skuInfoVo.getAreaCode());
                vo.setBrand(skuInfoVo.getBrand());
                vo.setShortProducer(skuInfoVo.getShortProducer());
            }
        });
    }

    /**
     * 资金账户内业务单查询
     */
    @Override
    public TableDataInfo<SupTradeAccSupDeptAvailTransVo> customSupPageList(SupTradeAccSupDeptAcctDetailQueryBo bo) {
        List<List<LocalDate>> dates = getRangeDate(bo.getTransDateStart(), bo.getTransDateEnd(), bo.getAccountDate());
        Page<SupTradeAccSupDeptAvailTransVo> pageList = new Page<>();
        int days = dates.get(0).size();
        if (days > 0) {
            if(ObjectUtil.isEmpty(bo.getSettleStatus()) || Objects.equals(0, bo.getSettleStatus())){
                bo.setTransDateStart(dates.get(0).get(days - 1)).setTransDateEnd(dates.get(0).get(0));
                Page<TradeAccTrans> page = bo.build();
                page.setOptimizeJoinOfCountSql(false);
                pageList = baseMapper.customSupUndonePageList(page, dates.get(0), bo);
            }
        }
        int availDay = dates.get(1).size();
        if (availDay > 0) {
            if(ObjectUtil.isEmpty(bo.getSettleStatus()) || (Objects.equals(1, bo.getSettleStatus()) || Objects.equals(2, bo.getSettleStatus()))){
                bo.setTransDateStart(dates.get(1).get(availDay - 1)).setTransDateEnd(dates.get(1).get(0));
                Page<TradeAccAvailTrans> page = bo.build(); // 两次查询， page 对象不能使用同一个 ！！！！！！！！！！！！！！！！！
                page.setOptimizeJoinOfCountSql(false);
                Page<SupTradeAccSupDeptAvailTransVo> list = baseMapper.customSupAvailPageList(page, dates.get(1), bo);
                List<SupTradeAccSupDeptAvailTransVo> records = new ArrayList<>(pageList.getRecords());
                records.addAll(CollectionUtil.sub(list.getRecords(), 0, availDay));
                pageList.setRecords(records);
                pageList.setTotal(pageList.getTotal() + list.getTotal());
                /*
                Map<String, SupTradeAccSupDeptAvailTransVo> listMap = list.getRecords().stream().collect(Collectors.toMap(vo -> vo.getTransDate().toString(), Function.identity()));
                for (SupTradeAccSupDeptAvailTransVo transVo : pageList.getRecords()) {
                    SupTradeAccSupDeptAvailTransVo availTransVo = listMap.get(transVo.getTransDate().toString());
                    availTransVo.setStatus(0);
                    availTransVo.setTotalAmt(transVo.getTotalAmt().add(availTransVo.getTotalAmt()));
                    availTransVo.setPayAmt(transVo.getPayAmt().add(availTransVo.getPayAmt()));
                    availTransVo.setRefundAmt(transVo.getRefundAmt().add(availTransVo.getRefundAmt()));
                    // availTransVo.setRefundAmt1(transVo.getRefundAmt1().add(availTransVo.getRefundAmt1()));
                    availTransVo.setRefundLackAmt1(transVo.getRefundLackAmt1().add(availTransVo.getRefundLackAmt1()));
                    availTransVo.setRefundShortAmt1(transVo.getRefundShortAmt1().add(availTransVo.getRefundShortAmt1()));
                    availTransVo.setRefundAmt2(transVo.getRefundAmt2().add(availTransVo.getRefundAmt2()));
                    availTransVo.setRefundAmt3(transVo.getRefundAmt3().add(availTransVo.getRefundAmt3()));
                    availTransVo.setInOutAmt(transVo.getInOutAmt().add(availTransVo.getInOutAmt()));
                    availTransVo.setInAmt(transVo.getInAmt().add(availTransVo.getInAmt()));
                    availTransVo.setOutAmt(transVo.getOutAmt().add(availTransVo.getOutAmt()));
                    availTransVo.setUndonePayAmt(transVo.getUndonePayAmt().add(availTransVo.getUndonePayAmt()));
                    availTransVo.setUndoneRefundAmt(transVo.getUndoneRefundAmt().add(availTransVo.getUndoneRefundAmt()));
                    availTransVo.setUndoneRefundLackAmt1(transVo.getUndoneRefundLackAmt1().add(availTransVo.getUndoneRefundLackAmt1()));
                    availTransVo.setUndoneRefundShortAmt1(transVo.getUndoneRefundShortAmt1().add(availTransVo.getUndoneRefundShortAmt1()));
                    availTransVo.setUndoneRefundAmt3(transVo.getUndoneRefundAmt3().add(availTransVo.getUndoneRefundAmt3()));
                    availTransVo.setOverduePayAmt(transVo.getOverduePayAmt().add(availTransVo.getOverduePayAmt()));
                    availTransVo.setOverdueRefundAmt(transVo.getOverdueRefundAmt().add(availTransVo.getOverdueRefundAmt()));
                    availTransVo.setOverdueRefundLackAmt1(transVo.getOverdueRefundLackAmt1().add(availTransVo.getOverdueRefundLackAmt1()));
                    availTransVo.setOverdueRefundShortAmt1(transVo.getOverdueRefundShortAmt1().add(availTransVo.getOverdueRefundShortAmt1()));
                    availTransVo.setOverdueRefundAmt3(transVo.getOverdueRefundAmt3().add(availTransVo.getOverdueRefundAmt3()));
                }
                pageList = list;
                */
            }
        }
        return TableDataInfo.build(pageList);
    }

    public static List<List<LocalDate>> getRangeDate(LocalDate start, LocalDate end, LocalDate accountDate) {
        List<List<LocalDate>> list = new ArrayList<>();
        list.add(new ArrayList<>());
        list.add(new ArrayList<>());
        LocalDate current = end;
        LocalDate split = accountDate == null ? start.plusDays(-1) : accountDate.plusDays(1);
        while (!current.isBefore(start)) {
            if (!current.isBefore(split)) {
                list.get(0).add(current);
            } else {
                list.get(1).add(current);
            }
            current = current.plusDays(-1);
        }
        return list;
    }

    /**
     * 查询结算单列表
     */
    public SheetTableDataInfo<SupTradeAccSupDeptAvailTransSkuVo> customSupSkuPageList(SupTradeAccSupAvailTransSkuQueryBo bo) {
        Page<TradeAccAvailTrans> page = bo.build();
        page.setOptimizeJoinOfCountSql(false);
        String symbol = bo.getType().contains("Undone") ? "-" : "+";
        Page<SupTradeAccSupDeptAvailTransSkuVo> list = bo.getStatus() == 0 ?
                baseMapper.customSupUndoneSkuPageList(page, bo, symbol) : baseMapper.customSupAvailSkuPageList(page, bo, symbol);
        SupTradeAccSupDeptAvailTransSkuVo totalRow = bo.getStatus() == 0 ?
                baseMapper.customSupUndoneSkuPageTotal(bo, symbol) : baseMapper.customSupAvailSkuPageTotal(bo, symbol);
        setSupplierSku(list.getRecords());
        return SheetTableDataInfo.build(list, totalRow);
    }

    public List<SupTradeAccSupDeptAvailTransSkuVo> customSupSkuTransIds(SupTradeAccSupAvailTransSkuQueryBo bo, List<SupTradeAccSupDeptAvailTransSkuVo> list) {
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Long> skuIds = list.stream().map(SupTradeAccSupDeptAvailTransSkuVo::getSupplierSkuId).collect(Collectors.toSet()).stream().toList();
        return bo.getStatus() == 0 ? baseMapper.customSupUndoneSkuTransIds(bo, skuIds) : baseMapper.customSupAvailSkuTransIds(bo, skuIds);
    }

    /**
     * 查询结算单列表
     */
    public SheetTableDataInfo<SupTradeAccSupDeptAvailTransOrderVo> customSupOrderPageList(SupTradeAccSupAvailTransSkuOrderQueryBo bo) {
        Page<TradeAccAvailTrans> page = bo.build();
        page.setOptimizeJoinOfCountSql(false);
        String symbol = bo.getType().contains("Undone") ? "-" : "+";
        Page<SupTradeAccSupDeptAvailTransOrderVo> list = bo.getStatus() == 0 ?
                baseMapper.customSupUndoneOrderPageList(page, bo, symbol) : baseMapper.customSupAvailOrderPageList(page, bo, symbol);
        SupTradeAccSupDeptAvailTransOrderVo totalRow = bo.getStatus() == 0 ?
                baseMapper.customSupUndoneOrderPageTotal(bo, symbol) : baseMapper.customSupAvailOrderPageTotal(bo, symbol);
        setSupplierSku(list.getRecords());
        return SheetTableDataInfo.build(list, totalRow);
    }

    @Override
    @BaseEntityAutoFill
    public List<SupTradeAccSupDeptAcctRefundExcelVo> customSupAvailTransExportList(SupTradeAccSupAvailTransExportBo bo) {
        List<SupTradeAccSupDeptAcctRefundExcelVo> exportList = baseMapper.customSupAvailTransExportList(bo);
        setSupplierSku(exportList);
        return exportList;
    }

    @Override
    public List<RemoteSupAvailDateVo> queryeAvailTransList(LocalDate availDate, int pageNum, int pageSize) {
        PageQuery page = new PageQuery();
        page.setPageNum(pageNum == 0 ? 1 : pageNum);
        page.setPageSize(pageSize);
        // 返回 结算 + 最小销售日
        return MapstructUtils.convert(tradeAccAvailMapper.selectList(page.build(), Wrappers.lambdaQuery(TradeAccAvail.class)
                        .select(TradeAccAvail::getAvailDate, TradeAccAvail::getOrgCode, TradeAccAvail::getOrgId)
                        .eq(TradeAccAvail::getAvailDate, availDate).isNull(pageNum == 0, TradeAccAvail::getTransDate)
                        .eq(TradeAccAvail::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode())
                        .groupBy(TradeAccAvail::getAvailDate, TradeAccAvail::getOrgCode, TradeAccAvail::getOrgId)
                .orderByAsc(TradeAccAvail::getOrgId)
        ), RemoteSupAvailDateVo.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void appendAvailTrans(LocalDate accountDate, LocalDate availDate) {
        List<TradeAccTransVo> transVos = tradeAccTransMapper.selectVoList(Wrappers.query(TradeAccTrans.class)
                .select("org_code,min(org_id) as org_id,min(acct_org_id) as acct_org_id")
                .eq("trans_date", accountDate).eq("org_type", AccountOrgTypeEnum.SUPPLIER.getCode())
                .eq("del_flag", 0).in("trans_type", Stream.concat(Constants.RefundTypes.stream(), Constants.PayTypes.stream()).toList())
                .groupBy("org_code"));
        List<TradeAccAvailVo> availVos = tradeAccAvailMapper.selectVoList(Wrappers.lambdaQuery(TradeAccAvail.class)
                .select(TradeAccAvail::getOrgCode)
                .eq(TradeAccAvail::getAvailDate, availDate).eq(TradeAccAvail::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode())
                .groupBy(TradeAccAvail::getOrgCode));
        Map<String, TradeAccAvailVo> availMap = availVos.stream().collect(Collectors.toMap(TradeAccAvailVo::getOrgCode, Function.identity()));
        List<TradeAccAvail> noExists = transVos.stream().filter(vo -> !availMap.containsKey(vo.getOrgCode())).map(vo -> {
            TradeAccAvail accAvail = new TradeAccAvail();
            accAvail.setAcctOrgId(vo.getAcctOrgId());
            accAvail.setOrgId(vo.getOrgId());
            accAvail.setOrgCode(vo.getOrgCode());
            accAvail.setOrgType(AccountOrgTypeEnum.SUPPLIER.getCode());
            accAvail.setIsAuto(1);
            accAvail.setDeptId(0L);
            accAvail.setAvailDate(availDate);
            accAvail.setStatus(AccountStatusEnum.INTI.getCode());
            accAvail.setStatusTime(Convert.toDate(DateUtil.now()));
            accAvail.setAvailNo(tradeBaseUtilBizService.getNextAvailNo(accAvail.getAvailDate()));
            return accAvail;
        }).toList();
        tradeAccAvailMapper.insertBatch(noExists);
    }

    @Override
    public void deleteAvailTransAll(LocalDate availDate) {
        tradeAccAvailMapper.update(Wrappers.lambdaUpdate(TradeAccAvail.class)
                .set(TradeAccAvail::getTransDate, null).eq(TradeAccAvail::getAvailDate, availDate)
                .eq(TradeAccAvail::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode()));
    }

    @Override
    @CacheEvict(cacheNames = TradeCacheNames.CACHE_BASE_DATA, key = "#supplierCode + '_date'")
    @Transactional(rollbackFor = Exception.class)
    public void createAvailTrans(String supplierCode, LocalDate accountDate, LocalDate availDate) {
        // 0 更新账单日
        tradeOrgRelationMapper.update(Wrappers.lambdaUpdate(TradeOrgRelation.class)
                .set(TradeOrgRelation::getAccountDate, accountDate).eq(TradeOrgRelation::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode())
                .eq(TradeOrgRelation::getOrgCode, supplierCode).eq(TradeOrgRelation::getOutOrgProperty, AccountOrgPropertyEnum.SH.getCode())
                .and(ll -> ll.isNull(TradeOrgRelation::getAccountDate).or(l -> l.lt(TradeOrgRelation::getAccountDate, accountDate)))
        );
        // 1 清除历史数据
        baseMapper.delete(Wrappers.lambdaQuery(TradeAccAvailTrans.class)
                .eq(TradeAccAvailTrans::getAccountDate, accountDate).eq(TradeAccAvailTrans::getOrgCode, supplierCode));
        // 2 插入销售日数据
        baseMapper.insertSupUndonePay(supplierCode, accountDate, availDate);
        // 2 插入销售日数据
        baseMapper.insertSupUndoneOccupy(supplierCode, accountDate, availDate);
        // 3 插入非销售日的结算数据
        List<Long> availIds = tradeAccAvailMapper.selectVoList(Wrappers.lambdaQuery(TradeAccAvail.class)
                .select(TradeAccAvail::getId).eq(TradeAccAvail::getAvailDate, availDate)
                .eq(TradeAccAvail::getOrgCode, supplierCode).eq(TradeAccAvail::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode()))
                .stream().map(TradeAccAvailVo::getId).toList();
        if (availIds.size() > 0) {
            baseMapper.insertSupOverdue(availIds, accountDate, availDate);
            tradeAccAvailMapper.update(Wrappers.lambdaUpdate(TradeAccAvail.class)
                    .set(TradeAccAvail::getTransDate, accountDate).in(TradeAccAvail::getId, availIds));
        }
    }

    /**
     * 查询超时结算单列表
     */
    @Override
    public List<TradeAccAvailTransVo> querySupOverdueList(String supplierCode, LocalDate accountDate, LocalDate availDate) {
        return baseMapper.selectVoList(Wrappers.lambdaQuery(TradeAccAvailTrans.class)
                .select(TradeAccAvailTrans::getTransId, TradeAccAvailTrans::getAvailId,
                        TradeAccAvailTrans::getAvailNo, TradeAccAvailTrans::getAvailDate)
                .eq(TradeAccAvailTrans::getAccountDate, accountDate).eq(TradeAccAvailTrans::getOrgCode, supplierCode)
                .ne(TradeAccAvailTrans::getStatus, 0).lt(TradeAccAvailTrans::getTransDate, accountDate)
        );
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSupUndoneBatch(List<TradeAccAvailTransVo> transVos) {
        baseMapper.updateSupUndoneBatch(transVos);
    }

    @Override
    @Cacheable(cacheNames = TradeCacheNames.CACHE_BASE_DATA, key = "#supplierCode + '_date'")
    public String queryeSupAccountDate(String supplierCode) {
        TradeOrgRelationVo vo = tradeOrgRelationMapper.selectVoOne(Wrappers.lambdaQuery(TradeOrgRelation.class)
                .select(TradeOrgRelation::getAccountDate)
                .isNotNull(TradeOrgRelation::getAccountDate).eq(TradeOrgRelation::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode())
                .eq(TradeOrgRelation::getOrgCode, supplierCode).eq(TradeOrgRelation::getOutOrgProperty, AccountOrgPropertyEnum.SH.getCode()));
        if (vo != null && vo.getAccountDate() != null) {
            return vo.getAccountDate().toString();
        }
        LocalDate nowDate = LocalDate.now();
        for (int i = 0; i < 10 ; i ++) {
            // 每 10 天查一次， 大部分第一次的sql就有结果
            List<TradeAccAvail> vos = tradeAccAvailMapper.selectList(Wrappers.lambdaQuery(TradeAccAvail.class)
                    .select(TradeAccAvail::getTransDate).eq(TradeAccAvail::getOrgCode, supplierCode)
                    .eq(TradeAccAvail::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode()).isNotNull(TradeAccAvail::getTransDate)
                    .between(TradeAccAvail::getAvailDate, nowDate.plusDays(- 9 - i * 10L), nowDate.plusDays(- i * 10L))
                    .orderByDesc(TradeAccAvail::getTransDate).last("limit 1"));
            if (vos.size() > 0) {
                return vos.get(0).getTransDate().toString();
            }
        }
        return null;
    }

}

