package cn.xianlink.trade.controller.admin;

import cn.xianlink.common.api.enums.trade.AccountFileTypeEnum;
import cn.xianlink.common.api.enums.trade.PayChannelEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.domain.bo.TradeAccOssDownBo;
import cn.xianlink.trade.domain.bo.TradeAccOssQueryBo;
import cn.xianlink.trade.domain.vo.excel.ExcelDataInfo;
import cn.xianlink.trade.domain.vo.TradeAccOssVo;
import cn.xianlink.trade.service.ITradeAccOssService;
import cn.xianlink.trade.service.biz.TradeTransFileBizService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 对账数据文件
 *
 * <AUTHOR>
 * @date 2024-06-12
 * @folder 般果管理中心/财务流水/对账数据文件
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/accOss")
public class TradeAccOssController extends BaseController {

    private final transient ITradeAccOssService tradeAccOssService;
    private final transient TradeTransFileBizService tradeTransFileBizService;

    /**
     * 查询数据文件
     */
    @Operation(summary = "查询数据文件")
    @PostMapping("/page")
    public R<TableDataInfo<TradeAccOssVo>> page(@Validated @RequestBody TradeAccOssQueryBo bo) {
        return R.ok(tradeAccOssService.queryPageList(bo));
    }

    /**
     * 日对账数据导出（ CZ:充值文件;TX:提现文件;JY:交易文件;YE:余额文件）
     *   选中页面一行后的文件导出
     */
    @Operation(summary = "日对账数据导出")
    @PostMapping("/export")
    public void ossExport(@Validated TradeAccOssDownBo bo, HttpServletResponse response) {
        checkOssDownBo(bo);
        ExcelDataInfo<T> data = tradeTransFileBizService.createExcelData(bo);
        ExcelUtil.exportExcel(data.getList(), data.getTitle(), data.getClazz(), response);
    }

    /**
     * 云下载并对账
     *  @ignore
     */
    @Operation(summary = "云下载并对账")
    @RepeatSubmit()
    @PostMapping("/cloudDownload")
    public R<Void> cloudDownload(@Validated @RequestBody TradeAccOssDownBo bo) {
        checkOssDownBo(bo);
        tradeTransFileBizService.cloudDownload(bo);
        return R.ok();
    }

    private void checkOssDownBo(TradeAccOssDownBo bo) {
        AccountFileTypeEnum typeEnum = AccountFileTypeEnum.getEnumByCode(bo.getFileType());
        if (typeEnum == null) {
            throw new ServiceException("不支持的文件类型");
        }
        if (PayChannelEnum.PAY_PINGAN_CLOUD_WX.getCode().equals(typeEnum.getChannel())) {
            bo.setMerchantId("");
        } else if (StringUtils.isBlank(bo.getMerchantId())) {
            throw new ServiceException("商户号不能为空");
        }
    }
}
