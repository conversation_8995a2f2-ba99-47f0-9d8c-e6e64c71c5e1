package cn.xianlink.trade.controller.sup;

import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.common.api.enums.system.SysUserTypeEnum;
import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.system.api.model.LoginUser;
import cn.xianlink.trade.config.properties.ChannelsProperties;
import cn.xianlink.trade.domain.vo.org.TradeSupBaseDataVo;
import cn.xianlink.trade.service.ITradeAccAvailTransService;
import cn.xianlink.trade.service.biz.TradeBaseDataBizService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;


/**
 * 提现单
 * 前端访问路由地址为:/system/accCash
 *
 * <AUTHOR>
 * @date 2024-06-12
 * @folder 般果管理中心/财务流水/提现流水
 */
@Validated
@RequiredArgsConstructor
@Component
public class SupTradeAccUtils {

    private final transient ChannelsProperties channelsProperties;
    private final transient ITradeAccAvailTransService tradeAccAvailTransService;
    private final transient TradeBaseDataBizService tradeBaseDataBizService;

    @DubboReference
    private final transient RemoteRegionWhService remoteRegionWhService;

    /**
     * 查询供应商数据
     */
    public TradeSupBaseDataVo getLoginSupplier(Long supplierId, Long deptId) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Long relationId = loginUser.getRelationId();
        Long relateionDeptId = loginUser.getDeptId();
        if (SysUserTypeEnum.SYS_USER.getType().equals(loginUser.getUserType()) && deptId != null && deptId == -519696L) {
            relationId = supplierId;
            relateionDeptId = 0L;
        } else {
            if (!SysUserTypeEnum.SUPPLIER_USER.getType().equals(loginUser.getUserType()) || relationId == null) {
                throw new ServiceException("不是供应商用户-1");
            }
            if (deptId != null) {
                relateionDeptId = deptId; // 用户所属部门优先
            }
        }
        RemoteBaseDataVo vo = tradeBaseDataBizService.queryById(relationId, AccountOrgTypeEnum.SUPPLIER.getCode());
        if (vo == null) {
            throw new ServiceException("不是供应商用户-2");
        }
        TradeSupBaseDataVo baseDataVo = MapstructUtils.convert(vo, TradeSupBaseDataVo.class);
        baseDataVo.setDeptId(relateionDeptId == null ? 0L : relateionDeptId);
        // 账单日
        if (channelsProperties.isDebugLocal()) {
            baseDataVo.setAccountDate(LocalDate.now().plusDays(-3));
        } else {
            String accountDate = tradeAccAvailTransService.queryeSupAccountDate(baseDataVo.getCode());
            baseDataVo.setAccountDate(accountDate == null ? null : LocalDate.parse(accountDate));
        }
        return baseDataVo;
    }

    public LocalDate getLastSaleDate(Long regionWhId) {
        if (regionWhId != null) {
            RemoteRegionWhVo vo = remoteRegionWhService.queryById(regionWhId);
            if (vo != null) {
                return SaleDateUtil.getSaleDate(vo.getSalesTimeEnd());
            }
        }
        return LocalDate.now();

    }

}
