package cn.xianlink.trade.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.DateUtils;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.api.util.BaseEntityAutoFill;
import cn.xianlink.trade.api.domain.bo.OrderPayJobQueryBo;
import cn.xianlink.trade.config.properties.ChannelsProperties;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.TradeAccTrans;
import cn.xianlink.trade.domain.TradeAccTransfer;
import cn.xianlink.trade.domain.bo.*;
import cn.xianlink.trade.domain.vo.TradeAccSplitVo;
import cn.xianlink.trade.domain.vo.TradeAccTransVo;
import cn.xianlink.trade.domain.vo.TradeAccTransferVo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import cn.xianlink.trade.mapper.TradeAccTransMapper;
import cn.xianlink.trade.mapper.TradeAccTransferMapper;
import cn.xianlink.trade.service.ITradeAccSplitService;
import cn.xianlink.trade.service.ITradeAccTransService;
import cn.xianlink.trade.service.ITradeAccTransferService;
import cn.xianlink.trade.service.ITradePayService;
import cn.xianlink.trade.service.biz.TradeBaseDataBizService;
import cn.xianlink.trade.service.biz.TradeBaseUtilBizService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 划转流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class TradeAccTransferServiceImpl implements ITradeAccTransferService {

    private final transient ChannelsProperties channelsProperties;
    private final transient TradeAccTransferMapper baseMapper;
    private final transient TradeAccTransMapper tradeAccTransMapper;
    private final transient ITradePayService tradePayService;
    private final transient ITradeAccSplitService tradeAccSplitService;
    private final transient ITradeAccTransService tradeAccTransService;
    private final transient TradeBaseUtilBizService tradeBaseUtilBizService;
    private final transient TradeBaseDataBizService tradeBaseDataBizService;

    /**
     * 查询划转流水
     */
    @Override
    public TradeAccTransferVo queryByTransId(Long transId, String busiType) {
        return baseMapper.selectVoOne(Wrappers.lambdaQuery(TradeAccTransfer.class).eq(TradeAccTransfer::getDelFlag, 0)
                .eq(TradeAccTransfer::getTransId, transId).eq(TradeAccTransfer::getBusiType, busiType));
    }
    /**
     * 查询划转流水
     */
    @Override
    public TradeAccTransferVo queryByNo(String transNo){
        return baseMapper.selectVoOne(Wrappers.lambdaQuery(TradeAccTransfer.class)
                .eq(TradeAccTransfer::getTransNo, transNo).eq(TradeAccTransfer::getDelFlag, 0));
    }

    /**
     * 查询划转待审核数据查询
     */
    @Override
    public List<TradeAccTransferVo> queryTransferChecking(OrderPayJobQueryBo bo) {
        LambdaQueryWrapper<TradeAccTransfer> lqw = Wrappers.lambdaQuery();
        lqw.lt(TradeAccTransfer::getInfRetries, Math.max(bo.getInfRetries(),
                bo.getLongTime() ? channelsProperties.getInfMaxRetries():channelsProperties.getInfMinRetries()));
        lqw.between(TradeAccTransfer::getInfTime, bo.getInfTimeStart(), bo.getInfTimeEnd());
        // 1 换供应商,  TI TO 都要变成 AVAIL, 否则不能转账     2 其他划转 FREEZE 就可以转账
        lqw.and(l -> l.ne(TradeAccTransfer::getBusiType, TransferBusiTypeEnum.TRANSFER_CHANGE.getCode()).or()
                .eq(TradeAccTransfer::getStatus, AccountStatusEnum.AVAIL.getCode()));
        lqw.and(l -> l.eq(TradeAccTransfer::getInfSource, TransferInfStatusEnum.CHECK.getCode()).or()
                .eq(TradeAccTransfer::getInfStatus, TransferInfStatusEnum.CHECK.getCode()));
        if (bo.isTransferIn()) {
            // 是供应商转入
            lqw.eq(TradeAccTransfer::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode());
        } else {
            // 是供应商转出
            lqw.eq(TradeAccTransfer::getSourceOrgType, AccountOrgTypeEnum.SUPPLIER.getCode());
        }
        lqw.eq(TradeAccTransfer::getDelFlag, 0);
        lqw.orderByAsc(TradeAccTransfer::getId);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询失败的
     */
    @Override
    public List<TradeAccTransferVo> queryTransferFailed(LocalDate transDate) {
        LambdaQueryWrapper<TradeAccTransfer> lqw = Wrappers.lambdaQuery();
        lqw.between(TradeAccTransfer::getInfTime, transDate, transDate.plusDays(1));
        // 改成仅比较 供应商扣款
        lqw.eq(TradeAccTransfer::getBusiType, TransferBusiTypeEnum.TRANSFER_SUPPLIER.getCode());
        /* 1 换供应商,  TI TO 都要变成 AVAIL, 否则不能转账     2 其他划转 FREEZE 就可以转账
        lqw.and(l -> l.ne(TradeAccTransfer::getBusiType, TransferBusiTypeEnum.TRANSFER_CHANGE.getCode()).or()
                .eq(TradeAccTransfer::getStatus, AccountStatusEnum.AVAIL.getCode()));
        */
        lqw.and(l -> l.eq(TradeAccTransfer::getStatus, AccountStatusEnum.INTI.getCode()).or()
                .ne(TradeAccTransfer::getInfSource, TransferInfStatusEnum.SUCCESS.getCode()).or()
                .ne(TradeAccTransfer::getInfStatus, TransferInfStatusEnum.SUCCESS.getCode()));
        lqw.eq(TradeAccTransfer::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode()); // 并且是供应商转入
        lqw.eq(TradeAccTransfer::getDelFlag, 0);
        return baseMapper.selectVoList(lqw);
    }
    /**
     * 查询划转流水列表
     */
    @Override
    @BaseEntityAutoFill
    public TableDataInfo<TradeAccTransferVo> queryPageList(TradeAccTransferQueryBo bo) {
        LambdaQueryWrapper<TradeAccTransfer> lqw = buildQueryWrapper(bo);
        Page<TradeAccTransferVo> result = baseMapper.selectVoPage(bo.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询划转流水列表
     */
    @Override
    @BaseEntityAutoFill
    public List<TradeAccTransferVo> queryList(TradeAccTransferQueryBo bo) {
        LambdaQueryWrapper<TradeAccTransfer> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TradeAccTransfer> buildQueryWrapper(TradeAccTransferQueryBo bo) {
        LambdaQueryWrapper<TradeAccTransfer> lqw = Wrappers.lambdaQuery();
        lqw.between(TradeAccTransfer::getTransDate, bo.getTransDateStart(), bo.getTransDateEnd());
        lqw.eq(StringUtils.isNotBlank(bo.getTransNo()), TradeAccTransfer::getTransNo, bo.getTransNo());
        lqw.eq(StringUtils.isNotBlank(bo.getRelateNo()), TradeAccTransfer::getRelateNo, bo.getRelateNo());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceAvailNo()), TradeAccTransfer::getSourceAvailNo, bo.getSourceAvailNo());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceSplitNo()), TradeAccTransfer::getSourceSplitNo, bo.getSourceSplitNo());
        lqw.eq(StringUtils.isNotBlank(bo.getAvailNo()), TradeAccTransfer::getAvailNo, bo.getAvailNo());
        lqw.eq(StringUtils.isNotBlank(bo.getSplitNo()), TradeAccTransfer::getSplitNo, bo.getSplitNo());
        lqw.eq(bo.getBusiType() != null, TradeAccTransfer::getBusiType, bo.getBusiType());
        lqw.eq(bo.getStatus() != null, TradeAccTransfer::getStatus, bo.getStatus());
        lqw.in(CollectionUtil.isNotEmpty(bo.getOrgCodes()), TradeAccTransfer::getOrgCode, bo.getOrgCodes());
        lqw.eq(StringUtils.isNotBlank(bo.getOutAcctCode()), TradeAccTransfer::getOutAcctCode, bo.getOutAcctCode());
        lqw.in(CollectionUtil.isNotEmpty(bo.getSourceOrgCodes()), TradeAccTransfer::getSourceOrgCode, bo.getSourceOrgCodes());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceOutAcctCode()), TradeAccTransfer::getSourceOutAcctCode, bo.getSourceOutAcctCode());
        if (CollectionUtil.isNotEmpty(bo.getAcctOrgCodes())) {
            lqw.in(TradeAccTransfer::getAcctOrgId, tradeBaseUtilBizService.queryIdsByCodes(bo.getAcctOrgCodes(), BaseTypeEnum.REGION_WH.getCode()));
        }
        if (CollectionUtil.isNotEmpty(bo.getTransOrgCodes())) {
            lqw.in(TradeAccTransfer::getTransOrgId, tradeBaseUtilBizService.queryIdsByCodes(bo.getTransOrgCodes(), BaseTypeEnum.CITY_WH.getCode()));
        }
        lqw.eq(TradeAccTransfer::getDelFlag, 0);
        lqw.orderByDesc(TradeAccTransfer::getId);
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradeAccTransferVo insertByBo(TradeAccTransferBo tbo, List<TradeAccTransBo> transBos) {
        TradeAccTransfer add = MapstructUtils.convert(tbo, TradeAccTransfer.class);
        List<TradeAccTrans> addBos = MapstructUtils.convert(transBos, TradeAccTrans.class);
        // 相同机构仅支持 总仓的情况
        if (channelsProperties.getTransferDelayDays() == null || channelsProperties.getTransferDelayDays() <= 0) {
            add.setInfTime(Convert.toDate(DateUtil.now()));
        } else {
            add.setInfTime(DateUtils.addDays(Convert.toDate(DateUtil.now()), channelsProperties.getTransferDelayDays()));
        }
        add.setInfRetries(0);
        add.setInfSource(TransferInfStatusEnum.CHECK.getCode());
        add.setInfStatus(TransferInfStatusEnum.CHECK.getCode());
        add.setStatus(AccountStatusEnum.INTI.getCode());
        add.setSplitAmt(add.getTransAmt());
        // 增加默认值
        if (add.getFeeAmt() == null) {
            add.setFeeAmt(BigDecimal.ZERO);
        }
        if (add.getTransOrgId() == null) {
            add.setTransOrgId(0L);
        }
        // 机构不同，调用银行接口调账，填写分账单号
//        if (!add.getSourceOutOrgCode().equals(add.getOutOrgCode())) {
//            splitNo = ;
//        }
        /*
            1 两个OrgCode相同
                其他机构        异常
                都是总仓时       仅产生负 trans，   不产生 split
            2 两个OutOrgCode相同 而 orgCode 不同
                                一正一负 tran，   不产生 split
            3 OutOrgCode不同  orgCode 不同
                                一正一负 trans，  两条 split
         */
        // 2 转出流水
        Map<Long, RemoteBaseDataVo> skuMap = tradeBaseDataBizService.querySupplierSkuList(
                addBos.stream().map(TradeAccTrans::getSkuId).filter(Objects::nonNull).toList()
        ).stream().collect(Collectors.toMap(RemoteBaseDataVo::getId, Function.identity()));
        boolean isChange = TransferBusiTypeEnum.TRANSFER_CHANGE.getCode().equals(add.getBusiType());
        for (TradeAccTrans trans : addBos) {
            if (trans.getSkuId() != null && AccountOrgTypeEnum.SUPPLIER.getCode().equals(trans.getOrgType())) {
                RemoteBaseDataVo dataVo = skuMap.get(trans.getSkuId());
                if (dataVo == null) {
                    throw new ServiceException(String.format("批次商品id %s不存在",
                            trans.getSkuId() == null ? "" : trans.getSkuId()));
                }
                trans.setSkuName(dataVo.getName());
            } else if (isChange) {
                throw new ServiceException(StringUtils.isNotBlank(add.getTransNo()) ?
                        String.format("划转单 %s 批次商品不存在", add.getTransNo()) :
                        String.format("关联支付单 %s 批次商品不存在", add.getRelateNo()));
            } else {
                trans.setSkuId(0L);
            }
            if (AccountTransTypeEnum.TO.getCode().equals(trans.getTransType())) {
                trans.setBusiField(add.getSourceBusiField());
                trans.setOutOrgCode(add.getSourceOutOrgCode());
                trans.setOutAcctCode(add.getSourceOutAcctCode());
            } else {
                trans.setBusiField(add.getBusiField());
                trans.setOutOrgCode(add.getOutOrgCode());
                trans.setOutAcctCode(add.getOutAcctCode());
            }
            trans.setTransId(add.getTransId());
            trans.setTransNo(add.getTransNo());
            trans.setTransDate(add.getTransDate());
            trans.setTransOrgId(add.getTransOrgId());
            trans.setAcctOrgId(add.getAcctOrgId());
            trans.setStatusTime(Convert.toDate(DateUtil.now()));
            trans.setRemark(add.getRemark());
            trans.setTotalAmt(add.getTransAmt());
            trans.setSplitAmt(trans.getTransAmt());
            trans.setFeeAmt(BigDecimal.ZERO);
            trans.setCommissionAmt(BigDecimal.ZERO);
			trans.setTransMinAmt(BigDecimal.ZERO);
            trans.setTransMaxAmt(trans.getSplitAmt());
            trans.setSplitOrgCode(trans.getOutOrgCode());
            trans.setSplitAcctCode(trans.getOutAcctCode());
            trans.setSplitRelNo("");
            trans.setSplitRelAmt(BigDecimal.ZERO);
            trans.setBusiType(add.getBusiType());
            trans.setRelateType(AccountTransTypeEnum.TI.getCode());
			trans.setRelateId(add.getRelateId());
            trans.setRelateNo(add.getRelateNo());
            trans.setRelateAmt(add.getTransAmt());
        }
        if (isChange) {
            try {
                insertAccTransfer(add);
            } catch (DuplicateKeyException dke) {
                insertAccTransfer(add); // 重试一次
            }
            transferChangeRelate(add, addBos);
        } else {
            baseMapper.insert(add);
        }
        tradeAccSplitService.batchInsertByBo(isChange ? add.getRelateNo() : null, null, false, null, addBos);
        return MapstructUtils.convert(add, TradeAccTransferVo.class);
    }

    private void insertAccTransfer(TradeAccTransfer add) {
        add.setTransNo(tradeBaseUtilBizService.getNextTransferChangeNo(add.getTransDate()));
        baseMapper.insert(add);
    }

    private void transferChangeRelate(TradeAccTransfer add, List<TradeAccTrans> addBos) {
        TradePayVo payVo = tradePayService.queryByNo(add.getRelateNo());
        if (payVo == null) {
            throw new ServiceException(String.format("关联支付单 %s 不存在", add.getRelateNo()));
        }
        add.setAcctOrgId(payVo.getAcctOrgId()); // 对应订单的
        add.setTransOrgId(payVo.getOrderOrgId()); // 对应订单的
        List<TradeAccTransVo> relateTransVos = tradeAccTransService.queryOrderRelateList(payVo.getOrderNo(), null, addBos);
        Map<String, List<TradeAccTransVo>> relateTransMap = relateTransVos.stream().collect(
                Collectors.groupingBy(vo -> String.format("%s_%s_%s", vo.getOrgCode(), vo.getOrgType(), vo.getSkuId()), Collectors.toList()));
        List<TradeAccTransVo> relateSkuVos = null;
        for (TradeAccTrans trans : addBos) {
            if (AccountTransTypeEnum.TO.getCode().equals(trans.getTransType())) {
                relateSkuVos = relateTransMap.get(String.format("%s_%s_%s", trans.getOrgCode(), trans.getOrgType(), trans.getSkuId()));
                if (CollectionUtil.isEmpty(relateSkuVos)) {
                    throw new ServiceException(String.format("关联支付单 %s 商品不存在", payVo.getOrderNo()), R.FAIL);
                }
                if (relateSkuVos.stream().anyMatch(vo -> AccountTransTypeEnum.OR.getCode().equals(vo.getTransType()))) {
                    throw new ServiceException(String.format("关联支付单 %s 已退款", payVo.getOrderNo()));
                }
                if (relateSkuVos.stream().anyMatch(vo -> Constants.TransferTypes.contains(vo.getTransType()))) {
                    throw new ServiceException(String.format("关联支付单 %s 已更换供应商", payVo.getOrderNo()));
                }
                if (relateSkuVos.get(0).getTransAmt().compareTo(trans.getTransAmt().negate()) != 0) {
                    throw new ServiceException(String.format("关联支付单 %s 商品必须全部更换", payVo.getOrderNo()), R.FAIL);
                }
                trans.setSplitRelNo(relateSkuVos.get(0).getSplitNo());
                trans.setSplitRelAmt(relateSkuVos.get(0).getTransAmt());
                trans.setSplitAmt(relateSkuVos.get(0).getSplitAmt().negate());
            } else if (CollectionUtil.isNotEmpty(relateSkuVos)) {
                // 划转入在划转出的下一行
                trans.setSplitRelNo(relateSkuVos.get(0).getSplitNo());
                trans.setSplitRelAmt(relateSkuVos.get(0).getTransAmt());
                trans.setSplitAmt(relateSkuVos.get(0).getSplitAmt());
                add.setSplitAmt(relateSkuVos.get(0).getSplitAmt());
            }
			trans.setTransMaxAmt(trans.getSplitAmt());
            trans.setRelateType(AccountTransTypeEnum.OP.getCode());
            trans.setRelateNo(payVo.getOrderNo());  // 订单关联单号
            trans.setRelateId(payVo.getOrderId());  // 订单关联id
            trans.setRelateAmt(payVo.getPayAmt());
            trans.setTransDate(payVo.getOrderDate());
            // 对应订单的， 后生成的单号
            trans.setTransNo(add.getTransNo());
            trans.setAcctOrgId(payVo.getAcctOrgId());
            trans.setTransOrgId(payVo.getOrderOrgId());
        }
        TradeAccTransfer update = new TradeAccTransfer();
        update.setId(add.getId());
        update.setAcctOrgId(add.getAcctOrgId());             // 换供应商，来源订单
        update.setTransOrgId(add.getTransOrgId());
        update.setSplitAmt(add.getSplitAmt());
        update.setRelateId(payVo.getOrderId());
        baseMapper.updateById(update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradeAccTransferVo updateCheckByBo(boolean isSame, TradeAccTransferBo tbo) {
        // 对接银行的单号不重新生成（因换供应商的trans， splitRelNo 单号已经关联），仅记账日期，和接口调用时间修改 ！！！！！
        TradeAccTransfer update = new TradeAccTransfer();
        LambdaUpdateWrapper<TradeAccTransfer> updateWrapper = Wrappers.lambdaUpdate(TradeAccTransfer.class)
                .eq(TradeAccTransfer::getId, tbo.getId());
        update.setInfTime(Convert.toDate(DateUtil.now()));
        update.setInfRetries(tbo.getInfRetries() + 1); // 两个接口，一次完整处理会变成 2
        if (isSame) {
            update.setInfSource(TransferInfStatusEnum.INF_INIT.getCode());
            updateWrapper.eq(TradeAccTransfer::getInfSource, TransferInfStatusEnum.CHECK.getCode());
        } else {
            update.setInfStatus(TransferInfStatusEnum.INF_INIT.getCode());
            updateWrapper.eq(TradeAccTransfer::getInfStatus, TransferInfStatusEnum.CHECK.getCode());
        }
        if (baseMapper.update(update, updateWrapper) == 0) {
            throw new ServiceException("划转单状态已改变");
        }
        update.setId(tbo.getId());
        if (isSame) {
            // 记账日期
            List<TradeAccTransVo> transferVos = tradeAccTransService.queryTransList(tbo.getTransNo(), Constants.TransferTypes);
            List<TradeAccTransVo> transVos = transferVos.stream().filter(vo -> AccountTransTypeEnum.TO.getCode().equals(vo.getTransType())).toList();
            if (transVos.size() != 1) {
                throw new ServiceException(String.format("划转单 %s 数据异常", tbo.getTransNo()), R.FAIL);
            }
            boolean isChange = TransferBusiTypeEnum.TRANSFER_CHANGE.getCode().equals(tbo.getBusiType());
            if (isChange) {
                // 1 换供应商,  TI TO 都要变成 AVAIL, 否则不能转账     2 其他划转 FREEZE 就可以转账
                List<TradeAccTransVo> orderTransVos = tradeAccTransService.queryOrderRelateList(tbo.getRelateNo(), null,
                        MapstructUtils.convert(transferVos, TradeAccTrans.class));
                if (orderTransVos.stream().filter(vo -> !Constants.TransferTypes.contains(vo.getTransType()))
                        .anyMatch(vo -> AccountStatusEnum.FREEZE.getCode().equals(vo.getStatus()))) {
                    throw new ServiceException(String.format("划转单 %s 关联支付单未完成", tbo.getTransNo()));
                }
                // transVos.get(0).getSplitAmt() 调出是负数
                BigDecimal residueSplitAmt = transVos.get(0).getSplitAmt().negate().add(orderTransVos.stream()
                        .filter(vo -> !Constants.TransferTypes.contains(vo.getTransType()) && vo.getSplitRelNo().equals(transVos.get(0).getSplitRelNo()))
                        .map(TradeAccTransVo::getSplitAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                // 如果划转金额, 被退款扣减后, 实际转账金额会小
                if (residueSplitAmt.compareTo(transVos.get(0).getSplitAmt()) > 0) {
                    update.setSplitAmt(residueSplitAmt);
                    if (residueSplitAmt.compareTo(BigDecimal.ZERO) == 0) {
                        update.setInfSource(TransferInfStatusEnum.SUCCESS.getCode());
                        update.setInfStatus(TransferInfStatusEnum.SUCCESS.getCode());
                        update.setInfReason("");
                        update.setOutSuccessTime(Convert.toDate(DateUtil.now()));
                    }
                }
            }
            // 暂时不调用接口， 所以 batchUpdateByNo 不调用
//            List<TradeAccSplitVo> splitVos = tradeAccSplitService.batchUpdateByNo(
//                    new TradeAccSplitAddBo().setTotalAmt(transVos.get(0).getSplitAmt()).setChannel("").setTradeNo("")
//                            .setInfTime(update.getInfTime()).setInitTime(tbo.getCreateTime()),
//                    MapstructUtils.convert(transVos, TradeAccTrans.class));
//            if (CollectionUtil.isNotEmpty(splitVos)) {
//                update.setSourceSplitNo(splitVos.get(0).getSplitNo());
//            }
        } else {
            List<TradeAccTransVo> transVos = tradeAccTransService.queryTransList(tbo.getTransNo(),
                    Collections.singletonList(AccountTransTypeEnum.TI.getCode()));
            if (transVos.size() != 1) {
                throw new ServiceException(String.format("划转单 %s 数据异常", tbo.getTransNo()), R.FAIL);
            }
            // 暂时不调用接口， 所以 batchUpdateByNo 不调用
//            List<TradeAccSplitVo> splitVos = tradeAccSplitService.batchUpdateByNo(false,
//                    new TradeAccSplitAddBo().setTotalAmt(transVos.get(0).getSplitAmt()).setChannel("").setTradeNo("")
//                            .setInfTime(update.getInfTime()).setInitTime(tbo.getCreateTime()),
//                    MapstructUtils.convert(transVos, TradeAccTrans.class));
//            if (CollectionUtil.isNotEmpty(splitVos)) {
//                update.setSplitNo(splitVos.get(0).getSplitNo());
//            }
        }
        baseMapper.updateById(update);
        TradeAccTransferVo transferVo = MapstructUtils.convert(tbo, TradeAccTransferVo.class);
        BeanUtil.copyProperties(update, transferVo, Constants.BeanCopyIgnoreNullValue);
        return transferVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTransferInfData(TradeAccTransferBo bo, TradeAccTransferVo vo) {
        TradeAccTransfer update = MapstructUtils.convert(bo, TradeAccTransfer.class);
        boolean isFail = TransferInfStatusEnum.FAIL.getCode().equals(update.getInfStatus());
        update.setDelFlag(isFail ? update.getId() : 0);
        int count = baseMapper.updateById(update);
        if (count > 0) {
            if (isFail) {
                tradeAccSplitService.deleteSplit(true, new TradeAccSplitUpdateBo()
                        .setTransNo(vo.getTransNo()).setTransTypes(Constants.TransferTypes).setInfReason(update.getInfReason()));
            } else {
                List<String> transTypes = new ArrayList<>();
                if (TransferInfStatusEnum.SUCCESS.getCode().equals(update.getInfSource())) {
                    transTypes.add(AccountTransTypeEnum.TO.getCode());
                }
                if (TransferInfStatusEnum.SUCCESS.getCode().equals(update.getInfStatus())) {
                    transTypes.add(AccountTransTypeEnum.TI.getCode());
                }
                tradeAccSplitService.updateSplit(new TradeAccSplitUpdateBo()
                        .setTransNo(vo.getTransNo()).setTransTypes(transTypes)
                        .setInfStatus(TransferInfStatusEnum.SUCCESS.getCode())
                        .setOutSuccessTime(update.getOutSuccessTime() == null ? Convert.toDate(DateUtil.now()) : update.getOutSuccessTime())
                        .setOutTradeNo(update.getOutTradeNo()).setSplitAmt(vo.getSplitAmt()));
            }
        }
    }
    /**
     * 提现单调用接口后，异常，反填接口数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTransferInfFail(boolean isSame, Integer infStatus, TradeAccTransferVo vo, ServiceException se) {
        String message = StrUtil.sub(se.getMessage(), 0, 128);
        TradeAccTransfer update = new TradeAccTransfer();
        update.setId(vo.getId());
        update.setInfReason(message);
        if (isSame) {
            update.setInfSource(infStatus);
        } else {
            update.setInfStatus(infStatus);
        }
        int count = baseMapper.updateById(update);
        if (count > 0) {
            // 再次重试， 仅删除 split，
            tradeAccSplitService.deleteSplit(false, new TradeAccSplitUpdateBo().setTransNo(vo.getTransNo()).setInfReason(message)
                    .setTransTypes(Collections.singletonList(isSame ? AccountTransTypeEnum.TO.getCode() : AccountTransTypeEnum.TI.getCode())));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradeAccTransferVo updateDisCheckByBo(TradeAccTransferBo tbo) {
        if (!AccountOrgTypeEnum.SUPPLIER.getCode().equals(tbo.getSourceOrgType())
                && !AccountOrgTypeEnum.SUPPLIER.getCode().equals(tbo.getOrgType())
                || TransferBusiTypeEnum.TRANSFER_CHANGE.getCode().equals(tbo.getBusiType())) {
            // 仅支持双方，有一个是供应商的， 一个是供应商加款，  一个是供应商扣款
            throw new ServiceException("只处理供应商加款或扣款", R.FAIL);
        }
        boolean isTransferIn = AccountOrgTypeEnum.SUPPLIER.getCode().equals(tbo.getOrgType());
        // 对接银行的单号不重新生成（因换供应商的trans， splitRelNo 单号已经关联），仅记账日期，和接口调用时间修改 ！！！！！
        TradeAccTransfer update = new TradeAccTransfer();
        update.setInfTime(Convert.toDate(DateUtil.now()));
        update.setInfRetries(tbo.getInfRetries() + 1); // 两个接口，一次完整处理会变成 2
        update.setInfSource(TransferInfStatusEnum.PROCESSING.getCode());
        update.setInfStatus(TransferInfStatusEnum.PROCESSING.getCode());
        if (baseMapper.update(update, Wrappers.lambdaUpdate(TradeAccTransfer.class)
                .eq(TradeAccTransfer::getId, tbo.getId())
                .eq(TradeAccTransfer::getInfStatus, TransferInfStatusEnum.CHECK.getCode())) == 0) {
            throw new ServiceException(String.format("划转单 %s 必须是待审核", tbo.getTransNo()), R.FAIL);
        }
        update.setId(tbo.getId());
        List<TradeAccTransVo> transVos = tradeAccTransService.queryTransList(tbo.getTransNo(),
                Collections.singletonList(isTransferIn ? AccountTransTypeEnum.TI.getCode() : AccountTransTypeEnum.TO.getCode()));
        if (transVos.size() != 1) {
            throw new ServiceException(String.format("划转单 %s 不能调用营销转账接口", tbo.getTransNo()), R.FAIL);
        }
        List<TradeAccSplitVo> splitVos = tradeAccSplitService.batchUpdateByNo(
                new TradeAccSplitAddBo().setChannel("").setTradeNo("").setTotalAmt(transVos.get(0).getSplitAmt())
                        .setInfTime(update.getInfTime()).setInitTime(tbo.getCreateTime()),
                MapstructUtils.convert(transVos, TradeAccTrans.class));
        if (CollectionUtil.isNotEmpty(splitVos)) {
            if (isTransferIn) {
                update.setSplitNo(splitVos.get(0).getSplitNo());
            } else {
                update.setSourceSplitNo(splitVos.get(0).getSplitNo());
            }
        }
        baseMapper.updateById(update);
        TradeAccTransferVo transferVo = MapstructUtils.convert(tbo, TradeAccTransferVo.class);
        BeanUtil.copyProperties(update, transferVo, Constants.BeanCopyIgnoreNullValue);
        return transferVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTransferDisInfData(TradeAccTransferBo bo, TradeAccTransferVo vo) {
        TradeAccTransfer update = MapstructUtils.convert(bo, TradeAccTransfer.class);
        boolean isFail = TransferInfStatusEnum.FAIL.getCode().equals(update.getInfStatus());
        update.setDelFlag(isFail ? update.getId() : 0);
        update.setInfSource(update.getInfStatus());
        int count = baseMapper.updateById(update);
        if (count > 0) {
            if (isFail) {
                tradeAccSplitService.deleteSplit(true, new TradeAccSplitUpdateBo()
                        .setTransNo(vo.getTransNo()).setTransTypes(Constants.TransferTypes).setInfReason(update.getInfReason()));
            } else {
                tradeAccSplitService.updateSplit(new TradeAccSplitUpdateBo()
                        .setTransNo(vo.getTransNo()).setTransTypes(Constants.TransferTypes)
                        .setInfStatus(TransferInfStatusEnum.SUCCESS.getCode())
                        .setOutSuccessTime(update.getOutSuccessTime() == null ? Convert.toDate(DateUtil.now()) : update.getOutSuccessTime())
                        .setOutTradeNo(update.getOutTradeNo()));
            }
        }
    }
    /**
     * 提现单调用接口后，异常，反填接口数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTransferDisInfFail(Integer infStatus, TradeAccTransferVo vo, ServiceException se) {
        String message = StrUtil.sub(se.getMessage(), 0, 128);
        TradeAccTransfer update = new TradeAccTransfer();
        update.setId(vo.getId());
        update.setInfReason(message);
        update.setInfSource(infStatus);
        update.setInfStatus(infStatus);
        int count = baseMapper.updateById(update);
        if (count > 0) {
            // 再次重试， 仅删除 split，
            tradeAccSplitService.deleteSplit(false, new TradeAccSplitUpdateBo()
                    .setTransNo(vo.getTransNo()).setTransTypes(Constants.TransferTypes).setInfReason(message));
        }
    }

    /**
     * 更新状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOperStatus(TradeAccTransferStatusBo bo) {
        // 确认划转单， 把非供应商流水数据更新成已提现状态
        Integer preStatus;
        Integer status;
        Integer preOperStatus;
        if (bo.getOperStatus() == 0) {
            preStatus = AccountStatusEnum.CASH.getCode();
            status = AccountStatusEnum.FREEZE.getCode();
            preOperStatus = YNStatusEnum.ENABLE.getCode();
        } else {
            preStatus = AccountStatusEnum.FREEZE.getCode();
            status = AccountStatusEnum.CASH.getCode();
            preOperStatus = YNStatusEnum.DISABLE.getCode();
        }
        if (baseMapper.update(Wrappers.lambdaUpdate(TradeAccTransfer.class)
                .set(TradeAccTransfer::getOperStatus, bo.getOperStatus())
                .eq(TradeAccTransfer::getTransNo, bo.getTransNo()).eq(TradeAccTransfer::getDelFlag, 0)
                .eq(TradeAccTransfer::getOperStatus, preOperStatus)) > 0) {
            // 处理该单中，非供应商的 trans 转成可提现
            List<TradeAccTransVo> transVos = tradeAccTransMapper.selectVoList(Wrappers.lambdaQuery(TradeAccTrans.class)
                            .eq(TradeAccTrans::getTransNo, bo.getTransNo()).ne(TradeAccTrans::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode())
                            .eq(TradeAccTrans::getStatus, preStatus).in(TradeAccTrans::getTransType, Constants.TransferTypes));
            List<Long> transIds = transVos.stream().map(TradeAccTransVo::getId).toList();
            if (transIds.size() > 0) {
                // 3 更新状态
                tradeAccTransMapper.update(Wrappers.lambdaUpdate(TradeAccTrans.class)
                        .set(TradeAccTrans::getStatusTime, Convert.toDate(DateUtil.now()))
                        .set(TradeAccTrans::getCashTime, status.equals(AccountStatusEnum.CASH.getCode()) ? Convert.toDate(DateUtil.now()) : null)
                        .set(TradeAccTrans::getStatus, status)
                        .in(TradeAccTrans::getId, transIds));
                // 2 统计
                tradeAccTransService.insertTransAcctByTrans(transIds, preStatus, status, new ArrayList<>(), null);
            }
        }
    }

}
