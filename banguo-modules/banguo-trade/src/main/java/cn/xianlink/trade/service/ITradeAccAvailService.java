package cn.xianlink.trade.service;

import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupAvailQueryBo;
import cn.xianlink.trade.domain.bo.TradeAccAvailBo;
import cn.xianlink.trade.domain.bo.TradeAccSupAvailQueryBo;
import cn.xianlink.trade.domain.vo.TradeAccAvailVo;
import cn.xianlink.trade.domain.vo.TradeAccSupAvailVo;

import java.time.LocalDate;
import java.util.List;

/**
 * 结算单Service接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface ITradeAccAvailService {
    /**
     * 基于提现单id查询 结算单
     */
    List<TradeAccAvailVo> queryListByCashId(Long cashId);
    /**
     * 查询结算单列表
     */
    List<TradeAccAvailVo> queryListByIds(List<Long> availIds);
    /**
     * 查询结算单 (当日未提现的)
     */
    TradeAccAvailVo queryAvailOrById(Long acctOrgId, String orgCode, Long orgId, Integer orgType, Long deptId, Long availId, LocalDate availDate);
    /**
     * 查询结算单列表
     */
    List<TradeAccAvailVo> queryAvailProcessing(TradeAccSupAvailQueryBo bo);
    /**
     * 查询结算单列表
     */
    TableDataInfo<TradeAccAvailVo> querySupPageList(String orgCode, SupTradeAccSupAvailQueryBo bo);
    /**
     * 查询结算单列表
     */
    List<TradeAccAvailVo> querySupList(String orgCode, SupTradeAccSupAvailQueryBo bo);
    List<TradeAccAvailVo> querySupExclusionList(String orgCode, SupTradeAccSupAvailQueryBo queryBo);
    /**
     * 查询结算单列表
     */
    TableDataInfo<TradeAccSupAvailVo> querySupPageList(TradeAccSupAvailQueryBo bo);
    /**
     * 查询结算单列表
     */
    List<TradeAccSupAvailVo> querySupList(TradeAccSupAvailQueryBo bo);

    /**
     * 新增结算单
     */
    TradeAccAvailVo insertByBo(TradeAccAvailBo bo);

}
