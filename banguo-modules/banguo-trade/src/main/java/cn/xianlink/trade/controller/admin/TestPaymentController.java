package cn.xianlink.trade.controller.admin;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.api.domain.bo.*;
import cn.xianlink.trade.api.domain.vo.*;
import cn.xianlink.trade.channel.ChannelsContext;
import cn.xianlink.trade.channel.PaymentService;
import cn.xianlink.trade.channel.pingancloud.PinganCloudBalPayServiceImpl;
import cn.xianlink.trade.channel.weixinb2b.WeixinB2bPayServiceImpl;
import cn.xianlink.trade.domain.bo.TradeAccTransBo;
import cn.xianlink.trade.domain.bo.TradePayBalanceCompleteBo;
import cn.xianlink.trade.domain.bo.TradePayBo;
import cn.xianlink.trade.domain.bo.TradePayRefundBo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import cn.xianlink.trade.dubbo.RemotePaymentServiceImpl;
import cn.xianlink.trade.service.ITradePayService;
import cn.xianlink.trade.service.biz.TradeAccTransBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 支付测试类
 * <AUTHOR>
 * @date 2024-05-28
 * @folder 般果管理中心/财务流水/支付测试
 */
@Tag(name = "支付测试类")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/payment/test")
@ConditionalOnProperty(prefix = "channels", name = "debug-request", havingValue = "true", matchIfMissing = false)
public class TestPaymentController extends BaseController {

    private final transient ITradePayService tradePayService;
    private final transient TradeAccTransBizService tradeAccTransBizService;
    private final transient RemotePaymentServiceImpl remotePaymentService;
    private final transient WeixinB2bPayServiceImpl weixinB2bPayServiceImpl;
    private final transient PinganCloudBalPayServiceImpl pinganCloudBalPayServiceImpl;
    private final transient ChannelsContext channelsContext;

    /**
     * @ignore
     */
    @PostMapping("/wxToken")
    public R<String> wxToken(@Validated @RequestBody TradeAccTransBo bo) throws WxErrorException {
        weixinB2bPayServiceImpl.switchByPath(bo.getAvailNo());
        return R.ok(weixinB2bPayServiceImpl.getWxMaService().getAccessToken());
    }

    /**
     * @ignore
     */
    @PostMapping("/wxSession")
    public R<WxMaJscode2SessionResult> wxSession(@Validated @RequestBody TradeAccTransBo bo) throws WxErrorException {
        weixinB2bPayServiceImpl.switchByPath(bo.getAvailNo());
        return R.ok(weixinB2bPayServiceImpl.getWxMaService().getUserService().getSessionInfo(bo.getRemark()));
    }

    /**
     * @ignore
     */
    @PostMapping("/getRetailInfo")
    public R<RetailInfoVo> getRetailInfo(@Validated @RequestBody TradeAccTransBo bo) {
        weixinB2bPayServiceImpl.switchByPath(bo.getAvailNo());
        return R.ok(weixinB2bPayServiceImpl.getRetailInfo(new RetailInfoBo().setPayerId(bo.getRemark())));
    }

    /**
     * @ignore
     */
    @PostMapping("/wxRefund")
    public R<TradePayBo> wxRefund(@Validated @RequestBody TradeAccTransBo bo) {
        PaymentService paymentService = channelsContext.initPayment(bo.getTransType());
        paymentService.switchByPath(bo.getAvailNo());
        TradePayRefundVo payVo = new TradePayRefundVo();
        payVo.setTradeNo(bo.getRelateNo());
        payVo.setTradeRefundNo(bo.getTransNo());
        payVo.setRefundSplitAmt(bo.getSplitAmt());
        payVo.setPayerIp(bo.getSplitDisNo()); // 拉卡拉需要
        paymentService.refund(payVo);
        return R.ok();
    }
    /**
     * @ignore
     */
    @PostMapping("/wxPayQuery")
    public R<TradePayBo> wxPayQuery(@Validated @RequestBody TradeAccTransBo bo) {
        PaymentService paymentService = channelsContext.initPayment(bo.getTransType());
        paymentService.switchByPath(bo.getAvailNo());
        TradePayVo payVo = new TradePayVo();
        payVo.setTradeNo(bo.getTransNo());
        return R.ok(paymentService.payQuery(payVo));
    }

    /**
     * @ignore
     */
    @PostMapping("/wxRefundQuery")
    public R<TradePayRefundBo> wxRefundQuery(@Validated @RequestBody TradeAccTransBo bo) {
        PaymentService paymentService = channelsContext.initPayment(bo.getTransType());
        paymentService.switchByPath(bo.getAvailNo());
        TradePayRefundVo refundVo = new TradePayRefundVo();
        refundVo.setTradeRefundNo(bo.getTransNo());
        return R.ok(paymentService.refundQuery(refundVo));
    }

    /**
     * @ignore
     */
    @PostMapping("/payBalanceComplete")
    public R<Void> payBalanceComplete(@Validated @RequestBody TradePayBalanceCompleteBo bo) {
        pinganCloudBalPayServiceImpl.payBalanceComplete(bo.getRecvCode(), bo.getRecvAcctName(), bo.getRecvAmt());
        return R.ok();
    }

    /**
     * @ignore
     */
    @PostMapping("/pay")
    public R<OrderPayVo> pay(@Validated @RequestBody OrderPayBo vo) {
        return R.ok(remotePaymentService.pay(vo));
    }

    @Operation(summary = "支付查询（同步银行数据）")
    @PostMapping("/payQuery")
    public R<OrderPayQueryVo> payQuery(@Validated @RequestBody OrderPayQueryBo bo) {
        return R.ok(remotePaymentService.payQuery(bo));
    }

    @Operation(summary = "支付完成（调用分账回，并设置完整状态）")
    @PostMapping("/paySplitComplete")
    public R<Void> paySplitComplete(@Validated @RequestBody TradePayVo bo) {
        TradePayVo tvo = tradePayService.queryByNo(bo.getOrderNo());
        remotePaymentService.paySplitComplete(tvo);
        return R.ok();
    }

    @Operation(summary = "整单退款（已支付）")
    @PostMapping("/payCancel")
    public R<OrderRefundVo> payCancel(@Validated @RequestBody OrderPayCancelBo bo) {
        return R.ok(remotePaymentService.payCancel(bo));
    }

    @Operation(summary = "整单关闭（未支付）")
    @PostMapping("/payClose")
    public R<OrderPayCloseVo> payClose(@Validated @RequestBody OrderPayCloseBo bo) {
        return R.ok(remotePaymentService.payClose(bo));
    }


    @Operation(summary = "退款（可部分退）")
    @PostMapping("/refund")
    public R<OrderRefundVo> refund(@Validated @RequestBody OrderRefundBo bo) {
        return R.ok(remotePaymentService.refund(bo));
    }

    @Operation(summary = "退款审核（可部分退）")
    @PostMapping("/refundCheck")
    public R<OrderRefundVo> refundCheck(@Validated @RequestBody OrderRefundCheckBo bo) {
        return R.ok(remotePaymentService.refundCheck(bo));
    }

    @Operation(summary = "退单关闭（未审核且未退款的可关闭）")
    @PostMapping("/refundClose")
    public R<OrderRefundVo> refundClose(@Validated @RequestBody OrderRefundCheckBo bo) {
        return R.ok(remotePaymentService.refundClose(bo));
    }

    @Operation(summary = "退款查询（同步银行数据）")
    @PostMapping("/refundQuery")
    public R<OrderRefundQueryVo> refundQuery(@Validated @RequestBody OrderRefundQueryBo bo) {
        return R.ok(remotePaymentService.refundQuery(bo));
    }

    @Operation(summary = "取消退款占用")
    @PostMapping("/refundCancelOccupy")
    public R<OrderRefundVo> refundCancelOccupy(@Validated @RequestBody OrderRefundQueryBo bo) {
        return R.ok(remotePaymentService.refundCancelOccupy(bo));
    }

    @Operation(summary = "退款转可提现(对应订单商品数据必须是已转)")
    @PostMapping("/refundAvailInitByRelate")
    public R<Void> refundAvailInitByRelate(@Validated @RequestBody OrderRefundQueryBo bo) {
        tradeAccTransBizService.refundAvailInitByRelate(bo, null);
        return R.ok();
    }

    @Operation(summary = "分账结算")
    @PostMapping("/updateSplitInfData")
    public R<Void> updateSplitInfData(@Validated @RequestBody TradePayVo bo) {
        tradePayService.updateSplitInfData(true, bo);
        return R.ok();
    }
    /*
    @Operation(summary = "退款转可提现")
    @PostMapping("/refundException")
    public R<Void> refundException(@Validated @RequestBody OrderPayCloseBo bo) {
        TradePayVo tvo = tradePayService.queryByTradeNo(bo.getOrderNo());
        if (tvo != null) {
            tradePayRefundService.insertException(MapstructUtils.convert(tvo, TradePayBo.class));
        }
        return R.ok();
    }
    */

}
