package cn.xianlink.trade.controller.platform;


import java.util.List;

import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.resource.api.RemoteCaptchaService;
import cn.xianlink.resource.api.enums.CaptchaCodeType;
import cn.xianlink.system.api.model.LoginUser;
import cn.xianlink.trade.comm.CommServiceHelper;
import cn.xianlink.trade.domain.bo.TradeCommBankBo;
import cn.xianlink.trade.domain.vo.comm.SignContractVo;
import cn.xianlink.trade.domain.vo.comm.TradeCommBankVo;
import cn.xianlink.trade.service.ITradeCommBankService;
import cn.xianlink.trade.service.biz.TradeBaseDataBizService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
/**
 * 返利绑卡
 *
 * <AUTHOR>
 * @date 2025-04-18
 * @folder 采集平台(小程序)/返利绑卡
 */
@Tag(name = "返利绑定类")
@CustomLog
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/commBank")
public class TradeCommBankController extends BaseController {
    private final ITradeCommBankService tradeCommBankService;

    private final TradeBaseDataBizService tradeBaseDataBizService;

    private final CommServiceHelper commServiceHelper;

//    /**
//     * 查询客户有为签约列表
//     */
//    // @SaCheckPermission("franchisee:commBank:list")
//    @GetMapping("/list")
//    public TableDataInfo<TradeCommBankVo> list(TradeCommBankBo bo, PageQuery pageQuery) {
//        return tradeCommBankService.queryPageList(bo, pageQuery);
//    }
//
//    /**
//     * 导出客户有为签约列表
//     */
//    // @SaCheckPermission("franchisee:commBank:export")
//    @Log(title = "客户有为签约", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(TradeCommBankBo bo, HttpServletResponse response) {
//        List<TradeCommBankVo> list = tradeCommBankService.queryList(bo);
//        ExcelUtil.exportExcel(list, "客户有为签约", TradeCommBankVo.class, response);
//    }

    /**
     * 获取签约合同
     *
     * @param signChannel 签约渠道
     * @return
     */
    @GetMapping("/contract/{signChannel}")
    public R<SignContractVo> contract(@NotNull(message = "签约渠道不能为空") @PathVariable Integer signChannel) {
        SignContractVo contractVo = commServiceHelper.getService(signChannel).getSignContract();
        return R.ok(contractVo);
    }

    /**
     * 查询我的账户
     * @return
     */
    @GetMapping("/myCard")
    public R<TradeCommBankVo> myCard() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if(loginUser == null) {
            throw new ServiceException("没有登录");
        }
        Long relationId = loginUser.getRelationId();
        RemoteBaseDataVo vo = tradeBaseDataBizService.queryById(relationId, AccountOrgTypeEnum.CUSTOMER.getCode());
        if (vo == null) {
            throw new ServiceException("登录异常，不是客户");
        }
        return R.ok(tradeCommBankService.queryByCustomerId(vo.getId()));
    }

    /**
     * 获取客户有为签约详细信息
     *
     * @param id 主键
     */
    // @SaCheckPermission("franchisee:commBank:query")
    @GetMapping("/{id}")
    public R<TradeCommBankVo> getInfo(@NotNull(message = "主键不能为空")
                                      @PathVariable Long id) {
        return R.ok(tradeCommBankService.queryById(id));
    }

    /**
     * 重新签约
     *
     * @param id
     * @return
     */
    @PostMapping("/resign/{id}")
    public R<Void> resign(@NotNull(message = "主键不能为空") @PathVariable Long id,
                          @RequestParam(name = "force", required = false, defaultValue = "false") Boolean force) {
        return toAjax(tradeCommBankService.resign(id, force));
    }

    /**
     * 取消签约
     *
     * @param id
     * @return
     */
    @PostMapping("/signClose/{id}")
    public R<Void> signClose(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(tradeCommBankService.signClose(id));
    }

    /**
     * 检查签约
     *
     * @param id
     * @return
     */
    @GetMapping("/checkSign/{id}")
    public R<Void> checkSign(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(tradeCommBankService.checkSign(id));
    }


    /**
     * 新增客户有为签约
     */
    // @SaCheckPermission("franchisee:commBank:add")
    @Log(title = "客户有为签约", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TradeCommBankBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Long relationId = loginUser.getRelationId();
        RemoteBaseDataVo vo = tradeBaseDataBizService.queryById(relationId, AccountOrgTypeEnum.CUSTOMER.getCode());
        if (vo == null) {
            throw new ServiceException("登录异常，不是客户");
        }
        bo.setCustomerId(relationId);
        bo.setCustomerCode(vo.getCode());

        return toAjax(tradeCommBankService.insertByBo(bo));
    }

    /**
     * 修改客户有为签约
     */
    // @SaCheckPermission("franchisee:commBank:edit")
    @Log(title = "客户有为签约", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TradeCommBankBo bo) {
        return toAjax(tradeCommBankService.updateByBo(bo));
    }

}