package cn.xianlink.trade.service;

import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.domain.bo.TradeAccSupAccountQueryBo;
import cn.xianlink.trade.domain.bo.TradeAccSupAvailAccountQueryBo;
import cn.xianlink.trade.domain.vo.TradeAccAccountVo;
import cn.xianlink.trade.domain.vo.TradeAccSupAvailAccountVo;
import cn.xianlink.trade.domain.vo.TradeAccTransAcctVo;

import java.util.List;
import java.util.Map;

/**
 * 账务总Service接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface ITradeAccAccountService {

    /**
     * 查询账务总
     */
    TradeAccAccountVo querySupOrgAccount(String orgCode, Integer orgType, Long deptId);
    /**
     * 查询供应商账务总
     */
    TableDataInfo<TradeAccAccountVo> customSupPageList(TradeAccSupAccountQueryBo queryBo);
    /**
     * 查询供应商账务总
     */
    List<TradeAccAccountVo> customSupList(TradeAccSupAccountQueryBo queryBo);
    /**
     * 查询供应商账务总
     */
    TableDataInfo<TradeAccSupAvailAccountVo> customSupAvailPageList(TradeAccSupAvailAccountQueryBo queryBo);
    /**
     * 查询供应商账务总
     */
    List<TradeAccSupAvailAccountVo> customSupAvailList(TradeAccSupAvailAccountQueryBo queryBo);
    /**
     * 更新昨日数据
     */
    void updateAccountYdaAmt();
    /**
     * 查询未合计的流水
     */
    List<TradeAccTransAcctVo> getTransAcctList(int limit);
    /**
     * 查询已删除的
     */
    Map<String, List<TradeAccTransAcctVo>> getTransAcctDelMap();
    /**
     * 更新到总计表中
     */
    Map<String, List<TradeAccTransAcctVo>> updateAccountAmt(List<TradeAccTransAcctVo> acctVos);
    /**
     * 更新到总计表中
     */
    void updateStatusByTrans(List<TradeAccTransAcctVo> acctVos);


}
