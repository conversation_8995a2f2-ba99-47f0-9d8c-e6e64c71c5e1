package cn.xianlink.trade.controller.admin;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.domain.bo.TradeAccChargeCreateBo;
import cn.xianlink.trade.domain.bo.TradeAccChargeQueryBo;
import cn.xianlink.trade.domain.bo.TradeAccChargeRefundBo;
import cn.xianlink.trade.domain.vo.TradeAccChargeVo;
import cn.xianlink.trade.service.ITradeAccChargeService;
import cn.xianlink.trade.service.biz.TradeAccountBizService;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 营销充值
 *
 * <AUTHOR>
 * @date 2024-06-12
 * @folder 般果管理中心/财务流水/营销充值
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/accCharge")
public class TradeAccChargeController extends BaseController {

    private final transient ITradeAccChargeService tradeAccChargeService;
    private final transient TradeAccountBizService tradeAccountBizService;


    /**
     * 查询充值
     */
    @PostMapping("/page")
    public R<TableDataInfo<TradeAccChargeVo>> page(@Validated @RequestBody TradeAccChargeQueryBo bo) {
        return R.ok(tradeAccChargeService.queryPageList(bo));
    }

    /**
     * 查询充值 退款列表
     */
    @PostMapping("/refundList")
    public R<List<TradeAccChargeVo>> refundList(@NotEmpty @RequestParam(name = "chargeNo", required = true) String chargeNo) {
        return R.ok(tradeAccChargeService.queryRefundList(chargeNo));
    }

    /**
     * 充值
     */
    @RepeatSubmit()
    @PostMapping("/recharge")
    public R<Void> recharge(@Validated @RequestBody TradeAccChargeCreateBo bo) {
        tradeAccountBizService.recharge(bo);
        return R.ok();
    }

    /**
     * 充值退回 （ 选中一行充值记录进行退回）
     */
    @RepeatSubmit()
    @PostMapping("/rechargeRefund")
    public R<Void> rechargeRefund(@Validated @RequestBody TradeAccChargeRefundBo bo) {
        tradeAccountBizService.rechargeRefund(bo);
        return R.ok();
    }

}
