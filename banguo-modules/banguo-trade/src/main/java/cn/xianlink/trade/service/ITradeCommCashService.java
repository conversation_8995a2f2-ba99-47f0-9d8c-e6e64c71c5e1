package cn.xianlink.trade.service;

import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.api.domain.bo.OrderPayJobQueryBo;
import cn.xianlink.trade.domain.bo.comm.TradeCommCashBo;
import cn.xianlink.trade.domain.bo.platform.TradePlatformCommCashQueryBo;
import cn.xianlink.trade.domain.vo.comm.TradeCommCashVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;
import cn.xianlink.trade.domain.vo.platform.TradePlatformCommCashVo;

import java.time.LocalDate;
import java.util.List;

/**
 * 分账变更流水Service接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface ITradeCommCashService {
    /**
     * 资金账户内业务单查询
     */

    TradeCommCashVo queryById(Long cashId);

    TradeCommCashVo queryByMerchNo(String ywMerchNo);

    TableDataInfo<TradePlatformCommCashVo> queryPageList(TradePlatformCommCashQueryBo bo);

    List<TradeCommCashVo> queryCashProcessing(OrderPayJobQueryBo bo);

    List<TradeCommCashVo> queryCashFailed(LocalDate fileDate);

    TradeCommCashVo insertByBo(TradeCommCashBo bo, LocalDate commAvailDate);

    TradeCommCashVo updateCheckByBo(TradeCommCashBo cashBo, TradeOrgBankRelaVo bankVo);

    TradeCommCashVo updateInfData(TradeCommCashBo bo, TradeCommCashVo vo);

    void updateInfFail(TradeCommCashVo vo, ServiceException se);

    TradeCommCashVo updateCommCheckByBo(TradeCommCashBo cashBo);

    TradeCommCashVo updateCommInfData(TradeCommCashBo bo, TradeCommCashVo vo);

    void updateCommInfFail(TradeCommCashVo vo, ServiceException se);

    void updateCommRefundData(TradeCommCashBo bo);

    TradeCommCashVo insertExpireCash(LocalDate expireDate);

    void updatetExpireInfData(TradeCommCashBo bo);

}
