package cn.xianlink.trade.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.api.domain.bo.OrderPayJobQueryBo;
import cn.xianlink.trade.comm.SignChannelEnum;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.*;
import cn.xianlink.trade.domain.bo.comm.TradeCommCashBo;
import cn.xianlink.trade.domain.bo.platform.TradePlatformCommCashQueryBo;
import cn.xianlink.trade.domain.vo.comm.TradeCommCashVo;
import cn.xianlink.trade.domain.vo.comm.TradeCommTransVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;
import cn.xianlink.trade.domain.vo.platform.TradePlatformCommCashVo;
import cn.xianlink.trade.mapper.*;
import cn.xianlink.trade.service.ITradeCommCashService;
import cn.xianlink.trade.service.biz.TradeBaseUtilBizService;
import cn.xianlink.trade.utils.MapstructPageUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 分账变更流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class TradeCommCashServiceImpl implements ITradeCommCashService {

    private final transient TradeCommCashMapper baseMapper;
    private final transient TradeCommCashDetailMapper tradeCommCashDetailMapper;
    private final transient TradeCommTransMapper tradeCommTransMapper;
    private final transient TradeCustTransAcctMapper tradeCustTransAcctMapper;
    private final transient TradeAccSplitMapper tradeAccSplitMapper;
    private final transient TradeBaseUtilBizService tradeBaseUtilBizService;

    @Override
    public TradeCommCashVo queryById(Long cashId) {
        return baseMapper.selectVoById(cashId);
    }

    @Override
    public TradeCommCashVo queryByMerchNo(String ywMerchNo) {
        return baseMapper.selectVoOne(Wrappers.lambdaQuery(TradeCommCash.class).eq(TradeCommCash::getYwMerchNo, ywMerchNo));
    }

    @Override
    public TableDataInfo<TradePlatformCommCashVo> queryPageList(TradePlatformCommCashQueryBo queryBo) {
        LambdaQueryWrapper<TradeCommCash> lqw = Wrappers.lambdaQuery();
        lqw.eq(TradeCommCash::getCustomerId, queryBo.getCustomerId());
        lqw.ge(queryBo.getCashDateStart() != null, TradeCommCash::getCashDate, queryBo.getCashDateStart());
        lqw.le(queryBo.getCashDateEnd() != null, TradeCommCash::getCashDate, queryBo.getCashDateEnd());
        lqw.like(StringUtils.isNotBlank(queryBo.getCashNo()), TradeCommCash::getCashNo, queryBo.getCashNo());
        lqw.eq(queryBo.getCashStatus() != null, TradeCommCash::getCashStatus, queryBo.getCashStatus());
        if (queryBo.getIsFail() != null) {
            if (queryBo.getIsFail() == 0) {
                lqw.eq(TradeCommCash::getInfReason, "");
            } else {
                lqw.ne(TradeCommCash::getInfReason, "");
            }
        }
        lqw.orderByDesc(TradeCommCash::getId);
        IPage<TradeCommCashVo> result = baseMapper.selectVoPage(queryBo.build(), lqw);
        return TableDataInfo.build(MapstructPageUtils.convert(result, TradePlatformCommCashVo.class));
    }

    /**
     * 查询提现单进行中的提现单
     */
    @Override
    public List<TradeCommCashVo> queryCashProcessing(OrderPayJobQueryBo bo) {
        LambdaQueryWrapper<TradeCommCash> lqw = Wrappers.lambdaQuery(TradeCommCash.class)
                .select(TradeCommCash::getId, TradeCommCash::getCashNo, TradeCommCash::getCashDate,
                        TradeCommCash::getInfStatus, TradeCommCash::getSplitNo,
                        TradeCommCash::getYwInfStatus, TradeCommCash::getYwMerchNo, TradeCommCash::getCashStatus);
        lqw.between(TradeCommCash::getCashInfTime, bo.getInfTimeStart(), bo.getInfTimeEnd());
        lqw.eq(TradeCommCash::getCashStatus, CashInfStatusEnum.PROCESSING.getCode());
        lqw.eq(TradeCommCash::getCashType, CommCashTypeEnum.CASH.getCode());
        // 1 未超过执行次数， 或 执行中的
        lqw.and(l ->l.eq(TradeCommCash::getInfStatus, CashInfStatusEnum.PROCESSING.getCode()))
                .or(ll -> ll.eq(TradeCommCash::getYwInfStatus, CashInfStatusEnum.PROCESSING.getCode()));
        lqw.eq(TradeCommCash::getDelFlag, YNStatusEnum.DISABLE.getCode());
        lqw.orderByAsc(TradeCommCash::getId);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<TradeCommCashVo> queryCashFailed(LocalDate fileDate){
        LambdaQueryWrapper<TradeCommCash> lqw = Wrappers.lambdaQuery();
        lqw.between(TradeCommCash::getCashDate, fileDate.plusDays(-1), fileDate);
        lqw.and(l -> l.ne(TradeCommCash::getInfStatus, PayInfStatusEnum.SUCCESS.getCode())
                .or(ll -> ll.ne(TradeCommCash::getYwInfStatus, PayInfStatusEnum.SUCCESS.getCode())));
        lqw.eq(TradeCommCash::getDelFlag, 0);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradeCommCashVo insertByBo(TradeCommCashBo bo, LocalDate commAvailDate) {
        TradeCommCash commCash = MapstructUtils.convert(bo, TradeCommCash.class);
        commCash.setCashStatus(CashInfStatusEnum.CHECK.getCode());
        commCash.setCashCheckTime(Convert.toDate(DateUtil.now())); // 提交提现时间
        commCash.setCashInfTime(commCash.getCashCheckTime()); // job 校验的时间
        commCash.setCashDate(LocalDate.now());
        commCash.setInfStatus(CashInfStatusEnum.CHECK.getCode());
        // yzh渠道 跳过平安接口调用
        if (bo.isPinganWithdrawSkip()) {
            commCash.setInfTime(new Date());
            commCash.setInfStatus(CashInfStatusEnum.SUCCESS.getCode());
            commCash.setYwInfStatus(CashInfStatusEnum.CHECK.getCode());
        }
        commCash.setInfRetries(0);
        try {
            insertCash(commCash);
        } catch (DuplicateKeyException dke) {
            insertCash(commCash); // 重试一次
        }
        List<TradeCommTransVo> commTransVos = tradeCommTransMapper.selectVoList(Wrappers.lambdaQuery(TradeCommTrans.class)
                .select(TradeCommTrans::getId, TradeCommTrans::getTransType, TradeCommTrans::getTransId, TradeCommTrans::getTransNo,
                        TradeCommTrans::getTransDate, TradeCommTrans::getCommissionAmt, TradeCommTrans::getSalaryAmt, TradeCommTrans::getAvailDate)
                .eq(TradeCommTrans::getCustomerId, bo.getCustomerId()).eq(TradeCommTrans::getCashAmt, 0).eq(TradeCommTrans::getExpireAmt, 0)
                .ne(TradeCommTrans::getSalaryAmt, 0).ge(commAvailDate != null, TradeCommTrans::getAvailDate, commAvailDate)
                .eq(TradeCommTrans::getStatus, AccountStatusEnum.AVAIL.getCode()).orderByAsc(TradeCommTrans::getId));
        BigDecimal cashAmt = BigDecimal.ZERO;
        BigDecimal salaryAmt = BigDecimal.ZERO;
        List<TradeCommTrans> commTranss = new ArrayList<>();
        List<TradeCommCashDetail> commDetails = new ArrayList<>();
        List<TradeCustTransAcct> commAccts = new ArrayList<>();
        Integer status = AccountStatusEnum.CHECK.getCode();
        for (TradeCommTransVo transVo : commTransVos) {
            cashAmt = cashAmt.add(transVo.getCommissionAmt());
            salaryAmt = salaryAmt.add(transVo.getSalaryAmt());
            commTranss.add(new TradeCommTrans().setId(transVo.getId()).setCashAmt(transVo.getSalaryAmt()).setStatus(status));
            commDetails.add(new TradeCommCashDetail().setCommTransId(transVo.getId()).setCashId(commCash.getId())
                    .setCashType(commCash.getCashType()).setCommissionAmt(transVo.getCommissionAmt()).setSalaryAmt(transVo.getSalaryAmt()));
            commAccts.add(new TradeCustTransAcct().setCommAvailAmt(transVo.getCommissionAmt().negate()).setCommCheckAmt(transVo.getCommissionAmt())
                    .setSalaAvailAmt(transVo.getSalaryAmt().negate()).setSalaCheckAmt(transVo.getSalaryAmt())
                    .setSourceType(1).setCustomerId(bo.getCustomerId()).setCommAvailDate(transVo.getAvailDate())
                    .setTransDate(transVo.getTransDate()).setTransId(transVo.getTransId()).setCustTransId(transVo.getId())
                    .setTransType(transVo.getTransType()).setTransNo(transVo.getTransNo()));
        }
        if (commTranss.size() == 0) {
            throw new ServiceException("没有可提现的佣金记录");
        }
        if (bo.getCashAmt().compareTo(cashAmt) != 0) {
            throw new ServiceException("提现金额异常，请联系管理员");
        }
        baseMapper.updateById(new TradeCommCash().setCashAmt(cashAmt).setSalaryAmt(salaryAmt).setId(commCash.getId()));
        tradeCommTransMapper.updateBatchById(commTranss);
        tradeCommCashDetailMapper.insertBatch(commDetails);
        tradeCustTransAcctMapper.insertBatch(commAccts);
        return MapstructUtils.convert(commCash, TradeCommCashVo.class);

    }

    private void insertCash(TradeCommCash commCash) {
        commCash.setCashNo(tradeBaseUtilBizService.getNextCommCashNo(commCash.getCashDate()));
        commCash.setYwMerchNo(commCash.getCashNo()); //
        baseMapper.insert(commCash);
    }

    /**
     * 提现单调用接口前
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradeCommCashVo updateCheckByBo(TradeCommCashBo cashBo, TradeOrgBankRelaVo bankVo) {
        TradeCommCash update = new TradeCommCash();
        update.setInfStatus(CashInfStatusEnum.PROCESSING.getCode());
        update.setCashStatus(update.getInfStatus());
        update.setInfTime(Convert.toDate(DateUtil.now()));
        update.setCashInfTime(update.getInfTime());
        update.setInfRetries(cashBo.getInfRetries() + 1);
        update.setSplitNo(tradeBaseUtilBizService.getNextSplitNo(LocalDate.now()));
        update.setInfReason("");
        int count = baseMapper.update(update, Wrappers.lambdaUpdate(TradeCommCash.class)
                .eq(TradeCommCash::getId, cashBo.getId()).eq(TradeCommCash::getCustomerId, cashBo.getCustomerId())
                .ne(TradeCommCash::getInfStatus, CashInfStatusEnum.SUCCESS.getCode())
                .eq(TradeCommCash::getCashStatus, cashBo.getCashStatus()));
        if (count == 0) {
            throw new ServiceException(String.format("提现单 %s 状态已改变", cashBo.getCashNo()));
        }
        TradeAccSplit accSplit = new TradeAccSplit();
        accSplit.setSplitNo(update.getSplitNo());
        accSplit.setTransType(cashBo.getCashType());
        accSplit.setTransId(cashBo.getId());
        accSplit.setTransNo(cashBo.getCashNo());
        accSplit.setTransDate(cashBo.getCashDate());
        accSplit.setTransOrgId(0L);
        accSplit.setAcctOrgId(0L);
        accSplit.setTotalAmt(cashBo.getCashAmt());
        accSplit.setTransAmt(cashBo.getCashAmt());
        accSplit.setFeeAmt(cashBo.getOutFeeAmt());
        accSplit.setBusiType("CC");
        accSplit.setRelateType(accSplit.getTransType());
        accSplit.setRelateNo(accSplit.getTransNo());
        accSplit.setRelateAmt(accSplit.getTotalAmt());
        accSplit.setOutOrgCode(bankVo.getOutOrgCode());
        accSplit.setOutAcctCode(bankVo.getOutAcctCode());
        accSplit.setOrgCode(bankVo.getOrgCode());
        accSplit.setOrgType(bankVo.getOrgType());
        accSplit.setOrgId(bankVo.getOrgId());
        accSplit.setChannel("");
        accSplit.setInitTime(cashBo.getCashCheckTime());
        accSplit.setInfTime(update.getInfTime());
        accSplit.setInfStatus(update.getInfStatus());
        accSplit.setAcctDate(LocalDate.now());
        tradeAccSplitMapper.insert(accSplit);
        BeanUtil.copyProperties(update, cashBo, Constants.BeanCopyIgnoreNullValue);
        return MapstructUtils.convert(cashBo, TradeCommCashVo.class);
    }
    /**
     * 提现单调用接口后，反填接口数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradeCommCashVo updateInfData(TradeCommCashBo bo, TradeCommCashVo vo) {
        // 1 更新提现单的提现状态
        TradeCommCash update = MapstructUtils.convert(bo, TradeCommCash.class);
        boolean isFail = CashInfStatusEnum.FAIL.getCode().equals(bo.getInfStatus());
        boolean isSuccess = CashInfStatusEnum.SUCCESS.getCode().equals(bo.getInfStatus());
        update.setCashStatus(isFail ? CashInfStatusEnum.CHECK.getCode() : bo.getInfStatus());
        if (isSuccess) {
            if (AccountTransTypeEnum.CE.getCode().equals(vo.getCashType())) {
                update.setYwInfStatus(CashInfStatusEnum.SUCCESS.getCode());
                update.setCashStatus(update.getYwInfStatus());
            } else {
                update.setYwInfStatus(CashInfStatusEnum.CHECK.getCode());
                update.setCashStatus(update.getYwInfStatus());
                update.setCashInfTime(Convert.toDate(DateUtil.now()));
            }
        }
        int count = baseMapper.updateById(update);
        if (count > 0) {
            if (isSuccess) {
                // update trans set status = 3,4    +cash_amt,   -check_amt    where  基于  sum(detail)
                //  基于 detail 插入  cust_acct  账务中
                if (AccountTransTypeEnum.CE.getCode().equals(vo.getCashType())) {

                }
                // 4 更新分账单完成
                tradeAccSplitMapper.update(Wrappers.lambdaUpdate(TradeAccSplit.class)
                        .set(TradeAccSplit::getInfStatus, update.getInfStatus())
                        .set(TradeAccSplit::getOutTradeNo, update.getOutTradeNo())
                        .set(TradeAccSplit::getInfReason, "").set(TradeAccSplit::getOutSuccessTime, update.getCashTime())
                        .eq(TradeAccSplit::getSplitNo, vo.getSplitNo()).eq(TradeAccSplit::getDelFlag, 0));
            } else if (isFail) {
                tradeAccSplitMapper.update(Wrappers.lambdaUpdate(TradeAccSplit.class)
                        .setSql("del_flag=id").set(TradeAccSplit::getInfStatus, update.getInfStatus())
                        .set(TradeAccSplit::getInfReason, update.getInfReason())
                        .eq(TradeAccSplit::getSplitNo, vo.getSplitNo()).eq(TradeAccSplit::getDelFlag, 0));
            }
        }
        BeanUtil.copyProperties(update, vo, Constants.BeanCopyIgnoreNullValue);
        return vo;
    }
    /**
     * 提现单调用接口后，异常，反填接口数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInfFail(TradeCommCashVo vo, ServiceException se) {
        // boolean isFail = CashInfStatusEnum.INF_FAIL.getCode().equals(infStatus);
        String message = StrUtil.sub(se.getMessage(), 0, 128);
        TradeCommCash update = new TradeCommCash();
        update.setId(vo.getId());
        update.setInfStatus(CashInfStatusEnum.FAIL.getCode());
        update.setCashStatus(CashInfStatusEnum.CHECK.getCode());
        update.setInfReason(message);
        int count = baseMapper.updateById(update);
        if (count > 0) {
            tradeAccSplitMapper.update(Wrappers.lambdaUpdate(TradeAccSplit.class)
                    .setSql("del_flag=id").set(TradeAccSplit::getInfStatus, update.getInfStatus())
                    .set(TradeAccSplit::getInfReason, message)
                    .eq(TradeAccSplit::getSplitNo, vo.getSplitNo()).eq(TradeAccSplit::getDelFlag, 0));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradeCommCashVo updateCommCheckByBo(TradeCommCashBo cashBo) {
        TradeCommCash update = new TradeCommCash();
        update.setYwInfStatus(CashInfStatusEnum.PROCESSING.getCode());
        update.setCashStatus(update.getYwInfStatus());
        update.setYwInfTime(Convert.toDate(DateUtil.now()));
        update.setYwInfRetries(cashBo.getYwInfRetries() + 1);
        update.setCashInfTime(update.getYwInfTime());
        update.setInfReason("");
        if (YNStatusEnum.DISABLE.getCode().equals(cashBo.getYwOrderStatus()) && SignChannelEnum.YOUWEI.eq(cashBo.getSignChannel())) {
            // 创单不成功换单号， 再次重试
            update.setYwMerchNo(tradeBaseUtilBizService.getNextSplitNo(LocalDate.now()));
        }
        int count = baseMapper.update(update, Wrappers.lambdaUpdate(TradeCommCash.class)
                .eq(TradeCommCash::getId, cashBo.getId()).eq(TradeCommCash::getCustomerId, cashBo.getCustomerId())
                .eq(TradeCommCash::getInfStatus, CashInfStatusEnum.SUCCESS.getCode())
                .ne(TradeCommCash::getYwInfStatus, CashInfStatusEnum.SUCCESS.getCode())
                .eq(TradeCommCash::getCashStatus, cashBo.getCashStatus()));
        if (count == 0) {
            throw new ServiceException(String.format("提现单 %s 状态已改变", cashBo.getCashNo()));
        }
        BeanUtil.copyProperties(update, cashBo, Constants.BeanCopyIgnoreNullValue);
        return MapstructUtils.convert(cashBo, TradeCommCashVo.class);
    }

    /**
     * 提现单调用接口后，反填接口数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradeCommCashVo updateCommInfData(TradeCommCashBo bo, TradeCommCashVo vo) {
        // 1 更新提现单的提现状态
        TradeCommCash update = MapstructUtils.convert(bo, TradeCommCash.class);
        boolean isFail = CashInfStatusEnum.FAIL.getCode().equals(bo.getYwInfStatus());
        if (SignChannelEnum.YZH.eq(bo.getSignChannel())) {
            update.setCashStatus(bo.getYwInfStatus());
        } else {
            update.setCashStatus(isFail ? CashInfStatusEnum.CHECK.getCode() : bo.getYwInfStatus());
        }
        int count = baseMapper.update(update, Wrappers.lambdaQuery(TradeCommCash.class)
                .eq(TradeCommCash::getId, update.getId()).eq(TradeCommCash::getDelFlag, 0)
                .ne(TradeCommCash::getYwInfStatus, CashInfStatusEnum.SUCCESS.getCode()));
        if (count > 0 && CashInfStatusEnum.SUCCESS.getCode().equals(bo.getYwInfStatus())) {
            // TradeCommCashVo commCash = baseMapper.selectVoById(update.getId());
            List<TradeCommTransVo> commTransVos = tradeCommTransMapper.queryCommCashTransById(update.getId());
            List<TradeCommTrans> commTranss = new ArrayList<>();
            List<TradeCustTransAcct> commAccts = new ArrayList<>();
            for (TradeCommTransVo transVo : commTransVos) {
                // 如果该流水， 未全部提现， 则不改变 status 值
                if (transVo.getCashAmt().compareTo(transVo.getSalaryAmt()) == 0) {
                    commTranss.add(new TradeCommTrans().setId(transVo.getId()).setStatus(AccountStatusEnum.CASH.getCode()));
                }
                // ExpireAmt 是 detail 表中的 cashAmt 字段， 只是借用
                commAccts.add(new TradeCustTransAcct().setCommCheckAmt(transVo.getCommissionAmt().negate()).setCommCashAmt(transVo.getCommissionAmt())
                        .setSalaCheckAmt(transVo.getExpireAmt().negate()).setSalaCashAmt(transVo.getExpireAmt())
                        .setSalaExpectedAmt(commAccts.size() == 0 ? vo.getSalaryAmt().subtract(vo.getOutFeeAmt()) : BigDecimal.ZERO)  // 仅第一行插入
                        .setSourceType(1).setCustomerId(transVo.getCustomerId()).setCommAvailDate(transVo.getAvailDate())
                        .setTransDate(transVo.getTransDate()).setTransId(transVo.getTransId()).setCustTransId(transVo.getId())
                        .setTransType(transVo.getTransType()).setTransNo(transVo.getTransNo()));
            }
            if (commTranss.size() > 0) {
                tradeCommTransMapper.updateBatchById(commTranss);
            }
            tradeCustTransAcctMapper.insertBatch(commAccts);
        }
        BeanUtil.copyProperties(update, vo, Constants.BeanCopyIgnoreNullValue);
        return vo;
    }
    /**
     * 提现单调用接口后，异常，反填接口数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCommInfFail(TradeCommCashVo vo, ServiceException se) {
        String message = StrUtil.sub(se.getMessage(), 0, 128);
        TradeCommCash update = new TradeCommCash();
        update.setId(vo.getId());
        update.setCashStatus(CashInfStatusEnum.CHECK.getCode());
        update.setYwInfStatus(CashInfStatusEnum.FAIL.getCode());
        update.setInfReason(message);
        baseMapper.updateById(update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCommRefundData(TradeCommCashBo bo) {
        TradeCommCash update = MapstructUtils.convert(bo, TradeCommCash.class);
        baseMapper.updateById(update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradeCommCashVo insertExpireCash(LocalDate expireDate) {
        Date currDate = Convert.toDate(DateUtil.now());
        List<TradeCommTransVo> commTransVos = tradeCommTransMapper.selectVoList(Wrappers.query(TradeCommTrans.class)
                .select("id,trans_type,trans_id,trans_no,trans_date,commission_amt,salary_amt,cash_amt,salary_amt-cash_amt as expire_amt,customer_id")
                .between("avail_date", expireDate.plusDays(-1), expireDate).ne("salary_amt", 0).eq("cash_amt", 0).eq("expire_amt", 0)
                .eq("status", AccountStatusEnum.AVAIL.getCode()).orderByAsc("id"));
        if (commTransVos.size() == 0) {
            return null;
        }
        StringBuilder ids = new StringBuilder();
        BigDecimal cashExpireAmt = BigDecimal.ZERO;
        String splitNo = tradeBaseUtilBizService.getNextSplitNo(LocalDate.now());
        Map<Long, List<TradeCommTransVo>> commTransMap = commTransVos.stream().collect(Collectors.groupingBy(TradeCommTransVo::getCustomerId));
        Map<Long, RemoteBaseDataVo> commCustMap = tradeBaseUtilBizService.queryBaseListByIds(commTransMap.keySet().stream().toList(),
                AccountOrgTypeEnum.CUSTOMER.getCode()).stream().collect(Collectors.toMap(RemoteBaseDataVo::getId, Function.identity()));
        for (Map.Entry<Long, List<TradeCommTransVo>> entry : commTransMap.entrySet()) {
            RemoteBaseDataVo dataVo = commCustMap.get(entry.getKey());
            BigDecimal cashAmt = entry.getValue().stream().map(TradeCommTransVo::getCommissionAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal salaryAmt = entry.getValue().stream().map(TradeCommTransVo::getExpireAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            cashExpireAmt = cashExpireAmt.add(cashAmt);
            TradeCommCash commCash = new TradeCommCash().setCashType(AccountTransTypeEnum.CE.getCode())
                    .setCashAmt(cashAmt).setSalaryAmt(salaryAmt).setCustomerId(dataVo.getId()).setCustomerCode(dataVo.getCode())
                    .setSplitNo(splitNo).setCashStatus(CashInfStatusEnum.SUCCESS.getCode()).setCashCheckTime(currDate).setCashInfTime(currDate)
                    .setInfTime(currDate).setCashDate(LocalDate.now()).setInfStatus(CashInfStatusEnum.PROCESSING.getCode())
                    .setYwInfStatus(CashInfStatusEnum.SUCCESS.getCode());
            try {
                insertCash(commCash);
            } catch (DuplicateKeyException dke) {
                insertCash(commCash); // 重试一次
            }
            List<TradeCommTrans> commTranss = new ArrayList<>();
            List<TradeCommCashDetail> commDetails = new ArrayList<>();
            List<TradeCustTransAcct> commAccts = new ArrayList<>();
            Integer status = AccountStatusEnum.EXPIRE.getCode();
            for (TradeCommTransVo transVo : entry.getValue()) {
                BigDecimal sub = transVo.getExpireAmt();
                commTranss.add(new TradeCommTrans().setId(transVo.getId()).setExpireAmt(sub).setStatus(status));
                commDetails.add(new TradeCommCashDetail().setCommTransId(transVo.getId()).setCashId(commCash.getId())
                        .setCashType(commCash.getCashType()).setCommissionAmt(transVo.getCommissionAmt()).setSalaryAmt(sub));
                commAccts.add(new TradeCustTransAcct().setCommAvailAmt(transVo.getCommissionAmt().negate()).setCommExpireAmt(transVo.getCommissionAmt())
                        .setSalaAvailAmt(sub.negate()).setSalaExpireAmt(sub).setSourceType(1).setCustomerId(dataVo.getId()) // 不修改结算日期
                        .setTransDate(transVo.getTransDate()).setTransId(transVo.getTransId()).setCustTransId(transVo.getId())
                        .setTransType(transVo.getTransType()).setTransNo(transVo.getTransNo()));
            }
            tradeCommTransMapper.updateBatchById(commTranss);
            tradeCommCashDetailMapper.insertBatch(commDetails);
            tradeCustTransAcctMapper.insertBatch(commAccts);
            ids.append(",").append(commCash.getId());
        }
        if (cashExpireAmt.compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("过期总金额为负值");
        }
        return new TradeCommCashVo().setCustomerCode(ids.substring(1)).setCashNo(splitNo)
                .setSplitNo(splitNo).setCashAmt(cashExpireAmt).setOutFeeAmt(BigDecimal.ZERO);

    }

    /**
     * 提现单调用接口后，异常，反填接口数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatetExpireInfData(TradeCommCashBo bo) {
        List<TradeCommCash> list = Arrays.stream(bo.getCustomerCode().split(",")).map(vo -> {
            TradeCommCash update = MapstructUtils.convert(bo, TradeCommCash.class);
            update.setCustomerCode(null);
            update.setId(Long.parseLong(vo));
            return update;
        }).toList();
        baseMapper.updateBatchById(list);
    }

}