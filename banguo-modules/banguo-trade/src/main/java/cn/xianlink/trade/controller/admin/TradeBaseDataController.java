package cn.xianlink.trade.controller.admin;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.domain.bo.org.TradeBaseDataSelectBo;
import cn.xianlink.trade.domain.vo.org.TradeBaseDataPageVo;
import cn.xianlink.trade.service.biz.TradeBaseDataBizService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 公用查询
 *
 * <AUTHOR>
 * @date 2024-06-12
 * @folder 般果管理中心/财务流水/子账户余额
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/baseData")
public class TradeBaseDataController extends BaseController {

    private final transient TradeBaseDataBizService tradeBaseDataBizService;

    /**
     * 账户名称(查询条件) 最多返回20条
     */
    @Operation(summary = "账户名称(查询条件)")
    @PostMapping("/orgList")
    public R<List<TradeBaseDataPageVo>> orgList(@Validated @RequestBody TradeBaseDataSelectBo bo) {
        return R.ok(MapstructUtils.convert(tradeBaseDataBizService.selectListByName(bo), TradeBaseDataPageVo.class));
    }

}
