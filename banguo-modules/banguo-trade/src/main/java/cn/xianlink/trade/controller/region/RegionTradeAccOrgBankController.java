package cn.xianlink.trade.controller.region;

import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.api.domain.bo.RemoteOrgRelationUnbindBo;
import cn.xianlink.trade.api.domain.vo.RemoteOrgRelationStatusVo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankUnbindBo;
import cn.xianlink.trade.dubbo.RemoteOrgRelationServiceImpl;
import cn.xianlink.trade.service.biz.TradeBaseDataBizService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 提现单
 * 前端访问路由地址为:/system/accCash
 *
 * <AUTHOR>
 * @date 2024-06-12
 * @folder 供应商端(小程序)/供应商结算/提现单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/region/accOrgBank")
public class RegionTradeAccOrgBankController extends BaseController {

    private final transient TradeBaseDataBizService tradeBaseDataBizService;
    private final transient RemoteOrgRelationServiceImpl remoteOrgRelationService;


    /**
     * 解绑供应商银行卡，  status 返回 1 为解绑成功
     */
    @Operation(summary = "解绑银行卡")
    @RepeatSubmit()
    @PostMapping("/unbindBank")
    public R<RemoteOrgRelationStatusVo> unbindBank(@Validated @RequestBody TradeOrgBankUnbindBo bo) {
        RemoteBaseDataVo dataVo = tradeBaseDataBizService.queryById(bo.getSupplierId(), AccountOrgTypeEnum.SUPPLIER.getCode());
        if (dataVo == null || !dataVo.getCode().equals(bo.getSupplierCode())) {
            throw new ServiceException("供应商不存在");
        }
        return R.ok(remoteOrgRelationService.unbindBank(new RemoteOrgRelationUnbindBo()
                .setOrgCode(dataVo.getCode()).setOrgType(dataVo.getType())));

    }

}
