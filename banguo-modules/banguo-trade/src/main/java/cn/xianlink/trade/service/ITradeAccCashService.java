package cn.xianlink.trade.service;

import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.api.domain.bo.OrderPayJobQueryBo;
import cn.xianlink.trade.domain.bo.TradeAccCashBo;
import cn.xianlink.trade.domain.bo.TradeAccCashQueryBo;
import cn.xianlink.trade.domain.bo.TradeAccSupCashQueryBo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupCashQueryBo;
import cn.xianlink.trade.domain.vo.TradeAccCashVo;
import cn.xianlink.trade.domain.vo.TradeAccSupCashVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupCashVo;

import java.util.List;

/**
 * 提现单Service接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface ITradeAccCashService {

    /**
     * 查询提现单
     */
    TradeAccCashVo queryById(Long cashId);
    /**
     * 查询提现单
     */
    TradeAccCashVo queryByNo(String cashNo);

    TradeAccCashVo queryCashInfo(String orgCode, Long orgId, Integer orgType);
    /**
     * 查询提现单， 如
     */
    TradeAccCashVo queryCashOrById(String orgCode, Long orgId, Integer orgType, Long deptId,  Long cashId);
    /**
     * 查询提现单列表
     */
    TableDataInfo<SupTradeAccSupCashVo> querySupPageList(String orgCode, SupTradeAccSupCashQueryBo bp);
    /**
     * 查询提现单列表
     */
    TableDataInfo<TradeAccSupCashVo> querySupPageList(TradeAccSupCashQueryBo queryBo);
    /**
     * 查询提现单列表
     */
    List<TradeAccSupCashVo> querySupList(TradeAccSupCashQueryBo queryBo);
    /**
     * 查询提现单列表
     */
    TableDataInfo<TradeAccCashVo> queryPageList(TradeAccCashQueryBo queryBo);
    /**
     * 查询提现单列表
     */
    List<TradeAccCashVo> queryList(TradeAccCashQueryBo queryBo);


    List<TradeAccCashVo> queryCashProcessing(OrderPayJobQueryBo queryBo);
    /**
     * 查询提现单， 如
     */
    TradeAccCashVo insertByBo(TradeAccCashBo bo, TradeOrgBankRelaVo bankVo);
    /**
     * 合计金额
     */
    void updateCashAmt(Long cashId);


}
