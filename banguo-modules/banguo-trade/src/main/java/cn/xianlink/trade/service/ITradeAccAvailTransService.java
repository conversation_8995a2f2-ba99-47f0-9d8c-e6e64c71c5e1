package cn.xianlink.trade.service;

import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.api.domain.vo.RemoteSupAvailDateVo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupAvailTransExportBo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupAvailTransSkuOrderQueryBo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupAvailTransSkuQueryBo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupDeptAcctDetailQueryBo;
import cn.xianlink.trade.domain.vo.TradeAccAvailTransVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransOrderVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransSkuVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransVo;
import cn.xianlink.trade.domain.vo.sup.excel.SupTradeAccSupDeptAcctRefundExcelVo;
import cn.xianlink.trade.utils.SheetTableDataInfo;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

/**
 * 结算单Service接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface ITradeAccAvailTransService {

    /**
     * 查询变更流水列表
     */
    TableDataInfo<SupTradeAccSupDeptAvailTransVo> customSupPageList(SupTradeAccSupDeptAcctDetailQueryBo bo);
    /**
     * 查询结算单列表
     */
    SheetTableDataInfo<SupTradeAccSupDeptAvailTransSkuVo> customSupSkuPageList(SupTradeAccSupAvailTransSkuQueryBo bo);
    /**
     * 查询结算单列表
     */
    SheetTableDataInfo<SupTradeAccSupDeptAvailTransOrderVo> customSupOrderPageList(SupTradeAccSupAvailTransSkuOrderQueryBo bo);

    List<SupTradeAccSupDeptAvailTransSkuVo> customSupSkuTransIds(SupTradeAccSupAvailTransSkuQueryBo bo, List<SupTradeAccSupDeptAvailTransSkuVo> list);


    List<SupTradeAccSupDeptAcctRefundExcelVo> customSupAvailTransExportList(SupTradeAccSupAvailTransExportBo bo);


    List<RemoteSupAvailDateVo> queryeAvailTransList(LocalDate availDate, int pageNum, int pageSize);


    void appendAvailTrans(LocalDate accountDate, LocalDate availDate);

    void deleteAvailTransAll(LocalDate availDate);
    /*
     * 更新调用后提现完( 对 avail 为零的)
     */
    void createAvailTrans(String supplierCode, LocalDate transDate, LocalDate availDate);
    /*
     * 查询超时结算的数据
     */
    List<TradeAccAvailTransVo> querySupOverdueList(String supplierCode, LocalDate transDate, LocalDate availDate);
    /*
     * 更新历史未结的数据
     */
    void updateSupUndoneBatch(List<TradeAccAvailTransVo> transVos);


    String queryeSupAccountDate(String supplierCode);
}
