package cn.xianlink.trade.service;


import cn.xianlink.trade.domain.vo.*;

import java.util.List;

/**
 * 客户Service接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface ITradeCustAccountService {

    /**
     * 查询账务总
     */
    TradeCustAccountVo queryAccount(Long customerId);
    /**
     * 更新昨日数据
     */
    void updateAccountYdaAmt();
    /**
     * 查询未合计的流水
     */
    List<TradeCustTransAcctVo> getTransAcctList(int limit);
    /**
     * 更新到总计表中
     */
    void updateAccountAmt(List<TradeCustTransAcctVo> acctVos);
}
