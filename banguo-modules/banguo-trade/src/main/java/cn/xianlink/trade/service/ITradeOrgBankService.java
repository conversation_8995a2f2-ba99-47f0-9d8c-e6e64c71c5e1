package cn.xianlink.trade.service;

import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankAmtBo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankAmtQueryBo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankAuthQueryBo;
import cn.xianlink.trade.domain.vo.org.TradeAccDailyVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankAmtVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankAuthVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankVo;

import java.util.List;

/**
 * 机构子账户绑定Service接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface ITradeOrgBankService {

    /**
     * 查询机构子账户列表
     */
    TradeOrgBankVo queryByCode(String outOrgCode);
    /**
     * 查询机构子账户列表
     */
    List<TradeOrgBankVo> selectListByName(String name);
    /**
     * 查询机构子账户绑定列表
     */
    TableDataInfo<TradeOrgBankAuthVo> queryPageList(TradeOrgBankAuthQueryBo bo);

    /**
     * 查询机构子账户绑定列表
     */
    List<TradeOrgBankAuthVo> queryList(TradeOrgBankAuthQueryBo queryBo);
    /**
     * 查询机构子账户绑定列表
     */
    TableDataInfo<TradeOrgBankAmtVo> customPageList(TradeOrgBankAmtQueryBo bo);

    /**
     * 查询机构子账户绑定列表
     */
    List<TradeOrgBankAmtVo> customList(TradeOrgBankAmtQueryBo queryBo);

    /**
     * 修改机构子账户绑定
     */
    Boolean updateAmtByBo(TradeOrgBankAmtBo bo);

    /**
     * 批量更新昨日金额
     */
    void updateBankAmtBatch(List<TradeAccDailyVo> bos);

}
