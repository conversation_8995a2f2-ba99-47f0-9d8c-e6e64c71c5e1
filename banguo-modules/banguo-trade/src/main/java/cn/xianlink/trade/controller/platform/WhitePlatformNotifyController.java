package cn.xianlink.trade.controller.platform;

import cn.hutool.core.io.IoUtil;
import cn.xianlink.common.api.enums.trade.PayChannelEnum;
import cn.xianlink.common.api.enums.trade.PayInfStatusEnum;
import cn.xianlink.common.api.enums.trade.RefundInfStatusEnum;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.json.utils.JsonUtils;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.api.domain.bo.OrderPaySplitBo;
import cn.xianlink.trade.api.domain.bo.OrderRefundBo;
import cn.xianlink.trade.channel.ChannelsContext;
import cn.xianlink.trade.channel.pingancloud.PinganCloudWXPayServiceImpl;
import cn.xianlink.trade.channel.weixinb2b.WeixinB2bPayServiceImpl;
import cn.xianlink.trade.domain.bo.TradePayBo;
import cn.xianlink.trade.domain.bo.TradePayRefundBo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import cn.xianlink.trade.dubbo.RemotePaymentServiceImpl;
import cn.xianlink.trade.service.ITradePayRefundService;
import cn.xianlink.trade.service.ITradePayService;
import cn.xianlink.trade.service.ISupportMessageService;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotEmpty;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.List;


/**
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Tag(name = "支付白名单类")
@CustomLog
@RequiredArgsConstructor
@RestController
@RequestMapping("/white/notify")  // 因小程序回调url限制，去掉  /platform
public class WhitePlatformNotifyController extends BaseController {

    private final static String PAY_SUCCESS = "notify_success";
    private final static String PAY_FAILURE = "notify_failure";
    private final static String WX_PAY_NOTIFY = "retail_pay_notify";
    private final static String WX_REFUND_NOTIFY = "retail_refund_notify";
    private final static String WX_PAY_SUCCESS = "success";
    private final static String WX_PAY_FAILURE = "failure";
    // private final static String LAKALA_SUCCESS = "{\"code\": \"SUCCESS\", \"message\": \"执行成功\"}";
    // private final static String LAKALA_FAILURE = "{\"code\": \"FAIL\", \"message\": \"执行失败\"}";

    private final transient ChannelsContext channelsContext;
    private final transient ITradePayService tradePayService;
    private final transient ITradePayRefundService tradePayRefundService;
    private final transient RemotePaymentServiceImpl remotePaymentService;
    private final transient WeixinB2bPayServiceImpl weixinB2bPayServiceImpl;
    private final transient PinganCloudWXPayServiceImpl pinganCloudWXPayServiceImpl;
    private final transient ISupportMessageService supportMessageService;
    // private final transient LakalaWXPayServiceImpl lakalaWXPayServiceImpl;

    @Operation(summary = "微信小程序通知-url验证")
    @GetMapping("/weixinB2b/{path}")
    public String weixinB2bCheck(@NotEmpty(message = "地址错误") @PathVariable String path, HttpServletRequest request) {
        weixinB2bPayServiceImpl.switchByPath(path);
        return weixinB2bPayServiceImpl.callbackCheck(request);
    }

    @Operation(summary = "微信小程序通知")
    @PostMapping("/weixinB2b/{path}")
    public String weixinB2b(@NotEmpty(message = "地址错误") @PathVariable String path, HttpServletRequest request) throws IOException {
        channelsContext.initWebUserToken();
        String reqBody = IoUtil.read(request.getInputStream(), Charset.forName(request.getCharacterEncoding()));
        try {
            String appId = weixinB2bPayServiceImpl.switchByPath(path);
            JSONObject resp = weixinB2bPayServiceImpl.callback(reqBody, request);
            if (resp != null) {
                if (WX_PAY_NOTIFY.equals(resp.getString("Event"))) {
                    TradePayBo bo = weixinB2bPayServiceImpl.payCallback(resp);
                    OrderRefundBo orb = new OrderRefundBo().setSplits(List.of(new OrderPaySplitBo()));
                    weixinB2bPayServiceImpl.refundValidate(orb, MapstructUtils.convert(bo, TradePayVo.class));
                    payComplete(bo.setChannel(PayChannelEnum.PAY_WEIXIN_B2B.getCode()), MapstructUtils.convert(orb, TradePayRefundBo.class));
                } else if (WX_REFUND_NOTIFY.equals(resp.getString("Event"))) {
                    TradePayRefundBo bo = weixinB2bPayServiceImpl.refundCallback(resp);
                    refundComplete(bo.setChannel(PayChannelEnum.PAY_WEIXIN_B2B.getCode()));
                } else {
                    String messageBody = resp.toJSONString();
                    log.keyword("wechatOtherMessageNotify").info("其他通知: {}", messageBody);
                    // 处理微信消息回调
                    supportMessageService.handleMessage(messageBody);
                }
                return WX_PAY_SUCCESS;
            }
        } catch (Exception e) {
            log.keyword("payCallback").error("微信b2b回调", e);
        }
        return WX_PAY_FAILURE;
    }

    @Operation(summary = "平安云收款支付成功回调")
    @PostMapping("/pinganCloudWX/{path}")
    public String pinganCloudWX(@NotEmpty(message = "地址错误") @PathVariable String path, HttpServletRequest request) throws IOException {
        channelsContext.initWebUserToken();
        String reqBody = IoUtil.read(request.getInputStream(), Charset.forName(request.getCharacterEncoding()));
        try {
            JSONObject resp = pinganCloudWXPayServiceImpl.callback(reqBody, request);
            if (resp != null) {
                TradePayBo bo = pinganCloudWXPayServiceImpl.payCallback(resp);
                bo.setChannel(PayChannelEnum.PAY_PINGAN_CLOUD_WX.getCode());
                TradePayVo tvo = tradePayService.queryByTradeNo(bo.getTradeNo()); // 查询已删除的是否存在
                if (tvo == null) {
                    log.keyword(bo.getTradeNo(), "payCallback").error("平安回调，单据不存在 {}", JsonUtils.toJsonString(bo));
                } else if (PayInfStatusEnum.SUCCESS.getCode().equals(tvo.getPayInfStatus())) {
                    log.keyword(tvo.getOrderNo(), "payCallback").warn("平安回调，单据已完成，不处理 {}", JsonUtils.toJsonString(bo));
                } else {
                    log.keyword(tvo.getOrderNo(), "payCallback").info("平安回调，单据开始处理");
                    remotePaymentService.payUpdateInfData(false, bo.setId(tvo.getId()), tvo);
                }
                return PAY_SUCCESS;
            }
        } catch (Exception e) {
            log.keyword("payCallback").error("平安回调", e);
        }
        return PAY_FAILURE;
    }

    /*
    @Operation(summary = "拉卡拉通知")
    @PostMapping("/lakalaWX/{path}")
    public String lakalaWX(@NotEmpty(message = "地址错误") @PathVariable String path,HttpServletRequest request) throws IOException {
        channelsContext.initWebUserToken();
        String reqBody = IoUtil.read(request.getInputStream(), Charset.forName(request.getCharacterEncoding()));
        try {
            lakalaWXPayServiceImpl.switchByPath(path);
            JSONObject resp = lakalaWXPayServiceImpl.callback(reqBody, request);
            if (resp != null) {
                TradePayBo bo = lakalaWXPayServiceImpl.payCallback(resp);
                OrderRefundBo orb = new OrderRefundBo().setSplits(List.of(new OrderPaySplitBo()));
                lakalaWXPayServiceImpl.refundValidate(orb, MapstructUtils.convert(bo, TradePayVo.class));
                payComplete(bo.setChannel(PayChannelEnum.PAY_LAKALA_WX.getCode()), MapstructUtils.convert(orb, TradePayRefundBo.class));
                return LAKALA_SUCCESS;
            }
        } catch (Exception e) {
            log.keyword("payCallback").error("拉卡拉回调", e);
        }
        return LAKALA_FAILURE;
    }
    */

    private void payComplete(TradePayBo bo, TradePayRefundBo tbo) {
        if (PayInfStatusEnum.SUCCESS.getCode().equals(bo.getPayInfStatus())) {
            TradePayVo tvo = tradePayService.queryByTradeNo(bo.getTradeNo()); // 查询已删除的是否存在
            if (tvo == null) {
                log.keyword(bo.getTradeNo(), "payCallback").error("支付回调，单据不存在 {}", JsonUtils.toJsonString(bo));
            } else if (tvo.getDelFlag() != 0) {
                log.keyword(tvo.getOrderNo(), "payCallback").warn("支付回调，单据已删除，自动退款 {}", JsonUtils.toJsonString(bo));
                tradePayRefundService.insertException(bo.setId(tvo.getId()), tbo, tvo);
            } else if (PayInfStatusEnum.SUCCESS.getCode().equals(tvo.getPayInfStatus())) {
                log.keyword(tvo.getOrderNo(), "payCallback").warn("支付回调，单据已完成，不处理 {}", JsonUtils.toJsonString(bo));
            } else {
                log.keyword(tvo.getOrderNo(), "payCallback").info("支付回调，单据开始处理");
                remotePaymentService.payUpdateInfData(true, bo.setId(tvo.getId()), tvo);
            }
        } else {
            log.keyword(bo.getTradeNo(), "payCallback").info("支付回调，其他状态");
        }
    }

    private void refundComplete(TradePayRefundBo bo) {
        if (RefundInfStatusEnum.SUCCESS.getCode().equals(bo.getRefundInfStatus())) {
            TradePayRefundVo tvo = tradePayRefundService.queryByTradeNo(bo.getTradeRefundNo());
            if (tvo == null) {
                log.keyword(bo.getTradeRefundNo(), "payCallback").error("退款回调，单据不存在 {}", JsonUtils.toJsonString(bo));
            } else if (RefundInfStatusEnum.SUCCESS.getCode().equals(tvo.getRefundInfStatus())) {
                log.keyword(tvo.getRefundNo(), "payCallback").warn("退款回调，单据已完成，不处理 {}", JsonUtils.toJsonString(bo));
            } else {
                log.keyword(tvo.getRefundNo(), "payCallback").info("退款回调，单据开始处理");
                remotePaymentService.refundUpdateInfData(true, bo.setId(tvo.getId()), tvo);
            }
        } else {
            log.keyword(bo.getTradeRefundNo(), "payCallback").info("退款回调，其他状态");
        }
    }

}
