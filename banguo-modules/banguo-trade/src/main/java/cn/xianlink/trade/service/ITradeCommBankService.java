package cn.xianlink.trade.service;

import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.domain.bo.TradeCommBankBo;
import cn.xianlink.trade.domain.vo.comm.TradeCommBankVo;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 返利绑卡
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface ITradeCommBankService {
    /**
     * 查询客户有为签约
     */
    TradeCommBankVo queryById(Long id);

    /**
     * 查询客户有为签约列表
     */
    TableDataInfo<TradeCommBankVo> queryPageList(TradeCommBankBo bo, PageQuery pageQuery);

    /**
     * 查询客户有为签约列表
     */
    List<TradeCommBankVo> queryList(TradeCommBankBo bo);

    /**
     * 新增客户有为签约
     */
    Boolean insertByBo(TradeCommBankBo bo);

    /**
     * 修改客户有为签约
     */
    Boolean updateByBo(TradeCommBankBo bo);

    /**
     * 根据客户id查询客户账户绑定
     * @param id
     * @return
     */
    TradeCommBankVo queryByCustomerId(Long id);

    /**
     * 回调更新客户签约状态
     */
    void updateSignCallback(TradeCommBankBo bo);

    /**
     * 重新签约
     *
     * @param id
     * @param force
     * @return
     */
    boolean resign(Long id, Boolean force);

    /**
     * 取消签约
     *
     * @param id
     * @return
     */
    boolean signClose(Long id);

    /**
     * 检查签约情况
     *
     * @param id
     * @return
     */
    boolean checkSign(@NotNull(message = "主键不能为空") Long id);
}