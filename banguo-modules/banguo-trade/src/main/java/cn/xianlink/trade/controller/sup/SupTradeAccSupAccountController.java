package cn.xianlink.trade.controller.sup;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.xianlink.common.api.enums.trade.AccountOrgBindEnum;
import cn.xianlink.common.api.enums.trade.PayBusiTypeEnum;
import cn.xianlink.common.api.enums.trade.RefundBusiTypeEnum;
import cn.xianlink.common.api.enums.trade.TransferBusiTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.api.RemoteSupAccTransService;
import cn.xianlink.order.api.bo.RemoteSupAccTransBo;
import cn.xianlink.order.api.bo.RemoteSupAccTransQueryBo;
import cn.xianlink.order.api.vo.*;
import cn.xianlink.trade.config.properties.ChannelsProperties;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupAccountBusiQueryBo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupAccountBusiRecordQueryBo;
import cn.xianlink.trade.domain.convert.acc.SupTradeAccSupAccountConvert;
import cn.xianlink.trade.domain.vo.TradeAccAccountVo;
import cn.xianlink.trade.domain.vo.TradeAccTransVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankBindVo;
import cn.xianlink.trade.domain.vo.org.TradeSupBaseDataVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupAccountVo;
import cn.xianlink.trade.domain.vo.sup.SupTransRefundLossVo;
import cn.xianlink.trade.service.ITradeAccAccountService;
import cn.xianlink.trade.service.ITradeAccTransService;
import cn.xianlink.trade.service.biz.TradeOrgBankBizService;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;


/**
 * 账务总
 *
 * <AUTHOR>
 * @date 2024-06-12
 * @folder 供应商端(小程序)/供应商结算/资金账户
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/sup/accSupAccount")
@CustomLog
public class SupTradeAccSupAccountController extends BaseController {

    private final transient ChannelsProperties channelsProperties;
    private final transient ITradeAccAccountService tradeAccAccountService;
    private final transient ITradeAccTransService tradeAccTransService;
    private final transient TradeOrgBankBizService tradeOrgBankBizService;
    private final transient SupTradeAccUtils tradeAccUtils;

    @DubboReference(timeout = 10000)
    private final transient RemoteSupAccTransService remoteSupAccTransService;

    /**
     * 资金账户 : 资金账户概览
     */
    @PostMapping("/all")
    public R<SupTradeAccSupAccountVo> all() {
        TradeSupBaseDataVo vo = tradeAccUtils.getLoginSupplier(null, 0L);
        TradeAccAccountVo accountVo = tradeAccAccountService.querySupOrgAccount(vo.getCode(), vo.getType(), null);
        TradeOrgBankBindVo bindVo = tradeOrgBankBizService.queryBindByCode(vo.getCode(), vo.getType());
        accountVo.setOutOrgCode(StringUtils.isNotBlank(bindVo.getOutOrgCode()) ? bindVo.getOutOrgCode() : "");
        accountVo.setOutAcctCode(StringUtils.isNotBlank(bindVo.getOutAcctCode()) ? bindVo.getOutAcctCode() : "");
        accountVo.setBankAccount(AccountOrgBindEnum.BIND.getCode().equals(bindVo.getStatus()) ? bindVo.getBankAccount() : "");
        accountVo.setWithdrawFeeAmt(channelsProperties.getWithdrawFeeAmt());
        accountVo.setWithdrawLimitAmt(channelsProperties.getWithdrawLimitAmt());
        return R.ok(SupTradeAccSupAccountConvert.INSTANCE.toConvert(accountVo));
    }

    /**
     * 资金账户
     *  @ignore
     */
    @PostMapping("/trans")
    public R<TableDataInfo<RemoteSupAccTransBo>> trans(@Validated @RequestBody SupTradeAccSupAccountBusiQueryBo bo) {
        setSupplierInfo(bo, bo.getBusiField());
        return R.ok(accountTable(bo));
    }

    /**
     * 资金账户 : 查询订货列表
     */
    @PostMapping("/productOrder")
    public R<TableDataInfo<RemoteSupTransProductOrderVo>> productOrder(@Validated @RequestBody SupTradeAccSupAccountBusiQueryBo bo) {
        setSupplierInfo(bo, PayBusiTypeEnum.CUSTOM_ORDER.getField());
        TableDataInfo<RemoteSupAccTransBo> tableDataInfo = accountTable(bo);
        if (CollectionUtil.isNotEmpty(tableDataInfo.getRows())) {
            List<RemoteSupTransProductOrderVo> list = remoteSupAccTransService.queryProductOrder(new RemoteSupAccTransQueryBo()
                    .setTrans(tableDataInfo.getRows()).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
            return R.ok(TableDataInfo.build(list, tableDataInfo.getTotal()));
        }
        return R.ok(TableDataInfo.build());
    }

    /**
     * 资金账户 : 查询订货列表 - 单据明细
     */
    @PostMapping("/productOrderRecord")
    public R<TableDataInfo<RemoteSupTransProductOrderRecordVo>> productOrderRecord(@Validated @RequestBody SupTradeAccSupAccountBusiRecordQueryBo bo) {
        setSupplierInfo(bo, PayBusiTypeEnum.CUSTOM_ORDER.getField());
        TableDataInfo<RemoteSupAccTransBo> tableDataInfo = accountRecordTable(bo);
        if (CollectionUtil.isNotEmpty(tableDataInfo.getRows())) {
            List<RemoteSupTransProductOrderRecordVo> list = remoteSupAccTransService.queryProductOrderRecord(new RemoteSupAccTransQueryBo()
                    .setTrans(tableDataInfo.getRows()).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
            return R.ok(TableDataInfo.build(list, tableDataInfo.getTotal()));
        }
        return R.ok(TableDataInfo.build());
    }

    /**
     * 资金账户 : 退货退款
     */
    @PostMapping("/productRefund")
    public R<TableDataInfo<RemoteSupTransRefundVo>> productRefund(@Validated @RequestBody SupTradeAccSupAccountBusiQueryBo bo) {
        setSupplierInfo(bo, RefundBusiTypeEnum.REFUND_LACK.getField());
        TableDataInfo<RemoteSupAccTransBo> tableDataInfo = accountTable(bo);
        if (CollectionUtil.isNotEmpty(tableDataInfo.getRows())) {
            List<RemoteSupTransRefundVo> list = remoteSupAccTransService.queryProductRefund(new RemoteSupAccTransQueryBo()
                    .setTrans(tableDataInfo.getRows()).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
            return R.ok(TableDataInfo.build(list, tableDataInfo.getTotal()));
        }
        return R.ok(TableDataInfo.build());
    }

    /**
     * 资金账户 : 退货退款 - 单据明细
     */
    @PostMapping("/productRefundRecord")
    public R<TableDataInfo<RemoteSupTransRefundRecordVo>> productRefundRecord(@Validated @RequestBody SupTradeAccSupAccountBusiRecordQueryBo bo) {
        setSupplierInfo(bo, RefundBusiTypeEnum.REFUND_LACK.getField());
        TableDataInfo<RemoteSupAccTransBo> tableDataInfo = accountRecordTable(bo);
        if (CollectionUtil.isNotEmpty(tableDataInfo.getRows())) {
            List<RemoteSupTransRefundRecordVo> list = remoteSupAccTransService.queryProductRefundRecord(new RemoteSupAccTransQueryBo()
                    .setTrans(tableDataInfo.getRows()).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
            return R.ok(TableDataInfo.build(list, tableDataInfo.getTotal()));
        }
        return R.ok(TableDataInfo.build());
    }

    /**
     * 资金账户 : 差额退款
     */
    @PostMapping("/productRefundDiff")
    public R<TableDataInfo<RemoteSupTransRefundDiffVo>> productRefundDiff(@Validated @RequestBody SupTradeAccSupAccountBusiQueryBo bo) {
        setSupplierInfo(bo, RefundBusiTypeEnum.REFUND_BAL.getField());
        TableDataInfo<RemoteSupAccTransBo> tableDataInfo = accountTable(bo);
        if (CollectionUtil.isNotEmpty(tableDataInfo.getRows())) {
            List<RemoteSupTransRefundDiffVo> list = remoteSupAccTransService.queryProductRefundDiff(new RemoteSupAccTransQueryBo()
                    .setTrans(tableDataInfo.getRows()).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
            return R.ok(TableDataInfo.build(list, tableDataInfo.getTotal()));
        }
        return R.ok(TableDataInfo.build());
    }

    /**
     * 资金账户 : 差额退款 -- 单据明细
     */
    @PostMapping("/productRefundDiffRecord")
    public R<TableDataInfo<RemoteSupTransRefundRecordVo>> productRefundDiffRecord(@Validated @RequestBody SupTradeAccSupAccountBusiRecordQueryBo bo) {
        setSupplierInfo(bo, RefundBusiTypeEnum.REFUND_BAL.getField());
        TableDataInfo<RemoteSupAccTransBo> tableDataInfo = accountRecordTable(bo);
        if (CollectionUtil.isNotEmpty(tableDataInfo.getRows())) {
            List<RemoteSupTransRefundRecordVo> list = remoteSupAccTransService.queryProductRefundDiffRecord(new RemoteSupAccTransQueryBo()
                    .setTrans(tableDataInfo.getRows()).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
            return R.ok(TableDataInfo.build(list, tableDataInfo.getTotal()));
        }
        return R.ok(TableDataInfo.build());
    }

    /**
     * 资金账户 :  报损退款
     */
    @PostMapping("/productRefundLoss")
    public R<TableDataInfo<SupTransRefundLossVo>> productRefundLoss(@Validated @RequestBody SupTradeAccSupAccountBusiQueryBo bo) {
        setSupplierInfo(bo, RefundBusiTypeEnum.REFUND_LOSS.getField());
        TableDataInfo<RemoteSupAccTransBo> tableDataInfo = accountTable(bo);
        if (CollectionUtil.isNotEmpty(tableDataInfo.getRows())) {
            List<RemoteSupTransRefundLossVo> list = remoteSupAccTransService.queryProductRefundLoss(new RemoteSupAccTransQueryBo()
                    .setTrans(tableDataInfo.getRows()).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
            return R.ok(TableDataInfo.build(BeanUtil.copyToList(list, SupTransRefundLossVo.class), tableDataInfo.getTotal()));
        }
        return R.ok(TableDataInfo.build());
    }

    /**
     * 资金账户 :  报损退款 -- 单据明细
     */
    @PostMapping("/productRefundLossRecord")
    public R<TableDataInfo<RemoteSupTransRefundLossRecordVo>> productRefundLossRecord(@Validated @RequestBody SupTradeAccSupAccountBusiRecordQueryBo bo) {
        setSupplierInfo(bo, RefundBusiTypeEnum.REFUND_LOSS.getField());
        TableDataInfo<RemoteSupAccTransBo> tableDataInfo = accountRecordTable(bo);
        if (CollectionUtil.isNotEmpty(tableDataInfo.getRows())) {
            List<RemoteSupTransRefundLossRecordVo> list = remoteSupAccTransService.queryProductRefundLossRecord(new RemoteSupAccTransQueryBo()
                    .setTrans(tableDataInfo.getRows()).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
            return R.ok(TableDataInfo.build(list, tableDataInfo.getTotal()));
        }
        return R.ok(TableDataInfo.build());
    }

    /**
     * 资金账户 : 划转明细（加款）
     */
    @PostMapping("/transferIn")
    public R<TableDataInfo<RemoteSupTransDeductionVo>> transferIn(@Validated @RequestBody SupTradeAccSupAccountBusiQueryBo bo) {
        setSupplierInfo(bo, TransferBusiTypeEnum.TRANSFER_LOSS.getInField());
        TableDataInfo<RemoteSupAccTransBo> tableDataInfo = accountRecordTable(bo);
        if (CollectionUtil.isNotEmpty(tableDataInfo.getRows())) {
            List<RemoteSupTransDeductionVo> list = remoteSupAccTransService.queryDeduction(new RemoteSupAccTransQueryBo()
                    .setTrans(tableDataInfo.getRows()).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
            return R.ok(TableDataInfo.build(list, tableDataInfo.getTotal()));
        }
        return R.ok(TableDataInfo.build());
    }

    /**
     * 资金账户 : 划转明细（扣款）
     */
    @PostMapping("/transferOut")
    public R<TableDataInfo<RemoteSupTransDeductionVo>> transferOut(@Validated @RequestBody SupTradeAccSupAccountBusiQueryBo bo) {
        setSupplierInfo(bo, TransferBusiTypeEnum.TRANSFER_LOSS.getOutField());
        TableDataInfo<RemoteSupAccTransBo> tableDataInfo = accountRecordTable(bo);
        if (CollectionUtil.isNotEmpty(tableDataInfo.getRows())) {
            List<RemoteSupTransDeductionVo> list = remoteSupAccTransService.queryDeduction(new RemoteSupAccTransQueryBo()
                    .setTrans(tableDataInfo.getRows()).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
            return R.ok(TableDataInfo.build(list, tableDataInfo.getTotal()));
        }
        return R.ok(TableDataInfo.build());
    }

    private void setSupplierInfo(SupTradeAccSupAccountBusiQueryBo bo, String busiField) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getSupplierDeptId());
        bo.setSupplierId(baseVo.getId());
        bo.setSupplierCode(baseVo.getCode());
        bo.setBusiField(busiField);
        bo.setSupplierDeptId(baseVo.getDeptId());
    }

    private TableDataInfo<RemoteSupAccTransBo> accountTable(SupTradeAccSupAccountBusiQueryBo bo) {
        TableDataInfo<TradeAccTransVo> tableDataInfo = tradeAccTransService.customSupAccountTransPageList(bo);
        List<TradeAccTransVo> tradeAccTransVos = tradeAccTransService.customSupAccountTransIdsList(bo, tableDataInfo.getRows());
        List<RemoteSupAccTransBo> transVos = new ArrayList<>();
        for (TradeAccTransVo transVo: tradeAccTransVos) {
            transVos.add(new RemoteSupAccTransBo().setTransDate(transVo.getTransDate())
                    .setTransAmt(transVo.getTransAmt()).setSkuId(transVo.getSkuId()).setTransId(transVo.getTransId()));
        }
        return TableDataInfo.build(transVos, tableDataInfo.getTotal());
    }

    private TableDataInfo<RemoteSupAccTransBo> accountRecordTable(SupTradeAccSupAccountBusiQueryBo bo) {
        TableDataInfo<TradeAccTransVo> tableDataInfo = tradeAccTransService.querySupAccountTransRecordPageList(bo);
        List<RemoteSupAccTransBo> transVos = MapstructUtils.convert(tableDataInfo.getRows(), RemoteSupAccTransBo.class);
        return TableDataInfo.build(transVos, tableDataInfo.getTotal());
    }

    private void setSupplierInfo(SupTradeAccSupAccountBusiRecordQueryBo bo, String busiField) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getSupplierDeptId());
        bo.setSupplierId(baseVo.getId());
        bo.setSupplierCode(baseVo.getCode());
        bo.setBusiField(busiField);
        bo.setSupplierDeptId(baseVo.getDeptId());
    }

    private TableDataInfo<RemoteSupAccTransBo> accountRecordTable(SupTradeAccSupAccountBusiRecordQueryBo bo) {
        TableDataInfo<TradeAccTransVo> tableDataInfo = tradeAccTransService.querySupAccountTransRecordPageList(bo);
        List<RemoteSupAccTransBo> transVos = MapstructUtils.convert(tableDataInfo.getRows(), RemoteSupAccTransBo.class);
        return TableDataInfo.build(transVos, tableDataInfo.getTotal());
    }
}
