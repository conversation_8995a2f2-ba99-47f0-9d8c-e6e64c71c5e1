package cn.xianlink.trade.service;

import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.domain.bo.platform.TradePlatformCustTransQueryBo;
import cn.xianlink.trade.domain.vo.TradeCustTransPageVo;
import cn.xianlink.trade.domain.vo.TradePayRecvVo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户流水Service接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface ITradeCustTransService {


    TableDataInfo<TradeCustTransPageVo> queryTransPageList(TradePlatformCustTransQueryBo queryBo);
    /**
     * 订单预扣款处理
     */
    void updateFreezeByPay(Long customerId, String customerOutCode, BigDecimal payAmt);
    /**
     * 订单记录扣款
     */
    void insertTransByPay(Date availTime, TradePayVo payVo);
    /**
     * 退款记录扣款
     */
    void insertTransByRefund(Date availTime, TradePayRefundVo refundVo);
    /**
     * 存款记录扣款
     */
    void insertTransByRecv(Date availTime, TradePayRecvVo recvVo);
}
