package cn.xianlink.trade.service;

import cn.xianlink.trade.domain.bo.org.TradeOrgBankRelaBo;
import cn.xianlink.trade.domain.vo.org.*;

import java.util.List;

/**
 * 机构子账户绑定Service接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface ITradeOrgBankDetailService {

    String getOutOrgProperty();

    String getNextOutOrgCode();

    List<TradeOrgBankDetailVo> getBankList(String outOrgCode);

    List<TradeOrgBankVo> getRelationsByCode(String orgCode, Integer orgType);
    /**
     * 查询机构子账户绑定
     */
    TradeOrgRelationInfoVo getInfoByCode(String orgCode, Integer orgType);

    TradeOrgBankDetailVo getBankByCode(String outOrgCode, String bankAccount);

    void updateOrgRelate(String orgCode, Integer orgType, String outOrgCode, String outAcctCode);
    /**
     * 修改机构子账户绑定
     */
    void updateOrgRelate(TradeOrgBankRelaBo bo);
    /**
     * 修改机构子账户绑定
     */
    void updateBankByBo(TradeOrgBankRelaBo bo);

}
