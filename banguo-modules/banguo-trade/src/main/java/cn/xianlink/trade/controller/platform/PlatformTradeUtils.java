package cn.xianlink.trade.controller.platform;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.api.enums.system.SysUserTypeEnum;
import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.system.api.model.LoginUser;
import cn.xianlink.trade.service.biz.TradeBaseDataBizService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;


/**
 * 提现单
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Validated
@RequiredArgsConstructor
@Component
public class PlatformTradeUtils {

    private final transient TradeBaseDataBizService tradeBaseDataBizService;

    /**
     * 查询供应商数据
     */
    public RemoteBaseDataVo getLoginCustomer(Long customerId, Long userId) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if(loginUser == null) {
            return null;
        }
        Long relationId = loginUser.getRelationId();
        if (SysUserTypeEnum.SYS_USER.getType().equals(loginUser.getUserType()) && userId != null && userId == -519696L) {
            relationId = customerId; // 测试使用
        } else {
            if (ObjectUtil.isEmpty(loginUser.getUserId())) {
                throw new ServiceException("用户未登录");
            }
        }
        RemoteBaseDataVo vo = tradeBaseDataBizService.queryById(relationId, AccountOrgTypeEnum.CUSTOMER.getCode());
        if (vo == null) {
            throw new ServiceException("登录异常，不是客户");
        }
        return vo;
    }

}
