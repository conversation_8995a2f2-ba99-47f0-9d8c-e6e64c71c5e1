package cn.xianlink.trade.controller.admin;

import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.api.domain.bo.RemoteOrgRelationUnbindBo;
import cn.xianlink.trade.api.domain.vo.RemoteOrgRelationStatusVo;
import cn.xianlink.trade.domain.bo.TradeAccSupCashQueryBo;
import cn.xianlink.trade.domain.vo.TradeAccSupCashVo;
import cn.xianlink.trade.domain.vo.TradeAccTransAvailVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgMobileVo;
import cn.xianlink.trade.dubbo.RemoteOrgRelationServiceImpl;
import cn.xianlink.trade.service.ITradeAccCashService;
import cn.xianlink.trade.service.ITradeAccTransService;
import cn.xianlink.trade.service.ITradeOrgRelationService;
import cn.xianlink.trade.service.biz.TradeOrgBankBizService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 提现单
 * 前端访问路由地址为:/system/accCash
 *
 * <AUTHOR>
 * @date 2024-06-12
 * @folder 般果管理中心/供应商结算/提现单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/accSupCash")
public class TradeAccSupCashController extends BaseController {

    private final transient ITradeAccCashService tradeAccCashService;
    private final transient ITradeAccTransService tradeAccTransService;
    private final transient ITradeOrgRelationService tradeOrgRelationService;
    private final transient TradeOrgBankBizService tradeOrgBankBizService;
    private final transient RemoteOrgRelationServiceImpl remoteOrgRelationService;
    /**
     * 查询提现流水, 没有 城市仓,总仓 列
     */
    @Operation(summary = "提现查询")
    @PostMapping("/page")
    public R<TableDataInfo<TradeAccSupCashVo>> page(@Validated @RequestBody TradeAccSupCashQueryBo bo) {
        return R.ok(tradeAccCashService.querySupPageList(bo));
    }
    /**
     * 导出
     */
    @Operation(summary = "提现导出")
    @PostMapping("/export")
    public void export(@Validated TradeAccSupCashQueryBo bo, HttpServletResponse response) {
        List<TradeAccSupCashVo> list = tradeAccCashService.querySupList(bo);
        ExcelUtil.exportExcel(list, "提现单", TradeAccSupCashVo.class, response);
    }

    /**
     * 提现单明细流水导出
     */
    @Operation(summary = "提现单明细流水导出")
    @PostMapping("/exportTrans/{cashId}")
    public void exportTrans(@NotNull(message = "提现单id不能为空") @PathVariable Long cashId, HttpServletResponse response) {
        List<TradeAccTransAvailVo> list = tradeAccTransService.queryAvailTransList(true, cashId, null);
        ExcelUtil.exportExcel(list, "提现单明细流水", TradeAccTransAvailVo.class, response);
    }

    /**
     * 提现单 status=2 转成 待审核  status=3
     *   用于/admin/accSupAvail/availToCash 创建的提现单，转成待审核, 暂作废
     * @ignore
     */
    @RepeatSubmit()
    @PostMapping("/check/{id}")
    public R<Void> check(@NotNull(message = "提现单id不能为空") @PathVariable Long id) {
        tradeOrgBankBizService.updateCashUpdate(id);
        return R.ok();
    }

    /**
     * 提现审核操作（调用银行接口） 仅对 infStatus = 3 可调用
     */
    @Operation(summary = "提现审核操作（调用银行接口）")
    @RepeatSubmit()
    @PostMapping("/cash/{id}")
    public R<Void> cash(@NotNull(message = "提现单id不能为空") @PathVariable Long id) {
        tradeOrgBankBizService.withdrawCashCheck(id);
        return R.ok();
    }

    /**
     * 提现取消审核操作
     * @ignore

    @RepeatSubmit()
    @PostMapping("/cancel/{id}")
    public R<Void> cancel(@NotNull(message = "提现单id不能为空") @PathVariable Long id) {
        tradeOrgBankBizService.withdrawCashCancel(id);
        return R.ok();
    }*/

    /**
     * 提现取消审核操作（标记删除）
     */
    @Operation(summary = "提现取消审核操作（标记删除）")
    @RepeatSubmit()
    @PostMapping("/delete/{id}")
    public R<Void> delete(@NotNull(message = "提现单id不能为空") @PathVariable Long id) {
        tradeOrgBankBizService.withdrawCashDelete(id, null);
        return R.ok();
    }
    /**
     * 解绑供应商银行卡，  status 返回 1 为解绑成功
     */
    @Operation(summary = "解绑银行卡")
    @RepeatSubmit()
    @PostMapping("/unbindBank")
    public R<RemoteOrgRelationStatusVo> unbindBank(@Validated @RequestBody RemoteOrgRelationUnbindBo bo) {
        if (!AccountOrgTypeEnum.SUPPLIER.getCode().equals(bo.getOrgType())) {
            throw new ServiceException("仅支持供应商解绑卡");
        }
        return R.ok(remoteOrgRelationService.unbindBank(bo));
    }

    /**
     * 获取供应商联系方式
     */
    @Operation(summary = "获取供应商联系方式")
    @RepeatSubmit()
    @PostMapping("/getMobile")
    public R<TradeOrgMobileVo> getMobile(@Validated @RequestBody RemoteOrgRelationUnbindBo bo) {
        TradeOrgBankRelaVo vo = tradeOrgRelationService.queryBankByCode(bo.getOrgCode(), bo.getOrgType());
        if (vo == null || vo.getStatus() == null) {
            throw new ServiceException(String.format("[%s] 未开户", bo.getOrgCode()));
        }
        return R.ok(new TradeOrgMobileVo().setBankMobile(vo.getBankMobile()).setReprName(vo.getReprName()));
    }
}
