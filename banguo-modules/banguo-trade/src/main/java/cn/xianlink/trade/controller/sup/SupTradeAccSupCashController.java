package cn.xianlink.trade.controller.sup;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.api.domain.bo.RemoteOrgRelationUnbindBo;
import cn.xianlink.trade.api.domain.vo.RemoteOrgRelationStatusVo;
import cn.xianlink.trade.domain.bo.sup.*;
import cn.xianlink.trade.domain.convert.acc.SupTradeAccSupCashConvert;
import cn.xianlink.trade.domain.vo.TradeAccAccountVo;
import cn.xianlink.trade.domain.vo.org.TradeSupBaseDataVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupCashVo;
import cn.xianlink.trade.dubbo.RemoteOrgRelationServiceImpl;
import cn.xianlink.trade.service.ITradeAccAccountService;
import cn.xianlink.trade.service.ITradeAccCashService;
import cn.xianlink.trade.service.biz.TradeOrgBankBizService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 提现单
 * 前端访问路由地址为:/system/accCash
 *
 * <AUTHOR>
 * @date 2024-06-12
 * @folder 供应商端(小程序)/供应商结算/提现单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/sup/accSupCash")
public class SupTradeAccSupCashController extends BaseController {

    private final transient ITradeAccCashService tradeAccCashService;
    private final transient ITradeAccAccountService tradeAccAccountService;
    private final transient TradeOrgBankBizService tradeOrgBankBizService;
    private final transient RemoteOrgRelationServiceImpl remoteOrgRelationService;
    private final transient SupTradeAccUtils tradeAccUtils;

    /**
     * 供应商提现单： 分页查询 （已提现完成的）。 提现单详情使用行数据， 没有可提现金额
     */
    @PostMapping("/page")
    public R<TableDataInfo<SupTradeAccSupCashVo>> page(@Validated @RequestBody SupTradeAccSupCashQueryBo bo) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId());
        return R.ok(tradeAccCashService.querySupPageList(baseVo.getCode(), bo));
    }

    /**
     * 资金账户 --> 立即提现 --> 提现申请页面：  进入提现申请的页面
     */
    @PostMapping("/into")
    public R<SupTradeAccSupCashVo> intoCash(@Validated @RequestBody SupTradeAccSupDeptAccountBo bo) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId());
        SupTradeAccSupCashVo supCashVo = SupTradeAccSupCashConvert.INSTANCE
                .toConvert(tradeAccCashService.queryCashInfo(baseVo.getCode(), baseVo.getId(), baseVo.getType()));
        TradeAccAccountVo accountVo = tradeAccAccountService.querySupOrgAccount(baseVo.getCode(), baseVo.getType(), bo.getDeptId());
        supCashVo.setAvailAmt(accountVo.getAvailAmt());
        return R.ok(supCashVo);
    }

    /**
     * 作废：资金账户 --> 提现申请页面： 结算单勾使提现单金额，结算单数量变化， 使用cash_id查询最新提现单数据
    @PostMapping("/{cashId}")
    public R<SupTradeAccSupCashVo> getCash(@NotNull(message = "提现单id不能为空") @PathVariable Long cashId) {
        RemoteBaseDataVo vo = tradeAccUtils.getLoginSupplier();
        SupTradeAccSupCashVo supCashVo = SupTradeAccSupCashConvert.INSTANCE
                .toConvert(tradeAccCashService.queryById(cashId, vo.getCode(), vo.getType()));
        TradeAccAccountVo accountVo = tradeAccAccountService.querySupOrgAccount(vo.getCode(), vo.getType());
        if (accountVo != null) {
            supCashVo.setAvailAmt(accountVo.getAvailAmt());
        }
        return R.ok(supCashVo);
    }
     */
    /**
     * 作废资金账户 --> 提现申请页面 --> 立即提交 ： 执行提现操作（调用银行接口）
    @RepeatSubmit()
    @PostMapping("/cash/{cashId}")
    public R<Void> cash(@NotNull(message = "提现单id不能为空") @PathVariable Long cashId) {
        RemoteBaseDataVo vo = tradeAccUtils.getLoginSupplier();
        tradeOrgBankBizService.withdrawCash(cashId, vo.getCode(), vo.getType());
        return R.ok();
    }*/

    /**
     * 资金账户 --> 提现申请页面 --> 立即提交 ： 创建提现单并调用接口 或 进入待审核状态
     * */
     @RepeatSubmit()
     @PostMapping("/cash")
     public R<Void> cash(@Validated @RequestBody SupTradeAccSupCashAvaildsBo bo) {
         TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
         bo.setDeptId(baseVo.getDeptId());
         tradeOrgBankBizService.withdrawCash(false, bo, baseVo);
         return R.ok();
     }

    /**
     * 提现取消审核操作（标记删除）
     */
    @Operation(summary = "提现取消审核操作（标记删除）")
    @RepeatSubmit()
    @PostMapping("/delete")
    public R<Void> delete(@Validated @RequestBody SupTradeAccSupCashDeleteBo bo) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        tradeOrgBankBizService.withdrawCashDelete(bo.getCashId(), baseVo.getId());
        return R.ok();
    }

    /**
     * 解绑供应商银行卡，  status 返回 1 为解绑成功
     */
    @Operation(summary = "解绑银行卡")
    @RepeatSubmit()
    @PostMapping("/unbindBank")
    public R<RemoteOrgRelationStatusVo> unbindBank(@Validated @RequestBody SupTradeAccSupCashUnbindBo bo) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        return R.ok(remoteOrgRelationService.unbindBank(new RemoteOrgRelationUnbindBo()
                .setOrgType(baseVo.getType()).setOrgCode(baseVo.getCode())));
    }

}
