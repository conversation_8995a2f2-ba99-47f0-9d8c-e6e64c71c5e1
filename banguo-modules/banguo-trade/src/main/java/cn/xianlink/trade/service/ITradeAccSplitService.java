package cn.xianlink.trade.service;

import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.domain.TradeAccTrans;
import cn.xianlink.trade.domain.bo.TradeAccSplitAddBo;
import cn.xianlink.trade.domain.bo.TradeAccSplitUpdateBo;
import cn.xianlink.trade.domain.bo.TradeAccSupSplitQueryBo;
import cn.xianlink.trade.domain.vo.TradeAccSplitVo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;

import java.time.LocalDate;
import java.util.List;

/**
 * 支付分账流水Service接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface ITradeAccSplitService {

    /**
     * 查询供应商变更流水列表
     */
    TableDataInfo<TradeAccSplitVo> queryPageList(TradeAccSupSplitQueryBo bo);
    /**
     * 查询供应商变更流水列表
     */
    List<TradeAccSplitVo> queryList(TradeAccSupSplitQueryBo bo);
    /**
     * 查询日发生
     */
    List<TradePayVo> queryPaySplitError(LocalDate acctDate);

    List<TradePayVo> queryPaySplitDeleteError(LocalDate acctDate);

    List<TradePayRefundVo> queryRefundSplitError(LocalDate acctDate);

    List<TradePayRefundVo> queryRefundSplitDeleteError(LocalDate acctDate);

    /**
     * 订单新增流水
     */
    List<TradeAccSplitVo> batchInsertByBo(boolean isSplit, TradeAccSplitAddBo addBo, List<TradeAccTrans> adds);
    /**
     * 划转新 退单 增流水
     */
    List<TradeAccSplitVo> batchInsertByBo(String orderNo, List<String> refundNo, boolean isSplit, TradeAccSplitAddBo addBo, List<TradeAccTrans> addBos);
    /**
     * 更新记账日志
     */
    List<TradeAccSplitVo> batchUpdateByNo(TradeAccSplitAddBo addBo, List<TradeAccTrans> adds);
    /**
     * 更新接口状态
     */
    boolean updateSplit(TradeAccSplitUpdateBo updateBo);
    /**
     * 删除相关  仅其他 service 调用
     */
    void deleteSplit(boolean isTrans, TradeAccSplitUpdateBo updateBo);
    /**
     * 分账部分更改状态
     */
    boolean updateSplit(TradeAccSplitUpdateBo updateBo, List<TradeAccSplitVo> splitVos);
    /**
     * 删除相关  仅其他 service 调用
     */
    void deleteTrans(TradeAccSplitUpdateBo updateBo);
    /**
     * 更新对账状态
     */
    void updateAccStatus(LocalDate acctDate, List<String> splitNos);

}
