package cn.xianlink.trade.controller.sup;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupAvailQueryBo;
import cn.xianlink.trade.domain.convert.acc.SupTradeAccSupAvailConvert;
import cn.xianlink.trade.domain.vo.TradeAccAvailVo;
import cn.xianlink.trade.domain.vo.TradeAccTransAvailVo;
import cn.xianlink.trade.domain.vo.org.TradeSupBaseDataVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupAvailVo;
import cn.xianlink.trade.service.ITradeAccAvailService;
import cn.xianlink.trade.service.ITradeAccTransService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 结算单
 *
 * <AUTHOR>
 * @date 2024-06-12
 * @folder 供应商端(小程序)/供应商结算/结算单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/sup/accSupAvail")
public class SupTradeAccSupAvailController extends BaseController {

    private final transient ITradeAccAvailService tradeAccAvailService;
    private final transient ITradeAccTransService tradeAccTransService;
    private final transient SupTradeAccUtils tradeAccUtils;

    /**
     * 供应商结算单，分页查询，可查询到已提现的
     */
    @PostMapping("/page")
    public R<TableDataInfo<SupTradeAccSupAvailVo>> page(@Validated @RequestBody SupTradeAccSupAvailQueryBo bo) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId());
        TableDataInfo<TradeAccAvailVo> tdi = tradeAccAvailService.querySupPageList(baseVo.getCode(), bo);
        return R.ok(TableDataInfo.build(tdi.getRows().stream().map(SupTradeAccSupAvailConvert.INSTANCE::toConvert).toList(), tdi.getTotal()));
    }

    /**
     * 供应商结算单, 全量，不能查询到已提现的
     */
    @PostMapping("/list")
    public R<List<SupTradeAccSupAvailVo>> list(@Validated @RequestBody SupTradeAccSupAvailQueryBo bo) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId());
        List<TradeAccAvailVo> list = tradeAccAvailService.querySupExclusionList(baseVo.getCode(), bo);
        return R.ok(list.stream().map(SupTradeAccSupAvailConvert.INSTANCE::toConvert).toList());
    }

    /**
     * 结算单明细流水导出
     */
    @Operation(summary = "结算单明细流水导出")
    @PostMapping("/exportTrans/{availId}")
    public void exportTrans(@NotNull(message = "结算单id不能为空") @PathVariable Long availId, HttpServletResponse response) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(null, 0L);
        List<TradeAccTransAvailVo> list = tradeAccTransService.queryAvailTransList(false, availId, baseVo.getCode());
        ExcelUtil.exportExcel(list, "结算单明细流水", TradeAccTransAvailVo.class, response);
    }

    /**
     * 供应商结算单, 全量，不能查询到已提现的
    private R<List<SupTradeAccSupAvailVo>> list_(@Validated @RequestBody SupTradeAccSupAvailQueryBo bo) {
        TradeAccCashVo cashVo = getTradeAccCash(bo);
        List<TradeAccAvailVo> list = tradeAccAvailService.querySupList(cashVo.getOrgCode(), bo);
        priorityToCash(list, cashVo);
        return R.ok(list.stream().map(SupTradeAccSupAvailConvert.INSTANCE::toConvert).toList());
    }

    private TradeAccCashVo getTradeAccCash(SupTradeAccSupAvailQueryBo bo) {
     TradeSupBaseDataVo vo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        TradeAccCashVo cashVo = tradeAccCashService.queryCashOrById(vo.getCode(), vo.getId(), vo.getType(), bo.getCashId());
        bo.setCashId(cashVo.getId());
        return cashVo;
    }

    private void priorityToCash(List<TradeAccAvailVo> list, TradeAccCashVo cashVo) {
        // 优先级高的，强制勾选上
        List<TradeAccAvailVo> priorityList = list.stream().filter(v -> v.getPriority() != 0 && v.getCashId() == null).toList();
        if (priorityList.size() > 0) {
            tradeAccTransBizService.availToCash(priorityList, cashVo);
            priorityList.forEach(pvo -> {
                pvo.setCashId(cashVo.getId());
                pvo.setCashNo(cashVo.getCashNo());
            });
        }
    }*/
    /**
     * 作废： 全量设置提现状态， availIds 是选中生成到提现单中结算id。  返回更新后的全量结算单数据

    @RepeatSubmit()
    @PostMapping("/fullToCash")
    public R<Void> fullToCash(@Validated @RequestBody SupTradeAccSupCashAvaildsBo bo) {
        TradeSupBaseDataVo vo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        TradeAccCashVo cashVo = tradeAccCashService.queryCashOrById(vo.getCode(), vo.getId(), vo.getType(), bo.getCashId());
        if (CashInfStatusEnum.INF_INIT.getCode().equals(cashVo.getInfStatus())) {
            // 不能转入当前提现单， 也不报异常
            List<TradeAccAvailVo> list = tradeAccAvailService.querySupList(cashVo.getOrgCode(), new SupTradeAccSupAvailQueryBo().setCashId(cashVo.getId()));
            List<TradeAccAvailVo> availToCashList = list.stream().filter(v -> v.getCashId() == null && (v.getPriority() != 0
                    || bo.getAvailIds().contains(v.getId()))).toList();
            List<TradeAccAvailVo> cashToAvailList = list.stream().filter(v -> v.getCashId() != null && v.getPriority() == 0
                    && !bo.getAvailIds().contains(v.getId())).toList();
            tradeAccTransBizService.availToCash(availToCashList, cashVo);
            tradeAccTransBizService.cashToAvail(cashToAvailList, vo);
        }
        return R.ok();
    }
     */
    /**
     * 结算单转成提现状态

    @RepeatSubmit()
    @PostMapping("/availToCash")
    public R<Void> availToCash(@Validated @RequestBody SupTradeAccSupCashAvaildsBo bo) {
    TradeSupBaseDataVo vo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        TradeAccCashVo cashVo = tradeAccCashService.queryCashOrById(vo.getCode(), vo.getId(), vo.getType(), bo.getCashId());
        tradeAccTransBizService.availToCashByIds(bo.getAvailIds(), cashVo);
        return R.ok();
    }*/

    /**
     * 结算单取消提现状态

    @RepeatSubmit()
    @PostMapping("/cashToAvail")
    public R<Void> cashToAvail(@Validated @RequestBody SupTradeAccSupCashAvaildsBo bo) {
    TradeSupBaseDataVo vo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        tradeAccTransBizService.cashToAvailByIds(bo.getAvailIds(), vo);
        return R.ok();
    }*/
}
