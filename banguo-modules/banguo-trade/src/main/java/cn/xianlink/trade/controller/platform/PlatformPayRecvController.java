package cn.xianlink.trade.controller.platform;

import cn.hutool.core.bean.BeanUtil;
import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.api.enums.trade.PayChannelEnum;
import cn.xianlink.common.api.enums.trade.PayInfStatusEnum;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.api.domain.vo.OrderPayQueryVo;
import cn.xianlink.trade.channel.pingancloud.PinganCloudPayRecvService;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.bo.*;
import cn.xianlink.trade.domain.bo.platform.TradePlatformCustTransQueryBo;
import cn.xianlink.trade.domain.bo.platform.TradePlatformCustQueryBo;
import cn.xianlink.trade.domain.vo.*;
import cn.xianlink.trade.domain.vo.org.TradeOrgRelationInfoVo;
import cn.xianlink.trade.dubbo.RemotePaymentServiceImpl;
import cn.xianlink.trade.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;


/**
 * 客户大额
 * <AUTHOR>
 * @date 2024-05-28
 * @folder 采集平台(小程序)/客户资金/客户大额
 */
@Tag(name = "客户存钱类")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/payRecv")
public class PlatformPayRecvController extends BaseController {

    private final transient ITradePayService tradePayService;
    private final transient ITradePayRecvService tradePayRecvService;
    private final transient ITradeOrgBankDetailService tradeOrgBankDetailService;
    private final transient ITradeCustAccountService tradeCustAccountService;
    private final transient ITradeCustTransService tradeCustTransService;
    private final transient PinganCloudPayRecvService pinganCloudPayRecvService;
    private final transient PlatformTradeUtils platformTradeUtils;
    private final transient RemotePaymentServiceImpl remotePaymentService;

    /**
     * 客户存款流水查询
     */
    @Operation(summary = "客户存款流水查询")
    @PostMapping("/page")
    public R<TableDataInfo<TradePayRecvPageVo>> page(@Validated @RequestBody TradePayRecvQueryBo bo) {
        RemoteBaseDataVo dataVo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (dataVo == null) {
            throw new ServiceException("用户未登录");
        }
        bo.setCustomerId(dataVo.getId());
        return R.ok(tradePayRecvService.queryPageList(bo, true));
    }

    /**
     * 客户存款流水导出
     */
    @Operation(summary = "客户存款流水导出")
    @PostMapping("/export")
    public void export(@Validated TradePayRecvQueryBo bo, HttpServletResponse response) {
        RemoteBaseDataVo dataVo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (dataVo == null) {
            throw new ServiceException("用户未登录");
        }
        bo.setCustomerId(dataVo.getId());
        List<TradePayRecvPageVo> list = tradePayRecvService.queryList(bo, true);
        ExcelUtil.exportExcel(list, "存款流水", TradePayRecvPageVo.class, response);
    }

    /**
     * 查询客户当前可用余额
     */
    @Operation(summary = "查询客户当前可用余额")
    @PostMapping("/balanceAmt")
    public R<BigDecimal> balanceAmt(@Validated @RequestBody TradePlatformCustQueryBo bo) {
        RemoteBaseDataVo dataVo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (dataVo == null) {
            throw new ServiceException("用户未登录");
        }
        bo.setCustomerId(dataVo.getId());
        TradeCustAccountVo accountVo = tradeCustAccountService.queryAccount(dataVo.getId());
        return R.ok(accountVo.getAvailAmt() == null ? BigDecimal.ZERO : accountVo.getAvailAmt().add(accountVo.getFreezeAmt()));
    }
    /**
     * 查询客户账单流水查询 （存款 + 订单 + 退款）
     */
    @Operation(summary = "客户账单流水查询")
    @PostMapping("/accountPage")
    public R<TableDataInfo<TradeCustTransPageVo>> accountPage(@Validated @RequestBody TradePlatformCustTransQueryBo bo) {
        RemoteBaseDataVo dataVo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (dataVo == null) {
            throw new ServiceException("用户未登录");
        }
        bo.setCustomerId(dataVo.getId());
        return R.ok(tradeCustTransService.queryTransPageList(bo));
    }

    /**
     * 存款充值
     *   返回 outRecvCode， outRecvAccName 用于转账
     */
    @Operation(summary = "存款充值")
    @PostMapping("/recv")
    @RepeatSubmit()
    public R<TradePayRecvPageVo> recv(@Validated @RequestBody TradePayRecvAddBo bo) {
        RemoteBaseDataVo dataVo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (dataVo == null) {
            throw new ServiceException("用户未登录");
        }
        TradeOrgRelationInfoVo infoVo = tradeOrgBankDetailService.getInfoByCode(dataVo.getCode(), AccountOrgTypeEnum.CUSTOMER.getCode());
        if (infoVo == null || StringUtils.isEmpty(infoVo.getOutOrgCode()) || StringUtils.isEmpty(infoVo.getOutAcctCode())) {
            throw new ServiceException("未开户，不能充值");
        }
        if (infoVo.getStatus() != 2) {
            throw new ServiceException("未绑银行卡，不能充值");
        }
        TradePayRecvBo recvBo = new TradePayRecvBo();
        recvBo.setCustomerId(dataVo.getId());
        recvBo.setCustomerCode(dataVo.getCode());
        recvBo.setCustomerAcctCode(infoVo.getOutAcctCode());
        recvBo.setCustomerOutCode(infoVo.getOutOrgCode());
        recvBo.setRecvAmt(bo.getRecvAmt());
        TradePayRecvVo recvVo = tradePayRecvService.insertByBo(recvBo);
        try {
            TradePayRecvBo infTbo = pinganCloudPayRecvService.payRecv(recvVo);
            tradePayRecvService.updateInfData(infTbo, recvVo);
            BeanUtil.copyProperties(infTbo, recvVo, Constants.BeanCopyIgnoreNullValue);
        } catch (ServiceException se) {
            tradePayRecvService.updateInfFail(recvVo, se);
            throw se;
        }
        return R.ok(MapstructUtils.convert(recvVo, TradePayRecvPageVo.class));
    }

    /**
     * 确认存款转账是否到账（检查平安端是否已经收到转账金额）
     */
    @Operation(summary = "确认存款转账是否到账")
    @PostMapping("/recvCheck")
    @RepeatSubmit()
    public R<TradePayRecvPageVo> recvCheck(@Validated @RequestBody TradePayRecvCheckBo bo) {
        RemoteBaseDataVo dataVo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (dataVo == null) {
            throw new ServiceException("用户未登录");
        }
        TradePayRecvVo payRecvVo = tradePayRecvService.queryByNo(bo.getRecvNo());
        if (payRecvVo == null) {
            throw new ServiceException(String.format("存款单 %s 不存在", bo.getRecvNo()));
        }
        TradePayRecvBo infTbo = pinganCloudPayRecvService.payRecvQuery(payRecvVo);
        if (infTbo != null) {
            tradePayRecvService.updateInfData(infTbo, payRecvVo);
            BeanUtil.copyProperties(infTbo, payRecvVo, Constants.BeanCopyIgnoreNullValue);
        }
        return R.ok(MapstructUtils.convert(payRecvVo, TradePayRecvPageVo.class));
    }

    /**
     * 确认订单转账是否到账（检查平安端是否已经收到转账金额）
     */
    @Operation(summary = "确认订单转账是否到账")
    @PostMapping("/recvOrder")
    @RepeatSubmit()
    public R<TradePayRecvPageVo> recvOrder(@Validated @RequestBody TradePayRecvOrderBo bo) {
        RemoteBaseDataVo dataVo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (dataVo == null) {
            throw new ServiceException("用户未登录");
        }
        TradePayVo tvo = tradePayService.queryByNo(bo.getOrderNo());
        if (tvo == null || !PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode().equals(tvo.getChannel())
                || !Objects.equals(tvo.getCustomerId(), dataVo.getId())) {
            throw new ServiceException(String.format("支付单 %s 不存在", bo.getOrderNo()));
        }
        if (PayInfStatusEnum.UNPAID.getCode().equals(tvo.getPayInfStatus())) {
            OrderPayQueryVo payQueryVo = remotePaymentService.payRecvQuery(tvo);
            if (payQueryVo == null) {
                throw new ServiceException(String.format("支付单 %s 不存在", bo.getOrderNo()));
            }
        }
        TradePayRecvVo payRecvVo = tradePayRecvService.queryByNo(tvo.getTradeNo());
        if (payRecvVo == null) {
            throw new ServiceException(String.format("支付单 %s 不存在", bo.getOrderNo()));
        }
        return R.ok(MapstructUtils.convert(payRecvVo, TradePayRecvPageVo.class));
    }
}
