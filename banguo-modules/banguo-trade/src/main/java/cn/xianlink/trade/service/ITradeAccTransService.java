package cn.xianlink.trade.service;

import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.api.domain.bo.OrderPayJobQueryBo;
import cn.xianlink.trade.api.domain.bo.RemoteAccTransItemBo;
import cn.xianlink.trade.api.domain.vo.RemoteTradeAccTransVo;
import cn.xianlink.trade.domain.TradeAccTrans;
import cn.xianlink.trade.domain.bo.*;
import cn.xianlink.trade.domain.bo.sup.*;
import cn.xianlink.trade.domain.vo.*;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAcctDateVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptTransRowVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptTransVo;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 分账变更流水Service接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface ITradeAccTransService {

    List<TradeAccTransAvailVo> queryAvailTransList(boolean isCash, Long availId, String supplierCode);

    /**
     * 查询供应商变更流水列表
     */
    TableDataInfo<TradeAccSupAvailTransVo> querySupTransPageList(TradeAccSupAvailTransQueryBo bo);
    /**
     * 查询供应商变更流水列表
     */
    List<TradeAccSupAvailTransVo> querySupTransList(TradeAccSupAvailTransQueryBo bo);
    /**
     * 查询供应商账务总
     */
    TableDataInfo<TradeAccAccountVo> customSupPageList(TradeAccSupAccountQueryBo queryBo);
    /**
     * 查询供应商账务总
     */
    List<TradeAccAccountVo> customSupList(TradeAccSupAccountQueryBo queryBo);
    /**
     * 资金账户内业务单查询
     */
    TableDataInfo<TradeAccTransVo> customSupAccountTransPageList(SupTradeAccSupAccountBusiQueryBo bo);

    List<TradeAccTransVo> customSupAccountTransIdsList(SupTradeAccSupAccountBusiQueryBo bo, List<TradeAccTransVo> list);
    /**
     * 资金账户内业务单查询
     */
    TableDataInfo<TradeAccTransVo> querySupAccountTransRecordPageList(SupTradeAccSupAccountBusiRecordQueryBo bo);
    /**
     * 资金账户内划转单查询
     */
    TableDataInfo<TradeAccTransVo> querySupAccountTransRecordPageList(SupTradeAccSupAccountBusiQueryBo bo);
    /**
     * 查询变更流水列表
     */
    TableDataInfo<SupTradeAccSupDeptAcctDateVo> customSupDeptAcctDatePageList(SupTradeAccSupDeptAcctDetailQueryBo bo);
    /**
     * 分页
     */
    TableDataInfo<SupTradeAccSupDeptTransVo> customSupDeptTransPageList(SupTradeAccSupDeptAcctDetailQueryBo bo);
    /**
     * 查询变更流水明细
     */
    List<SupTradeAccSupDeptTransRowVo> customSupDeptTransList(SupTradeAccSupDeptAcctDetailQueryBo bo);
    /**
     * 查询变更流水明细
     */
    List<SupTradeAccSupDeptTransRowVo> customSupDeptTransList(SupTradeAccSupDeptAcctRecordQueryBo bo);
    /**
     * 查询变更划转单据明细
     */
    TableDataInfo<TradeAccTransVo> customSupDeptTransTransferList(SupTradeAccSupDeptAcctDetailQueryBo bo);
    /**
     * 查询变更流水列表
     */
    List<TradeAccTransVo> queryStatusListByIds(TradeAccTransChangeBo bo, List<Long> transIds);
    /**
     * 查询变更流水列表
     */
    List<TradeAccTransVo> queryStatusListByNo(TradeAccTransChangeBo bo, TradeAccTransStatusBo sbo);
    /**
     * 查询变更流水列表
     */
    List<TradeAccTransVo> queryStatusListByRelate(TradeAccTransChangeBo bo);
    /**
     * 查询订单关联列表, 基于机构商品关联
     */
    List<TradeAccTransVo> queryOrderRelateList(String relateNo, List<String> transTypes, List<TradeAccTrans> orgs);
    /**
     * 查询订单关联列表, 基于分账单号关联
     */
    List<TradeAccTransVo> queryOrderAvailTransList(String relateNo, List<String> splitNos);
    /**
     * 查询订单关联列表, 按类型，排序
     */
    Map<String, TradeAccTransVo> getOrderTransferNos(String orderNo);
    /**
     * 查询订单关联列表
     */
    List<TradeAccTransVo> queryTransList(String transNo, List<String> transTypes);
    /**
     * 查询订单关联列表
     */
    List<TradeAccTransVo> queryTransList(List<String> transNos, List<String> transType);
    /**
     * 查询供应商未结的数据
     */
    List<TradeAccTransVo> querySupplierFreezeTrans(String orderNo);
    /**
     * 查询可以取消的所有流水数据
     */
    List<TradeAccTransVo> queryPayCancelTrans(Boolean isZero, String orderNo);

    void updateCashAccount(List<Long> availIds, Date cashTime, Integer preStatus, Integer status);

    void updateAvailAccount(Long availId, Date cashTime, Integer preStatus, Integer status);
    /*
     * 待结算数据生成到结算单 （1）
     */
    void updateAvailInit(Long availId, List<TradeAccTransAvaildsBo> trans);
    /*
     * 待结算数据生成到结算单 （1）
     */
    void updateAvailInitCancel(Long availId, List<TradeAccTransAvaildsBo> trans);
    /*
     * 更新调用后提现完( 对 avail 为零的)
     */
    void updateAvailComplete(TradeAccAvailBo bo);
    /**
     * 订单 和 退款  0,1,6  的状态变化
     */
    void updateStatus(TradeAccTransChangeBo bo);
    /**
     * 结算转提现
     */
    void updateCashInit(SupTradeAccSupCashAvaildsBo bo);
    /**
     * 提现取消提现
     */
    void updateCashInitCancel(SupTradeAccSupCashAvaildsBo bo);
    /*
     * 提现待审核
     */
    void updateCashCheck(TradeAccCashBo cashBo);
    /*
     * 提现待审核，整体删除
     */
    void deleteCash(TradeAccCashBo cashBo);
    /*
     * 更新汇总账数据
     */
    void insertTransAcctByTrans(List<Long> transIds, Integer preStatus, Integer status, List<Long> commTransIds, Long availId);
    /*
     * 提现接口状态
     */
    TradeAccCashVo updateCashCheckByBo(TradeAccCashBo bo, TradeOrgBankRelaVo bankVo);
    /*
     * 更新调用后提现完
     */
    void updateCashInfData(TradeAccCashBo bo, TradeAccCashVo vo);
    /*
     * 更新调用后提现完
     */
    void updateCashInfFail(Integer infStatus, TradeAccCashVo vo, ServiceException se);
    /*
     * 查询可调用
     */
    List<TradeAccTransVo> queryDiscountChecking(OrderPayJobQueryBo bo);
    /*
     * 测试使用
     */
    List<TradeAccTransVo> queryDiscountTrans(TradeAccTransBo bo);
    /*
     * 查询失败的
     */
    List<TradeAccTransVo> queryDiscountFailed(LocalDate transDate);
    /*
     * 初始化调用
     */
    TradeAccTransVo updateDiscountInf(TradeAccTransBo accTransBo);
    /*
     * 接口回填
     */
    void updateDiscountInfData(TradeAccTransBo tbo, TradeAccTransVo tvo) ;
    /*
     * 接口回填
     */
    void updateDiscountInfFail(Integer infStatus, TradeAccTransVo tvo, ServiceException se) ;
    /**
     * 根据订单项目查询结算信息
     * @param orderCode
     * @param supplierSkuId
     * @return
     */
    List<RemoteTradeAccTransVo> getByOrderItemId(String orderCode, Long supplierSkuId);

    /**
     * 批量查询结算信息
     * <AUTHOR> on 2025/1/7:17:58
     * @param bos
     * @return orderCode +"_"+ skuId 为 key
     */
    Map<String, RemoteTradeAccTransVo> getByOrderItemIds(List<RemoteAccTransItemBo> bos);
}
