package cn.xianlink.trade.controller.sup;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.http.ContentType;
import cn.xianlink.common.api.enums.trade.AccountOrgBindEnum;
import cn.xianlink.common.api.enums.trade.AccountStatusEnum;
import cn.xianlink.common.api.enums.trade.AccountTransTypeEnum;
import cn.xianlink.common.api.enums.trade.BaseTypeEnum;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.json.utils.JsonUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.api.RemoteSupAccTransService;
import cn.xianlink.order.api.bo.RemoteSupAccSubsidyBo;
import cn.xianlink.order.api.bo.RemoteSupAccTransBo;
import cn.xianlink.order.api.bo.RemoteSupAccTransQueryBo;
import cn.xianlink.order.api.vo.*;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuFundsInfoVo;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.resource.api.domain.RemoteFile;
import cn.xianlink.trade.config.properties.ChannelsProperties;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.bo.TradeAccCashQueryBo;
import cn.xianlink.trade.domain.bo.sup.*;
import cn.xianlink.trade.domain.vo.*;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankBindVo;
import cn.xianlink.trade.domain.vo.org.TradeSupBaseDataVo;
import cn.xianlink.trade.domain.vo.sup.*;
import cn.xianlink.trade.domain.vo.sup.excel.*;
import cn.xianlink.trade.service.*;
import cn.xianlink.trade.service.biz.TradeBaseUtilBizService;
import cn.xianlink.trade.service.biz.TradeOrgBankBizService;
import cn.xianlink.trade.utils.SheetTableDataInfo;
import jakarta.servlet.http.HttpServletResponse;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 账务总
 *
 * <AUTHOR>
 * @date 2024-06-12
 * @folder 供应商端(小程序)/供应商结算/资金账户(新)
 */
@CustomLog
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/sup/accSupDeptAcct")
public class SupTradeAccSupDeptAcctController extends BaseController {

    private final transient ChannelsProperties channelsProperties;
    private final transient ITradeAccAccountService tradeAccAccountService;
    private final transient ITradeAccTransService tradeAccTransService;
    private final transient ITradeAccCashService tradeAccCashService;
    private final transient ITradeAccAvailTransService tradeAccAvailTransService;
    private final transient ITradePayService tradePayService;
    private final transient TradeOrgBankBizService tradeOrgBankBizService;
    private final transient TradeBaseUtilBizService tradeBaseUtilBizService;
    private final transient SupTradeAccUtils tradeAccUtils;

    @DubboReference(timeout = 300000)
    private final transient RemoteFileService remoteFileService;
    @DubboReference(timeout = 10000)
    private final transient RemoteSupAccTransService remoteSupAccTransService;
    @DubboReference
    private final transient RemoteSupplierSkuService remoteSupplierSkuService;

    /**
     * 资金账户 : 资金账户概览
     *  不传入 deptId， 供应商全部数据
     */
    @PostMapping("/all")
    public R<SupTradeAccSupDeptAccountVo> all(@Validated @RequestBody SupTradeAccSupDeptAccountBo bo) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId());
        TradeAccAccountVo accountVo = tradeAccAccountService.querySupOrgAccount(baseVo.getCode(), baseVo.getType(), bo.getDeptId());
        TradeOrgBankBindVo bindVo = tradeOrgBankBizService.queryBindByCode(baseVo.getCode(), baseVo.getType());
        accountVo.setOutOrgCode(StringUtils.isNotBlank(bindVo.getOutOrgCode()) ? bindVo.getOutOrgCode() : "");
        accountVo.setOutAcctCode(StringUtils.isNotBlank(bindVo.getOutAcctCode()) ? bindVo.getOutAcctCode() : "");
        accountVo.setBankAccount(AccountOrgBindEnum.BIND.getCode().equals(bindVo.getStatus()) ? bindVo.getBankAccount() : "");
        accountVo.setWithdrawFeeAmt(channelsProperties.getWithdrawFeeAmt());
        accountVo.setWithdrawLimitAmt(channelsProperties.getWithdrawLimitAmt());
        SupTradeAccSupDeptAccountVo deptAccountVo = MapstructUtils.convert(accountVo, SupTradeAccSupDeptAccountVo.class);
        deptAccountVo.setTotalAmt(deptAccountVo.getAvailAmt().add(deptAccountVo.getFreezeAmt()));
        deptAccountVo.setSaleDate(tradeAccUtils.getLastSaleDate(bo.getRegionWhId()));
        return R.ok(deptAccountVo);
    }

    /**
     * 资金账户 : 变动记录
     */
    @PostMapping("/trans")
    public R<TableDataInfo<SupTradeAccSupDeptAvailTransVo>> trans(@Validated @RequestBody SupTradeAccSupDeptAcctQueryBo bo) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId()).setSupplierCode(baseVo.getCode()).setSupplierId(baseVo.getId());
        SupTradeAccSupDeptAcctDetailQueryBo queryBo = new SupTradeAccSupDeptAcctDetailQueryBo();
        BeanUtil.copyProperties(bo, queryBo, Constants.BeanCopyIgnoreNullValue);
        queryBo.setAccountDate(baseVo.getAccountDate());
        TableDataInfo<SupTradeAccSupDeptAvailTransVo> result = tradeAccAvailTransService.customSupPageList(queryBo);
        /*
        Map<String, List<TradeAccAvailVo>> availVos = tradeAccAvailService.querySupList(baseVo.getCode(), new SupTradeAccSupAvailQueryBo()
                        .setDeptIds(bo.getDeptId() == null || bo.getDeptId() == 0L ? null : Collections.singletonList(bo.getDeptId()))
                        .setAvailDateStart(bo.getTransDateStart()).setAvailDateEnd(bo.getTransDateEnd())
                        .setStatuss(Arrays.asList(AccountStatusEnum.CASH.getCode(), AccountStatusEnum.CHECK.getCode())))
                .stream().collect(Collectors.groupingBy(vo -> vo.getAvailDate().toString(), Collectors.toList()));
        result.getRows().forEach(vo -> {
            if (vo.getAvailDate() != null) {
                List<TradeAccAvailVo> list = availVos.get(vo.getAvailDate().toString());
                vo.setCashAmt(CollectionUtil.isNotEmpty(list) ? list.stream().map(TradeAccAvailVo::getAvailAmt).reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO);
            } else {
                vo.setCashAmt(BigDecimal.ZERO);
            }
        });*/

        // 查询平台补贴
        RemoteSupAccTransQueryBo platformSubSidyQueryBo = new RemoteSupAccTransQueryBo().setSupplierId(bo.getSupplierId()).setSupplierDeptId(bo.getDeptId());
        result.getRows().forEach(vo -> platformSubSidyQueryBo.addTransDate(vo.getTransDate()));
        Map<LocalDate, BigDecimal> subsidyMap = remoteSupAccTransService.queryPlatformSubsidyByDate(platformSubSidyQueryBo);

        Map<String, List<TradeAccCashVo>> cashVos = tradeAccCashService.queryList(new TradeAccCashQueryBo().setOrgType(baseVo.getType())
                        .setOrgCodes(Collections.singletonList(baseVo.getCode()))
                        .setDeptIds(bo.getDeptId() == null || bo.getDeptId() == 0L ? null : Collections.singletonList(bo.getDeptId()))
                        .setCashDateStart(bo.getTransDateStart()).setCashDateEnd(bo.getTransDateEnd()))
                .stream().collect(Collectors.groupingBy(vo -> vo.getCashDate().toString(), Collectors.toList()));
        result.getRows().forEach(vo -> {
            vo.setPlatformSubsidyAmt(subsidyMap.getOrDefault(vo.getTransDate(), BigDecimal.ZERO));
            List<TradeAccCashVo> list = cashVos.get(vo.getTransDate().toString());
            vo.setCashAmt(CollectionUtil.isNotEmpty(list) ? list.stream().map(TradeAccCashVo::getCashAmt).reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO);
        });
        return R.ok(result);
    }

    /**
     * 补贴明细
     *
     * @param bo
     * @return
     */
    @PostMapping("/subsidyList")
    public R<SheetTableDataInfo<SupTradeAccSupDeptAvailTransOrderVo>> subsidyList(@Validated @RequestBody SupTradeAccSupSubsidyQueryBo bo) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        RemoteSupAccSubsidyBo subSidyQueryBo = new RemoteSupAccSubsidyBo().setSupplierId(baseVo.getId()).setSupplierDeptId(baseVo.getDeptId()).setTransDate(bo.getTransDate());
        subSidyQueryBo.setPageSize(bo.getPageSize());
        subSidyQueryBo.setPageNum(bo.getPageNum());

        RemoteSupBillVo total = remoteSupAccTransService.queryPlatformSubsidyTotal(subSidyQueryBo);
        // 查询汇总
        SupTradeAccSupDeptAvailTransOrderVo totalRow = new SupTradeAccSupDeptAvailTransOrderVo();
        totalRow.setTransQty(total.getCount());
        totalRow.setSubsidyAmt(total.getSubsidyAmt());

        List<RemoteSupBillVo> remoteSupBillVos = remoteSupAccTransService.queryPlatformSubsidyList(subSidyQueryBo);
        List<SupTradeAccSupDeptAvailTransOrderVo> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(remoteSupBillVos)) {
//            totalRow.setTransQty(remoteSupBillVos.size());
//            totalRow.setSubsidyAmt(remoteSupBillVos.stream().map(RemoteSupBillVo::getSubsidyAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
            remoteSupBillVos.forEach(vo -> {
                SupTradeAccSupDeptAvailTransOrderVo transOrderVo = new SupTradeAccSupDeptAvailTransOrderVo();
                transOrderVo.setTransQty(vo.getCount());
                transOrderVo.setSubsidyAmt(vo.getSubsidyAmt());  // 补贴
                transOrderVo.setSubsidyItemAmt(vo.getSubsidyItemAmt()); // 单件补贴
                transOrderVo.setTransTime(vo.getOrderCreateTime());
                transOrderVo.setRelateNo(vo.getBusinessNo());    // 订单号
                transOrderVo.setSpuStandards(vo.getSpuStandards());
                transOrderVo.setSupplierSkuName(vo.getSkuName());
                transOrderVo.setSupplierSkuId(vo.getSupplierSkuId());
                list.add(transOrderVo);
            });
        }

        return R.ok(SheetTableDataInfo.build(list, totalRow));
    }

    /**
     * 变动记录 -> 各类型商品列表
     */
    @PostMapping("/transSkuList")
    public R<SheetTableDataInfo<SupTradeAccSupDeptAvailTransSkuVo>> transSkuList(@Validated @RequestBody SupTradeAccSupAvailTransSkuQueryBo bo) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId()).setSupplierCode(baseVo.getCode()).setSupplierId(baseVo.getId());
        SheetTableDataInfo<SupTradeAccSupDeptAvailTransSkuVo> transSkuVos = tradeAccAvailTransService.customSupSkuPageList(bo);
        List<SupTradeAccSupDeptAvailTransSkuVo> transIds = tradeAccAvailTransService.customSupSkuTransIds(bo, transSkuVos.getRows());
        Map<String, List<SupTradeAccSupDeptAvailTransSkuVo>> skuTransMap = transIds.stream().collect(
                Collectors.groupingBy(vo -> String.format("%s_%s", vo.getSupplierSkuId(), vo.getTransType()), Collectors.toList()));
        for (SupTradeAccSupDeptAvailTransSkuVo transVo: transSkuVos.getRows()) {
            if ("commission".equals(bo.getType())) {
                transVo.setType(AccountTransTypeEnum.OP.getCode().equals(transVo.getTransType()) ? "pay" : "refund");
            } else {
                transVo.setType(bo.getType());
            }
            String rowKey = String.format("%s_%s", transVo.getSupplierSkuId(), transVo.getTransType());
            List<SupTradeAccSupDeptAvailTransSkuVo> skuTransIds = skuTransMap.get(rowKey);
            if (skuTransIds != null && skuTransIds.size() > 0) {
                List<RemoteSupAccTransBo> transBos = skuTransIds.stream().map(SupTradeAccSupDeptAvailTransSkuVo::getTransId).collect(Collectors.toSet())
                        .stream().map(vo -> new RemoteSupAccTransBo().setSkuId(transVo.getSupplierSkuId()).setTransId(vo)).toList();
                if (transVo.getType().contains("pay")) {
                    // querySaleBillList       skuId 必须只有一个值 ！！！！！！！！！！
                    List<RemoteSupBillVo> billVos = remoteSupAccTransService.querySaleBillList(new RemoteSupAccTransQueryBo()
                            .setTrans(transBos).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
                    if (CollectionUtil.isNotEmpty(billVos)) {
                        transVo.setTransCount(billVos.stream().map(RemoteSupBillVo::getCount).reduce(Integer::sum).orElse(0));
                        transVo.setFreeAmt(billVos.stream().map(RemoteSupBillVo::getProductFreeAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    } else {
                        transVo.setTransCount(0);
                        transVo.setFreeAmt(BigDecimal.ZERO);
                    }
                } else if (transVo.getType().contains("refund")) {
                    // queryRefundBillList       skuId 必须只有一个值 ！！！！！！！！！！
                    List<RemoteSupBillVo> billVos = remoteSupAccTransService.queryRefundBillList(new RemoteSupAccTransQueryBo()
                            .setTrans(transBos).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
                    transVo.setTransCount(billVos == null ? 0 : billVos.stream().map(RemoteSupBillVo::getCount).reduce(Integer::sum).orElse(0));
                    transVo.setFreeAmt(BigDecimal.ZERO);
                }
            }
            // 设置成当前， 前端使用
            transVo.setType(bo.getType());
        }
        return R.ok(transSkuVos);
    }

    /**
     * 变动记录 -> 各类型商品列表 -> 订单单据类别
     */
    @PostMapping("/transOrderList")
    public R<SheetTableDataInfo<SupTradeAccSupDeptAvailTransOrderVo>> transOrderList(@Validated @RequestBody SupTradeAccSupAvailTransSkuOrderQueryBo bo) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId()).setSupplierCode(baseVo.getCode()).setSupplierId(baseVo.getId());
        SheetTableDataInfo<SupTradeAccSupDeptAvailTransOrderVo> transSkuVos = tradeAccAvailTransService.customSupOrderPageList(bo);
        if (CollectionUtil.isNotEmpty(transSkuVos.getRows())) {
            setRemoteSupAccTransData(transSkuVos.getRows(), remoteSupAccTransService.querySaleBillList(getRemoteSupAccTransQuery(bo, transSkuVos.getRows())));
        }
        return R.ok(transSkuVos);
    }
    /**
     * 变动记录 -> 各类型商品列表 -> 退款单据类别
     */
    @PostMapping("/transRefundList")
    public R<SheetTableDataInfo<SupTradeAccSupDeptAvailTransOrderVo>> transRefundList(@Validated @RequestBody SupTradeAccSupAvailTransSkuOrderQueryBo bo) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId()).setSupplierCode(baseVo.getCode()).setSupplierId(baseVo.getId());
        SheetTableDataInfo<SupTradeAccSupDeptAvailTransOrderVo> transSkuVos = tradeAccAvailTransService.customSupOrderPageList(bo);
        if (CollectionUtil.isNotEmpty(transSkuVos.getRows())) {
            setRemoteSupAccTransData(transSkuVos.getRows(), remoteSupAccTransService.queryRefundBillList(getRemoteSupAccTransQuery(bo, transSkuVos.getRows())));
        }
        return R.ok(transSkuVos);
    }

    private RemoteSupAccTransQueryBo getRemoteSupAccTransQuery(SupTradeAccSupAvailTransSkuOrderQueryBo bo, List<SupTradeAccSupDeptAvailTransOrderVo> transSkuVos) {
        List<RemoteSupAccTransBo> transVos = transSkuVos.stream().map(SupTradeAccSupDeptAvailTransOrderVo::getTransId).collect(Collectors.toSet())
                .stream().map(vo -> new RemoteSupAccTransBo().setSkuId(bo.getSkuId()).setTransId(vo)).toList();
        return new RemoteSupAccTransQueryBo().setTrans(transVos).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode());
    }

    private void setRemoteSupAccTransData(List<SupTradeAccSupDeptAvailTransOrderVo> transSkuVos, List<RemoteSupBillVo> billVos) {
        Map<Long, List<RemoteSupBillVo>> listMap = billVos.stream().collect(Collectors.groupingBy(RemoteSupBillVo::getTransId, Collectors.toList()));
        for (SupTradeAccSupDeptAvailTransOrderVo orderVo : transSkuVos) {
            List<RemoteSupBillVo> billVo = listMap.get(orderVo.getTransId());
            if (billVo == null) {
                if (channelsProperties.isDebugLog()) {
                    log.keyword("transSkuList").warn(String.format("orderVo = %s, billVo = null",  JsonUtils.toJsonString(orderVo)));
                }
            } else {
                orderVo.setFreeAmt(billVo.get(0).getProductFreeAmount());
                orderVo.setTransQty(billVo.get(0).getCount());
                orderVo.setTransPrice(billVo.get(0).getSkuPrice());
                orderVo.setTransTime(billVo.get(0).getOrderCreateTime());
                orderVo.setBusinessNo(billVo.get(0).getBusinessNo());
                if (channelsProperties.isDebugLog()) {
                    if (orderVo.getTransAmt().abs().compareTo(billVo.get(0).getAmount()) != 0) {
                        log.keyword("transSkuList").warn(String.format("orderVo = %s billVo = %s", orderVo.getTransAmt(), JsonUtils.toJsonString(billVo.get(0))));
                    }
                }
            }
        }
    }

    /**
     * 变动记录 -> 各类型商品列表 -> 佣金单据类别
     */
    @PostMapping("/transCommissionList")
    public R<SheetTableDataInfo<SupTradeAccSupDeptAvailTransOrderVo>> transCommissionList(@Validated @RequestBody SupTradeAccSupAvailTransSkuOrderQueryBo bo) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId()).setSupplierCode(baseVo.getCode()).setSupplierId(baseVo.getId()).setType("commission");
        SheetTableDataInfo<SupTradeAccSupDeptAvailTransOrderVo> transSkuVos = tradeAccAvailTransService.customSupOrderPageList(bo);
        if (CollectionUtil.isNotEmpty(transSkuVos.getRows())) {
            List<SupTradeAccSupDeptAvailTransOrderVo> orderList = transSkuVos.getRows().stream().filter(vo -> AccountTransTypeEnum.OP.getCode().equals(vo.getTransType())).toList();
            List<SupTradeAccSupDeptAvailTransOrderVo> refundList = transSkuVos.getRows().stream().filter(vo -> !AccountTransTypeEnum.OP.getCode().equals(vo.getTransType())).toList();
            if (CollectionUtil.isNotEmpty(orderList)) {
                setRemoteSupAccTransData(orderList, remoteSupAccTransService.querySaleBillList(getRemoteSupAccTransQuery(bo, orderList)));
            }
            if (CollectionUtil.isNotEmpty(refundList)) {
                setRemoteSupAccTransData(refundList, remoteSupAccTransService.queryRefundBillList(getRemoteSupAccTransQuery(bo, refundList)));
            }
        }
        return R.ok(transSkuVos);
    }

    /**
     * 变动记录 -> 各类型商品列表 -> 划转单据类别
     */
    @PostMapping("/transTransferList")
    public R<TableDataInfo<RemoteSupTransDeductionVo>> transTransferList(@Validated @RequestBody SupTradeAccSupAvailTransSkuOrderQueryBo bo) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId()).setSupplierCode(baseVo.getCode()).setSupplierId(baseVo.getId()).setType("transfer");
        SheetTableDataInfo<SupTradeAccSupDeptAvailTransOrderVo> transSkuVos = tradeAccAvailTransService.customSupOrderPageList(bo);
        if (CollectionUtil.isNotEmpty(transSkuVos.getRows())) {
            List<RemoteSupTransDeductionVo> billVos = remoteSupAccTransService.queryDeduction(getRemoteSupAccTransQuery(bo, transSkuVos.getRows()));
            return R.ok(TableDataInfo.build(billVos, transSkuVos.getTotal()));
        }
        return R.ok(TableDataInfo.build());
    }

    /**
     * 资金账户 : 变动记录
     */
    @PostMapping("/transDate")
    public R<TableDataInfo<SupTradeAccSupDeptAcctDateVo>> transDate(@Validated @RequestBody SupTradeAccSupDeptAcctQueryBo bo) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId()).setSupplierCode(baseVo.getCode()).setSupplierId(baseVo.getId());
        SupTradeAccSupDeptAcctDetailQueryBo queryBo = new SupTradeAccSupDeptAcctDetailQueryBo();
        BeanUtil.copyProperties(bo, queryBo, Constants.BeanCopyIgnoreNullValue);
        TableDataInfo<SupTradeAccSupDeptAcctDateVo> result = tradeAccTransService.customSupDeptAcctDatePageList(queryBo);
        Map<String, List<TradeAccCashVo>> cashVos = tradeAccCashService.queryList(new TradeAccCashQueryBo().setOrgType(baseVo.getType())
                        .setOrgCodes(Collections.singletonList(baseVo.getCode()))
                        .setDeptIds(bo.getDeptId() == null || bo.getDeptId() == 0L ? null : Collections.singletonList(bo.getDeptId()))
                        .setCashDateStart(bo.getTransDateStart()).setCashDateEnd(bo.getTransDateEnd()))
                .stream().collect(Collectors.groupingBy(vo -> vo.getCashDate().toString(), Collectors.toList()));
        result.getRows().forEach(vo -> {
            List<TradeAccCashVo> list = cashVos.get(vo.getTransDate().toString());
            vo.setCashAmt(CollectionUtil.isNotEmpty(list) ? list.stream().map(TradeAccCashVo::getCashAmt).reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO);
        });
        return R.ok(result);
    }
    /**
     * 资金账户 : 变动记录明细
     */
    @PostMapping("/transDetail")
    public R<TableDataInfo<SupTradeAccSupDeptAcctDetailVo>> transDetail(@Validated @RequestBody SupTradeAccSupDeptAcctDetailQueryBo bo)
            throws InvocationTargetException, IllegalAccessException {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId()).setSupplierCode(baseVo.getCode()).setSupplierId(baseVo.getId());
        // 日期分页查询
        TableDataInfo<SupTradeAccSupDeptTransVo> pageList = tradeAccTransService.customSupDeptTransPageList(bo);
        int total = pageList.getRows().size();
        if (total== 0) {
            return R.ok(TableDataInfo.build());
        }
        // 确定该页查询的日期范围
        bo.setTransDates(Arrays.asList(pageList.getRows().get(total - 1).getTransDate(), pageList.getRows().get(0).getTransDate()));
        return R.ok(TableDataInfo.build(transDetailPageList(bo), pageList.getTotal()));
    }

    private List<SupTradeAccSupDeptAcctDetailVo> transDetailPageList(SupTradeAccSupDeptAcctDetailQueryBo bo)
            throws InvocationTargetException, IllegalAccessException {
        List<SupTradeAccSupDeptAcctDetailVo> list = new ArrayList<>();
        Map<String, SupTradeAccSupDeptAcctDetailVo> detailMap = new HashMap<>();
        Map<String, SupTradeAccSupDeptAcctDetailStatusVo> statusMap = new HashMap<>();
        Map<String, SupTradeAccSupDeptAcctDetailGoodsVo> goodsMap = new HashMap<>();
        Map<String, SupTradeAccSupDeptAcctDetailListVo> detailListMap = new HashMap<>();
        List<SupTradeAccSupDeptTransRowVo> deptTransVos = tradeAccTransService.customSupDeptTransList(bo);
        Map<String, List<SupTradeAccSupDeptTransRowVo>> recordMap = deptTransVos.stream().collect(
                Collectors.groupingBy(vo -> String.format("%s_%s_%s", vo.getTransId(), vo.getSkuId(), vo.getStatus()), Collectors.toList()));
        Map<Long, RemoteSupplierSkuFundsInfoVo> skuMap = remoteSupplierSkuService.queryInfoListByFunds(
                deptTransVos.stream().map(SupTradeAccSupDeptTransRowVo::getSkuId).filter(Objects::nonNull)
                        .collect(Collectors.toSet()).stream().toList()
        ).stream().collect(Collectors.toMap(RemoteSupplierSkuFundsInfoVo::getSkuId, Function.identity()));
        Map<Long, RemoteBaseDataVo> deptMap = tradeBaseUtilBizService.queryBaseListByIds(
                skuMap.values().stream().map(RemoteSupplierSkuFundsInfoVo::getSupplierDeptId).filter(Objects::nonNull)
                        .collect(Collectors.toSet()).stream().toList(), BaseTypeEnum.DEPT.getCode()
        ).stream().collect(Collectors.toMap(RemoteBaseDataVo::getId, Function.identity()));
        for (Integer status : Arrays.asList(AccountStatusEnum.FREEZE.getCode(), AccountStatusEnum.AVAIL.getCode(),
                AccountStatusEnum.CHECK.getCode(), AccountStatusEnum.CASH.getCode())) {
            List<SupTradeAccSupDeptTransRowVo> statusTransVos = deptTransVos.stream()
                    .filter(vo -> status.equals(vo.getStatus())).toList();
            if (CollectionUtil.isNotEmpty(statusTransVos)) {
                List<SupTradeAccSupDeptTransRowVo> payStatusTransVos = statusTransVos.stream().filter(vo -> "pay_amt".equals(vo.getBusiField())).toList();
                List<RemoteSupAccTransBo> payAccTransVos = getRemoteTransDetail(payStatusTransVos, "getRelateId");
                if (CollectionUtil.isNotEmpty(payAccTransVos)) {
                    List<RemoteSupTransProductOrderRecordVo> payList = remoteSupAccTransService.queryProductOrderRecord(new RemoteSupAccTransQueryBo()
                            .setTrans(payAccTransVos).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
                    if (channelsProperties.isDebugLog()) {
                        log.keyword("transDetail", status).info("payParam=" + JsonUtils.toJsonString(payAccTransVos));
                        log.keyword("transDetail", status).info("payReturn=" + JsonUtils.toJsonString(payList));
                    }
                    for (RemoteSupTransProductOrderRecordVo orderVo: payList) {
                        SupTradeAccSupDeptAcctDetailStatusVo statusVo = transStatusAdd(status, orderVo.getSaleDate(), list, detailMap, statusMap);
                        SupTradeAccSupDeptAcctDetailListVo listVo = transDetailAdd(status, orderVo.getSaleDate(), orderVo.getSupplierSkuId(),
                                orderVo.getPrice(), skuMap, deptMap, statusVo, goodsMap, detailListMap);
                        listVo.setTotalAmt(listVo.getTotalAmt().add(orderVo.getProductAmount()));
                        if (listVo.getOrderAmt() == null) {
                            listVo.setOrderAmt(orderVo.getProductAmount()).setFreeAmt(orderVo.getProductFreeAmount()).setOrderPrice(orderVo.getPrice()).setOrderQty(orderVo.getCount());
                        } else {
                            listVo.setOrderAmt(listVo.getOrderAmt().add(orderVo.getProductAmount()))
                                    .setFreeAmt(listVo.getFreeAmt().add(orderVo.getProductFreeAmount()))
                                    .setOrderQty(listVo.getOrderQty() + orderVo.getCount());
                        }
                    }
                }
                List<SupTradeAccSupDeptTransRowVo> refundStatusTransVos = statusTransVos.stream().filter(vo -> "refund_amt1".equals(vo.getBusiField())).toList();
                List<RemoteSupAccTransBo> refundAccTransVos = getRemoteTransDetail(refundStatusTransVos, "getTransId");
                if (CollectionUtil.isNotEmpty(refundAccTransVos)) {
                    List<RemoteSupTransRefundRecordVo> refundList = remoteSupAccTransService.queryProductRefundRecord(new RemoteSupAccTransQueryBo()
                            .setTrans(refundAccTransVos).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
                    if (channelsProperties.isDebugLog()) {
                        log.keyword("transDetail", status).info("refundParam=" + JsonUtils.toJsonString(refundAccTransVos));
                        log.keyword("transDetail", status).info("refundReturn=" + JsonUtils.toJsonString(refundList));
                    }
                    for (RemoteSupTransRefundRecordVo refundVo: refundList) {
                        SupTradeAccSupDeptAcctDetailStatusVo statusVo = transStatusAdd(status, refundVo.getSaleDate(), list, detailMap, statusMap);
                        SupTradeAccSupDeptAcctDetailListVo listVo = transDetailAdd(status, refundVo.getSaleDate(), refundVo.getSupplierSkuId(),
                                refundVo.getOrderPrice(), skuMap, deptMap, statusVo, goodsMap, detailListMap);
                        BigDecimal amount = getRemoteTransDeptAmt(recordMap, refundVo.getRefundRecordId(), refundVo.getSupplierSkuId(), status);
                        if (amount.compareTo(BigDecimal.ZERO) != 0) {
                            // refundVo.getProductAmount()
                            listVo.setTotalAmt(listVo.getTotalAmt().subtract(amount));
                            if (listVo.getRefundAmt() == null) {
                                listVo.setRefundAmt(amount).setRefundPrice(refundVo.getPrice()).setRefundQty(refundVo.getCount());
                            } else {
                                listVo.setRefundAmt(listVo.getRefundAmt().add(amount)).setRefundQty(listVo.getRefundQty() + refundVo.getCount());
                            }
                        }
                    }
                }
                List<SupTradeAccSupDeptTransRowVo> refundDiffStatusTransVos = statusTransVos.stream().filter(vo -> "refund_amt2".equals(vo.getBusiField())).toList();
                List<RemoteSupAccTransBo> diffRefundAccTransVos = getRemoteTransDetail(refundDiffStatusTransVos, "getTransId");
                if (CollectionUtil.isNotEmpty(diffRefundAccTransVos)) {
                    List<RemoteSupTransRefundRecordVo> diffRefundList = remoteSupAccTransService.queryProductRefundDiffRecord(new RemoteSupAccTransQueryBo()
                            .setTrans(diffRefundAccTransVos).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
                    if (channelsProperties.isDebugLog()) {
                        log.keyword("transDetail", status).info("diffRefundParam=" + JsonUtils.toJsonString(diffRefundAccTransVos));
                        log.keyword("transDetail", status).info("diffRefundReturn=" + JsonUtils.toJsonString(diffRefundList));
                    }
                    for (RemoteSupTransRefundRecordVo refundDiffVo: diffRefundList) {
                        SupTradeAccSupDeptAcctDetailStatusVo statusVo = transStatusAdd(status, refundDiffVo.getSaleDate(), list, detailMap, statusMap);
                        SupTradeAccSupDeptAcctDetailListVo listVo = transDetailAdd(status, refundDiffVo.getSaleDate(), refundDiffVo.getSupplierSkuId(),
                                refundDiffVo.getOrderPrice(), skuMap, deptMap, statusVo, goodsMap, detailListMap);
                        BigDecimal amount = getRemoteTransDeptAmt(recordMap,refundDiffVo.getRefundRecordId(), refundDiffVo.getSupplierSkuId(), status);
                        if (amount.compareTo(BigDecimal.ZERO) != 0) {
                            // refundDiffVo.getProductAmount()
                            listVo.setTotalAmt(listVo.getTotalAmt().subtract(amount));
                            if (listVo.getDiffRefundAmt() == null) {
                                listVo.setDiffRefundAmt(amount).setDiffRefundPrice(refundDiffVo.getCurrentPrice()).setDiffRefundQty(refundDiffVo.getCount());
                            } else {
                                listVo.setDiffRefundAmt(listVo.getDiffRefundAmt().add(amount)).setDiffRefundQty(listVo.getDiffRefundQty() + refundDiffVo.getCount());
                            }
                        }
                    }
                }
                List<SupTradeAccSupDeptTransRowVo> refundLossStatusTransVos = statusTransVos.stream().filter(vo -> "refund_amt3".equals(vo.getBusiField())).toList();
                List<RemoteSupAccTransBo> lossRefundAccTransVos = getRemoteTransDetail(refundLossStatusTransVos, "getTransId");
                if (CollectionUtil.isNotEmpty(lossRefundAccTransVos)) {
                    List<RemoteSupTransRefundLossRecordVo> lossRefundList = remoteSupAccTransService.queryProductRefundLossRecord(new RemoteSupAccTransQueryBo()
                            .setTrans(lossRefundAccTransVos).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
                    if (channelsProperties.isDebugLog()) {
                        log.keyword("transDetail", status).info("lossRefundParam=" + JsonUtils.toJsonString(lossRefundAccTransVos));
                        log.keyword("transDetail", status).info("lossRefundReturn=" + JsonUtils.toJsonString(lossRefundList));
                    }
                    Set<String> lossRefundSet = lossRefundList.stream().map(RemoteSupTransRefundLossRecordVo::getReportLossNo).collect(Collectors.toSet());
                    for (RemoteSupTransRefundLossRecordVo refundLossVo: lossRefundList) {
                        SupTradeAccSupDeptAcctDetailStatusVo statusVo = transStatusAdd(status, refundLossVo.getSaleDate(), list, detailMap, statusMap);
                        SupTradeAccSupDeptAcctDetailListVo listVo = transDetailAdd(status, refundLossVo.getSaleDate(), refundLossVo.getSupplierSkuId(),
                                refundLossVo.getSkuPrice(), skuMap, deptMap, statusVo, goodsMap, detailListMap);
                        BigDecimal amount = getRemoteTransDeptAmt(recordMap, refundLossVo.getRefundRecordId(), refundLossVo.getSupplierSkuId(), status);
                        if (amount.compareTo(BigDecimal.ZERO) != 0) {
                            listVo.setTotalAmt(listVo.getTotalAmt().subtract(amount));
                            if (listVo.getLossRefundAmt() == null) {
                                listVo.setLossRefundAmt(amount);
                            } else {
                                listVo.setLossRefundAmt(listVo.getLossRefundAmt().add(amount));
                            }
                            if (lossRefundSet.remove(refundLossVo.getReportLossNo())) {
                                if (listVo.getLossApplyAmt() == null) {
                                    listVo.setLossApplyAmt(refundLossVo.getLossAmount()).setLossPeopleNum(1);
                                } else {
                                    listVo.setLossApplyAmt(listVo.getLossApplyAmt().add(refundLossVo.getLossAmount()))
                                            .setLossPeopleNum(listVo.getLossPeopleNum() + 1);
                                }
                            }
                        }
                    }
                }
                for (Map.Entry<String, List<SupTradeAccSupDeptTransRowVo>> entry: statusTransVos.stream().collect(
                        Collectors.groupingBy(vo -> vo.getTransDate().toString(), Collectors.toList())).entrySet()) {
                    SupTradeAccSupDeptAcctDetailStatusVo statusVo = transStatusAdd(status, entry.getValue().get(0).getTransDate(), list, detailMap, statusMap);
                    statusVo.setInAmt(entry.getValue().stream().filter(vo -> "in_amt".equals(vo.getBusiField()))
                            .map(SupTradeAccSupDeptTransRowVo::getTransAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                    statusVo.setOutAmt(entry.getValue().stream().filter(vo -> "out_amt".equals(vo.getBusiField()))
                            .map(SupTradeAccSupDeptTransRowVo::getTransAmt).reduce(BigDecimal.ZERO, BigDecimal::add).negate());
                    statusVo.setTotalAmt(entry.getValue().stream().map(SupTradeAccSupDeptTransRowVo::getTransAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
            }
        }
        goodsMap.values().forEach(vo -> vo.setTotalAmt(vo.getList().stream().map(SupTradeAccSupDeptAcctDetailListVo::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add)));
        return list.stream().sorted(Comparator.comparing(SupTradeAccSupDeptAcctDetailVo::getTransDate).reversed()).toList();
    }

    private SupTradeAccSupDeptAcctDetailStatusVo transStatusAdd(Integer status, LocalDate transDate, List<SupTradeAccSupDeptAcctDetailVo> detailList,
                                                                Map<String, SupTradeAccSupDeptAcctDetailVo> detailMap,
                                                                Map<String, SupTradeAccSupDeptAcctDetailStatusVo> statusMap) {
        String transDataStr = transDate.toString();
        SupTradeAccSupDeptAcctDetailVo accSupDeptAcctDetailVo = detailMap.get(transDataStr);
        if (accSupDeptAcctDetailVo == null) {
            accSupDeptAcctDetailVo = new SupTradeAccSupDeptAcctDetailVo()
                    .setTransDate(transDate).setStatusList(new ArrayList<>());
            detailMap.put(transDataStr, accSupDeptAcctDetailVo);
            detailList.add(accSupDeptAcctDetailVo);
        }
        String statusKey = String.format("%s_%s", transDataStr, status);
        SupTradeAccSupDeptAcctDetailStatusVo accSupDeptAcctDetailStatusVo = statusMap.get(statusKey);
        if (accSupDeptAcctDetailStatusVo == null) {
            accSupDeptAcctDetailStatusVo = new SupTradeAccSupDeptAcctDetailStatusVo()
                    .setStatus(status).setTotalAmt(BigDecimal.ZERO).setInAmt(BigDecimal.ZERO).setOutAmt(BigDecimal.ZERO).setGoodsList(new ArrayList<>());
            statusMap.put(statusKey, accSupDeptAcctDetailStatusVo);
            accSupDeptAcctDetailVo.getStatusList().add(accSupDeptAcctDetailStatusVo);
        }
        return accSupDeptAcctDetailStatusVo;
    }

    private SupTradeAccSupDeptAcctDetailListVo transDetailAdd(Integer status, LocalDate transDate, Long skuId, BigDecimal price,
                                                              Map<Long, RemoteSupplierSkuFundsInfoVo> skuMap, Map<Long, RemoteBaseDataVo> deptMap,
                                                              SupTradeAccSupDeptAcctDetailStatusVo accSupDeptAcctDetailStatusVo,
                                                              Map<String, SupTradeAccSupDeptAcctDetailGoodsVo> goodsMap,
                                                              Map<String, SupTradeAccSupDeptAcctDetailListVo> detailListMap) {
        String transDataStr = transDate.toString();
        String goodsKey = String.format("%s_%s_%s", transDataStr, status, skuId);
        SupTradeAccSupDeptAcctDetailGoodsVo accSupDeptAcctDetailGoodsVo = goodsMap.get(goodsKey);
        if (accSupDeptAcctDetailGoodsVo == null) {
            RemoteSupplierSkuFundsInfoVo skuVo = skuMap.get(skuId);
            if (skuVo == null) {
                accSupDeptAcctDetailGoodsVo = new SupTradeAccSupDeptAcctDetailGoodsVo().setTotalAmt(BigDecimal.ZERO)
                        .setSkuId(skuId).setList(new ArrayList<>());
            } else {
                RemoteBaseDataVo deptVo = deptMap.get(skuVo.getSupplierDeptId());
                accSupDeptAcctDetailGoodsVo = new SupTradeAccSupDeptAcctDetailGoodsVo().setTotalAmt(BigDecimal.ZERO)
                        .setProducer(skuVo.getProducer()).setSpuStandards(skuVo.getSpuStandards())
                        .setShortProducer(skuVo.getShortProducer()).setAreaCode(skuVo.getAreaCode()).setBrand(skuVo.getBrand())
                        .setSkuName(skuVo.getSkuName()).setDeptId(skuVo.getSupplierDeptId())
                        .setDeptName(deptVo == null ? null : deptVo.getName()).setSkuId(skuId).setList(new ArrayList<>());
            }
            goodsMap.put(goodsKey, accSupDeptAcctDetailGoodsVo);
            accSupDeptAcctDetailStatusVo.getGoodsList().add(accSupDeptAcctDetailGoodsVo);
        }
        String listKey = String.format("%s_%s_%s_%s", transDate, status, skuId, price.toString());
        SupTradeAccSupDeptAcctDetailListVo accSupDeptAcctDetailListVo = detailListMap.get(listKey);
        if (accSupDeptAcctDetailListVo == null) {
            accSupDeptAcctDetailListVo = new SupTradeAccSupDeptAcctDetailListVo().setTotalAmt(BigDecimal.ZERO);
            accSupDeptAcctDetailGoodsVo.getList().add(accSupDeptAcctDetailListVo);
            detailListMap.put(listKey, accSupDeptAcctDetailListVo);
        }
        return accSupDeptAcctDetailListVo;
    }

    private List<RemoteSupAccTransBo> getRemoteTransDetail(List<SupTradeAccSupDeptTransRowVo> deptTransVos, String methodName)
            throws InvocationTargetException, IllegalAccessException {
        Method method = ReflectUtil.getMethod(SupTradeAccSupDeptTransRowVo.class, methodName);
        Map<String, RemoteSupAccTransBo> map = new HashMap<>();
        for (SupTradeAccSupDeptTransRowVo deptTransVo: deptTransVos) {
            Long transId = (Long) method.invoke(deptTransVo);
            map.put(String.format("%s_%s_%s", transId, deptTransVo.getSkuId(), deptTransVo.getTransDate()),
                    new RemoteSupAccTransBo().setTransDate(deptTransVo.getTransDate()).setTransAmt(BigDecimal.ZERO)
                    .setSkuId(deptTransVo.getSkuId()).setTransId(transId));
        }
        return map.values().stream().toList();
    }

    private BigDecimal getRemoteTransDeptAmt(Map<String, List<SupTradeAccSupDeptTransRowVo>> recordMap, Long transId, Long skuId, Integer status) {
        List<SupTradeAccSupDeptTransRowVo> rowVos = recordMap.get(String.format("%s_%s_%s", transId, skuId, status));
        // boolean isOccupy = rowVos.stream().anyMatch(vo -> vo.getTransType().equals("OS"));
        return rowVos == null ? BigDecimal.ZERO :  rowVos.stream().map(SupTradeAccSupDeptTransRowVo::getTransAmt).reduce(BigDecimal.ZERO, BigDecimal::add).negate();
    }

    /**
     * 资金账户 : 变动记录流水
     */
    @PostMapping("/transRecord")
    public R<List<SupTradeAccSupDeptAcctRecordVo>> transRecord(@Validated @RequestBody SupTradeAccSupDeptAcctRecordQueryBo bo)
            throws InvocationTargetException, IllegalAccessException {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId()).setSupplierCode(baseVo.getCode()).setSupplierId(baseVo.getId());
        List<SupTradeAccSupDeptAcctRecordVo> list = new ArrayList<>();
        Map<Long, SupTradeAccSupDeptAcctRecordVo> recordVoMap = new HashMap<>();
        List<SupTradeAccSupDeptTransRowVo> statusTransVos = tradeAccTransService.customSupDeptTransList(bo);
        Map<String, List<SupTradeAccSupDeptTransRowVo>> recordMap = statusTransVos.stream().collect(
                Collectors.groupingBy(vo -> String.format("%s_%s_%s", vo.getTransId(), vo.getSkuId(), vo.getStatus()), Collectors.toList()));
        if (CollectionUtil.isNotEmpty(statusTransVos)) {
            Map<String, Date> orderNos = tradePayService.queryByNos(statusTransVos.stream().map(SupTradeAccSupDeptTransRowVo::getRelateNo).toList())
                    .stream().collect(Collectors.toMap(TradePayVo::getOrderNo, TradePayVo::getInfTime));
            List<SupTradeAccSupDeptTransRowVo> payStatusTransVos = statusTransVos.stream().filter(vo -> "pay_amt".equals(vo.getBusiField())).toList();
            List<RemoteSupAccTransBo> payAccTransVos = getRemoteTransDetail(payStatusTransVos, "getRelateId");
            if (CollectionUtil.isNotEmpty(payAccTransVos)) {
                List<RemoteSupTransProductOrderRecordVo> payList = remoteSupAccTransService.queryProductOrderRecord(new RemoteSupAccTransQueryBo()
                        .setTrans(payAccTransVos).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
                if (channelsProperties.isDebugLog()) {
                    log.keyword("transRecord", bo.getStatus()).info("payParam=" + JsonUtils.toJsonString(payAccTransVos));
                    log.keyword("transRecord", bo.getStatus()).info("payReturn=" + JsonUtils.toJsonString(payList));
                }
                for (RemoteSupTransProductOrderRecordVo orderRecordVo: payList) {
                    SupTradeAccSupDeptAcctRecordVo accSupDeptAcctRecordVo = transRecordAdd(orderRecordVo.getOrderId(), orderRecordVo.getOrderCode(), orderNos, list, recordVoMap);
                    accSupDeptAcctRecordVo.setOrderAmt(orderRecordVo.getProductAmount()).setFreeAmt(orderRecordVo.getProductFreeAmount())
                            .setOrderQty(orderRecordVo.getCount()).setOrderPrice(orderRecordVo.getPrice());
                }
            }
            List<SupTradeAccSupDeptTransRowVo> refundStatusTransVos = statusTransVos.stream().filter(vo -> "refund_amt1".equals(vo.getBusiField())).toList();
            List<RemoteSupAccTransBo> refundAccTransVos = getRemoteTransDetail(refundStatusTransVos, "getTransId");
            if (CollectionUtil.isNotEmpty(refundAccTransVos)) {
                List<RemoteSupTransRefundRecordVo> refundList = remoteSupAccTransService.queryProductRefundRecord(new RemoteSupAccTransQueryBo()
                        .setTrans(refundAccTransVos).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
                if (channelsProperties.isDebugLog()) {
                    log.keyword("transRecord", bo.getStatus()).info("refundParam=" + JsonUtils.toJsonString(refundAccTransVos));
                    log.keyword("transRecord", bo.getStatus()).info("refundReturn=" + JsonUtils.toJsonString(refundList));
                }
                for (RemoteSupTransRefundRecordVo refundRecordVo: refundList) {
                    SupTradeAccSupDeptAcctRecordVo accSupDeptAcctRecordVo = transRecordAdd(refundRecordVo.getOrderId(), refundRecordVo.getOrderCode(), orderNos, list, recordVoMap);
                    BigDecimal amount = getRemoteTransDeptAmt(recordMap, refundRecordVo.getRefundRecordId(), refundRecordVo.getSupplierSkuId(), bo.getStatus());
                    if (amount.compareTo(BigDecimal.ZERO) != 0) {
                        if (accSupDeptAcctRecordVo.getRefundAmt() == null) {
                            accSupDeptAcctRecordVo.setRefundAmt(amount).setRefundQty(refundRecordVo.getCount()).setRefundPrice(refundRecordVo.getPrice());
                        } else {
                            accSupDeptAcctRecordVo.setRefundAmt(accSupDeptAcctRecordVo.getRefundAmt().add(amount))
                                    .setRefundQty(accSupDeptAcctRecordVo.getRefundQty() + refundRecordVo.getCount());
                        }
                    }
                }
            }
            List<SupTradeAccSupDeptTransRowVo> refundDiffStatusTransVos = statusTransVos.stream().filter(vo -> "refund_amt2".equals(vo.getBusiField())).toList();
            List<RemoteSupAccTransBo> diffRefundAccTransVos = getRemoteTransDetail(refundDiffStatusTransVos, "getTransId");
            if (CollectionUtil.isNotEmpty(diffRefundAccTransVos)) {
                List<RemoteSupTransRefundRecordVo> diffRefundList = remoteSupAccTransService.queryProductRefundDiffRecord(new RemoteSupAccTransQueryBo()
                        .setTrans(diffRefundAccTransVos).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
                if (channelsProperties.isDebugLog()) {
                    log.keyword("transRecord", bo.getStatus()).info("diffRefundParam=" + JsonUtils.toJsonString(diffRefundAccTransVos));
                    log.keyword("transRecord", bo.getStatus()).info("diffRefundReturn=" + JsonUtils.toJsonString(diffRefundList));
                }
                for (RemoteSupTransRefundRecordVo refundDiffVo: diffRefundList) {
                    SupTradeAccSupDeptAcctRecordVo accSupDeptAcctRecordVo = transRecordAdd(refundDiffVo.getOrderId(), refundDiffVo.getOrderCode(), orderNos, list, recordVoMap);
                    BigDecimal amount = getRemoteTransDeptAmt(recordMap, refundDiffVo.getRefundRecordId(), refundDiffVo.getSupplierSkuId(), bo.getStatus());
                    if (amount.compareTo(BigDecimal.ZERO) != 0) {
                        if (accSupDeptAcctRecordVo.getDiffRefundAmt() == null) {
                            accSupDeptAcctRecordVo.setDiffRefundAmt(amount).setDiffRefundQty(refundDiffVo.getCount()).setDiffRefundPrice(refundDiffVo.getCurrentPrice());
                        } else {
                            accSupDeptAcctRecordVo.setDiffRefundAmt(accSupDeptAcctRecordVo.getDiffRefundAmt().add(amount))
                                    .setDiffRefundQty(accSupDeptAcctRecordVo.getDiffRefundQty() + refundDiffVo.getCount());
                        }
                    }
                }
            }
            List<SupTradeAccSupDeptTransRowVo> refundLossStatusTransVos = statusTransVos.stream().filter(vo -> "refund_amt3".equals(vo.getBusiField())).toList();
            List<RemoteSupAccTransBo> lossRefundAccTransVos = getRemoteTransDetail(refundLossStatusTransVos, "getTransId");
            if (CollectionUtil.isNotEmpty(lossRefundAccTransVos)) {
                List<RemoteSupTransRefundLossRecordVo> lossRefundList = remoteSupAccTransService.queryProductRefundLossRecord(new RemoteSupAccTransQueryBo()
                        .setTrans(lossRefundAccTransVos).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
                if (channelsProperties.isDebugLog()) {
                    log.keyword("transRecord", bo.getStatus()).info("lossRefundParam=" + JsonUtils.toJsonString(lossRefundAccTransVos));
                    log.keyword("transRecord", bo.getStatus()).info("lossRefundReturn=" + JsonUtils.toJsonString(lossRefundList));
                }
                Set<String> lossRefundSet = lossRefundList.stream().map(RemoteSupTransRefundLossRecordVo::getReportLossNo).collect(Collectors.toSet());
                for (RemoteSupTransRefundLossRecordVo refundLossVo: lossRefundList) {
                    SupTradeAccSupDeptAcctRecordVo accSupDeptAcctRecordVo = transRecordAdd(refundLossVo.getOrderId(), refundLossVo.getOrderCode(), orderNos, list, recordVoMap);
                    BigDecimal amount = getRemoteTransDeptAmt(recordMap, refundLossVo.getRefundRecordId(), refundLossVo.getSupplierSkuId(), bo.getStatus());
                    if (amount.compareTo(BigDecimal.ZERO) != 0) {
                        if (accSupDeptAcctRecordVo.getLossRefundAmt() == null) {
                            accSupDeptAcctRecordVo.setLossRefundAmt(amount);
                        } else {
                            accSupDeptAcctRecordVo.setLossRefundAmt(accSupDeptAcctRecordVo.getLossRefundAmt().add(amount));
                        }
                        if (lossRefundSet.remove(refundLossVo.getReportLossNo())) {
                            if (accSupDeptAcctRecordVo.getLossApplyAmt() == null) {
                                accSupDeptAcctRecordVo.setLossApplyAmt(refundLossVo.getLossAmount()).setLossPeopleNum(1);
                            } else {
                                accSupDeptAcctRecordVo.setLossApplyAmt(accSupDeptAcctRecordVo.getLossApplyAmt().add(refundLossVo.getLossAmount()))
                                        .setLossPeopleNum(accSupDeptAcctRecordVo.getLossPeopleNum() + 1);
                            }
                        }
                    }

                }
            }
        }
        return R.ok(list);
    }

    private SupTradeAccSupDeptAcctRecordVo transRecordAdd(Long orderId, String orderNo, Map<String, Date> orderNos,
                                List<SupTradeAccSupDeptAcctRecordVo> list, Map<Long, SupTradeAccSupDeptAcctRecordVo> recordVoMap) {
        SupTradeAccSupDeptAcctRecordVo accSupDeptAcctRecordVo = recordVoMap.get(orderId);
        if (accSupDeptAcctRecordVo == null) {
            accSupDeptAcctRecordVo = new SupTradeAccSupDeptAcctRecordVo().setOrderNo(orderNo).setInfTime(orderNos.get(orderNo));
            recordVoMap.put(orderId, accSupDeptAcctRecordVo);
            list.add(accSupDeptAcctRecordVo);
        }
        return accSupDeptAcctRecordVo;
    }

    /**
     * 资金账户 : 划转明细（加款 和 扣款 transType 必输）
     */
    @PostMapping("/transfer")
    public R<TableDataInfo<RemoteSupTransDeductionVo>> transfer(@Validated @RequestBody SupTradeAccSupDeptAcctTransferQueryBo bo) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId()).setSupplierCode(baseVo.getCode()).setSupplierId(baseVo.getId());
        SupTradeAccSupDeptAcctDetailQueryBo queryBo = new SupTradeAccSupDeptAcctDetailQueryBo();
        BeanUtil.copyProperties(bo, queryBo, Constants.BeanCopyIgnoreNullValue);
        TableDataInfo<TradeAccTransVo> tableDataInfo = tradeAccTransService.customSupDeptTransTransferList(queryBo);
        if (CollectionUtil.isNotEmpty(tableDataInfo.getRows())) {
            List<RemoteSupAccTransBo> trans = MapstructUtils.convert(tableDataInfo.getRows(), RemoteSupAccTransBo.class);
            List<RemoteSupTransDeductionVo> list = remoteSupAccTransService.queryDeduction(new RemoteSupAccTransQueryBo()
                    .setTrans(trans).setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode()));
            Map<Long, RemoteSupAccTransBo> transMap = trans.stream().collect(Collectors.toMap(RemoteSupAccTransBo::getTransId, Function.identity()));
            list.forEach(vo -> {
                RemoteSupAccTransBo transBo = transMap.get(vo.getId());
                if (transBo != null) {
                    vo.setStatus(transBo.getStatus());
                }
            });
            return R.ok(TableDataInfo.build(list, tableDataInfo.getTotal()));
        }
        return R.ok(TableDataInfo.build());
    }

    /**
     * 资金账户 : 供应商日账单导出
     */
    @PostMapping("/exportTrans")
    @RepeatSubmit(interval=180000, message="3分钟内不能重复导出")
    public R<String> exportTrans(@Validated @RequestBody SupTradeAccSupAvailTransExportBo bo) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId()).setSupplierCode(baseVo.getCode()).setSupplierId(baseVo.getId());
        List<SupTradeAccSupDeptAcctRefundExcelVo> dataVos = tradeAccAvailTransService.customSupAvailTransExportList(bo);
        if (CollUtil.isEmpty(dataVos)) {
            return R.fail("无数据");
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ExcelUtil.exportTemplateMultiSheet(exportMultiSheetData(bo, dataVos), Constants.SupplierAccountTemplate, baos);
        RemoteFile remoteFile = remoteFileService.uploadTempFile("供应商日账单", "供应商日账单.xlsx", ContentType.OCTET_STREAM.getValue(), baos.toByteArray(), 1);
        return R.ok("操作成功", remoteFile.getUrl());
    }

    /**
     *  @ignore
     */
    @PostMapping("/exportTransTest")
    public void exportTransTest(@Validated SupTradeAccSupAvailTransExportBo bo, HttpServletResponse response) {
        TradeSupBaseDataVo baseVo = tradeAccUtils.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId()).setSupplierCode(baseVo.getCode()).setSupplierId(baseVo.getId());
        List<SupTradeAccSupDeptAcctRefundExcelVo> dataVos = tradeAccAvailTransService.customSupAvailTransExportList(bo);
        if (CollUtil.isEmpty(dataVos)) {
            return;
        }
        ExcelUtil.exportTemplateMultiSheet(exportMultiSheetData(bo, dataVos), "供应商日账单.xlsx", Constants.SupplierAccountTemplate, response);
    }

    private String getSkuAllName(String skuName, String shortProducer, String brand, String spuStandards) {
        // brand + "-" + shortProducer+ "-"+ skuName + 【 + spustandards + 】
        StringBuffer sb = new StringBuffer();
        if (StringUtils.isNotBlank(brand)) {
            sb.append(brand).append("-");
        }
        if (StringUtils.isNotBlank(shortProducer)) {
            sb.append(shortProducer).append("-");
        }
        if (StringUtils.isNotBlank(skuName)) {
            sb.append(skuName);
        }
        if (StringUtils.isNotBlank(spuStandards)) {
            sb.append("【").append(spuStandards).append("】");
        }
        return sb.toString();
    }

    private List<Map<String, Object>> exportMultiSheetData(SupTradeAccSupAvailTransExportBo bo,List<SupTradeAccSupDeptAcctRefundExcelVo> dataVos) {
        // 1 销售，退款，加扣款 拆分
        List<SupTradeAccSupDeptAcctPayExcelVo> payVos = new ArrayList<>();
        List<SupTradeAccSupDeptAcctPayExcelVo> payOverdueVos = new ArrayList<>();
        List<SupTradeAccSupDeptAcctRefundExcelVo> refundVos = new ArrayList<>();
        List<SupTradeAccSupDeptAcctRefundExcelVo> refundOverdueVos = new ArrayList<>();
        List<SupTradeAccSupDeptAcctTransferExcelVo> transferVos = new ArrayList<>();
        List<SupTradeAccSupDeptAcctCommissionExcelVo> commissionVos = new ArrayList<>();
        for (SupTradeAccSupDeptAcctRefundExcelVo excelVo : dataVos) {
            excelVo.setSkuName(getSkuAllName(excelVo.getSkuName(), excelVo.getShortProducer(), excelVo.getBrand(), excelVo.getSpuStandards()));
            switch (Objects.requireNonNull(AccountTransTypeEnum.getEnumByCode(excelVo.getTransType()))) {
                case OP -> {
                    if (excelVo.getStatus() == 2) {
                        payOverdueVos.add(MapstructUtils.convert(excelVo, SupTradeAccSupDeptAcctPayExcelVo.class));
                    } else {
                        payVos.add(MapstructUtils.convert(excelVo, SupTradeAccSupDeptAcctPayExcelVo.class));
                    }
                }
                case OR, OS -> {
                    if (excelVo.getStatus() == 2) {
                        refundOverdueVos.add(excelVo);
                    } else {
                        refundVos.add(excelVo);
                    }
                }
                case TI, TO -> {
                    transferVos.add(MapstructUtils.convert(excelVo, SupTradeAccSupDeptAcctTransferExcelVo.class));
                }
            }
        }
        // 2 处理退款 status = 1 状态中，  OR 和 OS 合并
        Map<String, List<SupTradeAccSupDeptAcctRefundExcelVo>> refundMap = refundVos.stream().filter(vo -> vo.getStatus() == 1)
                .collect(Collectors.groupingBy(vo -> String.format("%s_%s", vo.getTransNo(), vo.getSkuId()), Collectors.toList()))
                .entrySet().stream().filter(entry -> entry.getValue().size() > 1)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        for (Map.Entry<String, List<SupTradeAccSupDeptAcctRefundExcelVo>> entry: refundMap.entrySet()) {
            SupTradeAccSupDeptAcctRefundExcelVo refundVo = entry.getValue().stream().filter(vo -> AccountTransTypeEnum.OR.getCode().equals(vo.getTransType())).findFirst().orElse(null);
            SupTradeAccSupDeptAcctRefundExcelVo refundOsVo = entry.getValue().stream().filter(vo -> AccountTransTypeEnum.OS.getCode().equals(vo.getTransType())).findFirst().orElse(null);
            if (refundVo != null && refundOsVo != null) {
                refundVo.setTransAmt(refundVo.getTransAmt().add(refundOsVo.getTransAmt()));
                refundVos.remove(refundOsVo);
            }
        }
        // 3 支付辅助数据
        setSupTradeAccSupDeptAcctPayExcel(bo, payVos);
        setSupTradeAccSupDeptAcctPayExcel(bo, payOverdueVos);
        // 4 退款辅助数据
        setSupTradeAccSupDeptAcctRefundExcel(bo, refundVos);
        setSupTradeAccSupDeptAcctRefundExcel(bo, refundOverdueVos);
        // 5 加扣款辅助数据
        setSupTradeAccSupDeptAcctTransferExcel(bo, transferVos);
        // 6 订单和退单的数据都获取完成后， 在处理佣金数据
        for (SupTradeAccSupDeptAcctPayExcelVo excelVo : payVos) {
            if (excelVo.getCommissionAmt().compareTo(BigDecimal.ZERO) != 0) {
                SupTradeAccSupDeptAcctCommissionExcelVo commissionVo = MapstructUtils.convert(excelVo, SupTradeAccSupDeptAcctCommissionExcelVo.class);
                commissionVos.add(commissionVo.setRelateNo(excelVo.getTransNo()).setTransNo("").setTransAmt(excelVo.getCommissionAmt()));
            }
        }
        for (SupTradeAccSupDeptAcctRefundExcelVo excelVo : refundVos) {
            if (excelVo.getCommissionAmt().compareTo(BigDecimal.ZERO) != 0) {
                SupTradeAccSupDeptAcctCommissionExcelVo commissionVo = MapstructUtils.convert(excelVo, SupTradeAccSupDeptAcctCommissionExcelVo.class);
                commissionVos.add(commissionVo.setTransAmt(excelVo.getCommissionAmt()));
            }
        }
        // 7 合计
        SupTradeAccSupDeptAcctTotalExcelVo payExcelVo = new SupTradeAccSupDeptAcctTotalExcelVo();
        payExcelVo.setTransAmt(payVos.stream().filter(vo -> vo.getStatus() == 1).map(SupTradeAccSupDeptAcctPayExcelVo::getTransAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        SupTradeAccSupDeptAcctTotalExcelVo payOverdueExcelVo = new SupTradeAccSupDeptAcctTotalExcelVo();
        payOverdueExcelVo.setTransAmt(payOverdueVos.stream().map(SupTradeAccSupDeptAcctPayExcelVo::getTransAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        SupTradeAccSupDeptAcctTotalExcelVo refundExcelVo = new SupTradeAccSupDeptAcctTotalExcelVo();
        refundExcelVo.setTransAmt(refundVos.stream().filter(vo -> vo.getStatus() == 1).map(SupTradeAccSupDeptAcctRefundExcelVo::getTransAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        SupTradeAccSupDeptAcctTotalExcelVo refundOverdueExcelVo = new SupTradeAccSupDeptAcctTotalExcelVo();
        refundOverdueExcelVo.setTransAmt(refundOverdueVos.stream().map(SupTradeAccSupDeptAcctRefundExcelVo::getTransAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        SupTradeAccSupDeptAcctTotalExcelVo transferExcelVo = new SupTradeAccSupDeptAcctTotalExcelVo();
        transferExcelVo.setTransAmt(transferVos.stream().map(SupTradeAccSupDeptAcctTransferExcelVo::getTransAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        SupTradeAccSupDeptAcctTotalExcelVo commissionExcelVo = new SupTradeAccSupDeptAcctTotalExcelVo();
        commissionExcelVo.setTransAmt(commissionVos.stream().filter(vo -> vo.getStatus() == 1).map(SupTradeAccSupDeptAcctCommissionExcelVo::getTransAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        List<SupTradeAccSupDeptAcctTotalExcelVo> totalVos = new ArrayList<>();
        totalVos.add(new SupTradeAccSupDeptAcctTotalExcelVo().setDesc("销售（已完结）").setTransAmt(payExcelVo.getTransAmt()));
        totalVos.add(new SupTradeAccSupDeptAcctTotalExcelVo().setDesc("退款（已完结）").setTransAmt(refundExcelVo.getTransAmt()));
        totalVos.add(new SupTradeAccSupDeptAcctTotalExcelVo().setDesc("佣金（已完结）").setTransAmt(commissionExcelVo.getTransAmt()));
        totalVos.add(new SupTradeAccSupDeptAcctTotalExcelVo().setDesc("加扣款").setTransAmt(transferExcelVo.getTransAmt()));
        totalVos.add(new SupTradeAccSupDeptAcctTotalExcelVo().setDesc("历史销售（未完结退还）").setTransAmt(payOverdueExcelVo.getTransAmt()));
        totalVos.add(new SupTradeAccSupDeptAcctTotalExcelVo().setDesc("历史售后（未完结退还）").setTransAmt(refundOverdueExcelVo.getTransAmt()));
        SupTradeAccSupDeptAcctTotalExcelVo totalExcelVo = new SupTradeAccSupDeptAcctTotalExcelVo();
        totalExcelVo.setTransAmt(totalVos.stream().map(SupTradeAccSupDeptAcctTotalExcelVo::getTransAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        // 8 创建多 sheet 数据
        List<Map<String, Object>> datas = new ArrayList<>();
        datas.add(Map.of("rows", payVos, "other", payExcelVo));
        datas.add(Map.of("rows", refundVos, "other", refundExcelVo));
        datas.add(Map.of("rows", commissionVos, "other", commissionExcelVo));
        datas.add(Map.of("rows", transferVos, "other", transferExcelVo));
        datas.add(Map.of("rows", payOverdueVos, "other", payOverdueExcelVo));
        datas.add(Map.of("rows", refundOverdueVos, "other", refundOverdueExcelVo));
        datas.add(Map.of("rows", totalVos, "other", totalExcelVo));
        return datas;
    }

    private void setSupTradeAccSupDeptAcctPayExcel(SupTradeAccSupAvailTransExportBo bo, List<SupTradeAccSupDeptAcctPayExcelVo> payVos) {
        for (Map.Entry<Long, List<SupTradeAccSupDeptAcctPayExcelVo>> entry: payVos.stream()
                .collect(Collectors.groupingBy(SupTradeAccSupDeptAcctPayExcelVo::getSkuId, Collectors.toList())).entrySet()) {
            RemoteSupAccTransQueryBo queryBo = new RemoteSupAccTransQueryBo().setTrans(entry.getValue().stream()
                            .map(SupTradeAccSupDeptAcctPayExcelVo::getTransId).collect(Collectors.toSet())
                            .stream().map(vo -> new RemoteSupAccTransBo().setSkuId(entry.getKey()).setTransId(vo)).toList())
                    .setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode());
            List<RemoteSupBillVo> billVos = remoteSupAccTransService.querySaleBillList(queryBo);
            Map<Long, List<RemoteSupBillVo>> listMap = billVos.stream().collect(Collectors.groupingBy(RemoteSupBillVo::getTransId, Collectors.toList()));
            for (SupTradeAccSupDeptAcctPayExcelVo payVo : entry.getValue()) {
                List<RemoteSupBillVo> billVo = listMap.get(payVo.getTransId());
                if (billVo == null) {
                    if (channelsProperties.isDebugLog()) {
                        log.keyword("setSupTradeAccSupDeptAcctExcel").warn(String.format("payVo = %s, billVo = null",  JsonUtils.toJsonString(payVo)));
                    }
                } else {
                    payVo.setFreeAmt(billVo.get(0).getProductFreeAmount());
                    payVo.setSkuCount(billVo.get(0).getCount());
                    payVo.setSkuPrice(billVo.get(0).getSkuPrice());
                    // payVo.setSkuWeight(billVo.get(0).getWeight());
                    payVo.setBuyerName(billVo.get(0).getBuyerName());
                    if (channelsProperties.isDebugLog()) {
                        if (payVo.getTransAmt().abs().compareTo(billVo.get(0).getAmount()) != 0) {
                            log.keyword("setSupTradeAccSupDeptAcctExcel").warn(String.format("payVo = %s billVo = %s", payVo.getTransAmt(), JsonUtils.toJsonString(billVo.get(0))));
                        }
                    }
                }
            }
        }
    }

    private void setSupTradeAccSupDeptAcctRefundExcel(SupTradeAccSupAvailTransExportBo bo, List<SupTradeAccSupDeptAcctRefundExcelVo> payVos) {
        for (Map.Entry<Long, List<SupTradeAccSupDeptAcctRefundExcelVo>> entry: payVos.stream()
                .collect(Collectors.groupingBy(SupTradeAccSupDeptAcctRefundExcelVo::getSkuId, Collectors.toList())).entrySet()) {
            RemoteSupAccTransQueryBo queryBo = new RemoteSupAccTransQueryBo().setTrans(entry.getValue().stream()
                            .map(SupTradeAccSupDeptAcctRefundExcelVo::getTransId).collect(Collectors.toSet())
                            .stream().map(vo -> new RemoteSupAccTransBo().setSkuId(entry.getKey()).setTransId(vo)).toList())
                    .setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode());
            List<RemoteSupBillVo> billVos = remoteSupAccTransService.queryRefundBillList(queryBo);
            Map<Long, List<RemoteSupBillVo>> listMap = billVos.stream().collect(Collectors.groupingBy(RemoteSupBillVo::getTransId, Collectors.toList()));
            for (SupTradeAccSupDeptAcctRefundExcelVo refundVo : entry.getValue()) {
                List<RemoteSupBillVo> billVo = listMap.get(refundVo.getTransId());
                if (billVo == null) {
                    if (channelsProperties.isDebugLog()) {
                        log.keyword("setSupTradeAccSupDeptAcctExcel").warn(String.format("refundVo = %s, billVo = null",  JsonUtils.toJsonString(refundVo)));
                    }
                } else {
                    refundVo.setSkuCount(billVo.get(0).getCount());
                    refundVo.setSkuPrice(billVo.get(0).getSkuPrice());
                    refundVo.setSourceNo(billVo.get(0).getBusinessNo());
                    refundVo.setBuyerName(billVo.get(0).getBuyerName());
                    refundVo.setPayTime(billVo.get(0).getPayTime());
                    // brand-shortProducer-goodsName【spuStandards】
                    if (channelsProperties.isDebugLog()) {
                        if (refundVo.getTransAmt().abs().compareTo(billVo.get(0).getAmount()) != 0) {
                            log.keyword("setSupTradeAccSupDeptAcctExcel").warn(String.format("refundVo = %s billVo = %s", refundVo.getTransAmt(), JsonUtils.toJsonString(billVo.get(0))));
                        }
                    }
                }
            }
        }

    }

    private void setSupTradeAccSupDeptAcctTransferExcel(SupTradeAccSupAvailTransExportBo bo, List<SupTradeAccSupDeptAcctTransferExcelVo> transferVos) {
        if (transferVos.size() == 0) {
            return;
        }
        RemoteSupAccTransQueryBo queryBo = new RemoteSupAccTransQueryBo().setTrans(transferVos.stream()
                        .map(SupTradeAccSupDeptAcctTransferExcelVo::getTransId).collect(Collectors.toSet())
                        .stream().map(vo -> new RemoteSupAccTransBo().setTransId(vo)).toList())
                .setSupplierId(bo.getSupplierId()).setSupplierCode(bo.getSupplierCode());
        List<RemoteSupTransDeductionVo> billVos = remoteSupAccTransService.queryDeduction(queryBo);
        Map<Long, List<RemoteSupTransDeductionVo>> listMap = billVos.stream().collect(Collectors.groupingBy(RemoteSupTransDeductionVo::getId, Collectors.toList()));
        for (SupTradeAccSupDeptAcctTransferExcelVo transferVo : transferVos) {
            List<RemoteSupTransDeductionVo> billVo = listMap.get(transferVo.getTransId());
            if (billVo == null) {
                if (channelsProperties.isDebugLog()) {
                    log.keyword("setSupTradeAccSupDeptAcctExcel").warn(String.format("transferVo = %s, billVo = null",  JsonUtils.toJsonString(transferVo)));
                }
            } else {
                transferVo.setRemark(billVo.get(0).getDeductionReason());
                transferVo.setOrderNo(billVo.get(0).getOrderNo());
//                transferVo.setRefundNo(billVo.get(0).getRefundCode());
                if (channelsProperties.isDebugLog()) {
                    if (transferVo.getTransAmt().abs().compareTo(billVo.get(0).getAmount()) != 0) {
                        log.keyword("setSupTradeAccSupDeptAcctExcel").warn(String.format("transferVo = %s billVo = %s", transferVo.getTransAmt(), JsonUtils.toJsonString(billVo.get(0))));
                    }
                }
            }
        }
    }
}