package cn.xianlink.trade.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.json.utils.JsonUtils;
import cn.xianlink.trade.domain.*;
import cn.xianlink.trade.domain.bo.TradeCustTransBo;
import cn.xianlink.trade.domain.vo.*;
import cn.xianlink.trade.mapper.*;
import cn.xianlink.trade.service.ITradeCustAccountService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 分账变更流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class TradeCustAccountServiceImpl implements ITradeCustAccountService {


    private final transient TradeCustAccountMapper baseMapper;
    private final transient TradeCustTransMapper tradeCustTransMapper;
    private final transient TradeCustTransAcctMapper tradeCustTransAcctMapper;

    @Override
    public TradeCustAccountVo queryAccount(Long customerId) {
        TradeCustAccountVo accountVo = baseMapper.queryAccountSum(customerId);
        return accountVo == null ? new TradeCustAccountVo() : accountVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAccountYdaAmt() {
        baseMapper.update(Wrappers.lambdaUpdate(TradeCustAccount.class).setSql("yda_avail_amt=freeze_amt+avail_amt"));
    }

    /**
     * 查询未合计的流水
     */
    @Override
    public List<TradeCustTransAcctVo> getTransAcctList(int limit) {
        LambdaQueryWrapper<TradeCustTransAcct> lqw = new LambdaQueryWrapper<>(TradeCustTransAcct.class)
                .select(TradeCustTransAcct::getId, TradeCustTransAcct::getCustomerId,
                        TradeCustTransAcct::getCustTransId, TradeCustTransAcct::getAvailAmt, TradeCustTransAcct::getSourceType)
                .eq(TradeCustTransAcct::getDelFlag, YNStatusEnum.DISABLE.getCode())
                .orderByAsc(TradeCustTransAcct::getId).last("limit " + limit);
        return tradeCustTransAcctMapper.selectVoList(lqw);
    }

    /**
     * 更新到总计表中
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAccountAmt(List<TradeCustTransAcctVo> acctVos) {
        if (acctVos.size() == 0) {
            return;
        }
        List<Long> acctIds = acctVos.stream().map(TradeCustTransAcctVo::getId).toList();
        int count = tradeCustTransAcctMapper.update(Wrappers.lambdaUpdate(TradeCustTransAcct.class)
                .setSql("del_flag=id").in(TradeCustTransAcct::getId, acctIds)
                .eq(TradeCustTransAcct::getDelFlag, YNStatusEnum.DISABLE.getCode())
        );
        if (acctIds.size() != count) {
            // 防止并发相同数据
            throw new ServiceException("不能统计已处理的数据");
        }
        // 1 合计当前余额
        Map<Long, List<TradeCustTransAcctVo>> transAcctMap = acctVos.stream()
                .collect(Collectors.groupingBy(TradeCustTransAcctVo::getCustomerId, Collectors.toList()));
        List<Long> custAcctIds = transAcctMap.keySet().stream().toList();
        List<TradeCustAccountVo> custAccountVos = baseMapper.queryAccountAmt(acctIds);
        count = baseMapper.updateAccountAmt(custAccountVos);
        if (transAcctMap.size() != count) {
            // 有新账户数据
            List<TradeCustAccountVo> existAccountVos = baseMapper.queryExistAccount(custAcctIds);
            Map<Long, List<TradeCustAccountVo>> existAccoutMap = existAccountVos.stream()
                    .collect(Collectors.groupingBy(TradeCustAccountVo::getCustomerId, Collectors.toList()));
            Map<Long, List<TradeCustTransAcctVo>> notExistAcctMap = transAcctMap.entrySet().stream()
                    .filter(entry -> !existAccoutMap.containsKey(entry.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            if (notExistAcctMap.size() > 0) {
                List<Long> notExistAcctIds = notExistAcctMap.values().stream().flatMap(List::stream).map(TradeCustTransAcctVo::getId).toList();
                baseMapper.insertNotExistAccount(notExistAcctMap.values().stream().map(vos -> vos.get(0)).toList());
                baseMapper.updateAccountAmt(baseMapper.queryAccountAmt(notExistAcctIds));
            }
        }
        // 2 回填客户流水的余额，  仅处理 sourceType = 0 的情况
        List<TradeCustTransAcctVo> transAcctVos = acctVos.stream().filter(vo -> vo.getSourceType() == 0)
                .sorted(Comparator.comparing(TradeCustTransAcctVo::getCustTransId).reversed()).toList();
        if (transAcctVos.size() > 0) {
            transAcctMap = transAcctVos.stream().collect(Collectors.groupingBy(TradeCustTransAcctVo::getCustomerId, Collectors.toList()));
            custAcctIds = transAcctMap.keySet().stream().toList();
            List<TradeCustAccountVo> existAccountVos = baseMapper.queryExistAccount(custAcctIds);
            Map<Long, List<TradeCustAccountVo>> existAccoutMap = existAccountVos.stream()
                    .collect(Collectors.groupingBy(TradeCustAccountVo::getCustomerId, Collectors.toList()));
            List<TradeCustTransBo> custBos = new ArrayList<>();
            for (Map.Entry<Long, List<TradeCustTransAcctVo>> entry : transAcctMap.entrySet()) {
                List<TradeCustAccountVo> accountVos = existAccoutMap.get(entry.getKey());
                if (CollectionUtil.isEmpty(accountVos)) {
                    log.keyword(entry.getKey(), "updateCustAccountAmt").warn("account表无数据 " + JsonUtils.toJsonString(entry.getValue()));
                    continue;
                }
                TradeCustAccountVo accountVo = accountVos.get(0);
                for (TradeCustTransAcctVo transAcctVo : entry.getValue()) {
                    custBos.add(new TradeCustTransBo().setId(transAcctVo.getCustTransId()).setAvailBalAmt(accountVo.getAvailAmt()));
                    accountVo.setAvailAmt(accountVo.getAvailAmt().subtract(transAcctVo.getAvailAmt()));
                }
            }
            if (custBos.size() > 0) {
                tradeCustTransMapper.updateCustBalAmt(custBos);
            }
        }
        // 2 结束
        tradeCustTransAcctMapper.deleteBatchIds(acctIds);
    }

}
