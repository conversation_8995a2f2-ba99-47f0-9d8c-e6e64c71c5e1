package cn.xianlink.trade.constant;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.xianlink.common.api.enums.trade.AccountTransTypeEnum;
import cn.xianlink.common.api.enums.trade.RefundBusiTypeEnum;
import cn.xianlink.common.api.enums.trade.TransferBusiTypeEnum;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public interface Constants {

    CopyOptions BeanCopyIgnoreNullValue = new CopyOptions().setIgnoreNullValue(true);

    List<String> DiscountTypes = Arrays.asList(AccountTransTypeEnum.DI.getCode(), AccountTransTypeEnum.DO.getCode());
    // 第一个关联单类型
    List<String> TransferTypes = Arrays.asList(AccountTransTypeEnum.TI.getCode(), AccountTransTypeEnum.TO.getCode());

    List<String> RefundTypes = Arrays.asList(AccountTransTypeEnum.OR.getCode(), AccountTransTypeEnum.OS.getCode());

    List<String> PayTypes = Collections.singletonList(AccountTransTypeEnum.OP.getCode());

    List<String> CashTypes = Collections.singletonList(AccountTransTypeEnum.CH.getCode());

    List<String> PayPositiveTypes = Arrays.asList(AccountTransTypeEnum.OP.getCode(), AccountTransTypeEnum.TI.getCode());

    List<String> NegativeTypes = Arrays.asList(
            AccountTransTypeEnum.OR.getCode(), AccountTransTypeEnum.OS.getCode(),
            AccountTransTypeEnum.TO.getCode(), AccountTransTypeEnum.CH.getCode(),
            AccountTransTypeEnum.CO.getCode(), AccountTransTypeEnum.DO.getCode());

    List<String> NegativeFields = Arrays.asList(
            RefundBusiTypeEnum.PAY_CANCEL.getField(), RefundBusiTypeEnum.REFUND_BAL.getField(),
            RefundBusiTypeEnum.REFUND_LOSS.getField(), TransferBusiTypeEnum.TRANSFER_LOSS.getOutField());

    // 业务端没有的退款类型
    List<String> RefundRecordNotExistsTypes = Collections.singletonList(RefundBusiTypeEnum.REFUND_EXCEPTION.getCode());

    String SupplierAccountTemplate = "/template/supplier_account.xlsx";

}

