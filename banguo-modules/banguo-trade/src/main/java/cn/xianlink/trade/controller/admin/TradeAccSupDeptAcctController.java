package cn.xianlink.trade.controller.admin;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.api.RemoteSupAccTransService;
import cn.xianlink.order.api.bo.RemoteSupAccTransQueryBo;
import cn.xianlink.trade.config.properties.ChannelsProperties;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.bo.TradeAccCashQueryBo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupDeptAcctDetailQueryBo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupDeptAcctQueryBo;
import cn.xianlink.trade.domain.vo.TradeAccCashVo;
import cn.xianlink.trade.domain.vo.org.TradeSupBaseDataVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransVo;
import cn.xianlink.trade.service.ITradeAccAvailTransService;
import cn.xianlink.trade.service.ITradeAccCashService;
import cn.xianlink.trade.service.biz.TradeBaseDataBizService;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 账务总
 *
 * <AUTHOR>
 * @date 2024-06-12
 * @folder 般果管理中心/供应商结算/资金账户(新)
 */
@CustomLog
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/accSupDeptAcct")
public class TradeAccSupDeptAcctController extends BaseController {

    private final transient ITradeAccCashService tradeAccCashService;
    private final transient ITradeAccAvailTransService tradeAccAvailTransService;

    @DubboReference(timeout = 10000)
    private final transient RemoteSupAccTransService remoteSupAccTransService;

    private final transient ChannelsProperties channelsProperties;
    private final transient TradeBaseDataBizService tradeBaseDataBizService;

    @DubboReference
    private final transient RemoteSupplierService remoteSupplierService;

    /**
     * 资金账户 : 变动记录
     */
    @PostMapping("/trans")
    public R<TableDataInfo<SupTradeAccSupDeptAvailTransVo>> trans(@Validated @RequestBody SupTradeAccSupDeptAcctQueryBo bo) {
        if(ObjectUtil.isEmpty(bo.getSupplierId())){
            throw new ServiceException("供应商id不能为空");
        }
        TradeSupBaseDataVo baseVo = this.getLoginSupplier(bo.getSupplierId(), bo.getDeptId());
        bo.setDeptId(baseVo.getDeptId()).setSupplierCode(baseVo.getCode()).setSupplierId(baseVo.getId());
        SupTradeAccSupDeptAcctDetailQueryBo queryBo = new SupTradeAccSupDeptAcctDetailQueryBo();
        BeanUtil.copyProperties(bo, queryBo, Constants.BeanCopyIgnoreNullValue);
        queryBo.setAccountDate(baseVo.getAccountDate());
        TableDataInfo<SupTradeAccSupDeptAvailTransVo> result = tradeAccAvailTransService.customSupPageList(queryBo);

        // 查询平台补贴
        RemoteSupAccTransQueryBo platformSubSidyQueryBo = new RemoteSupAccTransQueryBo().setSupplierId(bo.getSupplierId()).setSupplierDeptId(bo.getDeptId());
        result.getRows().forEach(vo -> platformSubSidyQueryBo.addTransDate(vo.getTransDate()));
        Map<LocalDate, BigDecimal> subsidyMap = remoteSupAccTransService.queryPlatformSubsidyByDate(platformSubSidyQueryBo);

        Map<String, List<TradeAccCashVo>> cashVos = tradeAccCashService.queryList(new TradeAccCashQueryBo().setOrgType(baseVo.getType())
                        .setOrgCodes(Collections.singletonList(baseVo.getCode()))
                        .setDeptIds(bo.getDeptId() == null || bo.getDeptId() == 0L ? null : Collections.singletonList(bo.getDeptId()))
                        .setCashDateStart(bo.getTransDateStart()).setCashDateEnd(bo.getTransDateEnd()))
                .stream().collect(Collectors.groupingBy(vo -> vo.getCashDate().toString(), Collectors.toList()));
        result.getRows().forEach(vo -> {
            vo.setPlatformSubsidyAmt(subsidyMap.getOrDefault(vo.getTransDate(), BigDecimal.ZERO));
            List<TradeAccCashVo> list = cashVos.get(vo.getTransDate().toString());
            vo.setCashAmt(CollectionUtil.isNotEmpty(list) ? list.stream().map(TradeAccCashVo::getCashAmt).reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO);
        });

        RemoteSupplierVo remoteSupplierVo = remoteSupplierService.getSupplierById(bo.getSupplierId());
        result.getRows().forEach(vo -> {
            vo.setSupplierName(remoteSupplierVo.getName());
            vo.setSupplierCode(remoteSupplierVo.getCode());
        });
        return R.ok(result);
    }

    /**
     * 查询供应商数据
     */
    public TradeSupBaseDataVo getLoginSupplier(Long supplierId, Long deptId) {
        RemoteBaseDataVo vo = tradeBaseDataBizService.queryById(supplierId, AccountOrgTypeEnum.SUPPLIER.getCode());
        if (vo == null) {
            throw new ServiceException("供应商不存在");
        }
        TradeSupBaseDataVo baseDataVo = MapstructUtils.convert(vo, TradeSupBaseDataVo.class);
        baseDataVo.setDeptId(deptId == null ? 0L : deptId);
        // 账单日
        if (channelsProperties.isDebugLocal()) {
            baseDataVo.setAccountDate(LocalDate.now().plusDays(-3));
        } else {
            String accountDate = tradeAccAvailTransService.queryeSupAccountDate(baseDataVo.getCode());
            baseDataVo.setAccountDate(accountDate == null ? null : LocalDate.parse(accountDate));
        }
        return baseDataVo;
    }
}