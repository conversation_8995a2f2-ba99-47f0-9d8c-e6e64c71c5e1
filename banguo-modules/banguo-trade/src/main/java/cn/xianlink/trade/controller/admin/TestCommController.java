package cn.xianlink.trade.controller.admin;

import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.comm.youwei.YouweiCommServiceImpl;
import cn.xianlink.trade.comm.yzh.YzhCommServiceImpl;
import cn.xianlink.trade.domain.bo.platform.TradePlatformCustQueryBo;
import cn.xianlink.trade.domain.vo.comm.TradeCommCashVo;
import cn.xianlink.trade.domain.vo.platform.TradePlatformCommCashVo;
import cn.xianlink.trade.service.biz.TradeOrgBankBizService;
import com.yunzhanghu.sdk.payment.domain.GetOrderResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;

/**
 * 佣金测试类
 * <AUTHOR>
 * @date 2024-05-28
 */
@Tag(name = "佣金测试类")
@CustomLog
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/comm/test")
@ConditionalOnProperty(prefix = "channels", name = "debug-request", havingValue = "true", matchIfMissing = false)
public class TestCommController extends BaseController {

    private final transient YouweiCommServiceImpl youweiCommService;

    private final transient TradeOrgBankBizService tradeOrgBankBizService;

    private final transient YzhCommServiceImpl yzhCommService;

    @PostMapping("/acctBalanceQuery")
    public R<String> acctBalanceQuery() {
        return R.ok(youweiCommService.acctBalanceQuery());
    }

    /**
     * 查询佣金提现状态
     *
     * @param cashId
     * @return
     */
    @GetMapping("/cashQuery/{cashId}")
    public R<TradeCommCashVo> cashQuery(@PathVariable Long cashId) {
        return R.ok(tradeOrgBankBizService.commWithdrawQuery(cashId));
    }

    /**
     * 查询yzh提现状态
     *
     * @param cashNo
     * @return
     */
    @GetMapping("/yzhQuery/{cashNo}")
    public R<GetOrderResponse> yzhQuery(@PathVariable String cashNo) {
        GetOrderResponse orderResponse = yzhCommService.payQuery(new TradeCommCashVo().setCashNo(cashNo));
        return R.ok(orderResponse);
    }

    /**
     * 发起提现调用
     */
    @PostMapping("/cashCheck/{cashId}")
    public R<TradeCommCashVo> commWithdrawCheck(@PathVariable Long cashId) {
        return R.ok(tradeOrgBankBizService.commWithdrawCheck(cashId, null));
    }

    // 仅做测试使用，手动提现
    @PostMapping("/commCash")
    public R<TradePlatformCommCashVo> commCash(@Validated @RequestBody TradePlatformCustQueryBo bo) {
        return R.ok(MapstructUtils.convert(tradeOrgBankBizService.commWithdraw(bo), TradePlatformCommCashVo.class));
    }
}