package cn.xianlink.trade.controller.admin;

import cn.hutool.core.collection.CollectionUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.domain.bo.*;
import cn.xianlink.trade.domain.vo.*;
import cn.xianlink.trade.service.ITradeAccAccountService;
import cn.xianlink.trade.service.ITradeAccAvailService;
import cn.xianlink.trade.service.ITradeAccTransService;
import cn.xianlink.trade.service.biz.TradeAccTransBizService;
import cn.xianlink.trade.service.biz.TradeOrgBankBizService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 结算单
 *
 * <AUTHOR>
 * @date 2024-06-12
 * @folder 般果管理中心/供应商结算/结算单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/accSupAvail")
public class TradeAccSupAvailController extends BaseController {

    private final transient ITradeAccAvailService tradeAccAvailService;
    private final transient ITradeAccTransService tradeAccTransService;
    private final transient ITradeAccAccountService tradeAccAccountService;
    private final transient TradeAccTransBizService tradeAccTransBizService;
    private final transient TradeOrgBankBizService tradeOrgBankBizService;

    /**
     * 结算单查询
     */
    @Operation(summary = "结算单查询")
    @PostMapping("/page")
    public R<TableDataInfo<TradeAccSupAvailVo>> page(@Validated @RequestBody TradeAccSupAvailQueryBo bo) {
        // 仅查供应商
        return R.ok(tradeAccAvailService.querySupPageList(bo));
    }
    /**
     * 结算单导出
     */
    @Operation(summary = "结算单导出")
    @PostMapping("/export")
    public void export(@Validated TradeAccSupAvailQueryBo bo, HttpServletResponse response) {
        List<TradeAccSupAvailVo> list = tradeAccAvailService.querySupList(bo);
        ExcelUtil.exportExcel(list, "结算单", TradeAccSupAvailVo.class, response);
    }

    /**
     * 结算单明细流水导出
     */
    @Operation(summary = "结算单明细流水导出")
    @PostMapping("/exportTrans/{availId}")
    public void exportTrans(@NotNull(message = "结算单id不能为空") @PathVariable Long availId, HttpServletResponse response) {
        List<TradeAccTransAvailVo> list = tradeAccTransService.queryAvailTransList(false, availId, null);
        ExcelUtil.exportExcel(list, "结算单明细流水", TradeAccTransAvailVo.class, response);
    }

    /**
     * 生成到提现单
     */
    @Operation(summary = "提现单读入结算单")
    @RepeatSubmit()
    @PostMapping("/availToCash")
    public R<Void> availToCash(@Validated @RequestBody TradeAccSupCashAvaiIdsBo bo) {
        if (CollectionUtil.isNotEmpty(bo.getAvailIds())) {
            List<TradeAccAvailVo> vos = tradeAccAvailService.queryListByIds(bo.getAvailIds());
            tradeOrgBankBizService.cashInit(vos, null);
        }
        return R.ok();
    }

    @Operation(summary = "提现单删除结算单")
    @RepeatSubmit()
    @PostMapping("/cashToAvail")
    public R<Void> cashToAvail(@Validated @RequestBody TradeAccSupCashAvaiIdsBo bo) {
        tradeOrgBankBizService.cashInitCancelByIds(bo.getAvailIds(), null);
        return R.ok();
    }
    /**
     * 待结算机构列表（基于供应商的合计）
     */
    @Operation(summary = "待结算机构列表")
    @PostMapping("/accountPage")
    public R<TableDataInfo<TradeAccSupAvailAccountVo>> accountPage(@Validated @RequestBody TradeAccSupAvailAccountQueryBo bo) {
        // 仅查供应商
        return R.ok(tradeAccAccountService.customSupAvailPageList(bo));
    }

    /**
     * 待结算列表， 仅查询订单供应商部分的流水
     */
    @Operation(summary = "待结算流水明细列表")
    @PostMapping("/transPage")
    public R<TableDataInfo<TradeAccSupAvailTransVo>> transPage(@Validated @RequestBody TradeAccSupAvailTransQueryBo bo) {
        // 仅查供应商
        return R.ok(tradeAccTransService.querySupTransPageList(bo));
    }


    /**
     * 手动结算
     */
    @Operation(summary = "手动结算")
    @RepeatSubmit()
    @PostMapping("/transToAvail")
    public R<Void> transToAvail(@Validated @RequestBody TradeAccAvailTransIdsBo bo) {
        tradeAccTransBizService.updateAvailInit(bo.getAvailDate(), bo.getTransIds());
        return R.ok();
    }

}
