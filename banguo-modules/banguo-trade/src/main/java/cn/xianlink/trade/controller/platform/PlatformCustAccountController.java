package cn.xianlink.trade.controller.platform;

import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.config.properties.ChannelsProperties;
import cn.xianlink.trade.domain.bo.platform.*;
import cn.xianlink.trade.domain.vo.TradeCustAccountVo;
import cn.xianlink.trade.domain.vo.platform.TradePlatformCommCashVo;
import cn.xianlink.trade.service.ITradeCommCashService;
import cn.xianlink.trade.service.ITradeCustAccountService;
import cn.xianlink.trade.service.biz.TradeOrgBankBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


/**
 * 客户余额
 * <AUTHOR>
 * @date 2024-05-28
 * @folder 采集平台(小程序)/客户资金/客户余额
 *
 */
@Tag(name = "客户余额")
@Validated
@CustomLog
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/custAccount")
public class PlatformCustAccountController extends BaseController {

    private final transient ChannelsProperties channelsProperties;
    private final transient ITradeCustAccountService tradeCustAccountService;
    private final transient ITradeCommCashService tradeCommCashService;
    private final transient TradeOrgBankBizService tradeOrgBankBizService;
    private final transient PlatformTradeUtils platformTradeUtils;


    @Operation(summary = "客户账户佣金余额 + 大额储值余额")
    @PostMapping("/all")
    public R<TradeCustAccountVo> all(@Validated @RequestBody TradePlatformCustQueryBo bo) {
        RemoteBaseDataVo vo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (vo == null) {
            throw new ServiceException("用户未登录");
        }
        TradeCustAccountVo account = tradeCustAccountService.queryAccount(vo.getId());
        if (account.getSalaAvailAmt() != null && account.getSalaAvailAmt().compareTo(BigDecimal.ZERO) < 0) {
            account.setSalaAvailAmt(BigDecimal.ZERO); // 处理发薪金额为负数
        }
        return R.ok(account);
    }

    @Operation(summary = "客户提现流水(提现和过期单)")
    @PostMapping("/page")
    public R<TableDataInfo<TradePlatformCommCashVo>> page(@Validated @RequestBody TradePlatformCommCashQueryBo bo) {
        RemoteBaseDataVo vo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (vo == null) {
            throw new ServiceException("用户未登录");
        }
        bo.setCustomerId(vo.getId());
        return R.ok(tradeCommCashService.queryPageList(bo));
    }


    @Operation(summary = "返回最低限额")
    @PostMapping("/getMinAmt")
    public R<BigDecimal> getMinAmt(@Validated @RequestBody TradePlatformCustQueryBo bo) {
        RemoteBaseDataVo vo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (vo == null) {
            throw new ServiceException("用户未登录");
        }
        return R.ok(channelsProperties.getWithdrawFeeAmt());
    }

    @Operation(summary = "返回预计到账金额")
    @PostMapping("/getExpectedAmt")
    public R<BigDecimal> getExpectedAmt(@Validated @RequestBody TradePlatformCustCashBo bo) {
        RemoteBaseDataVo vo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (vo == null) {
            throw new ServiceException("用户未登录");
        }
        return R.ok(tradeOrgBankBizService.getSalaExpectedAmt(bo.getCashAmt()));
    }


    @Operation(summary = "客户提现")
    @RepeatSubmit()
    @PostMapping("/commCash")
    public R<TradePlatformCommCashVo> commCash(@Validated @RequestBody TradePlatformCustQueryBo bo) {
        RemoteBaseDataVo vo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (vo == null) {
            throw new ServiceException("用户未登录");
        }
        bo.setCustomerId(vo.getId());
        return R.ok(MapstructUtils.convert(tradeOrgBankBizService.commWithdraw(bo), TradePlatformCommCashVo.class));
    }

    /*
        提现重新申请 （客户之前银行卡异常， 重新绑定银行卡后，可再次执行该提现单）
     */
    @Operation(summary = "客户提现重新申请")
    @RepeatSubmit()
    @PostMapping("/reCommCash")
    public R<TradePlatformCommCashVo> reCommCash(@Validated @RequestBody TradePlatformCustCashCheckBo bo) {
        RemoteBaseDataVo vo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (vo == null) {
            throw new ServiceException("用户未登录");
        }
        return R.ok(MapstructUtils.convert(tradeOrgBankBizService.commWithdrawCheck(bo.getCashId(), vo.getId()), TradePlatformCommCashVo.class));
    }

    /**
     * 查询平安 和 有为，并回填状态
     * @ignore
     */
    @RepeatSubmit()
    @PostMapping("/ignoreCommCashQuery")
    public R<TradePlatformCommCashVo> commCashQuery(@Validated @RequestBody TradePlatformCustCashCheckBo bo) {
        RemoteBaseDataVo vo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (vo == null) {
            throw new ServiceException("用户未登录");
        }
        return R.ok(MapstructUtils.convert(tradeOrgBankBizService.commWithdrawQuery(bo.getCashId()), TradePlatformCommCashVo.class));
    }

    /**
     * 运维接口， 解决预计到账金额计算的错误
     *  先取消之前的提佣， 更改预计到账金额后， 重新创建发薪单
     * @ignore
     */
    @RepeatSubmit()
    @PostMapping("/ignoreCommCashReCheck")
    public R<TradePlatformCommCashVo> commCashReCheck(@Validated @RequestBody TradePlatformCustCashReCheckBo bo) {
        RemoteBaseDataVo vo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (vo == null) {
            throw new ServiceException("用户未登录");
        }
        return R.ok(MapstructUtils.convert(tradeOrgBankBizService.commWithdrawReCheck(bo.getCashId(), bo.getExpectedAmt()), TradePlatformCommCashVo.class));
    }


    /**
     * @ignore
     */
    @RepeatSubmit()
    @PostMapping("/ignoreCommExpire")
    public R<Void> ignoreCommExpire(@Validated @RequestBody TradePlatformCommCashQueryBo bo) {
        RemoteBaseDataVo vo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (vo == null) {
            throw new ServiceException("用户未登录");
        }
        tradeOrgBankBizService.commWithdrawExpire(bo.getCashDateEnd());
        return R.ok();
    }
}