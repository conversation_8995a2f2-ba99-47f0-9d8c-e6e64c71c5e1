package cn.xianlink.trade.controller.platform;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.channel.AccountService;
import cn.xianlink.trade.channel.pingancloud.PinganCloudOrgBankRelaService;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.bo.org.*;
import cn.xianlink.trade.domain.bo.platform.TradePlatformCustQueryBo;
import cn.xianlink.trade.domain.bo.platform.TradePlatformOrgRelationBo;
import cn.xianlink.trade.domain.bo.platform.TradePlatformOrgRelationCheckBo;
import cn.xianlink.trade.domain.bo.platform.TradePlatformOrgRelationUnbindBo;
import cn.xianlink.trade.domain.vo.org.*;
import cn.xianlink.trade.service.ITradeOrgBankDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * 客户绑卡
 * <AUTHOR>
 * @date 2024-05-28
 * @folder 采集平台(小程序)/客户资金/客户绑卡
 */
@Tag(name = "客户绑定类")
@CustomLog
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/relation")
public class PlatformOrgRelationController extends BaseController {

    private final transient ITradeOrgBankDetailService tradeOrgBankDetailService;
    private final transient PinganCloudOrgBankRelaService pinganCloudOrgBankRelaService;
    private final transient PlatformTradeUtils platformTradeUtils;
    private final transient AccountService accountService;

    @Operation(summary = "获取开户基础信息")
    @PostMapping("/getInfo")
    public R<TradeOrgRelationInfoVo> getInfo(@Validated @RequestBody TradePlatformCustQueryBo bo) {
        RemoteBaseDataVo dataVo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (dataVo == null) {
            throw new ServiceException("用户未登录");
        }
        return R.ok(tradeOrgBankDetailService.getInfoByCode(dataVo.getCode(), dataVo.getType()));
    }

    @Operation(summary = "银行卡列表")
    @PostMapping("/bankList")
    public R<List<TradeOrgBankDetailVo>> bankList(@Validated @RequestBody TradePlatformCustQueryBo bo) {
        RemoteBaseDataVo dataVo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (dataVo == null) {
            throw new ServiceException("用户未登录");
        }
        TradeOrgRelationInfoVo infoVo = tradeOrgBankDetailService.getInfoByCode(dataVo.getCode(), dataVo.getType());
        if (infoVo == null) {
            return R.ok(new ArrayList<>());
        }
        return R.ok(tradeOrgBankDetailService.getBankList(infoVo.getOutOrgCode()));
    }


    @Operation(summary = "开户 + 绑卡（新增）")
    @RepeatSubmit()
    @PostMapping("/bindBank")
    public R<Void> bindBank(@Validated @RequestBody TradePlatformOrgRelationBo bo) {
        RemoteBaseDataVo dataVo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (dataVo == null) {
            throw new ServiceException("用户未登录");
        }
        if (!IdcardUtil.isValidCard18(bo.getReprGlobalId(), false)) {
            // 只能是 18 位身份证， 结尾字符必须是大写 X
            throw new ServiceException("身份证不合法");
        }
        log.keyword(dataVo.getCode(), "bindBank").info("绑定银行");
        TradeOrgRelationInfoVo infoVo = tradeOrgBankDetailService.getInfoByCode(dataVo.getCode(), dataVo.getType());
        if (infoVo == null) {
            infoVo = new TradeOrgRelationInfoVo();
        }
        TradeOrgBankRelaVo relaVo = MapstructUtils.convert(bo, TradeOrgBankRelaVo.class)
                .setOrgId(dataVo.getId()).setOrgCode(dataVo.getCode()).setOrgType(dataVo.getType())
                .setOrgName(dataVo.getName()).setOutOrgProperty(tradeOrgBankDetailService.getOutOrgProperty());
        if (!ObjectUtil.equals(relaVo.getBusinessFlag(), infoVo.getBusinessFlag())
            || !ObjectUtil.equals(relaVo.getReprGlobalId(), infoVo.getReprGlobalId())
            || !ObjectUtil.equals(relaVo.getReprName(), infoVo.getReprName())
            || !ObjectUtil.equals(relaVo.getCompanyName(), infoVo.getCompanyName())
            || !ObjectUtil.equals(relaVo.getCompanyGlobalId(), infoVo.getCompanyGlobalId())) {
            if (AccountOrgBindEnum.BIND.getCode().equals(infoVo.getStatus())) {
                if (AccountBusinessFlagEnum.PERSON.getCode().equals(infoVo.getBusinessFlag())) {
                    throw new ServiceException("先解绑所有银行卡后，才能变更绑卡人信息");
                } else {
                    throw new ServiceException("先解绑所有银行卡后，才能变更公司信息");
                }
            }
            if (AccountBusinessFlagEnum.PERSON.getCode().equals(relaVo.getBusinessFlag())) {
                if (StringUtils.isBlank(relaVo.getReprName())) {
                    throw new ServiceException("姓名不能为空");
                }
            } else {
                if (StringUtils.isBlank(relaVo.getCompanyName())) {
                    throw new ServiceException("公司名称不能为空");
                }
                if (StringUtils.isBlank(relaVo.getCompanyGlobalId())) {
                    throw new ServiceException("公司证件号不能为空");
                }
                if (StringUtils.isBlank(relaVo.getReprName())) {
                    throw new ServiceException("法人不能为空");
                }
            }
            /* 三种情况
                1 非个人时，仅公司名，法人变更了
                2 更换成之前已有的 OutOrgCode
                3 创建了新的 OutOrgCode
             */
            TradeOrgBankRelaBo infTbo;
            if (ObjectUtil.equals(relaVo.getBusinessFlag(), infoVo.getBusinessFlag())
                && ObjectUtil.equals(relaVo.getCompanyGlobalId(), infoVo.getCompanyGlobalId())
                && !AccountBusinessFlagEnum.PERSON.getCode().equals(relaVo.getBusinessFlag())) {
                // 如果类型不变， 且不是个人, 企业号不变， 则不重新创建
                TradeOrgBankRelaVo relaInfoVo = MapstructUtils.convert(infoVo, TradeOrgBankRelaVo.class);
                infTbo = pinganCloudOrgBankRelaService.updateOrgRelate(relaVo, relaInfoVo);
                tradeOrgBankDetailService.updateOrgRelate(infTbo);
                BeanUtil.copyProperties(infTbo, infoVo, Constants.BeanCopyIgnoreNullValue);
                log.keyword(dataVo.getCode(), "bindBank").info("变更公司基础信息，" + infoVo.getOutOrgCode());
            } else {
                if (StringUtils.isNotBlank(infoVo.getOutOrgCode())) {
                    // 如果变更账户， 验证当前账户是否有余额
                    TradeOrgBankAmtBo amtBo = accountService.queryBankAmt(infoVo.getOutOrgCode(), infoVo.getOutAcctCode());
                    if (amtBo != null && amtBo.getBankAvailAmt().compareTo(BigDecimal.ZERO) > 0) {
                        throw new ServiceException(String.format("当前账户有余额 %s，不能变更公司信息", amtBo.getBankAvailAmt()));
                    }
                }
                List<TradeOrgBankVo> bankVos = tradeOrgBankDetailService.getRelationsByCode(dataVo.getCode(), dataVo.getType());
                boolean isReplaceOutOrgCode = false;
                for (TradeOrgBankVo bankVo: bankVos) {
                    if (relaVo.getBusinessFlag().equals(bankVo.getBusinessFlag())) {
                        if (AccountBusinessFlagEnum.PERSON.getCode().equals(relaVo.getBusinessFlag())
                                && relaVo.getReprGlobalId().equals(bankVo.getReprGlobalId())) {
                            isReplaceOutOrgCode = true;
                            tradeOrgBankDetailService.updateOrgRelate(dataVo.getCode(), dataVo.getType(),
                                    bankVo.getOutOrgCode(), bankVo.getOutAcctCode());
                            BeanUtil.copyProperties(bankVo, infoVo, Constants.BeanCopyIgnoreNullValue);
                            log.keyword(dataVo.getCode(), "bindBank").info("变更历史个人账号，" + bankVo.getOutOrgCode());
                        }
                        if (!AccountBusinessFlagEnum.PERSON.getCode().equals(relaVo.getBusinessFlag())
                                    && relaVo.getCompanyGlobalId().equals(bankVo.getCompanyGlobalId())) {
                            isReplaceOutOrgCode = true;
                            TradeOrgBankRelaVo relaInfoVo = MapstructUtils.convert(bankVo, TradeOrgBankRelaVo.class)
                                    .setOrgId(dataVo.getId()).setOrgCode(dataVo.getCode()).setOrgType(dataVo.getType())
                                    .setOrgName(dataVo.getName()).setOutOrgProperty(AccountOrgPropertyEnum.PAY.getCode());
                            infTbo = pinganCloudOrgBankRelaService.updateOrgRelate(relaVo, relaInfoVo);
                            tradeOrgBankDetailService.updateOrgRelate(infTbo);
                            BeanUtil.copyProperties(infTbo, infoVo, Constants.BeanCopyIgnoreNullValue);
                            log.keyword(dataVo.getCode(), "bindBank").info("变更历史企业账号，" + bankVo.getOutOrgCode());
                        }
                    }
                }
                if (!isReplaceOutOrgCode) {
                    relaVo.setOutOrgCode(tradeOrgBankDetailService.getNextOutOrgCode());
                    infTbo = pinganCloudOrgBankRelaService.openOrgRelate(relaVo);
                    tradeOrgBankDetailService.updateOrgRelate(infTbo);
                    BeanUtil.copyProperties(infTbo, infoVo, Constants.BeanCopyIgnoreNullValue);
                    log.keyword(dataVo.getCode(), "bindBank").info("创建账号，" + infoVo.getOutOrgCode());
                }
            }
        }
        TradeOrgBankDetailVo bankVo = tradeOrgBankDetailService.getBankByCode(infoVo.getOutOrgCode(), bo.getBankAccount());
        if (bankVo != null && AccountOrgBindEnum.BIND.getCode().equals(bankVo.getStatus())) {
            throw new ServiceException("该银行卡已绑定，获取验证码失败");
        }
        relaVo.setOutOrgType(infoVo.getOutOrgType());
        relaVo.setOutOrgCode(infoVo.getOutOrgCode());
        relaVo.setOutAcctCode(infoVo.getOutAcctCode());
        relaVo.setOutOrgProperty(infoVo.getOutOrgProperty());
        relaVo.setStatus(bankVo == null ? infoVo.getStatus() : bankVo.getStatus());
        // 以下 5 个字段，从数据库中获取， 开户后是不可修改的
        relaVo.setCompanyGlobalId(infoVo.getCompanyGlobalId());
        relaVo.setCompanyName(infoVo.getCompanyName());
        relaVo.setReprGlobalId(infoVo.getReprGlobalId());
        relaVo.setReprName(infoVo.getReprName());
        relaVo.setBusinessFlag(infoVo.getBusinessFlag());
        TradeOrgBankRelaBo infTbo;
        if (AccountBankFlagEnum.COMPANY.getCode().equals(relaVo.getBankFlag())) {
            if (bankVo != null) {
                try {
                    // 有无异常，都重置单号。（再次 bindBankAmt 请求时会重新获得相同的绑定操作单号）
                    relaVo.setBankBindNo(bankVo.getBankBindNo());
                    pinganCloudOrgBankRelaService.queryBindBankAmt(relaVo);
                } catch (ServiceException se) {
                    // 如果异常，去掉之前的绑定单号，才能再次发起绑定请求
                    infTbo = new TradeOrgBankRelaBo();
                    infTbo.setOutOrgCode(infTbo.getOutOrgCode());
                    infTbo.setBankAccount(infTbo.getBankAccount());
                    infTbo.setBankBindNo("");
                    tradeOrgBankDetailService.updateBankByBo(infTbo);
                    throw se;
                }
            }
            infTbo = pinganCloudOrgBankRelaService.bindBankAmt(relaVo);
        } else {
            infTbo = pinganCloudOrgBankRelaService.bindBank(relaVo);
        }
        if (infTbo != null) {
            tradeOrgBankDetailService.updateBankByBo(infTbo);
        }
        return R.ok();
    }

    @Operation(summary = "绑卡短信 + 转账金额验证")
    @RepeatSubmit()
    @PostMapping("/bindBankCheck")
    public R<Void> bindBankCheck(@Validated @RequestBody TradePlatformOrgRelationCheckBo bo) {
        RemoteBaseDataVo dataVo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (dataVo == null) {
            throw new ServiceException("用户未登录");
        }
        TradeOrgRelationInfoVo infoVo = tradeOrgBankDetailService.getInfoByCode(dataVo.getCode(), dataVo.getType());
        if (infoVo == null || infoVo.getStatus() == null) {
            throw new ServiceException("请先执行获取验证码操作");
        }
        TradeOrgBankDetailVo bankVo = tradeOrgBankDetailService.getBankByCode(infoVo.getOutOrgCode(), bo.getBankAccount());
        if (bankVo == null) {
            throw new ServiceException("请先执行获取验证码操作");
        }
        if (AccountOrgBindEnum.BIND.getCode().equals(bankVo.getStatus())) {
            throw new ServiceException("该银行卡已绑定");
        }
        log.keyword(dataVo.getCode(), "bindBankCheck").info("绑定银行验证");
        TradeOrgBankRelaCheckVo checkVo = MapstructUtils.convert(bo, TradeOrgBankRelaCheckVo.class);;
        TradeOrgBankRelaVo relaVo = new TradeOrgBankRelaVo().setOutOrgCode(infoVo.getOutOrgCode())
                .setOutAcctCode(infoVo.getOutAcctCode()).setBankBindNo(bankVo.getBankBindNo()).setBankAccount(bankVo.getBankAccount());
        pinganCloudOrgBankRelaService.bindBankRecord(relaVo);
        TradeOrgBankRelaBo infTbo;
        if (AccountBankFlagEnum.COMPANY.getCode().equals(bankVo.getBankFlag())) {
            if (bo.getAuthAmt() == null) {
                throw new ServiceException("转账金额不能为空");
            }
            if (!pinganCloudOrgBankRelaService.queryBindBankAmt(relaVo)) {
                throw new ServiceException("请先执行获取验证码操作");
            }
            infTbo = pinganCloudOrgBankRelaService.checkBindBankAmt(relaVo, checkVo);
        } else {
            infTbo = pinganCloudOrgBankRelaService.checkBindBank(relaVo, checkVo);
        }
        tradeOrgBankDetailService.updateBankByBo(infTbo);
        return R.ok();
    }

    @Operation(summary = "解绑（删除银行卡）")
    @RepeatSubmit()
    @PostMapping("/unbindBank")
    public R<Void> unbindBank(@Validated @RequestBody TradePlatformOrgRelationUnbindBo bo) {
        RemoteBaseDataVo dataVo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (dataVo == null) {
            throw new ServiceException("用户未登录");
        }
        TradeOrgRelationInfoVo infoVo = tradeOrgBankDetailService.getInfoByCode(dataVo.getCode(), dataVo.getType());
        if (infoVo == null || infoVo.getStatus() == null) {
            // throw new ServiceException("未执行绑卡操作");
            return R.ok();
        }
        TradeOrgBankDetailVo bankVo = tradeOrgBankDetailService.getBankByCode(infoVo.getOutOrgCode(), bo.getBankAccount());
        if (bankVo != null && AccountOrgBindEnum.BIND.getCode().equals(bankVo.getStatus())) {
            log.keyword(dataVo.getCode(), "unbindBank").info("解绑银行");
            TradeOrgBankRelaVo relaVo = new TradeOrgBankRelaVo().setOrgCode(infoVo.getOrgCode())
                    .setOrgType(infoVo.getOrgType()).setOutOrgProperty(infoVo.getOutOrgProperty())
                    .setOutOrgCode(infoVo.getOutOrgCode()).setOutAcctCode(infoVo.getOutAcctCode())
                    .setBankAccount(bankVo.getBankAccount());
            TradeOrgBankRelaBo infTbo = pinganCloudOrgBankRelaService.unbindBank(relaVo);
            tradeOrgBankDetailService.updateBankByBo(infTbo);
        }
        return R.ok();
    }


}
