package cn.xianlink.trade.service;


import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.api.domain.bo.OrderPayRecvJobQueryBo;
import cn.xianlink.trade.domain.bo.*;
import cn.xianlink.trade.domain.vo.TradePayRecvPageVo;
import cn.xianlink.trade.domain.vo.TradePayRecvVo;

import java.time.LocalDate;
import java.util.List;

/**
 * 转账Service接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface ITradePayRecvService {

    TradePayRecvVo queryByNo(String recvNo);

    TableDataInfo<TradePayRecvPageVo> queryPageList(TradePayRecvQueryBo bo, boolean isCustomer);


    List<TradePayRecvPageVo> queryList(TradePayRecvQueryBo bo, boolean isCustomer);

    /**
     * 查询未完成支付
     */
    List<TradePayRecvVo> queryPayRecvProcessing(OrderPayRecvJobQueryBo bo);
    /**
     * 创建存款单
     */
    TradePayRecvVo insertByBo(TradePayRecvBo bo);
    /**
     * 更新
     */
    boolean updateInfData(TradePayRecvBo bo, TradePayRecvVo recvVo);

    /**
     * 失败
     */
    void updateInfFail(TradePayRecvVo tvo, ServiceException se);

    void updateAcctStatus(LocalDate acctDate, List<TradePayRecvBo> bos);

}
