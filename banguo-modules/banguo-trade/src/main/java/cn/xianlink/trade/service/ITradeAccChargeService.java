package cn.xianlink.trade.service;

import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.domain.bo.TradeAccChargeBo;
import cn.xianlink.trade.domain.bo.TradeAccChargeCreateBo;
import cn.xianlink.trade.domain.bo.TradeAccChargeQueryBo;
import cn.xianlink.trade.domain.bo.TradeAccChargeRefundBo;
import cn.xianlink.trade.domain.vo.TradeAccChargeVo;

import java.util.List;

/**
 * 数据文件Service接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface ITradeAccChargeService {
    /**
     * 查询数据
     */
    TradeAccChargeVo queryById(Long id);
    /**
     * 查询数据
     */
    TableDataInfo<TradeAccChargeVo> queryPageList(TradeAccChargeQueryBo bo);
    /**
     * 查询数据
     */
    List<TradeAccChargeVo> queryList(TradeAccChargeQueryBo bo);
    /**
     * 查询数据
     */
    List<TradeAccChargeVo> queryRefundList(String relateNo);
    /**
     * 新增
     */
    TradeAccChargeVo insertByBo(TradeAccChargeCreateBo bo, String saleFeeAcctCode);
    /**
     * 新增退款
     */
    TradeAccChargeVo insertByBo(TradeAccChargeRefundBo bo, String saleFeeAcctCode);
    /*
     * 更新调用后
     */
    void updateInfData(TradeAccChargeBo bo, TradeAccChargeVo vo);
    /*
     * 更新调用后
     */
    void updateInfFail(Integer infStatus, TradeAccChargeVo vo, ServiceException se);

}
