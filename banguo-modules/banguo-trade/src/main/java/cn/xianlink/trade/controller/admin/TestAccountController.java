package cn.xianlink.trade.controller.admin;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.api.enums.trade.PayInfStatusEnum;
import cn.xianlink.common.api.enums.trade.RefundInfStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.api.domain.bo.RemoteOrgDeptBo;
import cn.xianlink.trade.api.domain.bo.RemoteTransAvailBo;
import cn.xianlink.trade.api.domain.bo.RemoteTransferQueryBo;
import cn.xianlink.trade.api.domain.vo.RemoteAvailJobVo;
import cn.xianlink.trade.api.domain.vo.RemoteCashJobVo;
import cn.xianlink.trade.api.domain.vo.RemoteSupAvailDateVo;
import cn.xianlink.trade.api.domain.vo.RemoteTransAvailVo;
import cn.xianlink.trade.comm.youwei.YouweiCommServiceImpl;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.bo.TradeAccAvailBo;
import cn.xianlink.trade.domain.bo.TradeAccCashBo;
import cn.xianlink.trade.domain.bo.TradeAccOssBo;
import cn.xianlink.trade.domain.bo.TradeAccTransAvaildsBo;
import cn.xianlink.trade.domain.bo.TradeAccTransBo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupCashAvaildsBo;
import cn.xianlink.trade.domain.vo.*;
import cn.xianlink.trade.domain.vo.excel.CZExcelColumnVo;
import cn.xianlink.trade.domain.vo.excel.ExcelDataInfo;
import cn.xianlink.trade.domain.vo.org.TradeAccDailyVo;
import cn.xianlink.trade.domain.vo.org.TradeSupBaseDataVo;
import cn.xianlink.trade.dubbo.RemoteJobTradeServiceImpl;
import cn.xianlink.trade.mq.producer.TradePayCompleteProducer;
import cn.xianlink.trade.mq.producer.TradePayRefundCompleteProducer;
import cn.xianlink.trade.service.ITradeAccAccountService;
import cn.xianlink.trade.service.ITradeAccAvailTransService;
import cn.xianlink.trade.service.ITradeAccTransService;
import cn.xianlink.trade.service.biz.TradeAccTransBizService;
import cn.xianlink.trade.service.biz.TradeAccountBizService;
import cn.xianlink.trade.service.biz.TradeOrgBankBizService;
import cn.xianlink.trade.service.biz.TradeTransFileBizService;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;


/**
 * 统计测试类
 * <AUTHOR>
 * @date 2024-05-28
 */
@Tag(name = "统计测试类")
@CustomLog
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/account/test")
@ConditionalOnProperty(prefix = "channels", name = "debug-request", havingValue = "true", matchIfMissing = false)
public class TestAccountController extends BaseController {

    private final transient ITradeAccTransService tradeAccTransService;
    private final transient ITradeAccAccountService tradeAccAccountService;
    private final transient ITradeAccAvailTransService tradeAccAvailTransService;
    private final transient RemoteJobTradeServiceImpl remoteJobTradeService;
    private final transient TradeAccTransBizService tradeAccTransBizService;
    private final transient TradeOrgBankBizService tradeOrgBankBizService;
    private final transient TradeAccountBizService tradeAccountBizService;
    private final transient TradePayCompleteProducer tradePayCompleteProducer;
    private final transient TradePayRefundCompleteProducer tradePayRefundCompleteProducer;
    private final transient YouweiCommServiceImpl youweiCommService;
    private final transient TradeTransFileBizService tradeTransFileBizService;

    @PostMapping("/discountPay")
    public R<Void> discountPay(@Validated @RequestBody TradeAccTransBo bo) {
        for (TradeAccTransVo vo : tradeAccTransService.queryDiscountTrans(bo)){
            tradeAccountBizService.disposeDiscount(MapstructUtils.convert(vo, TradeAccTransBo.class));
        }
        return R.ok();
    }

    @PostMapping("/payCompleteSend")
    public R<Void> payCompleteSend(@Validated @RequestBody TradePayVo tvo) {
        // 仅加 id
        tradePayCompleteProducer.send(tvo, PayInfStatusEnum.INF_INIT.getCode(), false);
        return R.ok();
    }

    @PostMapping("/refundCompleteSend")
    public R<Void> refundCompleteSend(@Validated @RequestBody TradePayRefundVo tvo) {
        // 仅加 id
        tradePayRefundCompleteProducer.send(tvo, RefundInfStatusEnum.INF_INIT.getCode());
        return R.ok();
    }

    @PostMapping("/transferFreeze")
    public R<Void> transferFreeze(@Validated @RequestBody RemoteTransferQueryBo bo) {
        // 划转  0 -> 1  暂无用， 划转创建单据后就转 1 或 2 状态
        // TradeAccTransferVo vo = tradeAccTransferService.queryByNo(bo.getTransNo());
        tradeAccTransBizService.updateFreeze(bo.getTransNo(), Constants.RefundTypes, null);
        return R.ok();
    }


    @PostMapping("/accContrast")
    public R<Void> accContrast(@Validated @RequestBody RemoteCashJobVo bo) {
        // 仅加 id
        remoteJobTradeService.accTransContrast(bo.getCashNo(), bo.getCashDate());
        return R.ok();
    }

    @PostMapping("/updateAvailInit")
    public R<List<TradeAccTransVo>> updateAvailInit(@Validated @RequestBody RemoteTransAvailBo bo) {
        // 订单和退款  FREEZE 读入 结算单
        return R.ok(tradeAccTransBizService.updateAvailInitByRelate(bo.getTransNo(), bo.getTransType(), bo.getAvailDate(),
                MapstructUtils.convert(bo.getTrans(), TradeAccTransAvaildsBo.class)));
    }

    @PostMapping("/updateAvailInitCancel")
    public R<Void> updateAvailInitCancel(@Validated @RequestBody RemoteTransAvailVo bo) {
        // 结算单中所有trans数据 1 -> 2
        tradeAccTransBizService.updateAvailInitCancel(bo.getTransNo(), bo.getTransType());
        return R.ok();
    }

    @PostMapping("/updateAvailComplete")
    public R<Void> updateAvailComplete(@Validated @RequestBody RemoteAvailJobVo bo) {
        // 结算单中所有trans数据 1 -> 2
        tradeAccTransService.updateAvailComplete(MapstructUtils.convert(bo, TradeAccAvailBo.class));
        return R.ok();
    }

    // 审核状态的提现单， 流水数据转成已提现（升成逻辑后要手工调用）
    @PostMapping("/updateAvailAccount")
    public R<Void> updateAvailAccount(@Validated @RequestBody SupTradeAccSupCashAvaildsBo bo) {
        String[] status = bo.getCashNo().split("->");
        tradeAccTransService.updateAvailAccount(bo.getAvailIds().get(0), Convert.toDate(DateUtil.now()),
                Integer.parseInt(status[0]), Integer.parseInt(status[1]));
        return R.ok();
    }

    @PostMapping("/updateCashAccount")
    public R<Void> updateCashAccount(@Validated @RequestBody SupTradeAccSupCashAvaildsBo bo) {
        String[] status = bo.getCashNo().split("->");
        tradeAccTransService.updateCashAccount(bo.getAvailIds(), Convert.toDate(DateUtil.now()),
                Integer.parseInt(status[0]), Integer.parseInt(status[1]));
        return R.ok();
    }

    // 不进入待审核，直接提测试
    @PostMapping("/cash")
    public R<Void> cash(@Validated @RequestBody SupTradeAccSupCashAvaildsBo bo) {
        tradeOrgBankBizService.withdrawCash(true, bo, new TradeSupBaseDataVo()
                .setId(bo.getCashId()).setCode(bo.getCashNo()).setType(AccountOrgTypeEnum.SUPPLIER.getCode()));
        return R.ok();
    }

    // 初始化的提现单， 删除
    @PostMapping("/deleteCash")
    public R<Void> deleteCash(@Validated @RequestBody RemoteCashJobVo bo) {
        // 仅加 id
        tradeAccTransService.deleteCash(new TradeAccCashBo().setId(bo.getId()).setCashNo(bo.getCashNo()).setStatus(bo.getStatus()));
        return R.ok();
    }

    @PostMapping("/orgAccount")
    public R<TradeAccAccountVo> orgAccount(@Validated @RequestBody RemoteOrgDeptBo bo) {
        return R.ok(tradeAccAccountService.querySupOrgAccount(bo.getOrgCode(), bo.getOrgType(), bo.getDeptId()));
    }

    @PostMapping("/updateAccountYdaAmt")
    public R<Void> updateAccountYdaAmt() {
        remoteJobTradeService.updateAccountYdaAmt();
        return R.ok();
    }

    @PostMapping("/updateAccountAmt")
    public R<Void> updateAccountAmt() {
        remoteJobTradeService.updateAccountAmt();
        return R.ok();
    }

    @PostMapping("/deleteAvailTransAll")
    public R<Void> deleteAvailTransAll(@Validated @RequestBody RemoteSupAvailDateVo vo) {
        tradeAccAvailTransService.deleteAvailTransAll(vo.getAvailDate());
        return R.ok();
    }

    @PostMapping("/appendAvailTrans")
    public R<Integer> appendAvailTrans(@Validated @RequestBody RemoteSupAvailDateVo vo) {
        remoteJobTradeService.appendAvailTrans(vo.getAvailDate());
        return R.ok();
    }

    @PostMapping("/createAvailTrans")
    public R<Void> createAvailTrans(@Validated @RequestBody RemoteSupAvailDateVo vo) {
        remoteJobTradeService.createAvailTrans(vo);
        return R.ok();
    }

    @PostMapping("/createAvailTransAll")
    public R<Integer> createAvailTransAll(@Validated @RequestBody RemoteSupAvailDateVo vo) {
        int errs = 0;
        while (true) {
            List<RemoteSupAvailDateVo> suppVos = remoteJobTradeService.queryeAvailTransList(vo.getAvailDate(), 0, 1000);
            for (RemoteSupAvailDateVo supVo : suppVos) {
                try {
                    remoteJobTradeService.createAvailTrans(supVo);
                } catch (Exception e) {
                    errs++;
                }
            }
            if (suppVos.size() < 1000) {
                break;
            }
        }
        return R.ok(errs);
    }

    @PostMapping("/updateSupUndoneBatch")
    public R<Void> updateSupUndoneBatch(@Validated @RequestBody TradePayRefundVo vo) {
        int pageNum = 1;
        while (true) {
            List<RemoteSupAvailDateVo> suppVos = remoteJobTradeService.queryeAvailTransList(vo.getRefundDate(), pageNum, 1000);
            for (RemoteSupAvailDateVo supVo : suppVos) {
                try {
                    List<TradeAccAvailTransVo> transVos = tradeAccAvailTransService.querySupOverdueList(supVo.getOrgCode(), vo.getOrderDate(), vo.getRefundDate());
                    for (List<TradeAccAvailTransVo> vos : Lists.partition(transVos, 200)) {
                        tradeAccAvailTransService.updateSupUndoneBatch(vos);
                    }
                } catch (Exception e) {
                    log.keyword("updateSupUndoneBatch").error("", e);
                }
            }
            if (suppVos.size() < 1000) {
                break;
            } else {
                pageNum ++;
            }
        }
        return R.ok();
    }

    @PostMapping("/updateSupUndone")
    public R<Void> updateSupUndone(@Validated @RequestBody TradePayRefundVo vo) {
        // 更新 status = 0 的， 判断是否已结， 并填上最终账单日期  real_account_date
        List<TradeAccAvailTransVo> transVos = tradeAccAvailTransService.querySupOverdueList(vo.getRefundNo(), vo.getOrderDate(), vo.getRefundDate());
        for (List<TradeAccAvailTransVo> vos : Lists.partition(transVos, 200)) {
            tradeAccAvailTransService.updateSupUndoneBatch(vos);
        }
        return R.ok();
    }

    @PostMapping("/invoiceAble")
    public R<Void> invoiceAble(@Validated @RequestBody TradePayRefundVo vo) {
        // 更新 status = 0 的， 判断是否已结， 并填上最终账单日期  real_account_date
        youweiCommService.invoiceAble();
        youweiCommService.invoiceCategories();
//        youweiCommService.invoiceQuery();
        youweiCommService.acctBalanceQuery();
        return R.ok();
    }

    /**
     * 日账单导出
     */
    @Operation(summary = "日账单导出")
    @PostMapping("/downloadAccFile")
    public void downloadAccFile(@Validated @RequestBody TradeAccOssBo bo, HttpServletResponse response) {
        ExcelDataInfo<Object> data = tradeTransFileBizService.downloadAndParse(bo.getFileType(), bo.getUrl());
        ExcelUtil.exportExcel(data.getList(), "日账单", data.getClazz(), response);
    }

    @Operation(summary = "统计充值文件金额")
    @PostMapping("/sumCzFileAmt")
    public R<Map<String, Object>> sumCzFileAmt(@Validated @RequestBody TradeAccOssBo bo) {
        ExcelDataInfo<CZExcelColumnVo> data = tradeTransFileBizService.downloadAndParse(bo.getFileType(), bo.getUrl());
        List<CZExcelColumnVo> columns = data.getList();
        BigDecimal czAmt = BigDecimal.ZERO;
        int czCount = 0;
        BigDecimal refundAmt = BigDecimal.ZERO;
        int refundCount = 0;
        for (CZExcelColumnVo vo:  columns) {
            if ("在途充值".equals(vo.getBusiType())) {
                czCount ++;
                czAmt = czAmt.add(vo.getTransAmt());
            } else if ("在途充值撤销".equals(vo.getBusiType())) {
                refundCount ++;
                refundAmt = refundAmt.add(vo.getTransAmt());
            }
        }
        StringBuilder buf = new StringBuilder();
        buf.append(czCount).append(" ");
        buf.append(czAmt).append(" ");
        buf.append(refundCount).append(" ");
        buf.append(refundAmt);
        return R.ok(Map.of("在途充值金额", czAmt, "在途充值数量", czCount, "在途充值撤销金额", refundAmt, "在途充值撤销数量", refundCount, "CZ", buf.toString()));
    }
}