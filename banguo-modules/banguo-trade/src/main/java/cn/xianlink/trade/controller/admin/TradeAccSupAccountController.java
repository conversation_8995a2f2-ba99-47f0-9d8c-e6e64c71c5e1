package cn.xianlink.trade.controller.admin;

import cn.xianlink.common.api.enums.trade.AccountStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.domain.bo.TradeAccSupAccountQueryBo;
import cn.xianlink.trade.domain.vo.TradeAccAccountVo;
import cn.xianlink.trade.domain.vo.TradeAccSupAccountVo;
import cn.xianlink.trade.service.ITradeAccAccountService;
import cn.xianlink.trade.service.ITradeAccTransService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;


/**
 * 结算单
 *
 * <AUTHOR>
 * @date 2024-06-12
 * @folder 般果管理中心/供应商结算/供应商结算
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/accSupAccount")
public class TradeAccSupAccountController extends BaseController {

    private final transient ITradeAccAccountService tradeAccAccountService;
    private final transient ITradeAccTransService tradeAccTransService;
    /**
     * 结算单查询
     */
    @Operation(summary = "供应商结算查询")
    @PostMapping("/page")
    public R<TableDataInfo<TradeAccSupAccountVo>> page(@Validated @RequestBody TradeAccSupAccountQueryBo bo) {
        TableDataInfo<TradeAccAccountVo> result;
        if (bo.getSaleDateStart() == null && bo.getSaleDateEnd() == null) {
            result = tradeAccAccountService.customSupPageList(bo);
        } else {
            vaildSaleDates(bo);
            result = tradeAccTransService.customSupPageList(bo);
        }
        return R.ok(TableDataInfo.build(transList(bo, result.getRows()), result.getTotal()));
    }

    /**
     * 结算单导出
     */
    @Operation(summary = "供应商结算导出")
    @PostMapping("/export")
    public void export(@Validated TradeAccSupAccountQueryBo bo, HttpServletResponse response) {
        List<TradeAccAccountVo> list;
        if (bo.getSaleDateStart() == null && bo.getSaleDateEnd() == null) {
            list = tradeAccAccountService.customSupList(bo);
        } else {
            vaildSaleDates(bo);
            list = tradeAccTransService.customSupList(bo);
        }
        ExcelUtil.exportExcel(transList(bo, list), "供应商结算", TradeAccSupAccountVo.class, true, response);
    }

    private void vaildSaleDates(TradeAccSupAccountQueryBo bo) {
        if (bo.getSaleDateStart() == null) {
            throw new ServiceException("开始日期不能为空");
        }
        if (bo.getSaleDateEnd() == null) {
            throw new ServiceException("结束日期不能为空");
        }
        if (ChronoUnit.DAYS.between(bo.getSaleDateStart(), bo.getSaleDateEnd()) > 60){
            throw new ServiceException("日期范围不能大于60天");
        }
    }
    private List<TradeAccSupAccountVo> transList(TradeAccSupAccountQueryBo bo, List<TradeAccAccountVo> result) {
        List<TradeAccSupAccountVo> list = new ArrayList<>();
        for (TradeAccAccountVo accountVo: result) {
            AccountStatusEnum statusEnum = AccountStatusEnum.FREEZE;
            if (bo.getStatus() == null || statusEnum.getCode().equals(bo.getStatus())) {
                TradeAccSupAccountVo supAccountVo = new TradeAccSupAccountVo();
                supAccountVo.setOrgCode(accountVo.getOrgCode());
                supAccountVo.setOrgName(accountVo.getOrgName());
                supAccountVo.setStatus(statusEnum.getCode());
                supAccountVo.setStatusDesc(statusEnum.getDesc());
                supAccountVo.setPayAmt(accountVo.getFreezePayAmt());
                supAccountVo.setRefundAmt1(accountVo.getFreezeRefundAmt1());
                supAccountVo.setRefundAmt2(accountVo.getFreezeRefundAmt2());
                supAccountVo.setRefundAmt3(accountVo.getFreezeRefundAmt3());
                supAccountVo.setInAmt(accountVo.getFreezeInAmt());
                supAccountVo.setOutAmt(accountVo.getFreezeOutAmt());
                supAccountVo.setTotalAmt(accountVo.getFreezeAmt());
                list.add(supAccountVo);
            }
            statusEnum = AccountStatusEnum.AVAIL;
            if (bo.getStatus() == null || statusEnum.getCode().equals(bo.getStatus())) {
                TradeAccSupAccountVo supAccountVo = new TradeAccSupAccountVo();
                supAccountVo.setOrgCode(accountVo.getOrgCode());
                supAccountVo.setOrgName(accountVo.getOrgName());
                supAccountVo.setStatus(statusEnum.getCode());
                supAccountVo.setStatusDesc(statusEnum.getDesc());
                supAccountVo.setPayAmt(accountVo.getAvailPayAmt());
                supAccountVo.setRefundAmt1(accountVo.getAvailRefundAmt1());
                supAccountVo.setRefundAmt2(accountVo.getAvailRefundAmt2());
                supAccountVo.setRefundAmt3(accountVo.getAvailRefundAmt3());
                supAccountVo.setInAmt(accountVo.getAvailInAmt());
                supAccountVo.setOutAmt(accountVo.getAvailOutAmt());
                supAccountVo.setTotalAmt(accountVo.getAvailAmt());
                list.add(supAccountVo);
            }
            statusEnum = AccountStatusEnum.CHECK;
            if (bo.getStatus() == null || statusEnum.getCode().equals(bo.getStatus())) {
                TradeAccSupAccountVo supAccountVo = new TradeAccSupAccountVo();
                supAccountVo.setOrgCode(accountVo.getOrgCode());
                supAccountVo.setOrgName(accountVo.getOrgName());
                supAccountVo.setStatus(statusEnum.getCode());
                supAccountVo.setStatusDesc(statusEnum.getDesc());
                supAccountVo.setPayAmt(accountVo.getCheckPayAmt());
                supAccountVo.setRefundAmt1(accountVo.getCheckRefundAmt1());
                supAccountVo.setRefundAmt2(accountVo.getCheckRefundAmt2());
                supAccountVo.setRefundAmt3(accountVo.getCheckRefundAmt3());
                supAccountVo.setInAmt(accountVo.getCheckInAmt());
                supAccountVo.setOutAmt(accountVo.getCheckOutAmt());
                supAccountVo.setTotalAmt(accountVo.getCheckAmt());
                list.add(supAccountVo);
            }
            statusEnum = AccountStatusEnum.CASH;
            if (bo.getStatus() == null || statusEnum.getCode().equals(bo.getStatus())) {
                TradeAccSupAccountVo supAccountVo = new TradeAccSupAccountVo();
                supAccountVo.setOrgCode(accountVo.getOrgCode());
                supAccountVo.setOrgName(accountVo.getOrgName());
                supAccountVo.setStatus(statusEnum.getCode());
                supAccountVo.setStatusDesc(statusEnum.getDesc());
                supAccountVo.setPayAmt(accountVo.getCashPayAmt());
                supAccountVo.setRefundAmt1(accountVo.getCashRefundAmt1());
                supAccountVo.setRefundAmt2(accountVo.getCashRefundAmt2());
                supAccountVo.setRefundAmt3(accountVo.getCashRefundAmt3());
                supAccountVo.setInAmt(accountVo.getCashInAmt());
                supAccountVo.setOutAmt(accountVo.getCashOutAmt());
                supAccountVo.setTotalAmt(accountVo.getCashAmt());
                list.add(supAccountVo);
            }
            if (bo.getStatus() == null) {
                TradeAccSupAccountVo supAccountVo = new TradeAccSupAccountVo();
                supAccountVo.setOrgCode(accountVo.getOrgCode());
                supAccountVo.setOrgName(accountVo.getOrgName());
                supAccountVo.setStatusDesc("合计金额 (元)：");
                supAccountVo.setPayAmt(accountVo.getFreezePayAmt().add(accountVo.getAvailPayAmt()).add(accountVo.getCheckPayAmt()).add(accountVo.getCashPayAmt()));
                supAccountVo.setRefundAmt1(accountVo.getFreezeRefundAmt1().add(accountVo.getAvailRefundAmt1()).add(accountVo.getCheckRefundAmt1()).add(accountVo.getCashRefundAmt1()));
                supAccountVo.setRefundAmt2(accountVo.getFreezeRefundAmt2().add(accountVo.getAvailRefundAmt2()).add(accountVo.getCheckRefundAmt2()).add(accountVo.getCashRefundAmt2()));
                supAccountVo.setRefundAmt3(accountVo.getFreezeRefundAmt3().add(accountVo.getAvailRefundAmt3()).add(accountVo.getCheckRefundAmt3()).add(accountVo.getCashRefundAmt3()));
                supAccountVo.setInAmt(accountVo.getFreezeInAmt().add(accountVo.getAvailInAmt()).add(accountVo.getCheckInAmt()).add(accountVo.getCashInAmt()));
                supAccountVo.setOutAmt(accountVo.getFreezeOutAmt().add(accountVo.getAvailOutAmt()).add(accountVo.getCheckOutAmt()).add(accountVo.getCashOutAmt()));
                supAccountVo.setTotalAmt(accountVo.getFreezeAmt().add(accountVo.getAvailAmt()).add(accountVo.getCheckAmt()).add(accountVo.getCashAmt()));
                list.add(supAccountVo);
            }
        }
        return list;
    }

}
