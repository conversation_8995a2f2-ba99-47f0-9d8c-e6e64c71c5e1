package cn.xianlink.trade.controller.admin;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.api.domain.bo.RemoteOrgAuthCheckBo;
import cn.xianlink.trade.channel.pingancloud.PinganCloudOrgBankRelaService;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankAuthBo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankAuthQueryBo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankAuthSmsBo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankRelaBo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankAuthVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;
import cn.xianlink.trade.service.ITradeOrgBankService;
import cn.xianlink.trade.service.ITradeOrgRelationService;
import cn.xianlink.trade.service.biz.TradeOrgBankBizService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 账务总
 *
 * <AUTHOR>
 * @date 2024-06-12
 * @folder 般果管理中心/财务流水/子账户授权
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/accOrgBankAuth")
public class TradeAccOrgBankAuthController extends BaseController {

    private final transient ITradeOrgBankService tradeOrgBankService;
    private final transient ITradeOrgRelationService tradeOrgRelationService;
    private final transient TradeOrgBankBizService tradeOrgBankBizService;
    private final transient PinganCloudOrgBankRelaService pinganCloudOrgBankRelaService;


    /**
     * 查询子账户
     */
    @Operation(summary = "查询子账户")
    @PostMapping("/page")
    public R<TableDataInfo<TradeOrgBankAuthVo>> page(@Validated @RequestBody TradeOrgBankAuthQueryBo bo) {
        return R.ok(tradeOrgBankService.queryPageList(bo));
    }

    /**
     * 查询子账户
     */
    @Operation(summary = "导出")
    @PostMapping("/export")
    public void export(@Validated TradeOrgBankAuthQueryBo bo, HttpServletResponse response) {
        List<TradeOrgBankAuthVo> list = tradeOrgBankService.queryList(bo);
        ExcelUtil.exportExcel(list, "账户授权", TradeOrgBankAuthVo.class, response);
    }
    /**
     * 默认授权参数（仅回传3个授权参数， 如该条记录这三个参数无值时使用）
     */
    @Operation(summary = "默认授权参数")
    @PostMapping("/default")
    public R<TradeOrgBankAuthVo> defaultAuth() {
        return R.ok(pinganCloudOrgBankRelaService.getSameOrgDefaultParams());
    }
    /**
     * 账户授权请求
     */
    @Operation(summary = "账户授权请求")
    @RepeatSubmit()
    @PostMapping("/sameOrgAuth")
    public R<TradeOrgBankAuthVo> sameOrgAuth(@Validated @RequestBody TradeOrgBankAuthBo bo) {
        return R.ok(tradeOrgBankBizService.sameOrgAuth(bo));
    }
    /**
     * 账户授权请求短信验证
     */
    @Operation(summary = "账户授权请求短信验证")
    @RepeatSubmit()
    @PostMapping("/sameOrgAuthCheck")
    public R<TradeOrgBankAuthVo> sameOrgAuthCheck(@Validated @RequestBody TradeOrgBankAuthSmsBo bo) {
        return R.ok(tradeOrgBankBizService.sameOrgAuthCheck(bo));
    }

    /**
     * 同名开通
     * @ignore
     */
    @Operation(summary = "同名开通")
    @PostMapping("/sameOrgBank")
    public R<TradeOrgBankRelaBo> sameOrgBank(@Validated @RequestBody RemoteOrgAuthCheckBo bo) {
        TradeOrgBankRelaVo vo = new TradeOrgBankRelaVo();
        vo.setOutOrgCode(bo.getSmsCode());
        vo.setOrgCode(bo.getOrgCode());
        vo.setOrgType(bo.getOrgType());
        TradeOrgBankRelaBo infTbo = pinganCloudOrgBankRelaService.sameOrgBank(vo);
        tradeOrgRelationService.updateBankByBo(infTbo);
        return R.ok(infTbo);
    }

}
