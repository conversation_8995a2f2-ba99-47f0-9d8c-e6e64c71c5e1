package cn.xianlink.trade.service;

import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.domain.bo.TradeAccOssBo;
import cn.xianlink.trade.domain.bo.TradeAccOssDownBo;
import cn.xianlink.trade.domain.bo.TradeAccOssQueryBo;
import cn.xianlink.trade.domain.vo.TradeAccOssVo;

import java.time.LocalDate;
import java.util.List;

/**
 * 数据文件Service接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface ITradeAccOssService {
    /**
     * 查询数据文件
     */
    TradeAccOssVo queryByBo(TradeAccOssDownBo bo);
    /**
     * 查询数据文件列表
     */
    TableDataInfo<TradeAccOssVo> queryPageList(TradeAccOssQueryBo bo);
    /**
     * 查询数据文件列表
     */
    List<TradeAccOssVo> queryList(TradeAccOssQueryBo bo);
    /**
     * 新增文件
     */
    boolean insertByBo(TradeAccOssBo bo);
    /**
     * 更改文件状态
     */
    boolean updateAcctStatus(Long fileId, String fileType, LocalDate transDate);
    /**
     * 更改文件状态
     */
    boolean updateByBo(TradeAccOssBo updateBo);
    /**
     * 删除
     */
    boolean deleteByIds(List<Long>fileIds);

}
