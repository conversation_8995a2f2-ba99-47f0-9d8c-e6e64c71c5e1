package cn.xianlink.trade.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.xianlink.common.api.enums.trade.AccountOrgPropertyEnum;
import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.constant.TradeCacheNames;
import cn.xianlink.trade.domain.TradeOrgBank;
import cn.xianlink.trade.domain.TradeOrgRelation;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankRelaAuthBo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankRelaBo;
import cn.xianlink.trade.domain.vo.org.*;
import cn.xianlink.trade.mapper.TradeOrgBankMapper;
import cn.xianlink.trade.mapper.TradeOrgRelationMapper;
import cn.xianlink.trade.service.ITradeOrgRelationService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 机构子账户绑定Service业务层处理
 *      该类仅支持 ， 开通 收款 SH 账号
 * <AUTHOR>
 * @date 2024-05-27
 */
@RequiredArgsConstructor
@Service
public class TradeOrgRelationServiceImpl implements ITradeOrgRelationService {

    private final transient TradeOrgRelationMapper baseMapper;
    private final transient TradeOrgBankMapper tradeOrgBankMapper;

    @Override
    public String getOutOrgProperty() {
        return AccountOrgPropertyEnum.SH.getCode();
    }
    /**
     * 查询机构子账户绑定，绑定操作使用，不缓存
     */
    @Cacheable(cacheNames = TradeCacheNames.CACHE_ORG_BIND, key = "#orgCode + '_' + #orgType")
    @Override
    public TradeOrgBankBindVo queryBindByCode(String orgCode, Integer orgType) {
        return MapstructUtils.convert(queryBankByCode(orgCode, orgType), TradeOrgBankBindVo.class);
    }

    @Override
    public TradeOrgBankRelaAuthVo queryAuthByCode(String orgCode, Integer orgType) {
        return MapstructUtils.convert(queryBankByCode(orgCode, orgType), TradeOrgBankRelaAuthVo.class);
    }

    @Override
    public TradeOrgBankRelaVo queryBankByOutCode(String outOrgCode, Integer orgType) {
        List<TradeOrgRelationVo> vos =  baseMapper.selectVoList(Wrappers.lambdaQuery(TradeOrgRelation.class)
                .eq(TradeOrgRelation::getOutOrgCode, outOrgCode).eq(TradeOrgRelation::getOrgType, orgType)
                .eq(TradeOrgRelation::getOutOrgProperty, getOutOrgProperty()));
        if (vos.size() == 0) {
            return null;
        }
        return getOrgBankRelaVo(vos.get(0), outOrgCode);
    }

    private TradeOrgBankRelaVo getOrgBankRelaVo(TradeOrgRelationVo vo, String outOrgCode) {
        TradeOrgBankVo bankVo = tradeOrgBankMapper.selectVoOne(Wrappers.lambdaQuery(TradeOrgBank.class)
                .eq(TradeOrgBank::getOutOrgCode, outOrgCode));
        if (bankVo == null) {
            return null;
        }
        TradeOrgBankRelaVo bankRelaVo = MapstructUtils.convert(bankVo, TradeOrgBankRelaVo.class);
        bankRelaVo.setRelaId(vo.getId());
        bankRelaVo.setOrgType(vo.getOrgType());
        bankRelaVo.setOrgId(vo.getOrgId());
        bankRelaVo.setOrgCode(vo.getOrgCode());
        bankRelaVo.setOrgName(vo.getOrgName());
        return bankRelaVo;
    }

    @Override
    public TradeOrgBankRelaVo queryBankByCode(String orgCode, Integer orgType) {
        return queryBankByCode(orgCode, orgType, null);
    }

    @Override
    public TradeOrgBankRelaVo queryBankByCode(String orgCode, Integer orgType, String outOrgCode) {
        TradeOrgRelationVo vo =  baseMapper.selectVoOne(Wrappers.lambdaQuery(TradeOrgRelation.class)
                .eq(TradeOrgRelation::getOrgCode, orgCode).eq(TradeOrgRelation::getOrgType, orgType)
                .eq(TradeOrgRelation::getOutOrgProperty, getOutOrgProperty()));
        if (vo == null) {
            return null;
        }
        TradeOrgBankRelaVo bankRelaVo = null;
        String outCode = StringUtils.isNotEmpty(outOrgCode) ? outOrgCode : vo.getOutOrgCode();
        if (StringUtils.isNotEmpty(outCode)) {
            bankRelaVo = getOrgBankRelaVo(vo, outCode);
        }
        if (bankRelaVo == null) {
            bankRelaVo = new TradeOrgBankRelaVo();
            BeanUtil.copyProperties(vo, bankRelaVo, Constants.BeanCopyIgnoreNullValue);
            bankRelaVo.setRelaId(vo.getId());
            bankRelaVo.setId(null);
        }
        return bankRelaVo;
    }

    /**
     * 新增机构子账户绑定(对已存在 trade_org_bank 数据的)
     */
    @CacheEvict(cacheNames = TradeCacheNames.CACHE_ORG_BIND, key = "#bo.orgCode + '_' + #bo.orgType")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(TradeOrgBankRelaBo bo, boolean isExists) {
        TradeOrgRelation add = MapstructUtils.convert(bo, TradeOrgRelation.class);
        if (isExists) {
            add.setId(null);
            return baseMapper.update(add, Wrappers.lambdaUpdate(TradeOrgRelation.class)
                    .eq(TradeOrgRelation::getOrgType, add.getOrgType()).eq(TradeOrgRelation::getOrgCode, add.getOrgCode())
                    .eq(TradeOrgRelation::getOutOrgProperty, getOutOrgProperty())) > 0;
        } else {
           return baseMapper.insert(add) > 0;
        }
    }

    /**
     * 新增机构子账户绑定
     */
    @Override
    @CacheEvict(cacheNames = TradeCacheNames.CACHE_ORG_BIND, key = "#bo.orgCode + '_' + #bo.orgType")
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBankByBo(TradeOrgBankRelaBo bo, boolean isExists) {
        TradeOrgRelation add = MapstructUtils.convert(bo, TradeOrgRelation.class);
        TradeOrgBank addBank = MapstructUtils.convert(bo, TradeOrgBank.class);
        if (isExists) {
            add.setId(null);
            baseMapper.update(add, Wrappers.lambdaUpdate(TradeOrgRelation.class)
                    .eq(TradeOrgRelation::getOrgType, add.getOrgType()).eq(TradeOrgRelation::getOrgCode, add.getOrgCode())
                    .eq(TradeOrgRelation::getOutOrgProperty, getOutOrgProperty()));
        } else {
            boolean flag = baseMapper.insert(add) > 0;
            if (flag) {
                bo.setRelaId(add.getId());
            }
        }
        addBank.setSourceType(0);
        addBank.setSourceOrgId(add.getOrgId());
        addBank.setSourceOrgCode(add.getOrgCode());
        boolean flag = tradeOrgBankMapper.insert(addBank) > 0;
        if (flag) {
            bo.setId(addBank.getId());
        }
        return flag;
    }

    /**
     * 修改机构子账户绑定
     */
    @Override
    @CacheEvict(cacheNames = TradeCacheNames.CACHE_ORG_BIND, key = "#bo.orgCode + '_' + #bo.orgType")
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateOrgRelate(TradeOrgBankRelaBo bo) {
        baseMapper.update(Wrappers.lambdaUpdate(TradeOrgRelation.class).set(TradeOrgRelation::getOrgName, bo.getOutOrgName())
                .eq(TradeOrgRelation::getOrgCode, bo.getOrgCode()).eq(TradeOrgRelation::getOrgType, bo.getOrgType())
                .eq(TradeOrgRelation::getOutOrgProperty, getOutOrgProperty()));
        return updateBankByBo(bo);
    }

    /**
     * 修改机构子账户绑定
     */
    @Override
    @CacheEvict(cacheNames = TradeCacheNames.CACHE_ORG_BIND, key = "#bo.orgCode + '_' + #bo.orgType")
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBankByBo(TradeOrgBankRelaBo bo) {
        TradeOrgBank update = MapstructUtils.convert(bo, TradeOrgBank.class);
        return tradeOrgBankMapper.update(update, Wrappers.lambdaUpdate(TradeOrgBank.class)
                .eq(TradeOrgBank::getOutOrgCode, update.getOutOrgCode())) > 0;
    }

    @Override
    @CacheEvict(cacheNames = TradeCacheNames.CACHE_ORG_BIND, key = "#bo.orgCode + '_' + #bo.orgType")
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBankByAuthBo(TradeOrgBankRelaAuthBo bo) {
        TradeOrgBank update = MapstructUtils.convert(bo, TradeOrgBank.class);
        return tradeOrgBankMapper.update(update, Wrappers.lambdaUpdate(TradeOrgBank.class)
                .eq(TradeOrgBank::getOutOrgCode, update.getOutOrgCode())) > 0;
    }

}
