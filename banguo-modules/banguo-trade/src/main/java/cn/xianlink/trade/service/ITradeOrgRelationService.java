package cn.xianlink.trade.service;

import cn.xianlink.trade.domain.bo.org.TradeOrgBankRelaAuthBo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankRelaBo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaAuthVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankBindVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;

/**
 * 机构子账户绑定Service接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface ITradeOrgRelationService {
    String getOutOrgProperty() ;
    /**
     * 查询机构子账户绑定
     */
    TradeOrgBankBindVo queryBindByCode(String orgCode, Integer orgType);
    /**
     * 查询机构子账户绑定
     */
    TradeOrgBankRelaAuthVo queryAuthByCode(String orgCode, Integer orgType);
    /**
     * 查询机构子账户绑定
     */
    TradeOrgBankRelaVo queryBankByCode(String orgCode, Integer orgType);

    TradeOrgBankRelaVo queryBankByOutCode(String outOrgCode, Integer orgType);
    /**
     * 查询总部机构关系的银行卡对应关系
     */
    TradeOrgBankRelaVo queryBankByCode(String orgCode, Integer orgType, String outOrgCode);
    /**
     * 新增机构子账户绑定(对已存在 trade_org_bank 数据的)
     */
    Boolean insertByBo(TradeOrgBankRelaBo bo, boolean isExists);
    /**
     * 新增机构子账户绑定
     */
    Boolean insertBankByBo(TradeOrgBankRelaBo bo, boolean isExists);
    /**
     * 修改机构子账户绑定
     */
    Boolean updateOrgRelate(TradeOrgBankRelaBo bo);
    /**
     * 修改机构子账户绑定
     */
    Boolean updateBankByBo(TradeOrgBankRelaBo bo);
    /**
     * 修改机构子账户授权
     */
    Boolean updateBankByAuthBo(TradeOrgBankRelaAuthBo bo);

}
