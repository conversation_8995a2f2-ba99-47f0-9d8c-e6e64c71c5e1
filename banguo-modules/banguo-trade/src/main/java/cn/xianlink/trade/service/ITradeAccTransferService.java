package cn.xianlink.trade.service;

import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.api.domain.bo.OrderPayJobQueryBo;
import cn.xianlink.trade.domain.bo.TradeAccTransBo;
import cn.xianlink.trade.domain.bo.TradeAccTransferBo;
import cn.xianlink.trade.domain.bo.TradeAccTransferQueryBo;
import cn.xianlink.trade.domain.bo.TradeAccTransferStatusBo;
import cn.xianlink.trade.domain.vo.TradeAccTransferVo;

import java.time.LocalDate;
import java.util.List;

/**
 * 划转流水Service接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface ITradeAccTransferService {

    /**
     * 查询划转流水, 用于换供应商
     */
    TradeAccTransferVo queryByTransId(Long transId, String busiType);
    /**
     * 查询划转流水
     */
    TradeAccTransferVo queryByNo(String transNo);
    /**
     * job 查询可转账数据
     */
    List<TradeAccTransferVo> queryTransferChecking(OrderPayJobQueryBo bo);

    /**
     * job 查询失败的
     */
    List<TradeAccTransferVo> queryTransferFailed(LocalDate transDate);
    /**
     * 查询划转流水列表
     */
    TableDataInfo<TradeAccTransferVo> queryPageList(TradeAccTransferQueryBo bo);
    /**
     * 查询划转流水列表
     */
    List<TradeAccTransferVo> queryList(TradeAccTransferQueryBo bo);
    /**
     * 新增划水单
     */
    TradeAccTransferVo insertByBo(TradeAccTransferBo tbo, List<TradeAccTransBo> transBos);
    /**
     * 调用划转的银行接口
     */
    TradeAccTransferVo updateCheckByBo(boolean isSame, TradeAccTransferBo tbo);
    /**
     * 接口调用回填
     */
    void updateTransferInfData(TradeAccTransferBo bo, TradeAccTransferVo vo);
    /**
     * 接口调用回填异常
     */
    void updateTransferInfFail(boolean isSame, Integer infStatus, TradeAccTransferVo vo, ServiceException se);

    /**
     * 调用划转的银行接口
     */
    TradeAccTransferVo updateDisCheckByBo(TradeAccTransferBo tbo);
    /**
     * 接口调用回填
     */
    void updateTransferDisInfData(TradeAccTransferBo bo, TradeAccTransferVo vo);
    /**
     * 接口调用回填异常
     */
    void updateTransferDisInfFail(Integer infStatus, TradeAccTransferVo vo, ServiceException se);

    void updateOperStatus(TradeAccTransferStatusBo bo);
}
