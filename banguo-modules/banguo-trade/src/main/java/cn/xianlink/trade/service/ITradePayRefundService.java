package cn.xianlink.trade.service;

import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.api.domain.bo.OrderPayJobQueryBo;
import cn.xianlink.trade.api.domain.bo.OrderRefundBo;
import cn.xianlink.trade.domain.bo.TradeAccTransBo;
import cn.xianlink.trade.domain.bo.TradePayBo;
import cn.xianlink.trade.domain.bo.TradePayRefundBo;
import cn.xianlink.trade.domain.bo.TradePayRefundQueryBo;
import cn.xianlink.trade.domain.vo.TradePayRefundPageVo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;

import java.time.LocalDate;
import java.util.List;

/**
 * 支付退款Service接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface ITradePayRefundService {

    /**
     * 查询支付退款
     */
    List<TradePayRefundVo> queryRelByNo(String orderNo, String refundNo);
    /**
     * 查询退款
     */
    TradePayRefundVo queryByTradeNo(String tradeRefundNo);
    /**
     * 查询支付退款
     */
    List<TradePayRefundVo> queryByTradeNos(List<String> tradeNos);
    /**
     * 查询未完成退款
     */
    List<TradePayRefundVo> queryRefundProcessing(OrderPayJobQueryBo bo);
    /**
     * 查询退款未审核
     */
    List<TradePayRefundVo> queryRefundChecking(OrderPayJobQueryBo bo);
    /**
     * 查询失败的
     */
    List<TradePayRefundVo> queryRefundFailed(LocalDate transDate);
    /**
     * 查询未完成退款
     */
    TableDataInfo<TradePayRefundPageVo> queryPageList(TradePayRefundQueryBo bo);
    /**
     * 查询未完成退款
     */
    List<TradePayRefundPageVo> queryList(TradePayRefundQueryBo bo);
    /**
     * 基于已删除的支付单，创建退款单
     */
    void insertException(TradePayBo infTbo, TradePayRefundBo tbo, TradePayVo tvo);
    /**
     * 创建退款单
     */
    TradePayRefundVo insertByBo(TradePayRefundBo tbo, List<TradeAccTransBo> transBos, TradePayVo pvo, int remarkMaxSplit);
    /**
     * 创建退款单
     */
    TradePayRefundVo insertByRelease(List<TradePayRefundBo> refundBos, List<TradeAccTransBo> transBos);
    /**
     * 退款审核调用
     */
    TradePayRefundVo updateCheckByBo(TradePayRefundBo tbo);
    /**
     * 仅退款
     */
    TradePayRefundVo updateRefundCheckByBo(TradePayRefundBo tbo);
    /**
     * 仅退款
     */
    boolean updateRefundInfData(boolean isOnlyPay, TradePayRefundBo infTbo, TradePayRefundVo tvo);
    /**
     * 仅退款
     */
    void updateRefundInfFail(boolean isOnlyPay, Integer infStatus, TradePayRefundVo tvo, ServiceException se);
    /**
     * 仅分账
     */
    TradePayRefundVo updateSplitCheckByBo(TradePayRefundBo tbo);
    /**
     * 仅分账
     */
    void updateSplitInfData(boolean isEnd, TradePayRefundVo tvo);
    /**
     * 仅分账
     */
    void updateSplitInfFail(TradePayRefundVo tvo, ServiceException se);
    /**
     * 订单刷新退单的分账单号
     */
    void updateRefundSplitNo(String orderNo, String refundNo);
    /**
     * 查询新请求与原单据是否相同
     */
    boolean querySame(OrderRefundBo bo, TradePayRefundVo tvo);

    void updateAcctStatus(LocalDate acctDate, List<TradePayRefundBo> bos);
}
