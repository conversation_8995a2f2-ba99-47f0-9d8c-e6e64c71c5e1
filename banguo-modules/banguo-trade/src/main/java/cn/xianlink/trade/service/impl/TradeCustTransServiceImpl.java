package cn.xianlink.trade.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.xianlink.common.api.enums.trade.AccountStatusEnum;
import cn.xianlink.common.api.enums.trade.AccountTransTypeEnum;
import cn.xianlink.common.api.enums.trade.PayBusiTypeEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.TradeCustAccount;
import cn.xianlink.trade.domain.TradeCustTrans;
import cn.xianlink.trade.domain.TradeCustTransAcct;
import cn.xianlink.trade.domain.bo.platform.TradePlatformCustTransQueryBo;
import cn.xianlink.trade.domain.vo.*;
import cn.xianlink.trade.mapper.TradeCustAccountMapper;
import cn.xianlink.trade.mapper.TradeCustTransAcctMapper;
import cn.xianlink.trade.mapper.TradeCustTransMapper;
import cn.xianlink.trade.service.ITradeCustTransService;
import cn.xianlink.trade.utils.MapstructPageUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 分账变更流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class TradeCustTransServiceImpl implements ITradeCustTransService {

    private final transient TradeCustTransMapper baseMapper;
    private final transient TradeCustAccountMapper tradeCustAccountMapper;
    private final transient TradeCustTransAcctMapper tradeCustTransAcctMapper;

    @Override
    public TableDataInfo<TradeCustTransPageVo> queryTransPageList(TradePlatformCustTransQueryBo bo) {
        LambdaQueryWrapper<TradeCustTrans> lqw = Wrappers.lambdaQuery();
        lqw.ge(bo.getAcctDateStart() != null, TradeCustTrans::getAcctDate, bo.getAcctDateStart());
        lqw.le(bo.getAcctDateEnd() != null, TradeCustTrans::getAcctDate, bo.getAcctDateEnd());
        lqw.eq(StringUtils.isNotBlank(bo.getTransType()), TradeCustTrans::getTransType, bo.getTransType());
        lqw.eq(StringUtils.isNotBlank(bo.getTransNo()), TradeCustTrans::getTransNo, bo.getTransNo());
        lqw.orderByDesc(TradeCustTrans::getId);
        IPage<TradeCustTransVo> result = baseMapper.selectVoPage(bo.build(), lqw);
        return TableDataInfo.build(MapstructPageUtils.convert(result, TradeCustTransPageVo.class));
    }


    /**
     * 订单预扣款
     */
    @Override
    // 其他事务中
    public void updateFreezeByPay(Long customerId, String customerOutCode, BigDecimal payAmt) {
        UpdateWrapper<TradeCustAccount> uw = Wrappers.update();
        uw.setSql("freeze_amt = freeze_amt - {0}", payAmt)
                .eq("customer_id", customerId).ge("avail_amt + freeze_amt", payAmt);
        if (tradeCustAccountMapper.update(uw) == 0) {
            TradeCustAccountVo accountVo = tradeCustAccountMapper.selectVoOne(Wrappers.lambdaQuery(TradeCustAccount.class)
                    .select(TradeCustAccount::getAvailAmt, TradeCustAccount::getFreezeAmt).eq(TradeCustAccount::getCustomerId, customerId));
            throw new ServiceException(String.format("系统内余额 %s 元, 不能支付",
                    accountVo == null ? "0" : accountVo.getAvailAmt().add(accountVo.getFreezeAmt())));
        }
    }

    /**
     * 订单记录扣款
     */
    @Override
    // @Transactional(rollbackFor = Exception.class) 其他事务中
    public void insertTransByPay(Date availTime,TradePayVo payVo) {
        TradeCustTrans trans = new TradeCustTrans();
        trans.setTransType(AccountTransTypeEnum.OP.getCode());
        trans.setTransId(payVo.getOrderId());
        trans.setTransNo(payVo.getOrderNo());
        trans.setTransDate(payVo.getOrderDate());
        trans.setTransOrgId(payVo.getOrderOrgId());
        trans.setAcctOrgId(payVo.getAcctOrgId());
        trans.setCustomerId(payVo.getCustomerId());
        trans.setCustomerCode(payVo.getCustomerCode());
        trans.setCustomerOutCode(payVo.getCustomerOutCode());
        trans.setCustomerAcctCode(payVo.getCustomerAcctCode());
        trans.setBusiType(payVo.getBusiType());
        trans.setBusiField(payVo.getBusiField());
        trans.setRelateType(AccountTransTypeEnum.OP.getCode());
        trans.setRelateNo(payVo.getOrderNo());  // 订单关联单号
        trans.setRelateId(payVo.getOrderId());
        trans.setRelateAmt(payVo.getPayAmt());
        trans.setTransAmt(payVo.getPayAmt().negate());
        trans.setStatus(AccountStatusEnum.AVAIL.getCode());
        trans.setFreezeTime(payVo.getPayInfTime());
        trans.setAvailTime(availTime);
        trans.setAcctDate(LocalDateTimeUtil.of(trans.getAvailTime()).toLocalDate());
        insertTransByBo(trans, true);
    }

    /**
     * 退款记录扣款
     */
    @Override
    // @Transactional(rollbackFor = Exception.class) 其他事务中
    public void insertTransByRefund(Date availTime,TradePayRefundVo refundVo) {
        TradeCustTrans trans = new TradeCustTrans();
        trans.setTransType(AccountTransTypeEnum.OR.getCode());
        trans.setTransId(refundVo.getRefundId());
        trans.setTransNo(refundVo.getRefundNo());
        trans.setTransDate(refundVo.getOrderDate());
        trans.setTransOrgId(refundVo.getOrderOrgId());
        trans.setAcctOrgId(refundVo.getAcctOrgId());
        trans.setCustomerId(refundVo.getCustomerId());
        trans.setCustomerCode(refundVo.getCustomerCode());
        trans.setCustomerOutCode(refundVo.getCustomerOutCode());
        trans.setCustomerAcctCode(refundVo.getCustomerAcctCode());
        trans.setBusiType(refundVo.getBusiType());
        trans.setBusiField(refundVo.getBusiField());
        trans.setRelateType(AccountTransTypeEnum.OP.getCode());
        trans.setRelateNo(refundVo.getOrderNo());  // 订单关联单号
        trans.setRelateId(refundVo.getOrderId());
        trans.setRelateAmt(refundVo.getPayAmt());
        trans.setTransAmt(refundVo.getRefundAmt());
        trans.setStatus(AccountStatusEnum.AVAIL.getCode());
        trans.setFreezeTime(refundVo.getInfTime());
        trans.setAvailTime(availTime);
        trans.setAcctDate(LocalDateTimeUtil.of(trans.getAvailTime()).toLocalDate());
        insertTransByBo(trans, false);
    }

    /**
     * 存款记录扣款
     */
    @Override
    // @Transactional(rollbackFor = Exception.class)  其他事务中
    public void insertTransByRecv(Date availTime, TradePayRecvVo recvVo) {
        TradeCustTrans trans = new TradeCustTrans();
        trans.setTransType(AccountTransTypeEnum.OC.getCode());
        trans.setTransId(recvVo.getId());
        trans.setTransNo(recvVo.getRecvNo());
        trans.setTransDate(recvVo.getRecvDate());
        trans.setTransOrgId(0L);
        trans.setAcctOrgId(0L);
        trans.setCustomerId(recvVo.getCustomerId());
        trans.setCustomerCode(recvVo.getCustomerCode());
        trans.setCustomerOutCode(recvVo.getCustomerOutCode());
        trans.setCustomerAcctCode(recvVo.getCustomerAcctCode());
        trans.setBusiType(PayBusiTypeEnum.CUSTOM_RECHARGE.getCode());
        trans.setBusiField("");
        trans.setRelateType(AccountTransTypeEnum.OC.getCode());
        trans.setRelateNo(recvVo.getRecvNo());  // 订单关联单号
        trans.setRelateId(recvVo.getId());
        trans.setRelateAmt(recvVo.getRecvAmt());
        trans.setTransAmt(recvVo.getRecvAmt());
        trans.setStatus(AccountStatusEnum.AVAIL.getCode());
        trans.setFreezeTime(recvVo.getInfTime());
        trans.setAvailTime(availTime);
        trans.setAcctDate(LocalDateTimeUtil.of(trans.getAvailTime()).toLocalDate());
        insertTransByBo(trans, false);
    }

    private void insertTransByBo(TradeCustTrans trans, boolean isPay) {
        try {
            TradeCustTransAcct acct = new TradeCustTransAcct();
            BeanUtil.copyProperties(trans, acct, Constants.BeanCopyIgnoreNullValue); // 先拷贝就不会拷贝 id 值了
            if (baseMapper.insert(trans) > 0) {
                acct.setSourceType(0);
                acct.setCustTransId(trans.getId());
                acct.setAvailAmt(trans.getTransAmt());
                if (isPay) {
                    acct.setFreezeAmt(trans.getTransAmt().negate());
                }
                tradeCustTransAcctMapper.insert(acct);
            }
        } catch (DuplicateKeyException dke) {
            // 冲突不处理， 跳过
            log.keyword("insertCustTrans").warn("主键重复", dke);
        }
    }

}
