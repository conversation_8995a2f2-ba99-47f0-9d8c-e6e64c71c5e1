package cn.xianlink.trade.controller.platform;

import cn.hutool.core.bean.BeanUtil;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.api.RemoteOrderService;
import cn.xianlink.order.api.RemoteRefundRecordService;
import cn.xianlink.order.api.RemoteReportLossService;
import cn.xianlink.order.api.vo.RemoteOrderBusiAmtVo;
import cn.xianlink.order.api.vo.RemoteOrderInfoVo;
import cn.xianlink.trade.domain.bo.platform.OrderBusiTotalBo;
import cn.xianlink.trade.domain.bo.TradePayCustomerQueryBo;
import cn.xianlink.trade.domain.convert.pay.TradePayCustomerVoConvert;
import cn.xianlink.trade.domain.vo.platform.OrderBusiAmtVo;
import cn.xianlink.trade.domain.vo.platform.OrderBusiTotalVo;
import cn.xianlink.trade.domain.vo.TradePayCustomerVo;
import cn.xianlink.trade.service.ITradePayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;


/**
 * 结算单 - 查询已支付数据
 * <AUTHOR>
 * @date 2024-05-28
 * @folder 采集平台(小程序)/客户资金/结算单
 *
 */
@Tag(name = "结算单")
@Validated
@CustomLog
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/accAvail")
public class PlatformTradeAccAvailController extends BaseController {
    private final transient ITradePayService tradePayService;
    private final transient PlatformTradeUtils platformTradeUtils;
    @DubboReference
    private transient RemoteReportLossService remoteReportLossService;
    @DubboReference
    private transient RemoteRefundRecordService remoteRefundRecordService;
    @DubboReference
    private transient RemoteOrderService remoteOrderService;

    @Operation(summary = "查询")
    @PostMapping("/page")
    public R<TableDataInfo<TradePayCustomerVo>> page(@RequestBody TradePayCustomerQueryBo bo) {
        RemoteBaseDataVo vo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (vo == null) {
            throw new ServiceException("用户未登录");
        }
        bo.setCustomerId(vo.getId());
        return R.ok(tradePayService.queryCustomerPage(bo));
    }

    @Operation(summary = "订单金额")
    @PostMapping("/order")
    public R<OrderBusiTotalVo> order(@Validated @RequestBody OrderBusiTotalBo bo) {
        RemoteBaseDataVo vo = platformTradeUtils.getLoginCustomer(bo.getCustomerId(), bo.getUserId());
        if (vo == null) {
            throw new ServiceException("用户未登录");
        }
        bo.setCustomerId(vo.getId());
        OrderBusiTotalVo totalVo = new OrderBusiTotalVo();
        totalVo.setPay(TradePayCustomerVoConvert.INSTANCE.toConvert(tradePayService.queryByNo(bo.getOrderNo())));
        totalVo.setOrder(getOrder(bo));
        totalVo.setRefunds(new ArrayList<>());
        totalVo.getRefunds().add(getCancelRefund(bo));
        totalVo.getRefunds().add(getRegionRefund(bo));
        totalVo.getRefunds().add(getDiffRefund(bo));
        totalVo.getRefunds().add(getCityRefund(bo));
        totalVo.getRefunds().add(getLossRefund(bo));
        return R.ok(totalVo);
    }

    private OrderBusiAmtVo getOrder(OrderBusiTotalBo bo) {
        RemoteOrderInfoVo orderInfoVo = remoteOrderService.getOrderInfo(bo.getOrderId());
        OrderBusiAmtVo order = OrderBusiAmtVo.createOrderBusiAmtVo(orderInfoVo);
        order.setBusiId(bo.getOrderId());
        order.setBusiDesc("订单");
        return order;
    }

    private OrderBusiAmtVo getCancelRefund(OrderBusiTotalBo bo) {
        OrderBusiAmtVo order = new OrderBusiAmtVo();
        RemoteOrderBusiAmtVo amtVo = remoteRefundRecordService.getCancelRefund(bo.getOrderId());
        BeanUtil.copyProperties(amtVo, order);
        order.setBusiId(bo.getOrderId());
        order.setBusiDesc("退货退款-取消订单");
        return order;
    }

    private OrderBusiAmtVo getRegionRefund(OrderBusiTotalBo bo) {
        OrderBusiAmtVo order = new OrderBusiAmtVo();
        RemoteOrderBusiAmtVo amtVo = remoteRefundRecordService.getRegionRefund(bo.getOrderId());
        BeanUtil.copyProperties(amtVo, order);
        order.setBusiId(bo.getOrderId());
        order.setBusiDesc("退货退款-供应商缺货");
        return order;
    }

    private OrderBusiAmtVo getDiffRefund(OrderBusiTotalBo bo) {
        OrderBusiAmtVo order = new OrderBusiAmtVo();
        RemoteOrderBusiAmtVo amtVo = remoteRefundRecordService.getDiffRefund(bo.getOrderId());
        BeanUtil.copyProperties(amtVo, order);
        order.setBusiId(bo.getOrderId());
        order.setBusiDesc("退货退款-差额退");
        return order;
    }

    private OrderBusiAmtVo getCityRefund(OrderBusiTotalBo bo) {
        OrderBusiAmtVo order = new OrderBusiAmtVo();
        RemoteOrderBusiAmtVo amtVo = remoteRefundRecordService.getCityRefund(bo.getOrderId());
        BeanUtil.copyProperties(amtVo, order);
        order.setBusiId(bo.getOrderId());
        order.setBusiDesc("退货退款-城市仓少货");
        return order;
    }

    private OrderBusiAmtVo getLossRefund(OrderBusiTotalBo bo) {
        OrderBusiAmtVo order = new OrderBusiAmtVo();
        RemoteOrderBusiAmtVo amtVo = remoteReportLossService.selectRefundByOrderId(bo.getOrderId());
        BeanUtil.copyProperties(amtVo, order);
        order.setBusiId(bo.getOrderId());
        order.setBusiDesc("报损退款");
        return order;
    }
}
