package cn.xianlink.trade.controller.platform;

import cn.hutool.core.io.IoUtil;
import cn.xianlink.common.api.enums.trade.CashInfStatusEnum;
import cn.xianlink.common.json.utils.JsonUtils;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.channel.ChannelsContext;
import cn.xianlink.trade.comm.youwei.YouweiCommServiceImpl;
import cn.xianlink.trade.domain.bo.TradeCommBankBo;
import cn.xianlink.trade.domain.bo.comm.TradeCommCashBo;
import cn.xianlink.trade.domain.vo.comm.TradeCommCashVo;
import cn.xianlink.trade.service.ITradeCommBankService;
import cn.xianlink.trade.service.ITradeCommCashService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.List;


/**
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Tag(name = "有为白名单类")
@CustomLog
@RequiredArgsConstructor
@RestController
@RequestMapping("/white/ywNotify")
public class WhitePlatformYwNotifyController extends BaseController {

    private final static String YW_SUCCESS = "SUCCESS";
    private final static String YW_FAILURE = "FAILURE";
    private final transient ChannelsContext channelsContext;
    private final transient YouweiCommServiceImpl youweiCommService;
    private final transient ITradeCommBankService tradeCommBankService;
    private final transient ITradeCommCashService tradeCommCashService;

    @Operation(summary = "有为充值回调")
    @PostMapping("/payNotify")
    public String payNotify(HttpServletRequest request) throws IOException {
        channelsContext.initWebUserToken();
        String reqBody = IoUtil.read(request.getInputStream(), Charset.forName(request.getCharacterEncoding()));
        try {
            youweiCommService.payCallback(reqBody, request);
            return YW_SUCCESS;
        } catch (Exception e) {
            log.keyword("ywCallback").error("有为充值回调", e);
        }
        return YW_FAILURE;
    }

    @Operation(summary = "有为签约回调")
    @PostMapping("/signNotify")
    public String signNotify(HttpServletRequest request) throws IOException {
        channelsContext.initWebUserToken();
        String reqBody = IoUtil.read(request.getInputStream(), Charset.forName(request.getCharacterEncoding()));
        try {
            TradeCommBankBo bo = youweiCommService.signCallback(reqBody, request);
            tradeCommBankService.updateSignCallback(bo);
        } catch (Exception e) {
            log.keyword("ywCallback").error("有为签约回调", e);
        }
        return YW_SUCCESS;
    }

    @Operation(summary = "有为发薪回调")
    @PostMapping("/wagePayNotify")
    public String wagePayNotify(HttpServletRequest request) throws IOException {
        channelsContext.initWebUserToken();
        String reqBody = IoUtil.read(request.getInputStream(), Charset.forName(request.getCharacterEncoding()));
        try {
            TradeCommCashBo cashBo = youweiCommService.wagePayCallback(reqBody, request);
            TradeCommCashVo tvo = tradeCommCashService.queryByMerchNo(cashBo.getYwMerchNo());
            if (tvo == null) {
                log.keyword(cashBo.getYwMerchNo(), "ywCallback").error("有为发薪回调，单据不存在 {}", JsonUtils.toJsonString(cashBo));
            } else if (CashInfStatusEnum.SUCCESS.getCode().equals(tvo.getYwInfStatus())) {
                log.keyword(tvo.getCashNo(), "ywCallback").warn("有为发薪回调，单据已完成，不处理 {}", JsonUtils.toJsonString(cashBo));
            } else {
                log.keyword(tvo.getCashNo(), "ywCallback").info("有为发薪回调，单据开始处理");
                tradeCommCashService.updateCommInfData(cashBo.setId(tvo.getId()), tvo);
            }
            return YW_SUCCESS;
        } catch (Exception e) {
            log.keyword("ywCallback").error("有为发薪回调", e);
        }
        return YW_FAILURE;
    }

    @Operation(summary = "有为退票回调")
    @PostMapping("/wageRefundNotify")
    public String wageRefundNotify(HttpServletRequest request) throws IOException {
        channelsContext.initWebUserToken();
        String reqBody = IoUtil.read(request.getInputStream(), Charset.forName(request.getCharacterEncoding()));
        try {
            List<TradeCommCashBo> cashBos = youweiCommService.wageRefundCallback(reqBody, request);
            for (TradeCommCashBo cashBo : cashBos) {
                TradeCommCashVo tvo = tradeCommCashService.queryByMerchNo(cashBo.getYwMerchNo());
                if (tvo == null) {
                    log.keyword(cashBo.getYwMerchNo(), "ywCallback").error("有为退票回调，单据不存在 {}", JsonUtils.toJsonString(cashBo));
                } else if (CashInfStatusEnum.SUCCESS.getCode().equals(tvo.getYwInfStatus())) {
                    log.keyword(tvo.getCashNo(), "ywCallback").warn("有为退票回调，单据已完成，不处理 {}", JsonUtils.toJsonString(cashBo));
                } else {
                    log.keyword(tvo.getCashNo(), "ywCallback").info("有为退票回调，单据开始处理");
                    tradeCommCashService.updateCommRefundData(cashBo.setId(tvo.getId()));
                }
            }
            return YW_SUCCESS;
        } catch (Exception e) {
            log.keyword("ywCallback").error("有为退票回调", e);
        }
        return YW_FAILURE;
    }

}