package cn.xianlink.trade.controller.admin;

import cn.xianlink.trade.service.ISupportMessageService;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/support/message")
@RequiredArgsConstructor
public class TestSupportMessageController {

    private final ISupportMessageService supportMessageService;

    private static final String DEFAULT_MESSAGE_BODY = "{\"Content\":\"你好\",\"CreateTime\":1751881500,\"ToUserName\":\"gh_ec67c6255831\",\"FromUserName\":\"oP8WG6yS93lWss_VPGVkSnIz1V9Y\",\"MsgType\":\"text\",\"MsgId\":25080915546472102}";

    @PostMapping("/handle")
    public String handleMessage(@RequestBody(required = false) String messageBody) {
        String finalMessageBody = messageBody;
        if (!StringUtils.hasText(finalMessageBody)) {
            finalMessageBody = DEFAULT_MESSAGE_BODY;
        }
        supportMessageService.handleMessage(finalMessageBody);
        return "Message handled";
    }
} 