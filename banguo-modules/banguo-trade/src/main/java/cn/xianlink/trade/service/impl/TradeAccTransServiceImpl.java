package cn.xianlink.trade.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.api.util.BaseEntityAutoFill;
import cn.xianlink.trade.api.domain.bo.OrderPayJobQueryBo;
import cn.xianlink.trade.api.domain.bo.RemoteAccTransItemBo;
import cn.xianlink.trade.api.domain.vo.RemoteTradeAccTransVo;
import cn.xianlink.trade.comm.CommServiceHelper;
import cn.xianlink.trade.config.properties.ChannelsProperties;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.*;
import cn.xianlink.trade.domain.bo.*;
import cn.xianlink.trade.domain.bo.sup.*;
import cn.xianlink.trade.domain.vo.*;
import cn.xianlink.trade.domain.vo.comm.TradeCommTransVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAcctDateVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptTransRowVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptTransVo;
import cn.xianlink.trade.mapper.*;
import cn.xianlink.trade.service.ITradeAccCashService;
import cn.xianlink.trade.service.ITradeAccSplitService;
import cn.xianlink.trade.service.ITradeAccTransService;
import cn.xianlink.trade.service.biz.TradeBaseUtilBizService;
import cn.xianlink.trade.utils.MapstructPageUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分账变更流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class TradeAccTransServiceImpl implements ITradeAccTransService {

    private final transient ChannelsProperties channelsProperties;
    private final transient TradeAccTransMapper baseMapper;
    private final transient TradeAccSplitMapper tradeAccSplitMapper;
    private final transient TradeAccAccountMapper tradeAccAccountMapper;
    private final transient TradeCommTransMapper tradeCommTransMapper;
    private final transient TradeCustAccountMapper tradeCustAccountMapper;
    private final transient TradeAccAvailMapper tradeAccAvailMapper;
    private final transient TradeAccCashMapper tradeAccCashMapper;
    private final transient ITradeAccSplitService tradeAccSplitService;
    private final transient ITradeAccCashService tradeAccCashService;
    private final transient TradeBaseUtilBizService tradeBaseUtilBizService;
    private final transient CommServiceHelper commServiceHelper;

    /**
     * 查询分账变更流水列表
     */

    @Override
    @BaseEntityAutoFill
    public List<TradeAccTransAvailVo> queryAvailTransList(boolean isCash, Long availId, String supplierCode) {
        List<Long> availIds;
        if (isCash) {
            availIds = tradeAccAvailMapper.selectList(Wrappers.lambdaQuery(TradeAccAvail.class)
                    .select(TradeAccAvail::getId).eq(TradeAccAvail::getCashId, availId))
                    .stream().map(TradeAccAvail::getId).toList();
        } else {
            availIds = Collections.singletonList(availId);
        }
        if (availIds.size() == 0) {
            return new ArrayList<>();
        }
        return MapstructUtils.convert(baseMapper.selectList(Wrappers.lambdaUpdate(TradeAccTrans.class)
                .in(TradeAccTrans::getAvailId, availIds)
                .eq(StringUtils.isNotBlank(supplierCode), TradeAccTrans::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode())
                .eq(StringUtils.isNotBlank(supplierCode), TradeAccTrans::getOrgCode, supplierCode)
                .orderByAsc(TradeAccTrans::getAvailId, TradeAccTrans::getId)), TradeAccTransAvailVo.class);
    }

    /**
     * 查询供应商变更流水列表
     */
    @Override
    public TableDataInfo<TradeAccSupAvailTransVo> querySupTransPageList(TradeAccSupAvailTransQueryBo bo) {
        LambdaQueryWrapper<TradeAccTrans> lqw = buildQueryWrapper(bo);
        Page<TradeAccTransVo> result = baseMapper.selectVoPage(bo.build(), lqw);
        return TableDataInfo.build(MapstructPageUtils.convert(result, TradeAccSupAvailTransVo.class));
    }
    /**
     * 查询供应商变更流水列表
     */
    @Override
    public List<TradeAccSupAvailTransVo> querySupTransList(TradeAccSupAvailTransQueryBo bo) {
        LambdaQueryWrapper<TradeAccTrans> lqw = buildQueryWrapper(bo);
        return MapstructUtils.convert(baseMapper.selectVoList(lqw), TradeAccSupAvailTransVo.class);
    }

    private LambdaQueryWrapper<TradeAccTrans> buildQueryWrapper(TradeAccSupAvailTransQueryBo bo) {
        LambdaQueryWrapper<TradeAccTrans> lqw = Wrappers.lambdaQuery();
        lqw.eq(TradeAccTrans::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode());
        lqw.eq(TradeAccTrans::getOrgCode, bo.getOrgCode());
        lqw.eq(TradeAccTrans::getAcctOrgId, bo.getAcctOrgId());
        lqw.in(TradeAccTrans::getStatus, Arrays.asList(AccountStatusEnum.FREEZE.getCode(), AccountStatusEnum.AVAIL.getCode()));
        lqw.between(TradeAccTrans::getTransDate, bo.getTransDateStart(), bo.getTransDateEnd());
        lqw.eq(StringUtils.isNotBlank(bo.getBusiType()), TradeAccTrans::getBusiType, bo.getBusiType());
        lqw.eq(StringUtils.isNotBlank(bo.getRelateNo()), TradeAccTrans::getRelateNo, bo.getRelateNo());
        lqw.eq(StringUtils.isNotBlank(bo.getSplitNo()), TradeAccTrans::getSplitNo, bo.getSplitNo());
        lqw.eq(StringUtils.isNotBlank(bo.getOutAcctCode()), TradeAccTrans::getOutAcctCode, bo.getOutAcctCode());
        lqw.eq(bo.getStatus() != null, TradeAccTrans::getStatus, bo.getStatus());
        lqw.orderByDesc(TradeAccTrans::getId);
        return lqw;
    }

    /**
     * 查询供应商账务总
     */
    @Override
    @BaseEntityAutoFill
    public TableDataInfo<TradeAccAccountVo> customSupPageList(TradeAccSupAccountQueryBo queryBo) {
        Page<TradeAccTrans> page = queryBo.build();
        page.setOptimizeJoinOfCountSql(false);
        Page<TradeAccAccountVo> result = baseMapper.customSupList(page, buildQueryWrapper(queryBo));
        return TableDataInfo.build(result);
    }
    /**
     * 查询供应商账务总
     */
    @Override
    @BaseEntityAutoFill
    public List<TradeAccAccountVo> customSupList(TradeAccSupAccountQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccTrans> lqw = buildQueryWrapper(queryBo);
        return baseMapper.customSupList(lqw);
    }

    private LambdaQueryWrapper<TradeAccTrans> buildQueryWrapper(TradeAccSupAccountQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccTrans> lqw = Wrappers.lambdaQuery();
        lqw.between(TradeAccTrans::getTransDate, queryBo.getSaleDateStart(), queryBo.getSaleDateEnd());
        lqw.eq(TradeAccTrans::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode());
        lqw.in(CollectionUtil.isNotEmpty(queryBo.getOrgCodes()), TradeAccTrans::getOrgCode, queryBo.getOrgCodes());
//        if (CollectionUtil.isNotEmpty(queryBo.getAcctOrgCodes())) {
//            lqw.in(TradeAccAccount::getAcctOrgId, tradeBaseUtilBizService.queryIdsByCodes(queryBo.getAcctOrgCodes(), BaseTypeEnum.REGION_WH.getCode()));
//        }
        lqw.eq(queryBo.getStatus() != null, TradeAccTrans::getStatus, queryBo.getStatus() );
        lqw.eq(TradeAccTrans::getDelFlag, YNStatusEnum.DISABLE.getCode());
        return lqw;
    }
    /**
     * 资金账户内业务单查询
     */
    @Override
    public TableDataInfo<TradeAccTransVo> customSupAccountTransPageList(SupTradeAccSupAccountBusiQueryBo bo) {
        LambdaQueryWrapper<TradeAccTrans> lqw = buildQueryWrapper(bo);
        Page<TradeAccTrans> page = bo.build();
        page.setOptimizeJoinOfCountSql(false);
        return TableDataInfo.build(baseMapper.customSupAccountTransPageList(page, lqw));
    }
    /**
     * 资金账户内业务单查询
     */
    @Override
    public List<TradeAccTransVo> customSupAccountTransIdsList(SupTradeAccSupAccountBusiQueryBo bo, List<TradeAccTransVo> list) {
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TradeAccTrans> lqw = buildQueryWrapper(bo);
        lqw.in(TradeAccTrans::getSkuId, list.stream().map(TradeAccTransVo::getSkuId).collect(Collectors.toSet()).stream().toList());
        List<TradeAccTransVo> result = baseMapper.customSupAccountTransIdsList(lqw);
        LambdaQueryWrapper<TradeAccTrans> sumLqw = buildQueryWrapper(bo);
        sumLqw.in(TradeAccTrans::getTransDate, list.stream().map(TradeAccTransVo::getTransDate).collect(Collectors.toSet()).stream().toList());
        Map<LocalDate, TradeAccTransVo> dateMap = baseMapper.customSupAccountTransSumList(sumLqw)
                .stream().collect(Collectors.toMap(TradeAccTransVo::getTransDate, Function.identity()));
        if (Constants.NegativeFields.contains(bo.getBusiField())) {
            dateMap.values().forEach(vo -> vo.setTransAmt(vo.getTransAmt().negate()));
        }
        result.forEach(vo -> vo.setTransAmt(dateMap.get(vo.getTransDate()).getTransAmt()));
        return result;
    }
    /**
     * 资金账户内划转单查询
     */
    @Override
    public TableDataInfo<TradeAccTransVo> querySupAccountTransRecordPageList(SupTradeAccSupAccountBusiQueryBo bo) {
        LambdaQueryWrapper<TradeAccTrans> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(TradeAccTrans::getId);
        IPage<TradeAccTransVo> result = baseMapper.selectVoPage(bo.build(), lqw);
        return TableDataInfo.build(result);
    }


    private LambdaQueryWrapper<TradeAccTrans> buildQueryWrapper(SupTradeAccSupAccountBusiQueryBo bo) {
        LambdaQueryWrapper<TradeAccTrans> lqw = Wrappers.lambdaQuery();
        // 当日看不到（原供应商账户查询）   取消的查不到
        lqw.in(TradeAccTrans::getStatus, Arrays.asList(AccountStatusEnum.AVAIL.getCode(), AccountStatusEnum.CHECK.getCode(), AccountStatusEnum.CASH.getCode()));
        lqw.eq(TradeAccTrans::getBusiField, bo.getBusiField());
        lqw.eq(TradeAccTrans::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode());
        lqw.eq(TradeAccTrans::getOrgCode, bo.getSupplierCode());
        lqw.eq(bo.getSupplierDeptId() != null && bo.getSupplierDeptId() != 0L, TradeAccTrans::getDeptId, bo.getSupplierDeptId());
        lqw.like(StringUtils.isNotBlank(bo.getSkuName()), TradeAccTrans::getSkuName, bo.getSkuName());
        lqw.eq(bo.getAvailId() != null, TradeAccTrans::getAvailId, bo.getAvailId());
        lqw.eq(bo.getStatus() != null, TradeAccTrans::getStatus, bo.getStatus()); // 有结算单 AvailId 时不控制
        lqw.eq(TradeAccTrans::getDelFlag, YNStatusEnum.DISABLE.getCode());
        // 不能加排序， 和 自定义查询中冲突
        return lqw;
    }

    /**
     * 资金账户内业务单查询
     */
    @Override
    public TableDataInfo<TradeAccTransVo> querySupAccountTransRecordPageList(SupTradeAccSupAccountBusiRecordQueryBo bo) {
        IPage<TradeAccTransVo> result = baseMapper.selectVoPage(bo.build(), buildQueryWrapper(bo));
        if (PayBusiTypeEnum.CUSTOM_ORDER.getField().equals(bo.getBusiField())) {
            return TableDataInfo.build(result.getRecords().stream().peek(vo -> vo.setTransId(vo.getRelateId())).toList(), result.getTotal());
        }
        return TableDataInfo.build(result);
    }

    private LambdaQueryWrapper<TradeAccTrans> buildQueryWrapper(SupTradeAccSupAccountBusiRecordQueryBo bo) {
        LambdaQueryWrapper<TradeAccTrans> lqw = Wrappers.lambdaQuery();
        // 当日看不到（原供应商账户查询）   取消的查不到
        lqw.in(TradeAccTrans::getStatus, Arrays.asList(AccountStatusEnum.AVAIL.getCode(), AccountStatusEnum.CHECK.getCode(), AccountStatusEnum.CASH.getCode()));
        lqw.eq(TradeAccTrans::getSkuId, bo.getSkuId());
        lqw.eq(bo.getAvailId() != null, TradeAccTrans::getAvailId, bo.getAvailId());
        lqw.eq(bo.getStatus() != null, TradeAccTrans::getStatus, bo.getStatus()); // 有结算单 AvailId 时不控制
        lqw.eq(bo.getSupplierDeptId() != null && bo.getSupplierDeptId() != 0L, TradeAccTrans::getDeptId, bo.getSupplierDeptId());
        lqw.eq(TradeAccTrans::getBusiField, bo.getBusiField());
        lqw.eq(TradeAccTrans::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode());
        lqw.eq(TradeAccTrans::getOrgCode, bo.getSupplierCode());
        lqw.orderByDesc(TradeAccTrans::getId);
        return lqw;
    }

    /**
     * 资金账户内业务单查询
     */
    @Override
    public TableDataInfo<SupTradeAccSupDeptAcctDateVo> customSupDeptAcctDatePageList(SupTradeAccSupDeptAcctDetailQueryBo bo) {
        List<LocalDate> dates = getRangeDate(bo.getTransDateStart(), bo.getTransDateEnd());
        if (dates.size() == 0) {
            return TableDataInfo.build();
        }
        Page<TradeAccTrans> page = bo.build();
        page.setOptimizeJoinOfCountSql(false);
        return TableDataInfo.build(baseMapper.customSupDeptAcctDatePageList(page, dates, customSupDeptAcctWrapper(bo)));
    }

    private List<LocalDate> getRangeDate(LocalDate start, LocalDate end) {
        List<LocalDate> list = new ArrayList<>();
        LocalDate current = start;
        while (!current.isAfter(end)) {
            list.add(current);
            current = current.plusDays(1);
        }
        return list;
    }

    private LambdaQueryWrapper<TradeAccTrans> customSupDeptAcctWrapper(SupTradeAccSupDeptAcctDetailQueryBo bo) {
        LambdaQueryWrapper<TradeAccTrans> lqw = Wrappers.lambdaQuery();
        lqw.eq(TradeAccTrans::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode());
        lqw.eq(TradeAccTrans::getOrgCode, bo.getSupplierCode());
        lqw.in(TradeAccTrans::getStatus, Arrays.asList(AccountStatusEnum.FREEZE.getCode(), AccountStatusEnum.AVAIL.getCode(),
                AccountStatusEnum.CHECK.getCode(), AccountStatusEnum.CASH.getCode()));
        lqw.eq(bo.getDeptId() != null && bo.getDeptId() != 0L, TradeAccTrans::getDeptId, bo.getDeptId());
        if (bo.getTransDateStart() != null) {
            if (bo.getTransDateEnd() != null) {
                lqw.between(TradeAccTrans::getTransDate, bo.getTransDateStart(), bo.getTransDateEnd());
            } else {
                lqw.ge(TradeAccTrans::getTransDate, bo.getTransDateStart());
            }
        } else if (bo.getTransDateEnd() != null) {
            lqw.le(TradeAccTrans::getTransDate, bo.getTransDateEnd());
        }
        if (bo.getTransDates() != null) {
            lqw.between(TradeAccTrans::getTransDate, bo.getTransDates().get(0), bo.getTransDates().get(1));
        }
        lqw.eq(TradeAccTrans::getDelFlag, YNStatusEnum.DISABLE.getCode());
        return lqw;
    }

    /**
     * 资金账户内业务单明细查询
     */
    @Override
    public TableDataInfo<SupTradeAccSupDeptTransVo> customSupDeptTransPageList(SupTradeAccSupDeptAcctDetailQueryBo bo) {
        Page<TradeAccTrans> page = bo.build();
        page.setOptimizeJoinOfCountSql(false);
        LambdaQueryWrapper<TradeAccTrans> lqw = customSupDeptAcctWrapper(bo);
        lqw.like(StringUtils.isNotBlank(bo.getSkuName()), TradeAccTrans::getSkuName, bo.getSkuName());
        lqw.eq(bo.getStatus() != null, TradeAccTrans::getStatus, bo.getStatus());
        return TableDataInfo.build(baseMapper.customSupDeptTransPageList(page, lqw));
    }
    /**
     * 资金账户内业务单明细查询
     */
    @Override
    public List<SupTradeAccSupDeptTransRowVo> customSupDeptTransList(SupTradeAccSupDeptAcctDetailQueryBo bo) {
        LambdaQueryWrapper<TradeAccTrans> lqw = customSupDeptAcctWrapper(bo);
        lqw.select(TradeAccTrans::getTransType, TradeAccTrans::getBusiField, TradeAccTrans::getTransDate,
                TradeAccTrans::getStatus, TradeAccTrans::getSkuId, TradeAccTrans::getSkuName, TradeAccTrans::getRelateNo,
                TradeAccTrans::getRelateId, TradeAccTrans::getTransId, TradeAccTrans::getTransAmt);
        lqw.like(StringUtils.isNotBlank(bo.getSkuName()), TradeAccTrans::getSkuName, bo.getSkuName());
        lqw.eq(bo.getStatus() != null, TradeAccTrans::getStatus, bo.getStatus());
        lqw.orderByDesc(TradeAccTrans::getTransDate).orderByAsc(TradeAccTrans::getStatus, TradeAccTrans::getSkuId);
        return MapstructUtils.convert(baseMapper.selectVoList(lqw), SupTradeAccSupDeptTransRowVo.class);
    }
    /**
     * 资金账户内业务单明细查询
     */
    @Override
    public List<SupTradeAccSupDeptTransRowVo> customSupDeptTransList(SupTradeAccSupDeptAcctRecordQueryBo bo) {
        LambdaQueryWrapper<TradeAccTrans> lqw = customSupDeptTransRecordWrapper(bo);
        lqw.select(TradeAccTrans::getTransType, TradeAccTrans::getBusiField, TradeAccTrans::getTransDate,
                TradeAccTrans::getStatus, TradeAccTrans::getSkuId, TradeAccTrans::getSkuName, TradeAccTrans::getRelateNo,
                TradeAccTrans::getRelateId, TradeAccTrans::getTransId, TradeAccTrans::getTransAmt);
        lqw.orderByDesc(TradeAccTrans::getTransDate).orderByAsc(TradeAccTrans::getStatus, TradeAccTrans::getSkuId);
        return MapstructUtils.convert(baseMapper.selectVoList(lqw), SupTradeAccSupDeptTransRowVo.class);
    }

    private LambdaQueryWrapper<TradeAccTrans> customSupDeptTransRecordWrapper(SupTradeAccSupDeptAcctRecordQueryBo bo) {
        LambdaQueryWrapper<TradeAccTrans> lqw = Wrappers.lambdaQuery();
        lqw.eq(TradeAccTrans::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode());
        lqw.eq(TradeAccTrans::getOrgCode, bo.getSupplierCode());
        lqw.eq(TradeAccTrans::getStatus, bo.getStatus());
        lqw.eq(bo.getDeptId() != null && bo.getDeptId() != 0L, TradeAccTrans::getDeptId, bo.getDeptId());
        lqw.eq(TradeAccTrans::getTransDate, bo.getTransDate());
        lqw.eq(TradeAccTrans::getSkuId, bo.getSkuId());
        lqw.eq(TradeAccTrans::getRelateType, AccountTransTypeEnum.OP.getCode());
        lqw.eq(TradeAccTrans::getDelFlag, YNStatusEnum.DISABLE.getCode());
        return lqw;
    }

    /**
     * 资金账户内划转明细查询
     */
    @Override
    public TableDataInfo<TradeAccTransVo> customSupDeptTransTransferList(SupTradeAccSupDeptAcctDetailQueryBo bo) {
        LambdaQueryWrapper<TradeAccTrans> lqw = customSupDeptAcctWrapper(bo);
        lqw.in(TradeAccTrans::getTransType, Constants.TransferTypes);
        lqw.eq(StringUtils.isNotBlank(bo.getTransType()), TradeAccTrans::getTransType, bo.getTransType());
        lqw.eq(bo.getStatus() != null, TradeAccTrans::getStatus, bo.getStatus());
        lqw.eq(TradeAccTrans::getRelateType, AccountTransTypeEnum.TI.getCode());
        lqw.orderByDesc(TradeAccTrans::getTransDate);
        IPage<TradeAccTransVo> result = baseMapper.selectVoPage(bo.build(), lqw);
        return TableDataInfo.build(result);
    }
    /**
     * 查询结算单列表
     */
    @Override
    public List<TradeAccTransVo> queryStatusListByIds(TradeAccTransChangeBo bo, List<Long> transIds) {
        if (CollectionUtil.isEmpty(transIds)) {
            return new ArrayList<>();
        }
        return baseMapper.selectVoList(Wrappers.lambdaQuery(TradeAccTrans.class).in(TradeAccTrans::getId, transIds));
    }

    @Override
    public List<TradeAccTransVo> queryStatusListByNo(TradeAccTransChangeBo bo, TradeAccTransStatusBo sbo) {
        LambdaQueryWrapper<TradeAccTrans> lqw = Wrappers.lambdaQuery(TradeAccTrans.class)
                .eq(TradeAccTrans::getTransNo, bo.getTransNo()).in(TradeAccTrans::getTransType, bo.getTransTypes());
        if (sbo != null) {
            lqw.in(CollectionUtil.isNotEmpty(sbo.getBusiTypes()), TradeAccTrans::getBusiType, sbo.getBusiTypes())
                    .in(CollectionUtil.isNotEmpty(sbo.getSplitNos()), TradeAccTrans::getSplitNo, sbo.getSplitNos())
                    .eq(StringUtils.isNotEmpty(sbo.getOrgCode()), TradeAccTrans::getOrgCode, sbo.getOrgCode())
                    .eq(StringUtils.isNotEmpty(sbo.getRelateType()), TradeAccTrans::getRelateType, sbo.getRelateType())
                    .eq(StringUtils.isNotEmpty(sbo.getRelateNo()), TradeAccTrans::getRelateNo, sbo.getRelateNo())
                    .eq(sbo.getAvailId() != null, TradeAccTrans::getAvailId, sbo.getAvailId())
                    .eq(sbo.getCashId() != null, TradeAccTrans::getCashId, sbo.getCashId())
                    .eq(sbo.getPreStatus() != null, TradeAccTrans::getStatus, sbo.getPreStatus());
        }
        // 划转单优先供应商，优先负值 -->
        lqw.orderByDesc(TradeAccTrans::getOrgType).orderByAsc(TradeAccTrans::getTransAmt);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<TradeAccTransVo> queryStatusListByRelate(TradeAccTransChangeBo bo) {
        //  bo.getTrans() 为空列表， 则该单据全部未转的全部生成
        return baseMapper.queryStatusListByRelate(bo.getTransNo(), bo.getTransTypes().get(0), bo.getTrans());
    }

    @Override
    public List<TradeAccTransVo> queryOrderRelateList(String relateNo, List<String> transTypes, List<TradeAccTrans> orgs) {
        // 每次查询都包含佣金机构的数据
        List<TradeAccTrans> trans = new ArrayList<>();
        if (orgs != null) {
            trans.add(new TradeAccTrans().setOrgCode(commServiceHelper.getCommAccOrgCode())
                    .setOrgType(AccountOrgTypeEnum.REGION_WH.getCode()).setSkuId(0L));
            trans.addAll(orgs);
        }
        return baseMapper.queryOrderRelateList(relateNo, transTypes, trans);
    }

    @Override
    public List<TradeAccTransVo> queryOrderAvailTransList(String relateNo, List<String> splitNos) {
        if (CollectionUtil.isEmpty(splitNos)) {
            return new ArrayList<>();
        }
        return baseMapper.selectVoList(Wrappers.lambdaQuery(TradeAccTrans.class)
                .eq(TradeAccTrans::getRelateType, AccountTransTypeEnum.OP.getCode()).eq(TradeAccTrans::getRelateNo, relateNo)
                .in(TradeAccTrans::getSplitNo, splitNos).ne(TradeAccTrans::getStatus, AccountStatusEnum.CANCEL.getCode())
                .orderByAsc(TradeAccTrans::getId));  // 以id排序， 退款顺序查找
    }

    @Override
    public Map<String, TradeAccTransVo> getOrderTransferNos(String orderNo) {
        List<TradeAccTransVo> relateTransferVos = baseMapper.selectVoList(Wrappers.lambdaQuery(TradeAccTrans.class)
                .eq(TradeAccTrans::getRelateType, AccountTransTypeEnum.OP.getCode()).eq(TradeAccTrans::getRelateNo, orderNo)
                .in(TradeAccTrans::getTransType, Constants.TransferTypes).orderByAsc(TradeAccTrans::getId));  // 以id排序， 退款顺序查找
        // 从 relateTransferVos 中获取对应 TO
        Map<String, TradeAccTransVo> transferNos = new HashMap<>();
        for (Map.Entry<String, List<TradeAccTransVo>> entry : relateTransferVos.stream().collect(
                Collectors.groupingBy(TradeAccTransVo::getTransNo, Collectors.toList())).entrySet()) {
            TradeAccTransVo transVo = new TradeAccTransVo();
            for (TradeAccTransVo vo : entry.getValue()) {
                if (AccountTransTypeEnum.TI.getCode().equals(vo.getTransType())) {
                    transVo.setTransType(vo.getTransType());
                    transVo.setTransAmt(vo.getTransAmt());
                    transVo.setSplitAmt(vo.getSplitAmt());
					transVo.setTransMaxAmt(vo.getSplitAmt());
                    transVo.setSplitNo(vo.getSplitRelNo()); // 原订单的分账单号
                    transVo.setSplitRelNo(vo.getSplitRelNo());
                    transVo.setSplitRelAmt(vo.getSplitRelAmt());
                    transVo.setOutOrgCode(vo.getOutOrgCode());
                    transVo.setOutAcctCode(vo.getOutAcctCode());
                } else {
                    transVo.setSplitOrgCode(vo.getOutOrgCode());
                    transVo.setSplitAcctCode(vo.getOutAcctCode());
                }
            }
            // 入的分账号， 转成 订单的分账号
            transferNos.put(transVo.getSplitRelNo(), transVo);
        }
        return transferNos;
    }

    @Override
    public List<TradeAccTransVo> queryTransList(String transNo, List<String> transTypes) {
        return baseMapper.selectVoList(Wrappers.lambdaQuery(TradeAccTrans.class)
                .eq(TradeAccTrans::getTransNo, transNo).in(TradeAccTrans::getTransType, transTypes)
                .orderByAsc(TradeAccTrans::getId));
    }

    @Override
    public List<TradeAccTransVo> queryTransList(List<String> transNos, List<String> transTypes) {
        if (CollectionUtil.isEmpty(transNos)) {
            return new ArrayList<>();
        }
        return baseMapper.selectVoList(Wrappers.lambdaQuery(TradeAccTrans.class)
                .in(TradeAccTrans::getTransNo, transNos).in(TradeAccTrans::getTransType, transTypes)
                .orderByAsc(TradeAccTrans::getId));
    }

    @Override
    public List<TradeAccTransVo> querySupplierFreezeTrans(String orderNo) {
        return baseMapper.querySupplierFreezeTrans(orderNo, AccountTransTypeEnum.OP.getCode());
    }

    @Override
    public List<TradeAccTransVo> queryPayCancelTrans(Boolean isZero, String orderNo) {
        return baseMapper.queryPayCancelTrans(isZero, orderNo, null);
    }

    // 待结算数据生成到结算单 （1）
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAvailInit(Long availId, List<TradeAccTransAvaildsBo> trans) {
        if (CollectionUtil.isEmpty(trans)) {
            return;
        }
        Date nowTime = Convert.toDate(DateUtil.now());
        Set<String> allFields = tradeBaseUtilBizService.getAllFields();
        tradeAccAvailMapper.update(Wrappers.lambdaUpdate(TradeAccAvail.class)
                .set(TradeAccAvail::getUpdateTime, nowTime).eq(TradeAccAvail::getId, availId));  // 锁表
        baseMapper.update(Wrappers.lambdaUpdate(TradeAccTrans.class)
				/* 注释掉 2025.2.4 ， 当订单结算时，存在多个退款占用单， 同时占用总的退款额，会造成后续所有释放，和退款操作都失效
                .set(TradeAccTrans::getIsOccupy, 0) */
                .set(TradeAccTrans::getAvailId, availId).set(TradeAccTrans::getAvailTime, nowTime)
                .set(TradeAccTrans::getAvailNo, trans.get(0).getAvailNo())
                .in(TradeAccTrans::getId, trans.stream().map(TradeAccTransAvaildsBo::getTransId).toList())
                .eq(TradeAccTrans::getStatus, AccountStatusEnum.FREEZE.getCode())
                .eq(TradeAccTrans::getDelFlag, YNStatusEnum.DISABLE.getCode())
                .isNull(TradeAccTrans::getAvailId));
        tradeAccAvailMapper.updateAvailAmt(allFields, availId);
    }

    // 待结算数据生成到结算单 （1）
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAvailInitCancel(Long availId, List<TradeAccTransAvaildsBo> trans) {
        if (CollectionUtil.isEmpty(trans)) {
            return;
        }
        Date nowTime = Convert.toDate(DateUtil.now());
        Set<String> allFields = tradeBaseUtilBizService.getAllFields();
        tradeAccAvailMapper.update(Wrappers.lambdaUpdate(TradeAccAvail.class)
                .set(TradeAccAvail::getUpdateTime, nowTime).eq(TradeAccAvail::getId, availId));  // 锁表
        baseMapper.update(Wrappers.lambdaUpdate(TradeAccTrans.class)
                .set(TradeAccTrans::getAvailId, null).set(TradeAccTrans::getAvailTime, null).set(TradeAccTrans::getAvailNo, "")
                .in(TradeAccTrans::getId, trans.stream().map(TradeAccTransAvaildsBo::getTransId).toList())
                .eq(TradeAccTrans::getStatus, AccountStatusEnum.FREEZE.getCode())
                .eq(TradeAccTrans::getDelFlag, YNStatusEnum.DISABLE.getCode())
                .eq(TradeAccTrans::getAvailId, availId));
        tradeAccAvailMapper.updateAvailAmt(allFields, availId);  // 逐条更新
    }

    /**
     * 处理结算单为零的数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAvailComplete(TradeAccAvailBo bo) {
        // 1 更新结算单的提现状态
        Integer status;
        Date cashTime;
        Date nowTime = Convert.toDate(DateUtil.now());
        LambdaUpdateWrapper<TradeAccAvail> wrapper = Wrappers.lambdaUpdate(TradeAccAvail.class);
        wrapper.set(TradeAccAvail::getStatusTime, nowTime);
        if (BigDecimal.ZERO.compareTo(bo.getAvailAmt()) == 0) {
            cashTime = nowTime;
            status = AccountStatusEnum.CASH.getCode();
            wrapper.set(TradeAccAvail::getCashTime, nowTime)
                    .set(TradeAccAvail::getStatus, status)
                    .eq(TradeAccAvail::getAvailAmt, BigDecimal.ZERO)
                    .eq(TradeAccAvail::getStatus, AccountStatusEnum.FREEZE.getCode());
        } else {
            cashTime = null;
            status = AccountStatusEnum.AVAIL.getCode();
            wrapper.set(TradeAccAvail::getFinishTime, nowTime)
                    .set(TradeAccAvail::getStatus, status)
                    .ne(TradeAccAvail::getAvailAmt, BigDecimal.ZERO)
                    .eq(TradeAccAvail::getStatus, AccountStatusEnum.FREEZE.getCode());
        }
        wrapper.eq(TradeAccAvail::getId, bo.getId());
        int count = tradeAccAvailMapper.update(wrapper);
        if (count > 0) {
            updateAvailAccount(bo.getId(), cashTime, AccountStatusEnum.FREEZE.getCode(), status);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(TradeAccTransChangeBo bo) {
        if (CollectionUtil.isEmpty(bo.getTrans())) { // 没有可处理的数据
            return;
        }
        // 1 更新流水数据状态    保证 TradeAccTrans 中数据是同一个 transNo 中的
        Date nowTime = bo.getStatusTime() == null ? Convert.toDate(DateUtil.now()) : bo.getStatusTime();
        int count = baseMapper.update(Wrappers.lambdaUpdate(TradeAccTrans.class)
                .set(TradeAccTrans::getStatus, bo.getStatus()).set(TradeAccTrans::getStatusTime, nowTime)
                .set(AccountStatusEnum.INTI.getCode().equals(bo.getPreStatus()) &&
                        Arrays.asList(AccountStatusEnum.FREEZE.getCode(), AccountStatusEnum.CANCEL.getCode()).contains(bo.getStatus()),
                        TradeAccTrans::getFreezeTime, nowTime)
                .in(TradeAccTrans::getId, bo.getTrans().stream().map(TradeAccTransAvaildsBo::getTransId).toList())
                .eq(TradeAccTrans::getStatus, bo.getPreStatus())
                .eq(TradeAccTrans::getDelFlag, YNStatusEnum.DISABLE.getCode()));
        if (count != bo.getTrans().size()) {
            throw new ServiceException("单据状态已变化，刷新后再次处理");
        }
        List<Long> transIds = bo.getTrans().stream().map(TradeAccTransAvaildsBo::getTransId).toList();
        List<Long> commTransIds = bo.getTrans().stream().filter(vo -> vo.getCommissionAmt().compareTo(BigDecimal.ZERO)!=0)
                .map(TradeAccTransAvaildsBo::getTransId).toList();
        insertTransAcctByTrans(transIds, bo.getPreStatus(), bo.getStatus(), commTransIds, null);
        //
    }

    /**
     * 结算转提现 ( 维护 avail , trans 中的 cashNo )
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCashInit(SupTradeAccSupCashAvaildsBo bo) {
        if (CollectionUtil.isEmpty(bo.getAvailIds())) {
            return;
        }
        int status = AccountStatusEnum.AVAIL.getCode();
        int count = baseMapper.update(Wrappers.lambdaUpdate(TradeAccTrans.class)
                .set(TradeAccTrans::getCashId, bo.getCashId()).set(TradeAccTrans::getCashNo, bo.getCashNo())
                .in(TradeAccTrans::getAvailId, bo.getAvailIds())
                .isNull(TradeAccTrans::getCashId)
                .eq(TradeAccTrans::getStatus, status)
                .eq(TradeAccTrans::getDelFlag, YNStatusEnum.DISABLE.getCode()));
        if (count == 0) {
            throw new ServiceException("单据状态已变化，刷新后再次处理");
        }
        tradeAccAvailMapper.update(Wrappers.lambdaUpdate(TradeAccAvail.class)
                .set(TradeAccAvail::getCashId, bo.getCashId()).set(TradeAccAvail::getCashNo, bo.getCashNo())
                .in(TradeAccAvail::getId, bo.getAvailIds())
                .isNull(TradeAccAvail::getCashId)
                .eq(TradeAccAvail::getStatus, status)
                .eq(TradeAccAvail::getDelFlag, YNStatusEnum.DISABLE.getCode()));
        tradeAccCashService.updateCashAmt(bo.getCashId());
    }
    /**
     * 结算取消提现 ( 维护 avail , trans 中的 cashNo ，设置成 null)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCashInitCancel(SupTradeAccSupCashAvaildsBo bo) {
        if (CollectionUtil.isEmpty(bo.getAvailIds())) {
            return;
        }
        int status = AccountStatusEnum.AVAIL.getCode();
        int count = baseMapper.update(Wrappers.lambdaUpdate(TradeAccTrans.class)
                .set(TradeAccTrans::getCashId, null).set(TradeAccTrans::getCashNo, "")
                .in(TradeAccTrans::getAvailId, bo.getAvailIds())
                .eq(TradeAccTrans::getCashId, bo.getCashId())
                .eq(TradeAccTrans::getStatus, status)
                .eq(TradeAccTrans::getDelFlag, YNStatusEnum.DISABLE.getCode()));
        if (count == 0) {
            throw new ServiceException("单据状态已变化，刷新后再次处理");
        }
        tradeAccAvailMapper.update(Wrappers.lambdaUpdate(TradeAccAvail.class)
                .set(TradeAccAvail::getCashId, null).set(TradeAccAvail::getCashNo, "")
                .set(TradeAccAvail::getCashCheckTime, null)
                .in(TradeAccAvail::getId, bo.getAvailIds())
                .eq(TradeAccAvail::getCashId, bo.getCashId())
                .eq(TradeAccAvail::getStatus, status)
                .eq(TradeAccAvail::getDelFlag, YNStatusEnum.DISABLE.getCode()));
        tradeAccCashService.updateCashAmt(bo.getCashId());
    }

    /**
     * 提现单转审核状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCashCheck(TradeAccCashBo cashBo) {
        int preStatus = AccountStatusEnum.AVAIL.getCode();
        int status = AccountStatusEnum.CHECK.getCode();
        Date nowTime = Convert.toDate(DateUtil.now());
        int count = tradeAccCashMapper.update(Wrappers.lambdaUpdate(TradeAccCash.class)
                .set(TradeAccCash::getStatus, status)
                .set(TradeAccCash::getInfStatus, CashInfStatusEnum.CHECK.getCode())
                .set(TradeAccCash::getCashCheckTime, nowTime)
                .ne(TradeAccCash::getInfStatus, CashInfStatusEnum.SUCCESS.getCode())
                .eq(TradeAccCash::getStatus, preStatus)
                .eq(TradeAccCash::getId, cashBo.getId()));
        if (count == 0) {
            throw new ServiceException(String.format("提现单 %s 不是初始状态", cashBo.getCashNo()));
        }
        List<Long> availIds = tradeAccAvailMapper.selectVoList(Wrappers.lambdaQuery(TradeAccAvail.class)
                        .select(TradeAccAvail::getId, TradeAccAvail::getStatus)
                        .eq(TradeAccAvail::getCashId, cashBo.getId())).stream()
                .filter(vo-> preStatus == vo.getStatus()).map(TradeAccAvailVo::getId).toList();
        if (availIds.size() > 0) {
            tradeAccAvailMapper.update(Wrappers.lambdaUpdate(TradeAccAvail.class)
                    .set(TradeAccAvail::getStatus, status).set(TradeAccAvail::getStatusTime, nowTime)
                    .set(TradeAccAvail::getCashCheckTime, nowTime)
                    .in(TradeAccAvail::getId, availIds)
                    .eq(TradeAccAvail::getCashId, cashBo.getId())
                    .eq(TradeAccAvail::getStatus, preStatus));
            updateCashAccount(availIds, null, preStatus, status);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCash(TradeAccCashBo cashBo) {
        int preStatus = cashBo.getStatus(); // 支持 AVAIL 和 CHECK 状态
        int status = AccountStatusEnum.AVAIL.getCode();
        Date nowTime = Convert.toDate(DateUtil.now());
        int count = tradeAccCashMapper.delete(Wrappers.lambdaQuery(TradeAccCash.class)
                .eq(TradeAccCash::getId, cashBo.getId())
                .ne(TradeAccCash::getInfStatus, CashInfStatusEnum.SUCCESS.getCode())
                .eq(TradeAccCash::getStatus, preStatus));
        if (count == 0) {
            throw new ServiceException(String.format("提现单 %s 状态已改变", cashBo.getCashNo()));
        }
        List<Long> availIds = tradeAccAvailMapper.selectVoList(Wrappers.lambdaQuery(TradeAccAvail.class)
                        .select(TradeAccAvail::getId, TradeAccAvail::getStatus)
                        .eq(TradeAccAvail::getCashId, cashBo.getId())).stream()
                .filter(vo-> preStatus == vo.getStatus()).map(TradeAccAvailVo::getId).toList();
        if (AccountStatusEnum.CHECK.getCode().equals(preStatus) && availIds.size() > 0) {
            tradeAccAvailMapper.update(Wrappers.lambdaUpdate(TradeAccAvail.class)
                    .set(TradeAccAvail::getStatus, status).set(TradeAccAvail::getStatusTime, nowTime)
                    .set(TradeAccAvail::getCashCheckTime, null)
                    .in(TradeAccAvail::getId, availIds)
                    .eq(TradeAccAvail::getCashId, cashBo.getId())
                    .eq(TradeAccAvail::getStatus, preStatus));
            // 从审核状态转成 可提现
            updateCashAccount(availIds, null, preStatus, status);
        }
        // 移除提现 和 结算单关系
        updateCashInitCancel(new SupTradeAccSupCashAvaildsBo()
                .setCashId(cashBo.getId()).setCashNo(cashBo.getCashNo()).setAvailIds(availIds));
    }

    /**
     * 提现单调用接口前
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradeAccCashVo updateCashCheckByBo(TradeAccCashBo cashBo, TradeOrgBankRelaVo bankVo) {
        TradeAccCash update = new TradeAccCash();
        update.setInfStatus(CashInfStatusEnum.PROCESSING.getCode());
        update.setInfTime(Convert.toDate(DateUtil.now()));
        update.setInfRetries(cashBo.getInfRetries() + 1);
        // 记录提现时账户信息
        update.setOutOrgCode(bankVo.getOutOrgCode());
        update.setOutAcctCode(bankVo.getOutAcctCode());
        update.setBankAccName(bankVo.getBankAccName());
        update.setBankAccount(bankVo.getBankAccount());
        update.setBankBranchName(bankVo.getBankBranchName());
        update.setSplitNo(tradeBaseUtilBizService.getNextSplitNo(LocalDate.now()));
        int count = tradeAccCashMapper.update(update, Wrappers.lambdaUpdate(TradeAccCash.class)
                .eq(TradeAccCash::getId, cashBo.getId()).eq(TradeAccCash::getOrgCode, bankVo.getOrgCode())
                .eq(TradeAccCash::getOrgType, bankVo.getOrgType())
                .ne(TradeAccCash::getInfStatus, CashInfStatusEnum.SUCCESS.getCode())
                .eq(TradeAccCash::getStatus, cashBo.getStatus()));
        if (count == 0) {
            throw new ServiceException(String.format("提现单 %s 状态已改变", cashBo.getCashNo()));
        }
        TradeAccSplit accSplit = new TradeAccSplit();
        accSplit.setSplitNo(update.getSplitNo());
        accSplit.setTransType(AccountTransTypeEnum.CH.getCode());
        accSplit.setTransId(cashBo.getId());
        accSplit.setTransNo(cashBo.getCashNo());
        accSplit.setTransDate(cashBo.getCashDate());
        accSplit.setTransOrgId(0L);
        accSplit.setAcctOrgId(Long.parseLong(cashBo.getAcctOrgIds().split(";")[1]));
        accSplit.setTotalAmt(cashBo.getCashAmt());
        accSplit.setTransAmt(cashBo.getCashAmt());
        accSplit.setFeeAmt(cashBo.getOutFeeAmt());
        accSplit.setBusiType("");
        accSplit.setRelateType(accSplit.getTransType());
        accSplit.setRelateNo(accSplit.getTransNo());
        accSplit.setRelateAmt(accSplit.getTotalAmt());
        accSplit.setOutOrgCode(update.getOutOrgCode());
        accSplit.setOutAcctCode(update.getOutAcctCode());
        accSplit.setOrgCode(cashBo.getOrgCode());
        accSplit.setOrgType(cashBo.getOrgType());
        accSplit.setOrgId(cashBo.getOrgId());
		accSplit.setChannel("");
        accSplit.setInitTime(cashBo.getCreateTime());
        accSplit.setInfTime(update.getInfTime());
        accSplit.setInfStatus(RefundInfStatusEnum.PROCESSING.getCode());
		accSplit.setAcctDate(LocalDate.now());
        tradeAccSplitMapper.insert(accSplit);
        BeanUtil.copyProperties(update, cashBo, Constants.BeanCopyIgnoreNullValue);
        return MapstructUtils.convert(cashBo, TradeAccCashVo.class);
    }

    /**
     * 提现单调用接口后，反填接口数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCashInfData(TradeAccCashBo bo, TradeAccCashVo vo) {
        // 1 更新提现单的提现状态
        TradeAccCash update = MapstructUtils.convert(bo, TradeAccCash.class);
        boolean isSupplier = AccountOrgTypeEnum.SUPPLIER.getCode().equals(vo.getOrgType());
        boolean isFail = CashInfStatusEnum.FAIL.getCode().equals(bo.getInfStatus());
        if (isSupplier && isFail) {
            // 1 供应商的提现单， 失败后，等待再次绑卡， 再次执行提现      2  总仓的提现单失败后直接删除
            update.setInfStatus(CashInfStatusEnum.CHECK.getCode());
        }
        int count = tradeAccCashMapper.updateById(update);
        if (count > 0) {
            if (CashInfStatusEnum.SUCCESS.getCode().equals(bo.getInfStatus())) {
                if (isSupplier) {
                    // 2 统计
                    List<Long> availIds = tradeAccAvailMapper.selectVoList(Wrappers.lambdaQuery(TradeAccAvail.class)
                            .select(TradeAccAvail::getId).eq(TradeAccAvail::getCashId, update.getId())).stream().map(TradeAccAvailVo::getId).toList();
                    if (availIds.size() > 0) {
                        // 3 更新结算单的提现状态
                        tradeAccAvailMapper.update(Wrappers.lambdaUpdate(TradeAccAvail.class)
                                .set(TradeAccAvail::getStatusTime, Convert.toDate(DateUtil.now()))
                                .set(TradeAccAvail::getCashTime, update.getCashTime())
                                .set(TradeAccAvail::getStatus, AccountStatusEnum.CASH.getCode())
                                .in(TradeAccAvail::getId, availIds)
                                .eq(TradeAccAvail::getCashId, update.getId()));
                        updateCashAccount(availIds, update.getCashTime(), vo.getStatus(), AccountStatusEnum.CASH.getCode());
                    }
                }
                // 4 更新分账单完成
                tradeAccSplitService.updateSplit(new TradeAccSplitUpdateBo()
                        .setTransNo(vo.getCashNo()).setTransTypes(Collections.singletonList(AccountTransTypeEnum.CH.getCode()))
                        .setInfStatus(CashInfStatusEnum.SUCCESS.getCode()).setOutSuccessTime(update.getCashTime())
                        .setOutTradeNo(update.getOutTradeNo()));
            } else if (isFail) {
                updateCashInfFail(isSupplier, bo.getInfStatus(), bo.getInfReason(), vo);
            }
        }
    }
    /**
     * 提现单调用接口后，异常，反填接口数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCashInfFail(Integer infStatus, TradeAccCashVo vo, ServiceException se) {
        // boolean isFail = CashInfStatusEnum.INF_FAIL.getCode().equals(infStatus);
        String message = StrUtil.sub(se.getMessage(), 0, 128);
        TradeAccCash update = new TradeAccCash();
        update.setId(vo.getId());
        update.setInfStatus(infStatus);
        update.setInfReason(message);
        int count = tradeAccCashMapper.updateById(update);
        if (count > 0) {
            // 接口异常，移除分账单
            updateCashInfFail(AccountOrgTypeEnum.SUPPLIER.getCode().equals(vo.getOrgType()), infStatus, message, vo);
        }
    }

    private void updateCashInfFail(boolean isSupplier, Integer infStatus, String infReason, TradeAccCashVo vo) {
        if (!isSupplier) {
            tradeAccCashMapper.deleteById(vo.getId());
        }
        tradeAccSplitMapper.update(Wrappers.lambdaUpdate(TradeAccSplit.class)
                .setSql("del_flag=id").set(TradeAccSplit::getInfStatus, infStatus)
                .set(TradeAccSplit::getInfReason, infReason)
                .eq(TradeAccSplit::getSplitNo, vo.getSplitNo()).eq(TradeAccSplit::getDelFlag, 0));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCashAccount(List<Long> availIds, Date cashTime, Integer preStatus, Integer status) {
        if (availIds.size() == 0) {
            return;
        }
        baseMapper.update(Wrappers.lambdaUpdate(TradeAccTrans.class)
                .set(TradeAccTrans::getStatusTime, Convert.toDate(DateUtil.now()))
                .set(TradeAccTrans::getCashTime, cashTime)
                .set(TradeAccTrans::getStatus, status)
                .in(TradeAccTrans::getAvailId, availIds));
        tradeAccAccountMapper.insertTransAcctByAvail(getAddFields(preStatus, status), availIds);
    }

    private Map<String, String> getAddFields(Integer preStatus, Integer status) {
        Map<String, String> addFields = new HashMap<>();
        switch (preStatus) {
            case 1 -> addFields.put("freeze", "-");
            case 2 -> addFields.put("avail", "-");
            case 3 -> addFields.put("cash", "-");
            case 4 -> addFields.put("check", "-");
        }
        switch (status) {
            case 1 -> addFields.put("freeze", "+");
            case 2 -> addFields.put("avail", "+");
            case 3 -> addFields.put("cash", "+");
            case 4 -> addFields.put("check", "+");
        }
        return addFields;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAvailAccount(Long availId, Date cashTime, Integer preStatus, Integer status) {
        // 1 更新流水单的提现状态
        List<TradeAccTransVo> transVos = baseMapper.selectVoList(Wrappers.lambdaQuery(TradeAccTrans.class)
                        .select(TradeAccTrans::getId, TradeAccTrans::getStatus, TradeAccTrans::getCommissionAmt)
                        .eq(TradeAccTrans::getAvailId, availId)).stream()
                .filter(vo-> preStatus.equals(vo.getStatus())).toList();
        List<Long> transIds = transVos.stream().map(TradeAccTransVo::getId).toList();
        List<Long> commTransIds = transVos.stream().filter(vo -> vo.getCommissionAmt().compareTo(BigDecimal.ZERO)!=0)
                .map(TradeAccTransVo::getId).toList();
        if (transIds.size() > 0) {
            // 2 更新状态
            baseMapper.update(Wrappers.lambdaUpdate(TradeAccTrans.class)
                    .set(TradeAccTrans::getStatusTime, Convert.toDate(DateUtil.now()))
                    .set(TradeAccTrans::getCashTime, cashTime)
                    .set(TradeAccTrans::getStatus, status)
                    .in(TradeAccTrans::getId, transIds));
            // 3 统计
            insertTransAcctByTrans(transIds, preStatus, status, commTransIds, availId);
        }
    }


    @Override
    public void insertTransAcctByTrans(List<Long> transIds, Integer preStatus, Integer status, List<Long> commTransIds, Long availId) {
        if (transIds.size() > 0) {
            tradeAccAccountMapper.insertTransAcctByTrans(getAddFields(preStatus, status), transIds);
        }
        if (commTransIds.size() > 0) {
            Integer nStatsu = status;
            if (AccountStatusEnum.CASH.getCode().equals(nStatsu)) {
                // 结算单为零，直接是提现状态， 这里改回来
                nStatsu = AccountStatusEnum.AVAIL.getCode();
            }
            // preStatus = 0. 1   status = 0. 1. 2， 6
            List<TradeCommTransVo> commTransVos = tradeCommTransMapper.selectVoList(Wrappers.lambdaQuery(TradeCommTrans.class)
                            .select(TradeCommTrans::getId, TradeCommTrans::getAccTransId, TradeCommTrans::getStatus)
                            .in(TradeCommTrans::getAccTransId, commTransIds));
            List<Long> existTransIds = commTransVos.stream().map(TradeCommTransVo::getAccTransId).toList();
            List<Long> notExistTransIds = new ArrayList<>(commTransIds.stream().filter(vo -> !existTransIds.contains(vo)).toList());
            if (notExistTransIds.size() > 0) {
                tradeCommTransMapper.insertCommTransByTrans(notExistTransIds);
            }
            notExistTransIds.addAll(commTransVos.stream().filter(vo-> preStatus.equals(vo.getStatus()))
                    .map(TradeCommTransVo::getAccTransId).toList());
            if (notExistTransIds.size() > 0) {
                LocalDate availDate = null;
                if (availId != null) {
                    TradeAccAvailVo availVo = tradeAccAvailMapper.selectVoOne(Wrappers.lambdaQuery(TradeAccAvail.class)
                            .select(TradeAccAvail::getAvailDate).eq(TradeAccAvail::getId, availId));
                    if (availVo != null) {
                        availDate = availVo.getAvailDate();
                    }
                }
                tradeCommTransMapper.update(Wrappers.lambdaUpdate(TradeCommTrans.class)
                        .set(TradeCommTrans::getAvailDate, availDate)
                        .set(TradeCommTrans::getStatus, nStatsu)
                        .in(TradeCommTrans::getAccTransId, notExistTransIds));
                tradeCustAccountMapper.insertTransAcctByCommTrans(getAddFields(preStatus, nStatsu), notExistTransIds);
            }
        }
    }

    /**
     * 查询营销待转账, 只有订单 和 退单会有，并基于 transNo + outOrgCode 合计后一次调用
     */
    @Override
    public List<TradeAccTransVo> queryDiscountChecking(OrderPayJobQueryBo bo) {
        return baseMapper.queryDiscountChecking(bo, Math.max(bo.getInfRetries(),
                bo.getLongTime() ? channelsProperties.getInfMaxRetries():channelsProperties.getInfMinRetries()));
    }
    /*
     * 测试使用
     */
    @Override
    public List<TradeAccTransVo> queryDiscountTrans(TradeAccTransBo bo) {
        return baseMapper.queryDiscountTrans(bo);
    }
    /**
     * 查询营销待转账 超次数失败的
     */
    @Override
    public List<TradeAccTransVo> queryDiscountFailed(LocalDate transDate) {
        LambdaQueryWrapper<TradeAccTrans> lqw = Wrappers.lambdaQuery();
        lqw.between(TradeAccTrans::getInfTime, transDate, transDate.plusDays(1));
        lqw.eq(TradeAccTrans::getRelateType, AccountTransTypeEnum.OP.getCode());
        lqw.ne(TradeAccTrans::getInfStatus, DiscountInfStatusEnum.SUCCESS.getCode());
        lqw.eq(TradeAccTrans::getDelFlag, 0);
        lqw.orderByAsc(TradeAccTrans::getId);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 提现单调用接口前
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradeAccTransVo updateDiscountInf(TradeAccTransBo accTransBo) {
        accTransBo.setSplitDisNo(tradeBaseUtilBizService.getNextSplitNo(LocalDate.now()));
        TradeAccTrans update = new TradeAccTrans();
        update.setInfRetries(accTransBo.getInfRetries() + 1);
        update.setInfTime(Convert.toDate(DateUtil.now()));
        update.setInfStatus(DiscountInfStatusEnum.INF_INIT.getCode());
        update.setSplitDisNo(accTransBo.getSplitDisNo());
        int count = 0 ;
        List<Long> transIds = Arrays.stream(accTransBo.getRemark().split(";")).map(Long::parseLong).toList();
        if (transIds.size() > 0) {
            count = baseMapper.update(update, Wrappers.lambdaUpdate(TradeAccTrans.class)
                    .in(TradeAccTrans::getId, transIds)
                    .eq(TradeAccTrans::getInfStatus, DiscountInfStatusEnum.CHECK.getCode()));
        }
        if (count == 0) {
            return null;
        }
        TradeAccSplit accSplit = new TradeAccSplit();
        BigDecimal splitAmt = accTransBo.getSplitAmt();
        String transType = AccountTransTypeEnum.DI.getCode();
        Long transId = Constants.TransferTypes.contains(accTransBo.getTransType()) ? 1L : 0L;
        if (splitAmt.compareTo(BigDecimal.ZERO) < 0) {
            // 暂未考虑换供应商      查询 relateno 相同, splitNO = splitrelno or splitrelno = splitrelno and amt > 0    这个查询最多两行， 按 id排序后，取最后一行
            TradeAccSplitVo splitVo = tradeAccSplitMapper.selectVoList(Wrappers.lambdaQuery(TradeAccSplit.class)
                    .eq(TradeAccSplit::getTransType, transType).eq(TradeAccSplit::getTransNo, accTransBo.getRelateNo())
                    .eq(TradeAccSplit::getRelateType, AccountTransTypeEnum.OP.getCode()).eq(TradeAccSplit::getRelateNo, accTransBo.getRelateNo())
                    .eq(TradeAccSplit::getOutOrgCode, accTransBo.getSplitOrgCode()).eq(TradeAccSplit::getDelFlag, 0)).stream().findFirst().orElse(null);
            if (splitVo == null) {
                throw new ServiceException(String.format("营销 %s 支付未执行", accTransBo.getRelateNo()));
            }
            accSplit.setSplitRelAmt(splitVo.getTransAmt());
            accSplit.setSplitRelNo(splitVo.getSplitNo());
            accTransBo.setSplitRelNo(accSplit.getSplitRelNo()); // 临时返回给接口使用的关联单号
            splitAmt = splitAmt.negate();
            transType = AccountTransTypeEnum.DO.getCode();
        }
        accSplit.setSplitNo(accTransBo.getSplitDisNo());
        accSplit.setTransType(transType);
        accSplit.setTransId(transId);
        accSplit.setTransNo(accTransBo.getTransNo());
        accSplit.setTransDate(accTransBo.getTransDate());
        accSplit.setTransOrgId(accTransBo.getTransOrgId());
        accSplit.setAcctOrgId(accTransBo.getAcctOrgId());
        accSplit.setTotalAmt(splitAmt);
        accSplit.setTransAmt(splitAmt);
        accSplit.setFeeAmt(BigDecimal.ZERO);
        accSplit.setBusiType(accTransBo.getBusiType());
        accSplit.setRelateType(AccountTransTypeEnum.OP.getCode());
        accSplit.setRelateNo(accTransBo.getRelateNo());
        accSplit.setRelateAmt(accTransBo.getTotalAmt());
        accSplit.setOutOrgCode(accTransBo.getSplitOrgCode()); // 使用 split_org_code 营销支付,
        accSplit.setOutAcctCode(accTransBo.getSplitAcctCode());
        accSplit.setOrgCode(accTransBo.getOrgCode());
        accSplit.setOrgType(accTransBo.getOrgType());
        accSplit.setOrgId(accTransBo.getOrgId());
		accSplit.setChannel("");
        accSplit.setInitTime(accTransBo.getCreateTime());
        accSplit.setInfTime(update.getInfTime());
        accSplit.setInfStatus(RefundInfStatusEnum.PROCESSING.getCode());
		accSplit.setAcctDate(LocalDate.now());  //  注意： trans表日期和 split 日期可能不一致！！！！  split中日期和平安对账使用
        try {
            tradeAccSplitMapper.insert(accSplit);
        } catch (DuplicateKeyException dke) {
            // 再试一次
            accTransBo.setSplitDisNo(tradeBaseUtilBizService.getNextSplitNo(LocalDate.now()));
            accSplit.setSplitNo(accTransBo.getSplitDisNo());
            tradeAccSplitMapper.insert(accSplit);
            update.setSplitDisNo(accTransBo.getSplitDisNo());
            baseMapper.update(update, Wrappers.lambdaUpdate(TradeAccTrans.class)
                    .eq(TradeAccTrans::getTransType, accTransBo.getTransType())
                    .eq(TradeAccTrans::getTransNo, accTransBo.getTransNo())
                    .in(TradeAccTrans::getId, transIds)
                    .eq(TradeAccTrans::getInfStatus, DiscountInfStatusEnum.CHECK.getCode()));
        }
        return MapstructUtils.convert(accTransBo, TradeAccTransVo.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDiscountInfData(TradeAccTransBo tbo, TradeAccTransVo tvo) {
        TradeAccTrans update = MapstructUtils.convert(tbo, TradeAccTrans.class);
        int count = 0 ;
        List<Long> transIds = Arrays.stream(tvo.getRemark().split(";")).map(Long::parseLong).toList();
        if (transIds.size() > 0) {
            count = baseMapper.update(update, Wrappers.lambdaUpdate(TradeAccTrans.class).in(TradeAccTrans::getId, transIds));
        }
        if (count > 0 && StringUtils.isNotBlank(tvo.getSplitDisNo())
                && DiscountInfStatusEnum.SUCCESS.getCode().equals(update.getInfStatus())) {
            tradeAccSplitMapper.update(Wrappers.lambdaUpdate(TradeAccSplit.class)
                    .set(TradeAccSplit::getInfStatus, update.getInfStatus())
                    .set(TradeAccSplit::getOutTradeNo, update.getOutTradeNo())
					.set(TradeAccSplit::getOutSuccessTime, Convert.toDate(DateUtil.now()))
                    .eq(TradeAccSplit::getSplitNo, tvo.getSplitDisNo()).eq(TradeAccSplit::getDelFlag, 0));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDiscountInfFail(Integer infStatus, TradeAccTransVo tvo,ServiceException se) {
		String message = StrUtil.sub(se.getMessage(), 0, 128);
        int count = 0 ;
        List<Long> transIds = Arrays.stream(tvo.getRemark().split(";")).map(Long::parseLong).toList();
        if (transIds.size() > 0) {
            count = baseMapper.update(Wrappers.lambdaUpdate(TradeAccTrans.class)
                    .set(TradeAccTrans::getInfStatus, infStatus)
                    .set(TradeAccTrans::getInfReason, message)
                    .in(TradeAccTrans::getId, transIds));
        }
        if (count > 0) {
            tradeAccSplitMapper.update(Wrappers.lambdaUpdate(TradeAccSplit.class)
					.setSql("del_flag=id").set(TradeAccSplit::getInfReason, message)
                    .eq(TradeAccSplit::getSplitNo, tvo.getSplitDisNo()).eq(TradeAccSplit::getDelFlag, 0));
        }
    }

    @Override
    public List<RemoteTradeAccTransVo> getByOrderItemId(String orderCode, Long supplierSkuId) {
        LambdaQueryWrapper<TradeAccTrans> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TradeAccTrans::getTransType, AccountTransTypeEnum.OP.getCode());
        wrapper.eq(TradeAccTrans::getTransNo, orderCode);
        wrapper.eq(TradeAccTrans::getSkuId, supplierSkuId);
        List<TradeAccTransVo> tradeAccTransVos = baseMapper.selectVoList(wrapper);
        if (ObjectUtil.isEmpty(tradeAccTransVos)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(tradeAccTransVos, RemoteTradeAccTransVo.class);
    }

    @Override
    public Map<String, RemoteTradeAccTransVo> getByOrderItemIds(List<RemoteAccTransItemBo> bos) {
        List<TradeAccTransVo> list = baseMapper.queryOrderItemIds(bos);
        if (ObjectUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<String, RemoteTradeAccTransVo> map = new HashMap<>();
        list.forEach(vo -> {
            RemoteTradeAccTransVo rvo = BeanUtil.toBean(vo, RemoteTradeAccTransVo.class);
            map.put(vo.getTransNo() + "_" + vo.getSkuId(), rvo);
        });
        return map;
    }

}