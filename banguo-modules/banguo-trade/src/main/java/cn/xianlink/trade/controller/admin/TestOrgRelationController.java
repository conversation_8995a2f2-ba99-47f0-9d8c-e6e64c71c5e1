package cn.xianlink.trade.controller.admin;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.api.domain.bo.*;
import cn.xianlink.trade.api.domain.vo.RemoteOrgAuthVo;
import cn.xianlink.trade.api.domain.vo.RemoteOrgRelationStatusVo;
import cn.xianlink.trade.api.domain.vo.RemoteOrgRelationVo;
import cn.xianlink.trade.channel.pingancloud.PinganCloudOrgBankRelaService;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankRelaBo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;
import cn.xianlink.trade.dubbo.RemoteOrgRelationServiceImpl;
import cn.xianlink.trade.service.ITradeOrgRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 机构绑定类
 * <AUTHOR>
 * @date 2024-06-08
 */
@Tag(name = "机构绑定类")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/relation/test")
@ConditionalOnProperty(prefix = "channels", name = "debug-request", havingValue = "true", matchIfMissing = false)
public class TestOrgRelationController extends BaseController {

    private final transient ITradeOrgRelationService tradeOrgRelationService;
    private final transient RemoteOrgRelationServiceImpl remoteOrgRelationService;
    private final transient PinganCloudOrgBankRelaService pinganCloudOrgBankRelaService;

    @Operation(summary = "开户")
    @PostMapping("/openAccount")
    public R<RemoteOrgRelationStatusVo> openAccount(@Validated @RequestBody RemoteOrgRelationBindBo bo) {
        return R.ok(remoteOrgRelationService.openAccount(bo));
    }

    @Operation(summary = "账户修改")
    @PostMapping("/updateAccount")
    public R<RemoteOrgRelationStatusVo> updateAccount(@Validated @RequestBody RemoteOrgRelationUpdateBo bo) {
        return R.ok(remoteOrgRelationService.updateAccount(bo));
    }

    @Operation(summary = "绑卡")
    @PostMapping("/bindBank")
    public R<RemoteOrgRelationStatusVo> bindBank(@Validated @RequestBody RemoteOrgRelationBo bo) {
        return R.ok(remoteOrgRelationService.bindBank(bo));
    }

    @Operation(summary = "绑卡短信验证")
    @PostMapping("/bindBankCheck")
    public R<RemoteOrgRelationStatusVo> bindBankCheck(@Validated @RequestBody RemoteOrgRelationCheckBo bo) {
        return R.ok(remoteOrgRelationService.bindBankCheck(bo));
    }

    @Operation(summary = "解绑")
    @PostMapping("/unbindBank")
    public R<RemoteOrgRelationStatusVo> unbindBank(@Validated @RequestBody RemoteOrgRelationUnbindBo bo) {
        return R.ok(remoteOrgRelationService.unbindBank(bo));
    }

    @Operation(summary = "销户")
    @PostMapping("/closeAccount")
    public R<RemoteOrgRelationStatusVo> closeAccount(@Validated @RequestBody RemoteOrgRelationUnbindBo bo) {
        return R.ok(remoteOrgRelationService.closeAccount(bo));
    }

    @Operation(summary = "绑卡查询")
    @PostMapping("/queryBindBank")
    public R<RemoteOrgRelationVo> queryBindBank(@Validated @RequestBody RemoteOrgRelationQueryBo bo) {
        return R.ok(remoteOrgRelationService.queryBindBank(bo));
    }

    @Operation(summary = "同名开通")
    @PostMapping("/sameOrgBank")
    public R<TradeOrgBankRelaBo> sameOrgBank(@Validated @RequestBody RemoteOrgAuthCheckBo bo) {
        TradeOrgBankRelaVo vo = new TradeOrgBankRelaVo();
        vo.setOutOrgCode(bo.getSmsCode());
        vo.setOrgCode(bo.getOrgCode());
        vo.setOrgType(bo.getOrgType());
        TradeOrgBankRelaBo infTbo = pinganCloudOrgBankRelaService.sameOrgBank(vo);
        tradeOrgRelationService.updateBankByBo(infTbo);
        return R.ok(infTbo);
    }

    @Operation(summary = "转账授权申请")
    @PostMapping("/sameOrgAuth")
    public R<RemoteOrgAuthVo> sameOrgAuth(@Validated @RequestBody RemoteOrgAuthBo bo) {
        return R.ok(remoteOrgRelationService.sameOrgAuth(bo));
    }


    @Operation(summary = "转账授权申请 短信验证")
    @PostMapping("/sameOrgAuthCheck")
    public R<RemoteOrgAuthVo> sameOrgAuthCheck(@Validated @RequestBody RemoteOrgAuthCheckBo bo) {
        return R.ok(remoteOrgRelationService.sameOrgAuthCheck(bo));
    }


    @Operation(summary = " 转账授权查询")
    @PostMapping("/querySameOrgAuth")
    public R<RemoteOrgAuthVo> querySameOrgAuth(@Validated @RequestBody RemoteOrgRelationQueryBo bo) {
        return R.ok(remoteOrgRelationService.querySameOrgAuth(bo));
    }


}
