package cn.xianlink.trade.controller.city;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.controller.platform.PlatformTradeAccAvailController;
import cn.xianlink.trade.domain.bo.platform.OrderBusiTotalBo;
import cn.xianlink.trade.domain.bo.TradePayCustomerQueryBo;
import cn.xianlink.trade.domain.vo.platform.OrderBusiTotalVo;
import cn.xianlink.trade.domain.vo.TradePayCustomerVo;
import cn.xianlink.trade.service.ITradePayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 客户结算 - 查询已支付数据
 * <AUTHOR>
 * @date 2024-05-28
 * @folder 城市仓端(小程序)/客户结算单
 *
 */
@Tag(name = "客户结算单")
@Validated
@CustomLog
@RequiredArgsConstructor
@RestController
@RequestMapping("/city/accAvail")
public class CityTradeAccAvailController extends BaseController {
    private final transient ITradePayService tradePayService;
    private final transient PlatformTradeAccAvailController platformAvailController;

    @Operation(summary = "查询")
    @PostMapping("/page")
    public R<TableDataInfo<TradePayCustomerVo>> page(@Validated @RequestBody TradePayCustomerQueryBo bo) {
        return R.ok(tradePayService.queryCityPage(bo));
    }

    @Operation(summary = "订单金额")
    @PostMapping("/order")
    public R<OrderBusiTotalVo> order(@Validated @RequestBody OrderBusiTotalBo bo) {
        return platformAvailController.order(bo);
    }

}
