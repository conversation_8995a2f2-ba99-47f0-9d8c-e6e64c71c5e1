package cn.xianlink.trade.service;

import cn.xianlink.trade.domain.TradeAccDaily;
import cn.xianlink.trade.domain.vo.org.TradeAccDailyAmtVo;
import cn.xianlink.trade.domain.vo.org.TradeAccDailyVo;

import java.time.LocalDate;
import java.util.List;

/**
 * 账务总Service接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface ITradeAccDailyService {

    List<TradeAccDailyVo> queryList(LocalDate acctDate);

    List<TradeAccDailyAmtVo> queryDailyAmt(LocalDate acctDate);

    List<TradeAccDailyVo> queryDailyAmtError(LocalDate acctDate);

    List<TradeAccDailyVo> insertBatch(LocalDate acctDate, List<String> outOrgCodes);
    /**
     * 更新昨日数据
     */
    void updateBatch(List<TradeAccDaily> bos);

}
