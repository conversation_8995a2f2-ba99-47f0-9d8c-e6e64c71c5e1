package cn.xianlink.trade.controller.platform;

import cn.hutool.core.io.IoUtil;
import cn.xianlink.common.api.enums.trade.CashInfStatusEnum;
import cn.xianlink.common.json.utils.JsonUtils;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.api.domain.bo.RemoteDateJobQueryBo;
import cn.xianlink.trade.channel.ChannelsContext;
import cn.xianlink.trade.comm.yzh.YzhCommServiceImpl;
import cn.xianlink.trade.domain.bo.comm.TradeCommCashBo;
import cn.xianlink.trade.domain.vo.comm.TradeCommCashVo;
import cn.xianlink.trade.service.ITradeCommCashService;
import com.yunzhanghu.sdk.apiusersign.ApiUserSignServiceClient;
import com.yunzhanghu.sdk.notify.NotifyClient;
import com.yunzhanghu.sdk.notify.domain.NotifyRequest;
import com.yunzhanghu.sdk.notify.domain.NotifyResponse;
import com.yunzhanghu.sdk.payment.domain.NotifyOrderData;
import com.yunzhanghu.sdk.payment.domain.NotifyOrderRequest;
import com.yunzhanghu.sdk.utils.JsonUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;


/**
 * yzh提现回调
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Tag(name = "yzh白名单类")
@CustomLog
@RequiredArgsConstructor
@RestController
@RequestMapping("/white/yzhNotify")
public class WhitePlatformYzhNotifyController  extends BaseController {
    private final static String YW_SUCCESS = "SUCCESS";
    private final static String YW_FAILURE = "FAILURE";

    private final ChannelsContext channelsContext;

    private final YzhCommServiceImpl yzhCommService;

    private final ITradeCommCashService tradeCommCashService;
    @Operation(summary = "发薪回调")
    @PostMapping("/payNotify")
    public String payNotify(HttpServletRequest request) throws IOException {
        channelsContext.initWebUserToken();
        String reqBody = IoUtil.read(request.getInputStream(), Charset.forName(request.getCharacterEncoding()));
        try {
            TradeCommCashBo cashBo = yzhCommService.payCallback(reqBody);
            log.keyword("yzhNotifyPay", cashBo.getCashNo()).info("cashBo={}", cashBo);
            TradeCommCashVo tvo = tradeCommCashService.queryByMerchNo(cashBo.getYwMerchNo());
            if (tvo == null) {
                log.keyword(cashBo.getYwMerchNo(), "yzhCallback").error("发薪回调，单据不存在 {}", JsonUtils.toJsonString(cashBo));
            } else if (CashInfStatusEnum.SUCCESS.getCode().equals(tvo.getYwInfStatus())) {
                log.keyword(tvo.getCashNo(), "yzhCallback").warn("发薪回调，单据已完成，不处理 {}", JsonUtils.toJsonString(cashBo));
            } else {
                log.keyword(tvo.getCashNo(), "yzhCallback").info("发薪回调，单据开始处理");
                tradeCommCashService.updateCommInfData(cashBo.setId(tvo.getId()), tvo);
            }
            return YW_SUCCESS;
        } catch (Exception e) {
            log.keyword("payNotify").error("发薪回调", e);
        }
        return YW_FAILURE;
    }
}