package cn.xianlink.trade.controller.admin;

import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.api.domain.bo.RemoteOrgRelationUpdateBo;
import cn.xianlink.trade.api.domain.vo.RemoteOrgRelationStatusVo;
import cn.xianlink.trade.channel.AccountService;
import cn.xianlink.trade.domain.bo.TradeAccCashAcctBo;
import cn.xianlink.trade.domain.bo.TradeAccCashAcctCashBo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankAmtBo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankAmtQueryBo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankUpdateBo;
import cn.xianlink.trade.domain.vo.org.TradeBaseDataPageVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankAmtVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;
import cn.xianlink.trade.dubbo.RemoteOrgRelationServiceImpl;
import cn.xianlink.trade.service.ITradeOrgBankService;
import cn.xianlink.trade.service.ITradeOrgRelationService;
import cn.xianlink.trade.service.biz.TradeOrgBankBizService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 账务总
 *
 * <AUTHOR>
 * @date 2024-06-12
 * @folder 般果管理中心/财务流水/子账户余额
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/accOrgBank")
public class TradeAccOrgBankController extends BaseController {

    private final transient ITradeOrgBankService tradeOrgBankService;
    private final transient ITradeOrgRelationService tradeOrgRelationService;
    private final transient AccountService accountService;
    private final transient TradeOrgBankBizService tradeOrgBankBizService;
    private final transient RemoteOrgRelationServiceImpl remoteOrgRelationService;

    /**
     * 子账户列表(查询条件) 最多返回20条
     */
    @Operation(summary = "子账户列表(查询条件)")
    @PostMapping("/acctOrgList")
    public R<List<TradeBaseDataPageVo>> acctOrgList(@RequestParam(name = "name", required = false) String name) {
        List<TradeBaseDataPageVo> list = tradeOrgBankService.selectListByName(name).stream().map(e -> {
            TradeBaseDataPageVo vo = new TradeBaseDataPageVo();
            vo.setId(e.getId());
            vo.setCode(e.getOutOrgCode());
            vo.setName(e.getOutOrgName());
            return vo;
        }).collect(Collectors.toList());
        return R.ok(list);
    }

    /**
     * 查询子账户
     */
    @Operation(summary = "查询子账户")
    @PostMapping("/page")
    public R<TableDataInfo<TradeOrgBankAmtVo>> page(@Validated @RequestBody TradeOrgBankAmtQueryBo bo) {
        return R.ok(tradeOrgBankService.customPageList(bo));
    }

    /**
     * 刷新子账户，获取银行数据
     */
    @Operation(summary = "刷新本页子账户")
    @RepeatSubmit()
    @PostMapping("/refreshPage")
    public R<Void> refreshPage(@Validated @RequestBody TradeOrgBankAmtQueryBo bo) {
        TableDataInfo<TradeOrgBankAmtVo> amtVos = tradeOrgBankService.customPageList(bo);
        tradeOrgBankBizService.refreshPage(amtVos.getRows());
        return R.ok();
    }

    /**
     * 资金账户 -> 弹出提现页面（仅总仓账户） （实时查询银行余额接口）
     *  展示子账户，可用余额 bankAvailAmt，可提现余额 bankCashAmt ( 验证要大于 可提现 )
     */
    @PostMapping("/acctInfo")
    public R<TradeOrgBankAmtBo> acctInfo(@Validated @RequestBody TradeAccCashAcctBo bo) {
        return R.ok(accountService.queryBankAmt(bo.getOutOrgCode(), bo.getOutAcctCode()));
    }
    /**
     * 资金账户 -> 提现操作 （仅总仓账户）
     *      提现金额要大于 可提现余额 BankAvailAmt
     */
    @RepeatSubmit()
    @PostMapping("/acctCash")
    public R<Void> acctCash(@Validated @RequestBody TradeAccCashAcctCashBo bo) {
        tradeOrgBankBizService.withdrawCash(bo);
        return R.ok();
    }

    @Operation(summary = "供应商账户修改（公司名，法人）")
    @RepeatSubmit()
    @PostMapping("/updateAccount")
    public R<RemoteOrgRelationStatusVo> updateAccount(@Validated @RequestBody TradeOrgBankUpdateBo bo) {
        TradeOrgBankRelaVo relaVo = tradeOrgRelationService.queryBankByOutCode(bo.getOutOrgCode(), bo.getOutOrgType());
        if (!AccountOrgTypeEnum.SUPPLIER.getCode().equals(relaVo.getOrgType())) {
            throw new ServiceException("仅支持供应商修改公司名和法人");
        }
        return R.ok(remoteOrgRelationService.updateAccount(new RemoteOrgRelationUpdateBo()
                .setOrgCode(relaVo.getOrgCode()).setOrgType(relaVo.getOrgType()).setOrgId(relaVo.getOrgId()).setOrgName(bo.getCompanyName())
                .setCompanyName(bo.getCompanyName()).setReprName(bo.getReprName()).setReprGlobalId(bo.getReprGlobalId())));
    }
}
