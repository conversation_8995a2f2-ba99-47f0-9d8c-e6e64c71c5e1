package cn.xianlink.trade.service;

import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.api.domain.bo.OrderPayBo;
import cn.xianlink.trade.api.domain.bo.OrderPayJobQueryBo;
import cn.xianlink.trade.domain.bo.TradeAccTransBo;
import cn.xianlink.trade.domain.bo.TradePayBo;
import cn.xianlink.trade.domain.bo.TradePayCustomerQueryBo;
import cn.xianlink.trade.domain.bo.TradePayQueryBo;
import cn.xianlink.trade.domain.vo.TradePayCustomerVo;
import cn.xianlink.trade.domain.vo.TradePayPageVo;
import cn.xianlink.trade.domain.vo.TradePayVo;

import java.time.LocalDate;
import java.util.List;

/**
 * 支付Service接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface ITradePayService {

    List<TradePayVo> queryByNos(List<String> orderNos);

    List<TradePayVo> queryByTradeNos(List<String> tradeNos);
    /**
     * 查询支付
     */
    TradePayVo queryByNo(String orderNo);
    /**
     * 查询支付
     */
    TradePayVo queryByTradeNo(String tradeNo);
    /**
     * 查询客户支付列表
     */
    TableDataInfo<TradePayPageVo> queryPageList(TradePayQueryBo bo);
    /**
     * 查询客户支付列表
     */
    List<TradePayPageVo> queryList(TradePayQueryBo bo);
    /**
     * 查询客户支付列表
     */
    TableDataInfo<TradePayCustomerVo> queryCustomerPage(TradePayCustomerQueryBo queryBo);
    /**
     * 查询城市仓支付列表
     */
    TableDataInfo<TradePayCustomerVo> queryCityPage(TradePayCustomerQueryBo queryBo);
    /**
     * 查询未完成支付
     */
    List<TradePayVo> queryPayProcessing(OrderPayJobQueryBo bo);
    /**
     * 查询失败的
     */
    List<TradePayVo> queryPayFailed(LocalDate transDate);
    /**
     * 创建支付单
     */
    TradePayVo insertByBo(boolean isOnlyPay, TradePayBo tbo, List<TradeAccTransBo> transBos);
    /**
     * 重新签名
     */
    TradePayVo updatePayCheckByBo(TradePayBo tbo, TradePayVo tvo, List<TradeAccTransBo> accTransBos);
    /**
     * 仅支付接口
     */
    boolean updatePayInfData(boolean isOnlyPay, TradePayBo tbo, TradePayVo tvo);
    /**
     * 仅支付接口
     */
    void updatePayInfFail(boolean isOnlyPay, TradePayVo tvo, ServiceException se);
    /**
     * 分账
     */
    TradePayVo updateSplitCheckByBo(TradePayVo tvo);
    /**
     * 仅分账接口
     */
    void updateSplitInfData(boolean isEnd, TradePayVo tvo);
    /**
     * 仅分账接口
     */
    void updateSplitInfFail(TradePayVo tvo, ServiceException se);
    /**
     * 查询新请求与原单据是否相同
     */
    boolean querySame(OrderPayBo bo, TradePayVo tvo);


    void updateAcctStatus(LocalDate acctDate, List<TradePayBo> bos);

}
