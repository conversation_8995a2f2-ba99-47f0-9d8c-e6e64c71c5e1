package cn.xianlink.trade.service.impl;

import cn.xianlink.common.api.enums.trade.AccountOrgBindEnum;
import cn.xianlink.common.api.enums.trade.AccountOrgPropertyEnum;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.trade.domain.*;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankRelaBo;
import cn.xianlink.trade.domain.vo.TradeCustAccountVo;
import cn.xianlink.trade.domain.vo.org.*;
import cn.xianlink.trade.mapper.*;
import cn.xianlink.trade.service.ITradeOrgBankDetailService;
import cn.xianlink.trade.service.biz.TradeBaseUtilBizService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 客户绑卡
 *      该类仅支持 ， 开通 付款 00 账号
 * <AUTHOR>
 * @date 2024-05-27
 */
@RequiredArgsConstructor
@Service
public class TradeOrgBankDetailServiceImpl implements ITradeOrgBankDetailService {

    private final transient TradeOrgBankDetailMapper baseMapper;
    private final transient TradeOrgBankMapper tradeOrgBankMapper;
    private final transient TradeOrgRelationMapper tradeOrgRelationMapper;
    private final transient TradeCustAccountMapper tradeCustAccountMapper;
    private final transient TradeBaseUtilBizService tradeBaseUtilBizService;

    @Override
    public String getOutOrgProperty() {
        // 客户开通付款账户 （个体或企业）
        return AccountOrgPropertyEnum.PAY.getCode();
    }

    @Override
    public String getNextOutOrgCode() {
        String outOrgCode = tradeBaseUtilBizService.getNextOutOrgCode();
        Long count = tradeOrgBankMapper.selectCount(Wrappers.lambdaQuery(TradeOrgBank.class)
                .eq(TradeOrgBank::getOutOrgCode, outOrgCode));
        if (count != null && count > 0L) {
            outOrgCode = getNextOutOrgCode();
        }
        return outOrgCode;
    }

    @Override
    public List<TradeOrgBankDetailVo> getBankList(String outOrgCode) {
        return baseMapper.selectVoList(Wrappers.lambdaQuery(TradeOrgBankDetail.class)
                .eq(TradeOrgBankDetail::getOutOrgCode, outOrgCode)
                .eq(TradeOrgBankDetail::getStatus, AccountOrgBindEnum.BIND.getCode())
                .orderByDesc(TradeOrgBankDetail::getId));
    }

    @Override
    public List<TradeOrgBankVo> getRelationsByCode(String orgCode, Integer orgType) {
        return tradeOrgBankMapper.selectVoList(Wrappers.lambdaQuery(TradeOrgBank.class)
                .eq(TradeOrgBank::getSourceOrgCode, orgCode).eq(TradeOrgBank::getOutOrgType, orgType)
                .eq(TradeOrgBank::getOutOrgProperty, getOutOrgProperty()));
    }

    @Override
    public TradeOrgRelationInfoVo getInfoByCode(String orgCode, Integer orgType) {
        TradeOrgRelationVo relationVo = tradeOrgRelationMapper.selectVoOne(Wrappers.lambdaQuery(TradeOrgRelation.class)
                .eq(TradeOrgRelation::getOrgCode, orgCode).eq(TradeOrgRelation::getOrgType, orgType)
                .eq(TradeOrgRelation::getOutOrgProperty, getOutOrgProperty()));
        if (relationVo != null && StringUtils.isNotEmpty(relationVo.getOutOrgCode())) {
            TradeOrgBankVo bankVo = tradeOrgBankMapper.selectVoOne(Wrappers.lambdaQuery(TradeOrgBank.class)
                    .eq(TradeOrgBank::getOutOrgCode, relationVo.getOutOrgCode()));
            if (bankVo == null) {
                return null;
            }
            TradeOrgRelationInfoVo bankRelaVo = MapstructUtils.convert(bankVo, TradeOrgRelationInfoVo.class);
            bankRelaVo.setOrgType(relationVo.getOrgType());
            bankRelaVo.setOrgId(relationVo.getOrgId());
            bankRelaVo.setOrgCode(relationVo.getOrgCode());
            bankRelaVo.setOrgName(relationVo.getOrgName());
            return bankRelaVo;
        }
        return null;
    }

    @Override
    public TradeOrgBankDetailVo getBankByCode(String outOrgCode, String bankAccount) {
        return baseMapper.selectVoOne(Wrappers.lambdaQuery(TradeOrgBankDetail.class)
                .eq(TradeOrgBankDetail::getOutOrgCode, outOrgCode).eq(TradeOrgBankDetail::getBankAccount, bankAccount));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrgRelate(String orgCode, Integer orgType, String outOrgCode, String outAcctCode) {
        tradeOrgRelationMapper.update(Wrappers.lambdaUpdate(TradeOrgRelation.class)
                .set(TradeOrgRelation::getOutOrgCode, outOrgCode)
                .set(TradeOrgRelation::getOutAcctCode, outAcctCode)
                .eq(TradeOrgRelation::getOrgType, orgType).eq(TradeOrgRelation::getOrgCode, orgCode)
                .eq(TradeOrgRelation::getOutOrgProperty, getOutOrgProperty()));
    }
    /**
     * 新增机构子账户绑定
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrgRelate(TradeOrgBankRelaBo bo) {
        TradeOrgRelation add = MapstructUtils.convert(bo, TradeOrgRelation.class);
        TradeOrgBank addBank = MapstructUtils.convert(bo, TradeOrgBank.class);
        if (tradeOrgBankMapper.update(addBank, Wrappers.lambdaUpdate(TradeOrgBank.class)
                    .eq(TradeOrgBank::getOutOrgCode, addBank.getOutOrgCode())) == 0) {
            addBank.setSourceType(0);
            addBank.setSourceOrgId(add.getOrgId());
            addBank.setSourceOrgCode(add.getOrgCode());
            tradeOrgBankMapper.insert(addBank);
            if(tradeOrgRelationMapper.update(add, Wrappers.lambdaUpdate(TradeOrgRelation.class)
                    .eq(TradeOrgRelation::getOrgType, add.getOrgType()).eq(TradeOrgRelation::getOrgCode, add.getOrgCode())
                    .eq(TradeOrgRelation::getOutOrgProperty, getOutOrgProperty())) == 0) {
                tradeOrgRelationMapper.insert(add);
            }
            // 绑定银行时，同时创建余额记录, 用于余额支付
            TradeCustAccountVo accountVo =tradeCustAccountMapper.selectVoOne(Wrappers.lambdaQuery(TradeCustAccount.class)
                    .select(TradeCustAccount::getId).eq(TradeCustAccount::getCustomerId, add.getOrgId()));
            if(accountVo == null) {
                TradeCustAccount custAccount = new TradeCustAccount();
                custAccount.setCustomerId(add.getOrgId());
                tradeCustAccountMapper.insert(custAccount);
            }
        } else {
            // 更换 outOrgCode 的情况
            TradeOrgRelationVo relationVo = tradeOrgRelationMapper.selectVoOne(Wrappers.lambdaUpdate(TradeOrgRelation.class)
                    .eq(TradeOrgRelation::getOrgType, add.getOrgType()).eq(TradeOrgRelation::getOrgCode, add.getOrgCode())
                    .eq(TradeOrgRelation::getOutOrgProperty, getOutOrgProperty()));
            if (!relationVo.getOutOrgCode().equals(addBank.getOutOrgCode())) {
                tradeOrgRelationMapper.update(Wrappers.lambdaUpdate(TradeOrgRelation.class)
                        .set(TradeOrgRelation::getOutOrgCode, addBank.getOutOrgCode())
                        .set(TradeOrgRelation::getOutAcctCode, addBank.getOutAcctCode())
                        .set(StringUtils.isNotBlank(addBank.getOutOrgName()), TradeOrgRelation::getOrgName, addBank.getOutOrgName())
                        .eq(TradeOrgRelation::getOrgType, add.getOrgType()).eq(TradeOrgRelation::getOrgCode, add.getOrgCode())
                        .eq(TradeOrgRelation::getOutOrgProperty, getOutOrgProperty()));
            }
        }
    }

    /**
     * 修改机构子账户绑定
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBankByBo(TradeOrgBankRelaBo bo) {
        TradeOrgBankDetail update = MapstructUtils.convert(bo, TradeOrgBankDetail.class);
        update.setId(null);
        if (baseMapper.update(update, Wrappers.lambdaQuery(TradeOrgBankDetail.class)
                .eq(TradeOrgBankDetail::getOutOrgCode, update.getOutOrgCode())
                .eq(TradeOrgBankDetail::getBankAccount, update.getBankAccount())) == 0
                && StringUtils.isNotBlank(update.getBankAccName())) {
            baseMapper.insert(update);
        }
        Long count = baseMapper.selectCount(Wrappers.lambdaQuery(TradeOrgBankDetail.class)
                .eq(TradeOrgBankDetail::getOutOrgCode, update.getOutOrgCode())
                .eq(TradeOrgBankDetail::getStatus, AccountOrgBindEnum.BIND.getCode()));
        tradeOrgBankMapper.update(Wrappers.lambdaUpdate(TradeOrgBank.class)
                .set(TradeOrgBank::getStatus, count > 0 ? AccountOrgBindEnum.BIND.getCode() : AccountOrgBindEnum.OPEN.getCode())
                .eq(TradeOrgBank::getOutOrgCode, update.getOutOrgCode()));
    }

}
