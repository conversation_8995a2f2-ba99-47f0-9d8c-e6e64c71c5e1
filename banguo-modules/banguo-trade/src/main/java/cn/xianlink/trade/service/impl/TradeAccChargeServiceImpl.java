package cn.xianlink.trade.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.common.api.enums.trade.AccountTransTypeEnum;
import cn.xianlink.common.api.enums.trade.ChargeInfStatusEnum;
import cn.xianlink.common.api.enums.trade.RefundInfStatusEnum;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.domain.TradeAccCharge;
import cn.xianlink.trade.domain.TradeAccSplit;
import cn.xianlink.trade.domain.bo.TradeAccChargeBo;
import cn.xianlink.trade.domain.bo.TradeAccChargeCreateBo;
import cn.xianlink.trade.domain.bo.TradeAccChargeQueryBo;
import cn.xianlink.trade.domain.bo.TradeAccChargeRefundBo;
import cn.xianlink.trade.domain.vo.TradeAccChargeVo;
import cn.xianlink.trade.mapper.TradeAccChargeMapper;
import cn.xianlink.trade.mapper.TradeAccSplitMapper;
import cn.xianlink.trade.service.ITradeAccChargeService;
import cn.xianlink.trade.service.biz.TradeBaseUtilBizService;
import cn.xianlink.trade.service.biz.TradeOrgBankBizService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 账务总Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@RequiredArgsConstructor
@Service
public class TradeAccChargeServiceImpl implements ITradeAccChargeService {

    private final transient TradeAccChargeMapper baseMapper;
    private final transient TradeAccSplitMapper tradeAccSplitMapper;
    private final transient TradeBaseUtilBizService tradeBaseUtilBizService;
    private final transient TradeOrgBankBizService tradeOrgBankBizService;

    @Override
    public TradeAccChargeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public TableDataInfo<TradeAccChargeVo> queryPageList(TradeAccChargeQueryBo bo) {
        LambdaQueryWrapper<TradeAccCharge> lqw = buildQueryWrapper(bo);
        IPage<TradeAccChargeVo> result = baseMapper.selectVoPage(bo.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<TradeAccChargeVo> queryList(TradeAccChargeQueryBo bo) {
        LambdaQueryWrapper<TradeAccCharge> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TradeAccCharge> buildQueryWrapper(TradeAccChargeQueryBo bo) {
        LambdaQueryWrapper<TradeAccCharge> lqw = Wrappers.lambdaQuery();
        lqw.eq(TradeAccCharge::getChargeType, AccountTransTypeEnum.CI.getCode());
        lqw.between(TradeAccCharge::getChargeDate, bo.getChargeDateStart(), bo.getChargeDateEnd());
        lqw.eq(StringUtils.isNotBlank(bo.getChargeNo()), TradeAccCharge::getChargeNo, bo.getChargeNo());
        lqw.orderByDesc(TradeAccCharge::getId);
        return lqw;
    }

    @Override
    public List<TradeAccChargeVo> queryRefundList(String relateNo) {
        return baseMapper.selectVoList(Wrappers.lambdaQuery(TradeAccCharge.class)
                .eq(TradeAccCharge::getRelateNo, relateNo)
                .eq(TradeAccCharge::getChargeType, AccountTransTypeEnum.CO.getCode())
                .orderByAsc(TradeAccCharge::getId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradeAccChargeVo insertByBo(TradeAccChargeCreateBo addBo, String saleFeeAcctCode) {
        TradeAccCharge add = getAddTradeAccCharge(saleFeeAcctCode);
        add.setChargeAmt(addBo.getChargeAmt());
        add.setRemark(addBo.getRemark());
        add.setRelateNo(add.getChargeNo());
        add.setChargeType(AccountTransTypeEnum.CI.getCode());
        baseMapper.insert(add);
        TradeAccSplit accSplit = getAddTradeAccSplit(add);
        accSplit.setTotalAmt(add.getChargeAmt());
        accSplit.setTransAmt(add.getChargeAmt());
        accSplit.setRelateAmt(add.getChargeAmt());
        tradeAccSplitMapper.insert(accSplit);
        return MapstructUtils.convert(add, TradeAccChargeVo.class);
    }
    private TradeAccCharge getAddTradeAccCharge(String saleFeeAcctCode) {
        RemoteBaseDataVo manageOrgVo = tradeOrgBankBizService.getManageOrgCode();
        if (manageOrgVo == null) {
            throw new ServiceException("默认管理机构未定义");
        }
        TradeAccCharge add = new TradeAccCharge();
        add.setAcctOrgIds(String.format(";%s;", manageOrgVo.getId()));
        add.setOrgId(manageOrgVo.getId());
        add.setOrgType(manageOrgVo.getType());
        add.setOrgCode(manageOrgVo.getCode());
        add.setChargeDate(LocalDate.now());
        add.setChargeNo(tradeBaseUtilBizService.getNextChargeNo(add.getChargeDate()));
        add.setInfTime(Convert.toDate(DateUtil.now()));
        add.setInfStatus(ChargeInfStatusEnum.PROCESSING.getCode());
        add.setSplitNo(tradeBaseUtilBizService.getNextSplitNo(add.getChargeDate()));
        add.setOutAcctCode(saleFeeAcctCode);
        add.setOutOrgCode(saleFeeAcctCode);
        return add;
    }
    private TradeAccSplit getAddTradeAccSplit(TradeAccCharge add) {
        TradeAccSplit accSplit = new TradeAccSplit();
        accSplit.setSplitNo(add.getSplitNo());
        accSplit.setTransType(add.getChargeType());
        accSplit.setTransId(add.getId());
        accSplit.setTransNo(add.getChargeNo());
        accSplit.setTransDate(add.getChargeDate());
        accSplit.setTransOrgId(0L);
        accSplit.setAcctOrgId(add.getOrgId());
        accSplit.setOutAcctCode(add.getOutAcctCode());
        accSplit.setOutOrgCode(add.getOutOrgCode());
        accSplit.setOrgCode(add.getOrgCode());
        accSplit.setOrgType(add.getOrgType());
        accSplit.setOrgId(add.getOrgId());
        accSplit.setBusiType("");
        accSplit.setRelateNo(AccountTransTypeEnum.CI.getCode().equals(add.getChargeType()) ? add.getChargeNo() : add.getRelateNo());
        accSplit.setRelateType(AccountTransTypeEnum.CI.getCode());
        accSplit.setSplitRelNo(add.getSplitRelNo());
		accSplit.setChannel("");
        accSplit.setInitTime(add.getCreateTime());
        accSplit.setInfTime(add.getInfTime());
        accSplit.setInfStatus(RefundInfStatusEnum.PROCESSING.getCode());
		accSplit.setAcctDate(LocalDate.now());
        return accSplit;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradeAccChargeVo insertByBo(TradeAccChargeRefundBo addBo, String saleFeeAcctCode) {
        TradeAccCharge charge = baseMapper.selectById(addBo.getId());
        if (charge == null) {
            throw new ServiceException("充值单不存在");
        }
        if (charge.getChargeAmt().compareTo(charge.getRefundAmt().add(addBo.getRefundAmt())) < 0) {
            throw new ServiceException("充值单可退回金额不足");
        }
        TradeAccCharge add = getAddTradeAccCharge(saleFeeAcctCode);
        add.setRefundAmt(addBo.getRefundAmt());
        add.setRemark(addBo.getRemark());
        add.setRelateNo(charge.getChargeNo());
        add.setSplitRelNo(charge.getSplitNo());
        add.setChargeType(AccountTransTypeEnum.CO.getCode());
        baseMapper.insert(add);
        TradeAccSplit accSplit = getAddTradeAccSplit(add);
        accSplit.setTotalAmt(add.getRefundAmt());
        accSplit.setTransAmt(add.getRefundAmt());
        accSplit.setRelateAmt(charge.getChargeAmt());
        accSplit.setSplitRelAmt(charge.getChargeAmt());
        tradeAccSplitMapper.insert(accSplit);
        return MapstructUtils.convert(add, TradeAccChargeVo.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInfData(TradeAccChargeBo infTbo, TradeAccChargeVo vo) {
        TradeAccCharge update = MapstructUtils.convert(infTbo, TradeAccCharge.class);
        baseMapper.updateById(update);
        if (AccountTransTypeEnum.CO.getCode().equals(vo.getChargeType())) {
            TradeAccCharge charge = baseMapper.queryChargeByRefund(vo.getRelateNo());
            if (charge.getRefundCount() == null) {
                charge.setRefundCount(0);
                charge.setRefundAmt(BigDecimal.ZERO);
            }
            baseMapper.update(charge, Wrappers.lambdaQuery(TradeAccCharge.class)
                    .eq(TradeAccCharge::getChargeNo, vo.getRelateNo()));
        }
        tradeAccSplitMapper.update(Wrappers.lambdaUpdate(TradeAccSplit.class)
				.set(TradeAccSplit::getInfStatus, update.getInfStatus())
				.set(TradeAccSplit::getOutTradeNo, update.getOutTradeNo())
				.set(TradeAccSplit::getOutSuccessTime, Convert.toDate(DateUtil.now()))
                .eq(TradeAccSplit::getSplitNo, vo.getSplitNo()).eq(TradeAccSplit::getDelFlag, 0));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInfFail(Integer infStatus, TradeAccChargeVo vo, ServiceException se) {
		String message = StrUtil.sub(se.getMessage(), 0, 128);
        TradeAccCharge update = new TradeAccCharge();
        update.setId(vo.getId());
        update.setInfStatus(infStatus);
        update.setInfReason(message);
        int count = baseMapper.updateById(update);
        if (count > 0) {
            tradeAccSplitMapper.update(Wrappers.lambdaUpdate(TradeAccSplit.class)
					.setSql("del_flag=id").set(TradeAccSplit::getInfReason, message)
                    .eq(TradeAccSplit::getSplitNo, vo.getSplitNo()).eq(TradeAccSplit::getDelFlag, 0));
        }
    }

}
