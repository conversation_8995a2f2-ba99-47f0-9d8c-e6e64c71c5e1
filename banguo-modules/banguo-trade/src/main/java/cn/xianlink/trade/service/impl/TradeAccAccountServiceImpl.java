package cn.xianlink.trade.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.core.enums.CoreEnumRow;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.api.util.BaseEntityAutoFill;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.*;
import cn.xianlink.trade.domain.bo.TradeAccSupAccountQueryBo;
import cn.xianlink.trade.domain.bo.TradeAccSupAvailAccountQueryBo;
import cn.xianlink.trade.domain.convert.acc.TradeAccSupAvailAccountConvert;
import cn.xianlink.trade.domain.vo.*;
import cn.xianlink.trade.mapper.*;
import cn.xianlink.trade.service.ITradeAccAccountService;
import cn.xianlink.trade.service.biz.TradeBaseUtilBizService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 账务总Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class TradeAccAccountServiceImpl implements ITradeAccAccountService, InitializingBean {

    private final transient TradeAccAccountMapper baseMapper;
    private final transient TradeAccTransAcctMapper tradeAccTransAcctMapper;
    private final transient TradePayMapper tradePayMapper;
    private final transient TradePayRefundMapper tradePayRefundMapper;
    private final transient TradeAccTransferMapper tradeAccTransferMapper;
    private final transient TradeBaseUtilBizService tradeBaseUtilBizService;
    private transient List<String> virtualOrgList;


    @Override
    public void afterPropertiesSet() throws Exception {
        virtualOrgList = Arrays.stream(AccountVirtualOrgEnum.class.getEnumConstants())
                .map(AccountVirtualOrgEnum::getCode).toList();
    }


    /**
     * 查询账务总， 即使未发生业务，也返回 0
     */
    @Override
    // @Cacheable(cacheNames = TradeCacheNames.CACHE_ORG_ACCOUNT, key = "#orgCode + '_' + #orgType")
    public TradeAccAccountVo querySupOrgAccount(String orgCode, Integer orgType, Long deptId) {
        TradeAccAccountVo accountVo = baseMapper.queryOrgAccountSum(orgCode, orgType, deptId);
        return accountVo == null ? new TradeAccAccountVo() : accountVo;
    }

    @Override
    @BaseEntityAutoFill
    public TableDataInfo<TradeAccAccountVo> customSupPageList(TradeAccSupAccountQueryBo queryBo) {
        Page<TradeAccAccount> page = queryBo.build();
        page.setOptimizeJoinOfCountSql(false);
        Page<TradeAccAccountVo> result = baseMapper.customSupList(page, buildQueryWrapper(queryBo));
        return TableDataInfo.build(result);
    }

    @Override
    @BaseEntityAutoFill
    public List<TradeAccAccountVo> customSupList(TradeAccSupAccountQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccAccount> lqw = buildQueryWrapper(queryBo);
        return baseMapper.customSupList(lqw);
    }

    private LambdaQueryWrapper<TradeAccAccount> buildQueryWrapper(TradeAccSupAccountQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccAccount> lqw = Wrappers.lambdaQuery();
        lqw.eq(TradeAccAccount::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode());
        lqw.in(CollectionUtil.isNotEmpty(queryBo.getOrgCodes()), TradeAccAccount::getOrgCode, queryBo.getOrgCodes());
//        if (CollectionUtil.isNotEmpty(queryBo.getAcctOrgCodes())) {
//            lqw.in(TradeAccAccount::getAcctOrgId, tradeBaseUtilBizService.queryIdsByCodes(queryBo.getAcctOrgCodes(), BaseTypeEnum.REGION_WH.getCode()));
//        }
        lqw.eq(TradeAccAccount::getDelFlag, YNStatusEnum.DISABLE.getCode());
        return lqw;
    }

    @Override
    @BaseEntityAutoFill
    public TableDataInfo<TradeAccSupAvailAccountVo> customSupAvailPageList(TradeAccSupAvailAccountQueryBo queryBo) {
        Page<TradeAccAccount> page = queryBo.build();
        page.setOptimizeJoinOfCountSql(false);
        Page<TradeAccAccountVo> result = baseMapper.customSupAvailList(page, buildQueryWrapper(queryBo));
        return TableDataInfo.build(result.getRecords().stream()
                .map(TradeAccSupAvailAccountConvert.INSTANCE::toConvert).toList(), result.getTotal());
    }

    @Override
    @BaseEntityAutoFill
    public List<TradeAccSupAvailAccountVo> customSupAvailList(TradeAccSupAvailAccountQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccAccount> lqw = buildQueryWrapper(queryBo);
        return baseMapper.customSupAvailList(lqw).stream().map(TradeAccSupAvailAccountConvert.INSTANCE::toConvert).toList();
    }

    private LambdaQueryWrapper<TradeAccAccount> buildQueryWrapper(TradeAccSupAvailAccountQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccAccount> lqw = Wrappers.lambdaQuery();
        lqw.eq(TradeAccAccount::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode());
        lqw.in(CollectionUtil.isNotEmpty(queryBo.getOrgCodes()), TradeAccAccount::getOrgCode, queryBo.getOrgCodes());
        if (CollectionUtil.isNotEmpty(queryBo.getAcctOrgCodes())) {
            lqw.in(TradeAccAccount::getAcctOrgId, tradeBaseUtilBizService.queryIdsByCodes(queryBo.getAcctOrgCodes(), BaseTypeEnum.REGION_WH.getCode()));
        }
//        if (CollectionUtil.isNotEmpty(queryBo.getDeptIds())) {
//            lqw.in(TradeAccAccount::getDeptId, queryBo.getDeptIds());
//        }
        lqw.eq(TradeAccAccount::getDelFlag, YNStatusEnum.DISABLE.getCode());
        lqw.orderByAsc(TradeAccAccount::getId);
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAccountYdaAmt() {
        baseMapper.update(Wrappers.lambdaUpdate(TradeAccAccount.class)
                .setSql("yda_freeze_amt=freeze_amt,yda_avail_amt=avail_amt," +
                        "yda_acct_avail_amt=avail_amt+check_amt+freeze_amt-freeze_in_amt-freeze_out_amt"));
    }

    /**
     * 查询未合计的流水
     */
    @Override
    public List<TradeAccTransAcctVo> getTransAcctList(int limit) {
        LambdaQueryWrapper<TradeAccTransAcct> lqw = new LambdaQueryWrapper<>(TradeAccTransAcct.class)
                .select(TradeAccTransAcct::getId, TradeAccTransAcct::getOrgCode, TradeAccTransAcct::getOrgType,
                        TradeAccTransAcct::getDeptId, TradeAccTransAcct::getOutOrgCode, TradeAccTransAcct::getAcctOrgId,
                        TradeAccTransAcct::getOrgId, TradeAccTransAcct::getOutAcctCode, TradeAccTransAcct::getTransNo,
                        TradeAccTransAcct::getTransType, TradeAccTransAcct::getTransDate)
                .eq(TradeAccTransAcct::getDelFlag, YNStatusEnum.DISABLE.getCode())
                .orderByAsc(TradeAccTransAcct::getId).last("limit " + limit);
        return tradeAccTransAcctMapper.selectVoList(lqw);
    }

    /**
     * 查询未合计的流水
     */
    @Override
    public Map<String, List<TradeAccTransAcctVo>> getTransAcctDelMap() {
        LambdaQueryWrapper<TradeAccTransAcct> lqw = new LambdaQueryWrapper<>(TradeAccTransAcct.class)
                .select(TradeAccTransAcct::getId, TradeAccTransAcct::getOrgCode, TradeAccTransAcct::getOrgType,
                        TradeAccTransAcct::getDeptId, TradeAccTransAcct::getOutOrgCode, TradeAccTransAcct::getAcctOrgId,
                        TradeAccTransAcct::getOrgId, TradeAccTransAcct::getOutAcctCode, TradeAccTransAcct::getTransNo,
                        TradeAccTransAcct::getTransType, TradeAccTransAcct::getTransDate)
                .ne(TradeAccTransAcct::getDelFlag, YNStatusEnum.DISABLE.getCode())
                .orderByAsc(TradeAccTransAcct::getId);
        return toAcctTransMap(tradeAccTransAcctMapper.selectVoList(lqw));
    }

    /**
     * 更新到总计表中
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, List<TradeAccTransAcctVo>> updateAccountAmt(List<TradeAccTransAcctVo> acctVos) {
        if (acctVos.size() == 0) {
            return new HashMap<>();
        }
        List<Long> acctIds = acctVos.stream().map(TradeAccTransAcctVo::getId).toList();
        int count = tradeAccTransAcctMapper.update(Wrappers.lambdaUpdate(TradeAccTransAcct.class)
                .setSql("del_flag=id").in(TradeAccTransAcct::getId, acctIds)
                .eq(TradeAccTransAcct::getDelFlag, YNStatusEnum.DISABLE.getCode())
        );
        if (acctIds.size() != count) {
            // 防止并发相同数据
            throw new ServiceException("不能统计已处理的数据");
        }
        Map<String, List<TradeAccTransAcctVo>> acctMap = acctVos.stream().collect(Collectors.groupingBy(vo ->
                String.format("%s_%s_%s_%s_%s", vo.getOrgCode(), vo.getOrgType(), vo.getDeptId(), vo.getOutOrgCode(), vo.getAcctOrgId()), Collectors.toList()));
        count = baseMapper.updateAccountAmt(acctIds, virtualOrgList);
        if (acctMap.size() != count) {
            // 有新账户数据
            List<TradeAccAccountVo> existAccountVos = baseMapper.queryExistAccount(acctMap.values().stream().map(vos -> vos.get(0)).toList());
            Map<String, List<TradeAccAccountVo>> existAccoutMap = existAccountVos.stream().collect(Collectors.groupingBy(vo ->
                    String.format("%s_%s_%s_%s_%s", vo.getOrgCode(), vo.getOrgType(), vo.getDeptId(), vo.getOutOrgCode(), vo.getAcctOrgId()), Collectors.toList()));
            Map<String, List<TradeAccTransAcctVo>> notExistAcctMap = acctMap.entrySet().stream()
                    .filter(entry -> !existAccoutMap.containsKey(entry.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            if (notExistAcctMap.size() > 0) {
                List<Long> notExistAcctIds = notExistAcctMap.values().stream().flatMap(List::stream).map(TradeAccTransAcctVo::getId).toList();
                baseMapper.insertNotExistAccount(notExistAcctMap.values().stream().map(vos -> vos.get(0)).toList());
                baseMapper.updateAccountAmt(notExistAcctIds, virtualOrgList);
            }
        }
        return toAcctTransMap(acctVos);
    }

    private Map<String, List<TradeAccTransAcctVo>> toAcctTransMap(List<TradeAccTransAcctVo> acctVos) {
        return acctVos.stream().collect(Collectors.groupingBy(vo ->
                String.format("%s_%s",
                        AccountTransTypeEnum.OS.getCode().equals(vo.getTransType()) ? AccountTransTypeEnum.OR.getCode() : (
                                AccountTransTypeEnum.TO.getCode().equals(vo.getTransType()) ? AccountTransTypeEnum.TI.getCode() :
                                        vo.getTransType()), vo.getTransNo()), Collectors.toList()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusByTrans(List<TradeAccTransAcctVo> acctVos) {
        if (acctVos.size() == 0) {
            return;
        }
        tradeAccTransAcctMapper.deleteBatchIds(acctVos.stream().map(TradeAccTransAcctVo::getId).toList());
        TradeAccTransAcctVo transVo = acctVos.get(0);
        switch (Objects.requireNonNull(AccountTransTypeEnum.getEnumByCode(transVo.getTransType()))) {
            case OP -> {
                TradePay update = tradePayMapper.queryStatusByTrans(transVo.getTransNo(), Constants.PayTypes);
                if (update != null && update.getStatus() != null) {
                    update.setOrderNo(transVo.getTransNo());
                    tradePayMapper.updateStatusByTrans(update);
                }
            }
            case OR, OS -> {
                TradePayRefund update = tradePayRefundMapper.queryStatusByTrans(transVo.getTransNo(), Constants.RefundTypes);
                if (update != null && update.getStatus() != null) {
                    tradePayRefundMapper.update(update, Wrappers.lambdaQuery(TradePayRefund.class)
                            .eq(TradePayRefund::getRefundNo, transVo.getTransNo()).eq(TradePayRefund::getDelFlag, 0));
                }
            }
            case TI, TO -> {
                TradeAccTransfer update = tradeAccTransferMapper.queryStatusByTrans(transVo.getTransNo(), Constants.TransferTypes);
                if (update != null && update.getStatus() != null) {
                    // a.inf_time = case when a.busi_type = 'TA' and b.min_status = 2 and b.status = 2 then now() else a.inf_time end
                    update.setInfStatus(null);
                    tradeAccTransferMapper.update(update, Wrappers.lambdaQuery(TradeAccTransfer.class)
                            .eq(TradeAccTransfer::getTransNo, transVo.getTransNo()).eq(TradeAccTransfer::getDelFlag, 0));
                }
            }
        }
    }

}
