package cn.xianlink.trade.constant;

/**
 * 缓存组名称常量
 * <p>
 * key 格式为 cacheNames#ttl#maxIdleTime#maxSize
 * <p>
 * ttl 过期时间 如果设置为0则不过期 默认为0
 * maxIdleTime 最大空闲时间 根据LRU算法清理空闲数据 如果设置为0则不检测 默认为0
 * maxSize 组最大长度 根据LRU算法清理溢出数据 如果设置为0则无限长 默认为0
 * <p>
 * 例子: test#60s、test#0#60s、test#0#1m#1000、test#1h#0#500
 *
 * <AUTHOR> Li
 */
public interface TradeCacheNames {

    String ONLINE_TOKEN_KEY = "online_trade_tokens:";

    /**  缓存 */
    // 缓存 机构关系
    String CACHE_BASE_DATA = "trade:cache:base_data#300s";
    // 缓存 机构关系
    String CACHE_ORG_BIND = "trade:cache:org_bind#30s";
    // 机构金额和银行卡数据
    String CACHE_ORG_ACCOUNT = "trade:cache:org_account#30s";
    // 当日提现单缓存
    String CACHE_CREATE_CASH = "trade:cache:create_cash#30s";
    // 当日提现单缓存
    String CACHE_CREATE_AVAIL = "trade:cache:create_avail#30s";
    // 请求退款频度 同订单 60s 最多2单
    String CACHE_WX_REFUND_FREQ = "trade:cache:wx_refund_freq#60s";
    // 有为token缓存
    String CACHE_YW_ACCESS_TOKEN = "trade:cache:yw_access_token";

    /*  分布锁 */
    // 支付单据锁
    String LOCK_ORG_RELATION = "trade:lock:org_relation";
    // 支付单据锁
    String LOCK_ORDER_PAYMENT = "trade:lock:order_payment";
    // 支付单据锁
    String LOCK_CREATE_AVAIL = "trade:lock:create_avail";
    // 支付单据锁
    String LOCK_CREATE_CASH = "trade:lock:create_cash";
    // 云数据下载
    String LOCK_CLOUD_DOWNLOAD = "trade:lock:cloud_download";
    // 锁提现
    String LOCK_WITHDRAW_CASH = "trade:lock:withdraw_cash";
    // 有为token锁
    String LOCK_YW_ACCESS_TOKEN = "trade:lock:yw_access_token";
    /*  提现单号 */
    String NO_ACC_CASH = "trade:no:acc_cash_";
    /*  佣金单号 */
    String NO_COMM_CASH = "trade:no:comm_cash_";

}
