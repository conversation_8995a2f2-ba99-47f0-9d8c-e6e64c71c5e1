package cn.xianlink.trade.controller.admin;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.trade.api.domain.bo.RemoteTransferChangeBo;
import cn.xianlink.trade.api.domain.bo.RemoteTransferCreateBo;
import cn.xianlink.trade.api.domain.bo.RemoteTransferQueryBo;
import cn.xianlink.trade.domain.bo.TradeAccTransferQueryBo;
import cn.xianlink.trade.domain.bo.TradeAccTransferStatusBo;
import cn.xianlink.trade.domain.vo.TradeAccTransferCreateVo;
import cn.xianlink.trade.domain.vo.TradeAccTransferVo;
import cn.xianlink.trade.service.ITradeAccTransferService;
import cn.xianlink.trade.service.biz.TradeAccTransBizService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 划转流水
 *
 * <AUTHOR>
 * @date 2024-06-12
 * @folder 般果管理中心/财务流水/划转单处理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/accTransfer")
public class TradeAccTransferController extends BaseController {

    private final transient ITradeAccTransferService tradeAccTransferService;
    private final transient TradeAccTransBizService tradeAccTransBizService;
    /**
     * 划转单查询
     */
    @Operation(summary = "划转单查询")
    @PostMapping("/page")
    public R<TableDataInfo<TradeAccTransferVo>> page(@Validated @RequestBody TradeAccTransferQueryBo bo) {
        return R.ok(tradeAccTransferService.queryPageList(bo));
    }
    /**
     * 划转单导出
     */
    @Operation(summary = "划转单导出")
    @PostMapping("/export")
    public void export(@Validated TradeAccTransferQueryBo bo, HttpServletResponse response) {
        List<TradeAccTransferVo> list = tradeAccTransferService.queryList(bo);
        ExcelUtil.exportExcel(list, "划转流水", TradeAccTransferVo.class, response);
    }

    /**
     * 划转流水状态修改（非供应商的加扣款，做完结标记）
     */
    @Operation(summary = "划转单状态修改")
    @PostMapping("/transferStatus")
    public R<Void> page(@Validated @RequestBody TradeAccTransferStatusBo bo) {
        tradeAccTransferService.updateOperStatus(bo);
        return R.ok();
    }
    /**
     * 手动创建加扣款
     * @ignore
     */
    @RepeatSubmit()
    @PostMapping("/transferCreate")
    public R<TradeAccTransferCreateVo> transferCreate(@Validated @RequestBody RemoteTransferCreateBo bo) {
        return R.ok(MapstructUtils.convert(tradeAccTransBizService.transferCreate(bo), TradeAccTransferCreateVo.class));
    }

    /**
     * 手动更换供应商
     *  @ignore
     */
    @RepeatSubmit()
    @PostMapping("/transferChange")
    public R<TradeAccTransferCreateVo> transferChange(@Validated @RequestBody RemoteTransferChangeBo bo) {
        return R.ok(MapstructUtils.convert(tradeAccTransBizService.transferChange(bo), TradeAccTransferCreateVo.class));
    }

    /**
     * 供应商加款-手动调用银行接口
     */
    @Operation(summary = "供应商加款-手动调用银行接口")
    @RepeatSubmit()
    @PostMapping("/transferCheck")
    public R<TradeAccTransferCreateVo> transferCheck(@Validated @RequestBody RemoteTransferQueryBo bo) {
        return R.ok(MapstructUtils.convert(tradeAccTransBizService.transferDisCheck(bo.getTransNo()), TradeAccTransferCreateVo.class));
    }

    /**
     * 加扣款关闭-手动关闭（不含换供应商）
     */
    @Operation(summary = "加扣款关闭-手动关闭（关闭待结算的）")
    @RepeatSubmit()
    @PostMapping("/transferClose")
    public R<TradeAccTransferCreateVo> transferClose(@Validated @RequestBody RemoteTransferQueryBo bo) {
        return R.ok(MapstructUtils.convert(tradeAccTransBizService.transferClose(bo.getTransNo()), TradeAccTransferCreateVo.class));
    }
}
